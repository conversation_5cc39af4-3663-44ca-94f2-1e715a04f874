import { getStorageString, KEY } from "../storages";
import apiProtheus from "./apiProtheus";

export const apiProtheusAgenda = async (email: string) => {

  try {

    const result = await apiProtheus("/AgendaInstaladores", {
      method:"GET",
      headers:{        
        'Authorization': `Bearer ${await getStorageString(KEY.protheusToken)}`
      },
      params:{        
        'EMAIL' : email,
        'TipoCarga': 'C',
      },      
    });

    return result;
  }
  catch(error:any) {
      return error.response.data;
  }
}

export default apiProtheusAgenda;
