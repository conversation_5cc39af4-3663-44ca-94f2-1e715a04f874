import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import { MockVariableJson } from '../../__mocks__/VariableJsonMock';
import apiZordon from './apiZordon';
import apiZordonVariaveis from './apiZordonVariaveis';

import * as ApiZordon from './apiZordon';
const spyApiZordon = jest.spyOn(ApiZordon, 'default');

jest.mock('./apiZordon');

describe('apiZordonVariaveis - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiZordon.defaults.baseURL).toBe('https://telemetria-api-hmg.comerc.com.br/v1/');
        expect(apiZordon.defaults.responseType).toBe('json');
        expect(apiZordon.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/tabelas/EN_KRON_MULTK120/').reply(200, data);

        apiZordon.get = jest.fn().mockReturnValueOnce({data:MockVariableJson});

        const response = await apiZordon.get('/tabelas/EN_KRON_MULTK120/');
        expect(response.data).toEqual(MockVariableJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiZordon.mockRejectedValue({response:{data:'erro'}})
        const response = await apiZordonVariaveis('/tabelas/EN_KRON_MULTK120/');

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiZordon.mockResolvedValue({data:MockVariableJson})
        const response = await apiZordonVariaveis('/tabelas/EN_KRON_MULTK120/');
        expect(response.data).toEqual(MockVariableJson);    
    });    
});
