// __tests__/events.test.js
import { EventRegister } from 'react-native-event-listeners';

import {
  _testExports,
  connectMQTT,
  disconnectMQTT,
  eventAddCodi,
  eventApnAdd,
  eventApnConfig,
  eventApnRemove,
  eventBattery, eventCodi, eventCodiConfig, eventCodiEnd, eventCodiStatus, eventDateTime, eventEthernet, eventExceptions, eventFindedDbgack, eventFindedSysConfig,
  eventFirmware, eventFirmwareBoot, eventFormat, eventFormatFile, eventFormatMessage, eventGateway, eventGSM, eventHeap, eventION, eventIonAdd, eventIonAutoSearch, eventIonAutoSearching,
  eventIonConfig,
  eventIonData,
  eventIonEnd, eventIonStatus, eventIP, eventIsConnected,
  eventKhomp,
  eventListFiles, eventMessage, eventModbus, eventModbusAddBeginFim, eventModbusAddBeginIni, eventModbusAddDevice,
  eventModbusAddMean, eventModbusAddSlave, eventModbusAddVariable, eventModbusConfig,
  eventModbusData,
  eventModbusMaster, eventModbusReset, eventModuleScan, eventOperadora, eventOTA, eventOTARollback,
  eventOTAUpdate, eventOTAUpdating, eventPulso, eventPulsoAdd, eventPulsoConfig, eventPulsoEnd, eventPulsoReset, eventPulsoStatus, eventReconnect, eventRede, eventResets, eventSaveEthernet,
  eventSaveWifi, eventSignal, eventSistema, eventStatusVProbe, eventTotalMemory, eventTypeConnection, eventUsedMemory, eventVBattery, eventWifi,
  getApnAdd,
  getApnConfig,
  getApnRemove,
  getCODI,
  getCodiAdd,
  getCodiConfig, getCodiEnd,
  getCodiStatus,
  getDataHora,
  getExcecoes,
  getFirmware,
  getFirmwareBoot,
  getFormataArquivo,
  getFormatado,
  getHeap,
  getION,
  getIonAdd, getIonAutoSearch,
  getIonConfig, getIonData, getIonEnd,
  getIonStatus,
  getIP,
  getKhomp,
  getListarArquivos,
  getMensagensFormatado,
  getModbus,
  getModbusAddBeginFim,
  getModbusAddBeginIni,
  getModbusAddDevice,
  getModbusAddMean,
  getModbusAddSlave, getModbusAddVariable,
  getModbusConfig,
  getModbusData,
  getModbusMaster,
  getModbusReset,
  getModulosEscaneados,
  getNivelBateria,
  getOTA,
  getPulso,
  getPulsoAdd,
  getPulsoConfig, getPulsoEnd,
  getPulsoReset,
  getPulsoStatus,
  getReconectada,
  getRede,
  getRedeEthernet,
  getRedeGSM,
  getRedeWifi,
  getResets,
  getSalvaEthernet,
  getSalvaWifi,
  getSinal,
  getSistema,
  getStatusTensaoProbe,
  getSystemOTARollback, getSystemOTAUpdate,
  getTensaoBateria,
  getTipoConexaoProbe,
  getTipoOperadora,
  getTotalMemory, getUsedMemory,
  InfoReleased,
  infoReleasedAllMQTT,
  isMQTTConnected,
  publishMQTT,
  subscribeMQTT,
  unsubscribeMQTT
} from './mqtt';

import {
  Data,
  DataCompleta,
  DataTimeStamp,
  FormataTimer,
  formatJson,
  getIndexBaudRate,
  getIndexParidade,
  getIndexTempoEnvio,
  getPercentualSinal,
  Hora, HoraTimeStamp,
  isNumber,
  isObject,
  MascaraIP,
  padTo2Digits,
  ValidarIP
} from '../funcoes';

import { MockProbeSettings } from '../../__mocks__/ProbeSettingsMock';

import AsyncStorage from '@react-native-async-storage/async-storage';
import MQTT, { IMqttClient } from 'sp-react-native-mqtt';
import { MockCodiSettings } from '../../__mocks__/CodiSettingsMock';
import { MockDetalhesConcluidoOSHoje } from '../../__mocks__/DetalhesConcluidoOSHojeMock';
import { MockEtapasInstalacaoFro1Hoje } from '../../__mocks__/EtapasInstalacaoFro1HojeMock';
import { MockEtapasManutencaoHoje } from '../../__mocks__/EtapasManutencaoHojeMock';
import { MockEtapasRetiradaFro3Hoje } from '../../__mocks__/EtapasRetiradaFro3HojeMock';
import { MockEtapasTrocaFro2Hoje } from '../../__mocks__/EtapasTrocaFro2HojeMock';
import { MockEtapasVisitaHoje } from '../../__mocks__/EtapasVisitaHojeMock';
import { MockIonSettings } from '../../__mocks__/IonSettingsMock';
import { MockModbusSettings } from '../../__mocks__/ModbusSettingsMock';
import { MockPulsoSettings } from '../../__mocks__/PulsoSettingsMock';
import { BrokerHmg, BrokerProd, INFO_FINDED, INFO_IDLE, INFO_NOT_FINDED } from '../constantes';
import { AddDevice, ApnSettingsInicial, codiSettings, DetalhesConcluidoOSHoje, EtapasInstalacaoFro1Hoje, EtapasManutencaoHoje, EtapasRetiradaFro3Hoje, EtapasTrocaFro2Hoje, EtapasVisitaHoje, FilesProbe, ionSettings, modbusSettings, pulsoSettings } from '../data';
import { clearStorage, deleteStorage, getStorageBoolean, getStorageJson, getStorageString, KEY, setStorageBoolean, setStorageJson, setStorageString } from '../storages';


// Mock do MQTT
jest.mock('sp-react-native-mqtt', () => {
    return {
        MQTT: () => null,
    };
});

jest.mock('react-native-event-listeners', () => ({
  EventRegister: {
    emit: jest.fn(),
  },
}));

describe('MQTT', () => {
  
  it('deve emitir isConnectedMQTT evento com o argumento corretamente quando o argumento é true - eventIsConnected', () => {
    const isConnected = true;
    eventIsConnected(isConnected);
    expect(EventRegister.emit).toHaveBeenCalledWith('isConnectedMQTT', isConnected);
  });

  it('deve emitir isConnectedMQTT evento corretamento quando o argumento é false', () => {
    const isConnected = false;
    eventIsConnected(isConnected);
    expect(EventRegister.emit).toHaveBeenCalledWith('isConnectedMQTT', isConnected);
  });

  it('deve emitir messageMQTT evento com o argumento correto quando chegar mensagem - messageMQTT', () => {
    const message = "nova mensagem";
    eventMessage(message);
    expect(EventRegister.emit).toHaveBeenCalledWith('messageMQTT', message);
  });

  it('deve emitir messageMQTT evento corretamento quando chegar mensagem vazia', () => {
    const message = "";
    eventMessage(message);
    expect(EventRegister.emit).toHaveBeenCalledWith('messageMQTT', message);
  });

  it('deve emitir findDbgackMQTT evento quando encontrou as informações do dbgack', () => {
    const finded = true;
    eventFindedDbgack(finded);
    expect(EventRegister.emit).toHaveBeenCalledWith('findDbgackMQTT', finded);
  });

  it('deve emitir findDbgackMQTT evento quando não encontrou as informações do dbgack', () => {
    const finded = false;
    eventFindedDbgack(finded);
    expect(EventRegister.emit).toHaveBeenCalledWith('findDbgackMQTT', finded);
  });

  it('deve emitir findSysConfigMQTT evento quando encontrou as informações do sys_config', () => {
    const finded = true;
    eventFindedSysConfig(finded);
    expect(EventRegister.emit).toHaveBeenCalledWith('findSysConfigMQTT', finded);
  });

  it('deve emitir findSysConfigMQTT evento quando não encontrou as informações do sys_config', () => {
    const finded = false;
    eventFindedSysConfig(finded);
    expect(EventRegister.emit).toHaveBeenCalledWith('findSysConfigMQTT', finded);
  });

  it('deve emitir firmwareMQTT evento corretamento quando retornar a versão do firmware', () => {
    const firmware = "H3.0_Z2.0.31";
    eventFirmware(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('firmwareMQTT', firmware);
  });

  it('deve emitir firmwareMQTT evento quando não retornou a versão do firmware', () => {
    const firmware = '';
    eventFirmware(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('firmwareMQTT', firmware);
  });

  it('deve emitir dateTimeMQTT evento corretamento quando retornar a datahora em timestamp', () => {
    const datahora = 1733414902;
    eventDateTime(datahora);
    expect(EventRegister.emit).toHaveBeenCalledWith('dateTimeMQTT', datahora);
  });

  it('deve emitir signalMQTT evento corretamento quando retornar o valor do sinal do gsm ou wifi', () => {
    const sinal = 20;
    eventSignal(sinal);
    expect(EventRegister.emit).toHaveBeenCalledWith('signalMQTT', sinal);
  });

  it('deve emitir batteryMQTT evento corretamento quando retornar o valor da carga da bateria', () => {
    const bateria = 80;
    eventBattery(bateria);
    expect(EventRegister.emit).toHaveBeenCalledWith('batteryMQTT', bateria);
  });

  it('deve emitir vBatteryMQTT evento corretamento quando retornar o valor da tensão da bateria', () => {
    const tensao = 2.15;
    eventVBattery(tensao);
    expect(EventRegister.emit).toHaveBeenCalledWith('vBatteryMQTT', tensao);
  });

  it('deve emitir vStatusProbeMQTT evento corretamento quando retornar o valor da tensão da probe', () => {
    const tensao = 4.16;
    eventStatusVProbe(tensao);
    expect(EventRegister.emit).toHaveBeenCalledWith('vStatusProbeMQTT', tensao);
  });

  it('deve emitir vStatusProbeMQTT evento corretamento quando retornar o valor da tensão da probe', () => {
    const tensao = 4.16;
    eventStatusVProbe(tensao);
    expect(EventRegister.emit).toHaveBeenCalledWith('vStatusProbeMQTT', tensao);
  });

  it('deve emitir typeConnectionMQTT evento corretamento quando retornar o valor do tipo de conexão', () => {
    const tipo = 0; // wifi
    eventTypeConnection(tipo);
    expect(EventRegister.emit).toHaveBeenCalledWith('typeConnectionMQTT', tipo);
  });    

  it('deve emitir operadoraMQTT evento corretamento quando retornar o valor do tipo de operadora', () => {
    const tipo = 1; // vivo
    eventOperadora(tipo);
    expect(EventRegister.emit).toHaveBeenCalledWith('operadoraMQTT', tipo);
  });

  it('deve emitir sistemaMQTT evento indicando que o módulo sistema está ativo', () => {
    const status = true;
    eventSistema(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('sistemaMQTT', status);
  });

  it('deve emitir sistemaMQTT evento indicando que o módulo sistema está desativado', () => {
    const status = false;
    eventSistema(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('sistemaMQTT', status);
  });    

  it('deve emitir redeMQTT evento indicando que o módulo rede está ativo', () => {
    const status = true;
    eventRede(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('redeMQTT', status);
  });

  it('deve emitir redeMQTT evento indicando que o módulo rede está desativado', () => {
    const status = false;
    eventRede(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('redeMQTT', status);
  });

  it('deve emitir modbusMQTT evento indicando que o módulo modbus está ativo', () => {
    const status = true;
    eventModbus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusMQTT', status);
  });

  it('deve emitir modbusMQTT evento indicando que o módulo módbus está desativado', () => {
    const status = false;
    eventModbus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusMQTT', status);
  });

  it('deve emitir codiMQTT evento indicando que o módulo codi está ativo', () => {
    const status = true;
    eventCodi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiMQTT', status);
  });

  it('deve emitir codiMQTT evento indicando que o módulo codi está desativado', () => {
    const status = false;
    eventCodi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiMQTT', status);
  });

  it('deve emitir ionMQTT evento indicando que o módulo ion está ativo', () => {
    const status = true;
    eventION(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionMQTT', status);
  });

  it('deve emitir ionMQTT evento indicando que o módulo ion está desativado', () => {
    const status = false;
    eventION(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionMQTT', status);
  });

  it('deve emitir pulsoMQTT evento indicando que o módulo pulso está ativo', () => {
    const status = true;
    eventPulso(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoMQTT', status);
  });

  it('deve emitir pulsoMQTT evento indicando que o módulo pulso está desativado', () => {
    const status = false;
    eventPulso(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoMQTT', status);
  });

  it('deve emitir otaMQTT evento indicando que o módulo ota está ativo', () => {
    const status = true;
    eventOTA(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaMQTT', status);
  });

  it('deve emitir otaMQTT evento indicando que o módulo ota está desativado', () => {
    const status = false;
    eventOTA(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaMQTT', status);
  });

  it('deve emitir ipMQTT evento quando não retornou o ip da probe', () => {
    const ip = '*************';
    eventIP(ip);
    expect(EventRegister.emit).toHaveBeenCalledWith('ipMQTT', ip);
  });

  it('deve emitir ipMQTT evento quando não retornou o ip da probe', () => {
    const ip = '';
    eventIP(ip);
    expect(EventRegister.emit).toHaveBeenCalledWith('ipMQTT', ip);
  });

  it('deve emitir gatewayMQTT evento quando não retornou o ip gateway da  probe', () => {
    const ip = '***********';
    eventGateway(ip);
    expect(EventRegister.emit).toHaveBeenCalledWith('gatewayMQTT', ip);
  });

  it('deve emitir gatewayMQTT evento quando não retornou o ip gateway da probe', () => {
    const ip = '';
    eventGateway(ip);
    expect(EventRegister.emit).toHaveBeenCalledWith('gatewayMQTT', ip);
  });

  it('deve emitir resetsMQTT evento quando retorna o total de resets da probe', () => {
    const contador = 10;
    eventResets(contador);
    expect(EventRegister.emit).toHaveBeenCalledWith('resetsMQTT', contador);
  });

  it('deve emitir firmwareBootMQTT evento quando retorna data de instalação do firmware em timestamp', () => {
    const data = 1733414907;
    eventFirmwareBoot(data);
    expect(EventRegister.emit).toHaveBeenCalledWith('firmwareBootMQTT', data);
  });

  it('deve emitir exceptionsMQTT evento quando retorna o total de exceções que ocorreram na probe', () => {
    const contador = 10;
    eventExceptions(contador);
    expect(EventRegister.emit).toHaveBeenCalledWith('exceptionsMQTT', contador);
  });

  it('deve emitir heapMQTT evento quando retorna o tamanho da area heap da memória', () => {
    const tamanho = 1000;
    eventHeap(tamanho);
    expect(EventRegister.emit).toHaveBeenCalledWith('heapMQTT', tamanho);
  });

  it('deve emitir totalMemoryMQTT evento quando retorna o total de memoria flash disponivel', () => {
    const tamanho = 8201;
    eventTotalMemory(tamanho);
    expect(EventRegister.emit).toHaveBeenCalledWith('totalMemoryMQTT', tamanho);
  });

  it('deve emitir usedMemoryMQTT evento quando retorna o total de memoria utilizada', () => {
    const tamanho = 5212;
    eventUsedMemory(tamanho);
    expect(EventRegister.emit).toHaveBeenCalledWith('usedMemoryMQTT', tamanho);
  });

  it('deve emitir reconnectMQTT evento indicando que a probe foi reconectada', () => {
    const status = true;
    eventReconnect(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('reconnectMQTT', status);
  });

  it('deve emitir reconnectMQTT evento indicando que a probe não foi reconectada', () => {
    const status = false;
    eventReconnect(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('reconnectMQTT', status);
  });

  it('deve emitir formatMQTT evento indicando que a probe foi formatada', () => {
    const status = true;
    eventFormat(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatMQTT', status);
  });

  it('deve emitir formatMQTT evento indicando que a probe não foi formatada', () => {
    const status = false;
    eventFormat(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatMQTT', status);
  });

  it('deve emitir moduleScanMQTT evento retornando o total de modulos escaneados', () => {
    const total = 4;
    eventModuleScan(total);
    expect(EventRegister.emit).toHaveBeenCalledWith('moduleScanMQTT', total);
  });

  it('deve emitir formatMessageMQTT evento indicando que as mensagens foram formatadas', () => {
    const status = true;
    eventFormatMessage(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatMessageMQTT', status);
  });

  it('deve emitir formatMessageMQTT evento indicando que as mensagens foram formatadas', () => {
    const status = false;
    eventFormatMessage(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatMessageMQTT', status);
  });

  it('deve emitir listFilesMQTT evento indicando que a lista possui itens', () => {
    const list = [ 
                    { id: 0, name: 'file0', size:'10', timer:'1733414902'},
                    { id: 1, name: 'file1', size:'20', timer:'1733414903'},    
                 ];
    eventListFiles(list);
    expect(EventRegister.emit).toHaveBeenCalledWith('listFilesMQTT', list);
  });

  it('deve emitir formatMessageMQTT evento indicando que a lista não possui itens', () => {
    const list: FilesProbe[] = [];
    eventListFiles(list);
    expect(EventRegister.emit).toHaveBeenCalledWith('listFilesMQTT', list);
  });    

  it('deve emitir formatFileMQTT evento indicando que os arquivos foram formatadas', () => {
    const status = true;
    eventFormatFile(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatFileMQTT', status);
  });

  it('deve emitir formatFileMQTT evento indicando que os arquivos não foram formatadas', () => {
    const status = false;
    eventFormatFile(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('formatFileMQTT', status);
  });

  it('deve emitir wifiMQTT evento indicando que as configurações do Wifi foram recebidas', () => {
    const probe = MockProbeSettings();
    eventWifi(probe);
    expect(EventRegister.emit).toHaveBeenCalledWith('wifiMQTT', probe);
  });

  //it('deve emitir wifiMQTT evento indicando que as configurações do Wifi não foram recebidas', () => {
  //  const probe = null;
  //  eventWifi(probe);
  //  expect(EventRegister.emit).toHaveBeenCalledWith('wifiMQTT', probe);
  //}); 

  it('deve emitir gsmMQTT evento indicando que as configurações do GSM foram recebidas', () => {
    const probe = MockProbeSettings();
    eventGSM(probe);
    expect(EventRegister.emit).toHaveBeenCalledWith('gsmMQTT', probe);
  });

  //it('deve emitir gsmMQTT evento indicando que as configurações do GSM não foram recebidas', () => {
  //  const probe = null;
  //  eventGSM(probe);
  //  expect(EventRegister.emit).toHaveBeenCalledWith('gsmMQTT', probe);
  //});

  it('deve emitir ethernetMQTT evento indicando que as configurações do Ethernet foram recebidas', () => {
    const probe = MockProbeSettings();
    eventEthernet(probe);
    expect(EventRegister.emit).toHaveBeenCalledWith('ethernetMQTT', probe);
  });

  //it('deve emitir ethernetMQTT evento indicando que as configurações do Ethernet não foram recebidas', () => {
  //  const probe = null;
  //  eventEthernet(probe);
  //  expect(EventRegister.emit).toHaveBeenCalledWith('ethernetMQTT', probe);
  //});

  it('deve emitir saveWifiMQTT evento indicando que as informações do wifi foram salvas', () => {
    const status = true;
    eventSaveWifi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('saveWifiMQTT', status);
  });

  it('deve emitir saveWifiMQTT evento indicando que as informações do wifi não foram salvas', () => {
    const status = false;
    eventSaveWifi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('saveWifiMQTT', status);
  });

  it('deve emitir saveEthernetMQTT evento indicando que as informações do ethernet foram salvas', () => {
    const status = true;
    eventSaveEthernet(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('saveEthernetMQTT', status);
  });

  it('deve emitir saveEthernetMQTT evento indicando que as informações do ethernet não foram salvas', () => {
    const status = false;
    eventSaveEthernet(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('saveEthernetMQTT', status);
  });

  it('deve emitir modbusMasterMQTT evento indicando que o módulo modbus foi ativo', () => {
    const status = true;
    eventModbusMaster(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusMasterMQTT', status);
  });

  it('deve emitir modbusMasterMQTT evento indicando que o módulo modbus foi desativado', () => {
    const status = false;
    eventModbusMaster(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusMasterMQTT', status);
  });

  it('deve emitir modbusConfigMQTT evento indicando que as configurações do modbus foram recebidas', () => {
    const modbus = MockModbusSettings();
    eventModbusConfig(modbus);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusConfigMQTT', modbus);
  });

  it('deve emitir modbusConfigMQTT evento indicando que as configurações do modbus não foram recebidas', () => {
    const modbus: modbusSettings[] = [];
    eventModbusConfig(modbus);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusConfigMQTT', modbus);
  });

  it('deve emitir modbusResetMQTT evento indicando que as configurações modbus foram excluidas da probe', () => {
    const status = true;
    eventModbusReset(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusResetMQTT', status);
  });

  it('deve emitir modbusResetMQTT evento indicando que as configurações modbus não foram excluidas da probe', () => {
    const status = false;
    eventModbusReset(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusResetMQTT', status);
  });    

  it('deve emitir modbusAddBeginIniMQTT evento indicando que as configurações modbus foram reservadas com sucesso', () => {
    const status = true;
    eventModbusAddBeginIni(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddBeginIniMQTT', status);
  });

  it('deve emitir modbusAddBeginIniMQTT evento indicando que as configurações modbus não foram foram reservadas com sucesso', () => {
    const status = false;
    eventModbusAddBeginIni(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddBeginIniMQTT', status);
  });

  it('deve emitir modbusAddDeviceMQTT evento indicando que foi adicionado um device ao modbus', () => {
    const status = true;
    eventModbusAddDevice(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddDeviceMQTT', status);
  });

  it('deve emitir modbusAddDeviceMQTT evento indicando que não foi adicionado um device ao modbus', () => {
    const status = false;
    eventModbusAddDevice(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddDeviceMQTT', status);
  });
    
  it('deve emitir modbudAddSlaveMQTT evento indicando que foi adicionado um slave ao modbus', () => {
    const status = true;
    eventModbusAddSlave(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbudAddSlaveMQTT', status);
  });

  it('deve emitir modbudAddSlaveMQTT evento indicando que não foi adicionado um device ao modbus', () => {
    const status = false;
    eventModbusAddSlave(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbudAddSlaveMQTT', status);
  });

  it('deve emitir eventModbusAddVariable evento indicando que foi adicionado um variable ao modbus', () => {
    const status = true;
    eventModbusAddVariable(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddVariableMQTT', status);
  });

  it('deve emitir eventModbusAddVariable evento indicando que não foi adicionado um variable ao modbus', () => {
    const status = false;
    eventModbusAddVariable(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddVariableMQTT', status);
  });

  it('deve emitir modbusAddMeanMQTT evento indicando que foi adicionado um variable mean (média) ao modbus', () => {
    const status = true;
    eventModbusAddMean(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddMeanMQTT', status);
  });

  it('deve emitir modbusAddMeanMQTT evento indicando que não foi adicionado um variable mean (média) ao modbus', () => {
    const status = false;
    eventModbusAddMean(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddMeanMQTT', status);
  });    

  it('deve emitir modbusAddBeginFimMQTT evento indicando que foi finalizada com sucesso a configuração modbus', () => {
    const status = true;
    eventModbusAddBeginFim(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddBeginFimMQTT', status);
  });

  it('deve emitir modbusDataMQTT evento indicando que foi finalizada com sucesso a configuração modbus', () => {
    eventModbusData([]);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusDataMQTT', []);
  });  

  it('deve emitir modbusAddBeginFimMQTT evento indicando que não foi finalizada com sucesso a configuração modbus', () => {
    const status = false;
    eventModbusAddBeginFim(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('modbusAddBeginFimMQTT', status);
  });

  it('deve emitir codiStatusMQTT evento indicando que o módulo codi foi inicializado com sucesso', () => {
    const status = true;
    eventCodiStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiStatusMQTT', status);
  });

  it('deve emitir codiStatusMQTT evento indicando que o módulo codi foi inicializado com sucesso', () => {
    const status = false;
    eventCodiStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiStatusMQTT', status);
  });

  it('deve emitir codiConfigMQTT evento indicando que as configurações do codi foram recebidas', () => {
    const codi = MockCodiSettings();
    eventCodiConfig(codi);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiConfigMQTT', codi);
  });

  it('deve emitir codiConfigMQTT evento indicando que as configurações do codi não foram recebidas', () => {
    const codi : codiSettings[] = [];
    eventCodiConfig(codi);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiConfigMQTT', codi);
  });

  it('deve emitir codiEndMQTT evento indicando que o módulo codi foi desativado com sucesso', () => {
    const status = true;
    eventCodiEnd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiEndMQTT', status);
  });

  it('deve emitir codiEndMQTT evento indicando que o módulo codi não foi desativado com sucesso', () => {
    const status = false;
    eventCodiEnd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiEndMQTT', status);
  });

  it('deve emitir codiAddMQTT evento indicando que a configuração codi foi adicionada com sucesso', () => {
    const status = true;
    eventAddCodi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiAddMQTT', status);
  });

  it('deve emitir codiAddMQTT evento indicando que a configuração codi não foi adicionada com sucesso', () => {
    const status = false;
    eventAddCodi(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('codiAddMQTT', status);
  });

  it('deve emitir otaRollbackMQTT evento indicando que o firmware da probe retornou a versão anterior instalada', () => {
    const firmware = 'H3.0_Z2.0.30';
    eventOTARollback(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaRollbackMQTT', firmware);
  });

  it('deve emitir otaRollbackMQTT evento indicando que o firmware da probe não retornou a versão anterior instalada', () => {
    const firmware = '';
    eventOTARollback(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaRollbackMQTT', firmware);
  });

  it('deve emitir OTAupdatingMQTT evento indicando que o firmware da probe esta sendo atualizado', () => {
    const firmware = 'ota_status\":\"Download 0% (1447 / 1036784)';
    eventOTAUpdating(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('OTAupdatingMQTT', firmware);
  });

  it('deve emitir OTAupdatingMQTT evento indicando que o firmware da probe não esta sendo atualizado', () => {
    const firmware = '';
    eventOTAUpdating(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('OTAupdatingMQTT', firmware);
  });

  it('deve emitir otaUpdateMQTT evento indicando que o firmware da probe foi atualizado', () => {
    const firmware = 'H3.0_Z2.0.31';
    eventOTAUpdate(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaUpdateMQTT', firmware);
  });

  it('deve emitir otaUpdateMQTT evento indicando que o firmware da probe não foi atualizado', () => {
    const firmware = '';
    eventOTAUpdate(firmware);
    expect(EventRegister.emit).toHaveBeenCalledWith('otaUpdateMQTT', firmware);
  });

  it('deve emitir ionStatusMQTT evento indicando que o módulo ion foi ativado', () => {
    const status = true;
    eventIonStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionStatusMQTT', status);
  });

  it('deve emitir ionStatusMQTT evento indicando que o módulo ion não foi ativado', () => {
    const status = false;
    eventIonStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionStatusMQTT', status);
  });

  it('deve emitir ionConfigMQTT evento indicando que as configurações do ion foram recebidas', () => {
    const ion = MockIonSettings();
    eventIonConfig(ion);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionConfigMQTT', ion);
  });

  it('deve emitir ionConfigMQTT evento indicando que as configurações do ion não foram recebidas', () => {
    const ion : ionSettings[] = [];
    eventIonConfig(ion);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionConfigMQTT', ion);
  });

  it('deve emitir ionEndMQTT evento indicando que o módulo ion foi desativado com sucesso', () => {
    const status = true;
    eventIonEnd (status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionEndMQTT', status);
  });

  it('deve emitir ionEndMQTT evento indicando que o módulo ion foi desativado com sucesso', () => {
    const status = false;
    eventIonEnd (status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionEndMQTT', status);
  });

  it('deve emitir ionAddMQTT evento indicando que a configuração ion foi adicionada com sucesso', () => {
    const status = true;
    eventIonAdd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAddMQTT', status);
  });

  it('deve emitir ionAddMQTT evento indicando que a configuração ion não foi adicionada com sucesso', () => {
    const status = false;
    eventIonAdd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAddMQTT', status);
  });

  it('deve emitir ionAutoSearchMQTT evento indicando que uma configuração ion foi encontrada na busca', () => {
    const status = true;
    eventIonAutoSearch(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAutoSearchMQTT', status);
  });

  it('deve emitir ionAutoSearchMQTT evento indicando que uma configuração ion foi encontrada na busca', () => {
    const status = false;
    eventIonAutoSearch(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAutoSearchMQTT', status);
  });

  it('deve emitir ionAutoSearchingMQTT evento indicando que uma configuração ion esta sendo procurada', () => {
    const status = 'Tentando 8N1 baud rate 9600';
    eventIonAutoSearching(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAutoSearchingMQTT', status);
  });

  it('deve emitir ionAutoSearchingMQTT evento indicando que uma configuração ion não esta sendo procurada', () => {
    const status = '';
    eventIonAutoSearching(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionAutoSearchingMQTT', status);
  });

  it('deve emitir eventIonData  evento indicando que uma configuração ion esta sendo procurada', () => {
    const data : string[] = [];
    eventIonData (data);
    expect(EventRegister.emit).toHaveBeenCalledWith('ionDataMQTT', data);
  });  

  it('deve emitir pulsoStatusMQTT evento indicando que o módulo do contador de pulsos foi ativado', () => {
    const status = true;
    eventPulsoStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoStatusMQTT', status);
  });

  it('deve emitir pulsoStatusMQTT evento indicando que o módulo do contador de pulsos não foi ativado', () => {
    const status = false;
    eventPulsoStatus(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoStatusMQTT', status);
  });

  it('deve emitir pulsoConfigMQTT evento indicando que as configurações do contador de puslo foram recebidas', () => {
    const pulso = MockPulsoSettings();
    eventPulsoConfig(pulso);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoConfigMQTT', pulso);
  });

  it('deve emitir pulsoConfigMQTT evento indicando que as configurações do contador de puslo não foram recebidas', () => {
    const pulso : pulsoSettings[] = [];
    eventPulsoConfig(pulso);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoConfigMQTT', pulso);
  });

  it('deve emitir pulsoResetMQTT evento indicando que as configurações do contador de pulso foram excluidas', () => {
    const status = true;
    eventPulsoReset(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoResetMQTT', status);
  });

  it('deve emitir pulsoResetMQTT evento indicando que as configurações do contador de pulso não foram excluidas', () => {
    const status = false;
    eventPulsoReset(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoResetMQTT', status);
  });

  it('deve emitir pulsoEndMQTT evento indicando que o módulo do contador de pulsos foi desativado com sucesso', () => {
    const status = true;
    eventPulsoEnd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoEndMQTT', status);
  });

  it('deve emitir pulsoEndMQTT evento indicando que o módulo do contador de pulsos não foi desativado com sucesso', () => {
    const status = false;
    eventPulsoEnd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoEndMQTT', status);
  });
    
  it('deve emitir pulsoAddMQTT evento indicando que a configuração do contador de pulso foi adicionada', () => {
    const status = 0;
    eventPulsoAdd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoAddMQTT', status);
  });

  it('deve emitir pulsoAddMQTT evento indicando que a configuração do contador de pulso não foi adicionada', () => {
    const status = 4;
    eventPulsoAdd(status);
    expect(EventRegister.emit).toHaveBeenCalledWith('pulsoAddMQTT', status);
  });
  
  it('deve emitir khompMQTT evento indicando que a configuração do khomp foi encontrado', () => {
    const response = '';
    eventKhomp (response);
    expect(EventRegister.emit).toHaveBeenCalledWith('khompMQTT', response);
  });

  it('deve emitir apnConfigMQTT evento indicando que a configuração da apn foi encontrado', () => {
    const response = ApnSettingsInicial();
    eventApnConfig (response);
    expect(EventRegister.emit).toHaveBeenCalledWith('apnConfigMQTT', response);
  });  

  it('deve emitir apnAddMQTT evento indicando que a configuração da apn foi encontrado', () => {
    const response = true;
    eventApnAdd (response);
    expect(EventRegister.emit).toHaveBeenCalledWith('apnAddMQTT', response);
  });
  
  it('deve emitir apnAddMQTT evento indicando que a configuração da apn foi encontrado', () => {
    const response = true;
    eventApnRemove (response);
    expect(EventRegister.emit).toHaveBeenCalledWith('apnRemoveMQTT', response);
  });  

  describe('InfoReleased', () => {
    it('deve retornar false se indice é maior que a quantidade existente', () => {
      expect(InfoReleased(79)).toBe(false);
    });
    it('deve retornar false se variavel é INFO_IDLE', () => {
      expect(InfoReleased(0)).toBe(false);
    });
  
    it('deve retornar false se variavel é INFO_FINDED', () => {
      expect(InfoReleased(1)).toBe(false);
    });
  
    it('deve retornar true se variavel for diferente INFO_IDLE INFO_IDLE e INFO_FINDED', () => {
      infoReleasedAllMQTT(1);    
      expect(InfoReleased(2)).toBe(true);
    });
  });

  describe('getFirmware', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getFirmware('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou referência a informação da versão do firmware', () => {
      infoReleasedAllMQTT(1);        
      expect(getFirmware('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });  
    it('deve retornar se encontrou informação da versão firmware', () => {
      infoReleasedAllMQTT(1);        
      expect(getFirmware('{\"version\":\"H3.0_Z2.0.30\",\"time\":1729860083}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação do firmware', () => {
      infoReleasedAllMQTT(1);      
      expect(getFirmware('{\"version\":\"\",\"time\":1729860083}')).toHaveBeenCalled;
    });      
  }); 

  describe('getDataHora', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);
      expect(getDataHora('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação da data e hora', () => {
      infoReleasedAllMQTT(1);    
      expect(getDataHora('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação da data e hora', () => {
      infoReleasedAllMQTT(1);      
      expect(getDataHora('{\"time\":1729860083}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação da data e hora', () => {
      infoReleasedAllMQTT(1);    
      expect(getDataHora('{\"time\":\"\"}')).toHaveBeenCalled;
    });      
  });
    
  describe('getFirmwareBoot', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);
      expect(getFirmwareBoot('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação da data e hora de instalação do firmware', () => {
      infoReleasedAllMQTT(1);    
      expect(getFirmwareBoot('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação da data e hora de instalação do firmware', () => {
      infoReleasedAllMQTT(1);    
      expect(getFirmwareBoot('{\"firmware_boot\":1728591684}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação da data e hora de instalação do firmware', () => {
      infoReleasedAllMQTT(1);      
      expect(getFirmwareBoot('{\"firmware_boot\":\"\"}')).toHaveBeenCalled;
    });      
  });

  describe('getResets', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);    
      expect(getResets('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação do total de resets', () => {
      infoReleasedAllMQTT(1);      
      expect(getResets('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação do total de resets', () => {
      infoReleasedAllMQTT(1);      
      expect(getResets('{\"sys_resets\":88}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação do total de resets', () => {
      infoReleasedAllMQTT(1);    
      expect(getResets('{\"sys_resets\":\"\"}')).toHaveBeenCalled;
    });      
  });

  describe('getExcecoes', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getExcecoes('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação do total de exceções', () => {
      infoReleasedAllMQTT(1);      
      expect(getExcecoes('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação do total de exceções', () => {
      infoReleasedAllMQTT(1);      
      expect(getExcecoes('{\"sys_exceptions\":8}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação do total de exceções', () => {
      infoReleasedAllMQTT(1);      
      expect(getExcecoes('{\"sys_exceptions\":\"\"}')).toHaveBeenCalled;
    });      
  });

  describe('getHeap', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);    
      expect(getHeap('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação do total de memória livre', () => {
      infoReleasedAllMQTT(1);      
      expect(getHeap('{\"action_success\":true}')).toHaveBeenCalled;
    });
    it('deve retornar se encontrou informação do total de memória livre', () => {
      infoReleasedAllMQTT(1);      
      expect(getHeap('{\"sys_heap\":8}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação do total de memória livre', () => {
      infoReleasedAllMQTT(1);      
      expect(getHeap('{\"sys_heap\":\"\"}')).toHaveBeenCalled;
    });
  });

  describe('getNivelBateria', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getNivelBateria('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação do nível da bateria', () => {
      infoReleasedAllMQTT(1);      
      expect(getNivelBateria('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar a informação do nível da bateria', () => {
      infoReleasedAllMQTT(1);      
      expect(getNivelBateria('{\"pbatt\":8}')).toHaveBeenCalled;
    });
  });

  describe('getTensaoBateria', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getTensaoBateria('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência a informação da tensão da bateria', () => {
      infoReleasedAllMQTT(1);      
      expect(getTensaoBateria('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar a informação da tensão da bateria', () => {
      infoReleasedAllMQTT(1);      
      expect(getTensaoBateria('{\"vbatt\":8}')).toHaveBeenCalled;
    });
  });

  describe('getStatusTensaoProbe', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getStatusTensaoProbe('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência do status da tensão da probe', () => {
      infoReleasedAllMQTT(1);      
      expect(getStatusTensaoProbe('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação indicando que a probe ligada pela bateria', () => {
      infoReleasedAllMQTT(1);      
      expect(getStatusTensaoProbe('{\"voltalarm\":true}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação indicando que a probe ligada pela alimentação', () => {
      infoReleasedAllMQTT(1);      
      expect(getStatusTensaoProbe('{\"voltalarm\":false}')).toHaveBeenCalled;
    });
  });    

  describe('getSinal', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getSinal('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência do sinal do wi-fi ou gsm', () => {
      infoReleasedAllMQTT(1);      
      expect(getSinal('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação do sinal do wi-fi', () => {
      infoReleasedAllMQTT(1);      
      expect(getSinal('{\"rssi\":-51}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação do sinal do wi-fi', () => {
      infoReleasedAllMQTT(1);      
      expect(getSinal('{\"rssi\":\"\"}')).toHaveBeenCalled;
    });      
    it('deve retornar se encontrou informação do sinal do gsm', () => {
      infoReleasedAllMQTT(1);
      expect(getSinal('{\"siglvl\":18}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou informação do sinal do gsm', () => {
      infoReleasedAllMQTT(1);      
      expect(getSinal('{\"siglvl\":\"\"}')).toHaveBeenCalled;
    });      
  });    

  describe('getTipoConexaoProbe', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getTipoConexaoProbe('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência do tipo de conexão', () => {
      infoReleasedAllMQTT(1);    
      expect(getTipoConexaoProbe('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se encontrou informação da conexão por wi-fi', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoConexaoProbe('{\"rede\":{\"wifi\":{\"enabled\":true}}}')).toHaveBeenCalled;
    });      
    it('deve retornar se não encontrou informação da conexão por wi-fi', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoConexaoProbe('{\"rede\":{\"wifi\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });      
    it('deve retornar se encontrou informação da conexão por gsm', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoConexaoProbe('{\"rede\":{\"gsm\":{\"enabled\":true}}}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou informação da conexão por gsm', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoConexaoProbe('{\"rede\":{\"gsm\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });      
    it('deve retornar se encontrou informação da conexão por ethernet', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoConexaoProbe('{\"rede\":{\"eth\":{\"enabled\":true}}}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou informação da conexão por ethernet', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoConexaoProbe('{\"rede\":{\"eth\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });
  }); 

  describe('getTipoOperadora', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getTipoOperadora('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência da informação do tipo de operadora', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoOperadora('{\"action_success\":true}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se rede é gsm', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });    
    it('deve retornar a informação se tipo de operadora = Vivo', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Vivo\"}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se tipo de operadora = Vivo NB-IoT', () => {
      infoReleasedAllMQTT(1);    
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Vivo\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;      
    });
    it('deve retornar a informação se tipo de operadora = Tim', () => {
      infoReleasedAllMQTT(1); 
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Tim\"}}}')).toHaveBeenCalled;      
    });
    it('deve retornar a informação se tipo de operadora = Tim NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Tim\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;      
    });
    it('deve retornar a informação se tipo de operadora = Claro', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Claro\"}}}')).toHaveBeenCalled;      
    });      
    it('deve retornar a informação se tipo de operadora = Claro NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Claro\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se tipo de operadora = Oi', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Oi\"}}}')).toHaveBeenCalled;       
    });      
    it('deve retornar a informação se tipo de operadora = Oi NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Oi\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;      
    });            
    it('deve retornar a informação se tipo de operadora = Nextel', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Nextel\"}}}')).toHaveBeenCalled;      
    });      
    it('deve retornar a informação se tipo de operadora = Nextel NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Nextel\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se tipo de operadora = Algar', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Algar\"}}}')).toHaveBeenCalled;            
    });
    it('deve retornar a informação se tipo de operadora = Algar NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Algar\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;
    });            
    it('deve retornar a informação se tipo de operadora = Sercomtel', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Sercomtel\"}}}')).toHaveBeenCalled;      
    });      
    it('deve retornar a informação se tipo de operadora = Sercomtel NB-IoT', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"Sercomtel\",\"rede\":\"NB\"}}}')).toHaveBeenCalled;      
    });
    it('deve retornar a informação se rede é eth', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"eth\":{\"enabled\":true}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se rede não é eth', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"eth\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se rede é wifi', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"wifi\":{\"enabled\":true}}}')).toHaveBeenCalled;
    });
    it('deve retornar a informação se rede é wifi', () => {
      infoReleasedAllMQTT(1);
      expect(getTipoOperadora('{\"rede\":{\"wifi\":{\"enabled\":false}}}')).toHaveBeenCalled;
    });            
    it('deve retornar a informação se tipo de operadora = vazio', () => {
      infoReleasedAllMQTT(1);      
      expect(getTipoOperadora('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\" \",\"oper\":\"\",\"conn\":\"\"}}}')).toHaveBeenCalled;
    });      
  });

  describe('getSistema', () => {
    it('deve retornar não liberado para procura da informação', () => {
      infoReleasedAllMQTT(0);      
      expect(getSistema('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
    });
    it('deve retornar se não encontrou a referência da informação do status do módulo sistema', () => {
      infoReleasedAllMQTT(1);      
      expect(getSistema('{\"action_success\":true}')).toHaveBeenCalled;
    });    
    it('deve retornar se informação do status do módulo sistema = ON', () => {
      infoReleasedAllMQTT(1);      
      expect(getSistema('{\"modules\":{\"system\":true}}')).toHaveBeenCalled;
    });      
    it('deve retornar se informação do status do módulo sistema = OFF', () => {
      infoReleasedAllMQTT(1);      
      expect(getSistema('{\"modules\":{\"system\":false}}')).toHaveBeenCalled;
    });
  });

    describe('getRede', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getRede('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo rede', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRede('{\"action_success\":true}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo rede = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRede('{\"modules\":{\"rede\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo rede = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRede('{\"modules\":{\"rede\":false}}')).toHaveBeenCalled;
      });
    });    

    describe('getModbus', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbus('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbus('{\"action_success\":true}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo modbus = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbus('{\"modules\":{\"modbus_master\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo modbus = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbus('{\"modules\":{\"modbus_master\":false}}')).toHaveBeenCalled;
      });
    });

    describe('getCODI', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getCODI('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo codi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCODI('{\"action_success\":true}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo codi = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCODI('{\"modules\":{\"codi\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo codi = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCODI('{\"modules\":{\"codi\":false}}')).toHaveBeenCalled;
      });
    });

    describe('getION', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getION('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getION('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo ion = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getION('{\"modules\":{\"ion\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo ion = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getION('{\"modules\":{\"ion\":false}}')).toHaveBeenCalled;
      });
    });
    
    describe('getPulso', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulso('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo pulse', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulso('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo pulse = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulso('{\"modules\":{\"pulse\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo ion = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulso('{\"modules\":{\"pulse\":false}}')).toHaveBeenCalled;
      });
    });    

    describe('getOTA', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getOTA('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do status do módulo ota', () => {

        infoReleasedAllMQTT(1);
      
        expect(getOTA('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do status do módulo ota = ON', () => {

        infoReleasedAllMQTT(1);
      
        expect(getOTA('{\"modules\":{\"ota\":true}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do status do módulo ota = OFF', () => {

        infoReleasedAllMQTT(1);
      
        expect(getOTA('{\"modules\":{\"ota\":false}}')).toHaveBeenCalled;
      });
    });

    describe('getIP', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIP('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do ip', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do ip quando wifi habilitado e ip encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"wifi\":{\"enabled\":true,\"ip\":\"************\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do ip quando wifi habilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"wifi\":{\"enabled\":true,\"ip\":\"\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar se informação do ip quando wifi desabilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"wifi\":{\"enabled\":false,\"ip\":\"\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar se informação do ip quando ethernet habilitado e ip encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"************\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do ip quando ethernet habilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar se informação do ip quando ethernet desabilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"eth\":{\"enabled\":false,\"ip_use\":\"\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar se informação do ip quando gsm habilitado e ip encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"gsm\":{\"enabled\":true,\"ip\":\"************\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do ip quando gsm habilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"gsm\":{\"enabled\":true,\"ip\":\"\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar se informação do ip quando gsm desabilitado e ip não encontrado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIP('{\"rede\":{\"gsm\":{\"enabled\":false,\"ip\":\"\"}}}')).toHaveBeenCalled;
      });
    });

    describe('getTotalMemory', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getTotalMemory('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do total de memória da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getTotalMemory('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do total de memória da Probe foi encontrada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getTotalMemory('{\"sys\": {\"info\": {\"total_memory\":836081}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do total de memória da Probe não foi encontrada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getTotalMemory('{\"sys\": {\"info\": {\"total_memory\":\"\"}}}')).toHaveBeenCalled;
      });
    });

    describe('getUsedMemory', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getUsedMemory('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação do total de memória usada da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getUsedMemory('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação do total de memória usada da Probe foi encontrada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getUsedMemory('{\"sys\": {\"info\": {\"used_memory\":3514}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação do total de memória usada da Probe não foi encontrada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getUsedMemory('{\"sys\": {\"info\": {\"used_memory\":\"\"}}}')).toHaveBeenCalled;
      });
    });    

    describe('getReconectada', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getReconectada('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação de reconexão da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getReconectada('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se informação de reconexão da Probe pela Ethernet', () => {

        infoReleasedAllMQTT(1);
      
        expect(getReconectada('{\"reconn\":\"Ethernet\",\"time\":1730724403}')).toHaveBeenCalled;
      });      

      it('deve retornar se informação de reconexão da Probe pelo Wi-Fi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getReconectada('{\"reconn\":\"WiFi\",\"time\":1730724403}')).toHaveBeenCalled;
      });

      it('deve retornar se informação de reconexão da Probe pelo NB-IoT', () => {

        infoReleasedAllMQTT(1);
      
        expect(getReconectada('{\"reconn\":\"NB-IoT\",\"time\":1730724403}')).toHaveBeenCalled;
      });


      it('deve retornar se informação de reconexão não foi sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getReconectada('{\"reconn\":\"Gsm\",\"time\":1730724403}')).toHaveBeenCalled;
      });      
    });

    describe('getFormatado', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getFormatado('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da informação de formatação da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormatado('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a Probe foi formatada com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormatado('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha na formatação da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormatado('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModulosEscaneados', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModulosEscaneados('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência dos módulos escaneados da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModulosEscaneados('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar o total de módulos escaneados da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModulosEscaneados('{\"module_tasks_running\":8,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se não houve módulos escaneados na Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModulosEscaneados('{\"module_tasks_running\":\"\",\"time\":1729860083}')).toHaveBeenCalled;
      });
    });    

    describe('getMensagensFormatado', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getMensagensFormatado('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da formatação das mensagens da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getMensagensFormatado('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se as mensagens da Probe foram formatadas com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getMensagensFormatado('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha na formatação das mensagens da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getMensagensFormatado('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });


    describe('getListarArquivos', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getListarArquivos('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da lista de arquivos listados na Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getListarArquivos('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar os arquivos listados da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getListarArquivos('{\"sysfiles\": {\"files\": {\"1\": {\"name\":\"sys\",\"size\":\"     200\",\"time\":\"04/11/2024 16:59\",\"Final\": {\"free\":815,\"total\":816,\"files\":3,\"sizefiles\":216}}}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se não houve arquivos listados da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getListarArquivos('{\"sysfiles\": {\"files\": {\"Final\": {\"free\":815,\"total\":816,\"files\":3,\"sizefiles\":216}}}}')).toHaveBeenCalled;
      });

      it('deve retornar se houve falha ao listar os arquivos da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getListarArquivos('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getFormataArquivo', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getFormataArquivo('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da formatação do arquivo da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormataArquivo('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o arquivo da Probe foi formatado com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormataArquivo('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha na formatação do arquivo da Probe', () => {

        infoReleasedAllMQTT(1);
      
        expect(getFormataArquivo('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });    

    describe('getRedeEthernet', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getRedeEthernet('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência das informações da rede ethernet', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeEthernet('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar as informações da rede ethernet com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeEthernet('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"************\",\"gw_use\":\"***********0\",\"dns_use\":\"***********0\",\"msk_use\":\"*************\",\"mac\":\"10:06:1c:1d:49:9b\"}}}')).toHaveBeenCalled;
      });
      
      it('deve retornar as informações da rede ethernet esta em dhcp', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeEthernet('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"0.0.0.0\",\"gw_use\":\"***********0\",\"dns_use\":\"***********0\",\"msk_use\":\"*************\",\"mac\":\"10:06:1c:1d:49:9b\",\"ip\":\"0.0.0.0\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar as informações da rede ethernet esta em ip fixo', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeEthernet('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"************\",\"gw_use\":\"***********0\",\"dns_use\":\"***********0\",\"msk_use\":\"*************\",\"mac\":\"10:06:1c:1d:49:9b\",\"ip\":\"************\"}}}')).toHaveBeenCalled;
      });

      it('deve retornar as informações default da ethernet', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeEthernet('{\"rede\":{\"eth\":{\"enabled\":true,\"ip_use\":\"\",\"gw_use\":\"\",\"dns_use\":\"\",\"msk_use\":\"\",\"mac\":\"\"}}}')).toHaveBeenCalled;
      });
    });

    describe('getRedeWifi', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getRedeWifi('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência das informações da rede wifi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeWifi('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar as informações da rede wifi com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeWifi('{\"rede\":{\"wifi\":{\"enabled\":false,\"ip\":\"************\",\"gw\":\"***********0\",\"msk\":\"*************\",\"dns\":\"***********0\",\"rssi\":-30,\"ssid\":\"DOCLABS_uPLUG\",\"pass\":\"uPLUGDOC88IOT\",\"mac\":\"10:06:1c:1d:49:98\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar as informações default da wifi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeWifi('{\"rede\":{\"wifi\":{\"enabled\":false,\"ip\":\"\",\"gw\":\"\",\"msk\":\"\",\"dns\":\"\",\"rssi\":0,\"ssid\":\"\",\"pass\":\"\",\"ssid_used\":\"\",\"pass_used\":\"\",\"mac\":\"\"}}}')).toHaveBeenCalled;
      });
    });

    describe('getRedeGSM', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getRedeGSM('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência das informações da rede gsm', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeGSM('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar as informações da rede gsm com sucesso', () => {

        infoReleasedAllMQTT(1);
              
        expect(getRedeGSM('{\"rede\":{\"gsm\":{\"enabled\":true,\"rede\":\"2G\",\"modem\":\"bc92\",\"modemfw\":\"\",\"imei\":\"867989067174132\",\"imsi\":\"724068042485674\",\"iccid\":\"89550680157017224281\",\"oper\":\"Vivo\",\"apn\":\"inlog.vivo.com.br\",\"ip\":\"*************\",\"gw\":\"0.0.0.0\",\"msk\":\"0.0.0.0\",\"rssi\":\"18\",\"lat\":10,\"lng\":10}}}')).toHaveBeenCalled;
      });      

      it('deve retornar as informações default da gsm', () => {

        infoReleasedAllMQTT(1);
      
        expect(getRedeGSM('{\"rede\":{\"gsm\":{\"enabled\":false,\"rede\":\"\",\"modem\":\"\",\"modemfw\":\"\",\"imei\":\"\",\"imsi\":\"\",\"iccid\":\"\",\"oper\":\"\",\"apn\":\"\",\"ip\":\"\",\"gw\":\"\",\"msk\":\"\",\"rssi\":\"\",\"lat\":0,\"lng\":0}}}')).toHaveBeenCalled;
      });
    });

    describe('getSalvaWifi', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getSalvaWifi('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência das configurações do wifi salva', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaWifi('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do wifi foi salva com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaWifi('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha na configuração do wifi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaWifi('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getSalvaEthernet', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getSalvaEthernet('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência das configurações do ethernet salva', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaEthernet('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do ethernet foi salva com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaEthernet('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha na configuração do ethernet', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSalvaEthernet('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusMaster', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusMaster('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência do status do módulo modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusMaster('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o status do módulo modbus está ativo', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusMaster('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se o status do módulo modbus está desativado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusMaster('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusConfig', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      
      it('deve retornar se não encontrou a referência da configuração do modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });
      
      it('deve retornar se a configuração do modbus foi encontrada com sucesso', () => {

        infoReleasedAllMQTT(1);
        
        expect(getModbusConfig('{\"config\":{\"slaves\":1,\"device\":1,\"mean\":2,\"publish\":0}}')).toHaveBeenCalled;        
        expect(getModbusConfig('{\"modbus\":{\"device\":{\"0\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"endianness\":3}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"0\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"var_name\":\"voltan\",\"function\":3,\"address\":165,\"registers\":2,\"data_type\":3,\"factor\":0.1,\"send\":true,\"mean\":false}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"1\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"var_name\":\"voltbn\",\"function\":3,\"address\":167,\"registers\":2,\"data_type\":3,\"factor\":0.1,\"send\":true,\"mean\":true}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"2\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"var_name\":\"voltcn\",\"function\":3,\"address\":169,\"registers\":2,\"data_type\":3,\"factor\":0.1,\"send\":true,\"mean\":true}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"3\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"var_name\":\"voltab\",\"function\":3,\"address\":177,\"registers\":2,\"data_type\":3,\"factor\":1,\"send\":true,\"mean\":false}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"slave\":{\"0\":{\"slave_name\":\"Teste 00\",\"dev_name\":\"EN_SCHDR_ION8600\",\"slave\":8,\"send_time\":900,\"protocol\":0,\"uart\":\"8N1\",\"port_id\":0,\"baud\":9600}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"slave\":{\"1\":{\"slave_name\":\"Teste 01\",\"dev_name\":\"EN_SCHDR_ION8600\",\"slave\":8,\"send_time\":900,\"protocol\":1,\"conn\":\"0\",\"port\":80,\"slave_ip\":\"0.0.0.0\"}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });                   

      it('deve retornar se a configuração do modbus não foi encontrada ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusConfig('{\"config\":{\"slaves\":0,\"device\":0,\"mean\":0,\"publish\":0,\"action_success\":true}}')).toHaveBeenCalled;        

      });

      it('deve retornar se a configuração do modbus não econtrou device pois parametro incompleto', () => {

        infoReleasedAllMQTT(1);
              
        expect(getModbusConfig('{\"config\":{\"slaves\":1,\"device\":1,\"mean\":2,\"publish\":0}}')).toHaveBeenCalled;        
        expect(getModbusConfig('{\"modbus\":{\"device\":{\"10\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"endianness\":3}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"slave\":{\"0\":{\"slave_name\":\"Teste 01\",\"slave_ip\":\"0.0.0.0\"}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se a configuração do modbus foi encontrada com sucesso mas slave possui device com nome diferente', () => {

        infoReleasedAllMQTT(1);
        
        expect(getModbusConfig('{\"config\":{\"slaves\":1,\"device\":1,\"mean\":2,\"publish\":0}}')).toHaveBeenCalled;        
        expect(getModbusConfig('{\"modbus\":{\"device\":{\"0\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"endianness\":3}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"0\":{\"dev_name\":\"EN_SCHDR_ION8600\",\"var_name\":\"voltan\",\"function\":3,\"address\":165,\"registers\":2,\"data_type\":3,\"factor\":0.1,\"send\":true,\"mean\":false}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"variable\":{\"1\":{\"dev_name\":\"EN_SCHDR_ION8601\",\"var_name\":\"voltbn\",\"function\":3,\"address\":167,\"registers\":2,\"data_type\":3,\"factor\":0.1,\"send\":true,\"mean\":true}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"slave\":{\"0\":{\"slave_name\":\"Teste 00\",\"dev_name\":\"EN_SCHDR_ION8600\",\"slave\":8,\"send_time\":900,\"protocol\":0,\"uart\":\"8N1\",\"port_id\":0,\"baud\":9600}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"modbus\":{\"slave\":{\"1\":{\"slave_name\":\"Teste 01\",\"dev_name\":\"EN_SCHDR_ION8601\",\"slave\":8,\"send_time\":900,\"protocol\":1,\"conn\":\"0\",\"port\":80,\"slave_ip\":\"0.0.0.0\"}}}}')).toHaveBeenCalled;
        expect(getModbusConfig('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      
    });

    describe('getModbusReset', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusReset('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência de reset da configuração modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusReset('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar de reset da configuração modbus foi concluída com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusReset('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha no reset da configuração modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusReset('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusAddBeginIni', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddBeginIni('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da configuração inicial do modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginIni('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração inicial do modbus foi concluída com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginIni('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha configuração inicial do modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginIni('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });    

    describe('getModbusAddBeginFim', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddBeginFim('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da configuração final do modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginFim('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração final do modbus foi concluída com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginFim('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha configuração final do modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddBeginFim('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusAddDevice', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddDevice('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência para adicionar um device ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddDevice('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o device foi adicionado com sucesso ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddDevice('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha ao adicionar o device ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddDevice('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusAddSlave', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddSlave('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência para adicionar um slave ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddSlave('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o slave foi adicionado com sucesso ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddSlave('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha ao adicionar o slave ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddSlave('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusAddVariable', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddVariable('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência para adicionar uma variavel ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddVariable('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a variavel foi adicionado com sucesso ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddVariable('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha ao adicionar uma variavel ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddVariable('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusAddMean', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusAddMean('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência para adicionar uma variavel média ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddMean('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a variavel média foi adicionado com sucesso ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddMean('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha ao adicionar uma variavel média ao modbus', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusAddMean('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getModbusData', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getModbusData('{\"action_success\":true,\"time\":1744828675}')).toHaveBeenCalled;
      });

      it('deve retornar se encontrou uma referência invalida para adicionar um payload', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusData('{\"action_success\":true,\"time\":1744828675}')).toHaveBeenCalled;
        expect(getModbusData('{\"usr\":\"App\",\"variable\":\"mdb_refresh_data\",\"value\":1}')).toHaveBeenCalled;
      }); 

      it('deve retornar se o payload foi verificado como dado com sucesso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getModbusData('{\"energy_act_forward\":60.428,\"time\":1744829100,\"reactive\":111.889}')).toHaveBeenCalled;
      });      
    });



    describe('getCodiStatus', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getCodiStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência do status do módulo codi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o status do módulo codi está ativo', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiStatus('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se o status do módulo codi está desativado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiStatus('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getCodiConfig', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getCodiConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da configuração do codi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do codi foi encontrada com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getCodiConfig('{\"codi\":{\"codi_fix_repo\":false,\"1\":{\"port\":1,\"uartVirtualPort\":1,\"protocolo\":\"Normal\",\"reverse\":false,\"replicate\":true,\"sniffer\":false,\"repo\":false,\"misto\":false,\"conf\":\"8N1\",\"baud\":110}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do codi não foi encontrada ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiConfig('{\"codi\":{\"codi_fix_repo\":false}}')).toHaveBeenCalled;        

      });
    });

    describe('getCodiEnd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getCodiEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da exclusão de um codi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do codi foi excluída com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getCodiEnd('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do codi não foi foi excluida ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiEnd('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;        

      });

      it('deve retornar se a configuração do tentou excluir um codi não configurado ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiEnd('{"action_log\":\"Erro: nao existe nenhuma codi nessa porta\",\"time\":1736775917,\"action_success\":false}')).toHaveBeenCalled;        

      });      
    });    

    describe('getCodiAdd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getCodiAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da inclusão de um codi', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do codi foi incluida com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getCodiAdd('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do codi não foi foi incluida ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getCodiAdd('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;        
      });
    });

    describe('getSystemOTARollback', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getSystemOTARollback('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência de rollback do firmware', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSystemOTARollback('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o rollback do firmware teve sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getSystemOTARollback('{\"version\":\"H3.0_Z2.0.30\",\"time\":1729860300}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha rollback do firmware ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSystemOTARollback('{\"version\":\"\",\"time\":1729860300}')).toHaveBeenCalled;        
      });
    });

    describe('getSystemOTAUpdate', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getSystemOTAUpdate('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência de atualização do firmware', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSystemOTAUpdate('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se o atualização do firmware está em andamento', () => {

        infoReleasedAllMQTT(1);

        expect(getSystemOTAUpdate(' {\"ota_status\":\"Download 0% (1447 / 1036784)\",\"time\":1736785696}')).toHaveBeenCalled;
      });

      it('deve retornar se o atualização do firmware teve sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getSystemOTAUpdate('{\"ota_status\":\"Sucesso: Ota finalizado\",\"time\":1738794358}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha atualização do firmware ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getSystemOTAUpdate('{\"version\":\"H3.0_Z2.0.31\",\"time\":1729860300}')).toHaveBeenCalled;        
      });
    });

    describe('getIonStatus', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência do status do módulo ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o status do módulo ion está ativo', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonStatus('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se o status do módulo ion está desativado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonStatus('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getIonConfig', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da configuração do ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do ion foi encontrada com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getIonConfig('{\"ion\":{\"codi_fix_repo\":false,\"123\":{\"slave_id\":123,\"port\":0,\"baud\":9600,\"uartconf\":\"8N1\"}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do o não foi encontrada ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonConfig('{\"ion\":{\"codi_fix_repo\":false}}')).toHaveBeenCalled;        

      });
    });

    describe('getIonEnd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da exclusão de um ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do ion foi excluída com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getIonEnd('{"action_log\":\"Sucesso: ion removida\",\"time\":1736775750,\"action_success\":true}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do ion não foi foi excluida ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonEnd('{\"action_success\":false}')).toHaveBeenCalled;        

      });

      it('deve retornar se a configuração do tentou excluir um ion não configurado ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonEnd('{\"action_log\":\"Erro: nao existe nenhum ION nessa porta\",\"time\":1736965178,\"action_success\":false}')).toHaveBeenCalled;        

      });      
    }); 

    describe('getIonAdd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da inclusão de um ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do ion foi incluida com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getIonAdd('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do ion não foi foi incluida ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonAdd('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;        
      });
    });

    describe('getIonAutoSearch', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonAutoSearch('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da procura de um ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonAutoSearch('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a procura do ion foi incluida com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getIonAutoSearch('{\"ion_status\":\"Concluido\",\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se a procura do ion não foi cancelada ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonAutoSearch('{\"ion_status\":\"Comando cancelado\",\"time\":1729860083}')).toHaveBeenCalled;        
      });
    });

    describe('getIonData', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getIonData('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      
      it('deve retornar se não encontrou a referência da procura de um ion', () => {

        infoReleasedAllMQTT(1);
      
        expect(getIonData('{\"time\":1745529300,\"status\":"ION OK"}')).toHaveBeenCalled;
      });
    });

    describe('getPulsoStatus', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulsoStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência do status do módulo contador de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoStatus('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se o status do módulo contador de pulso está ativo', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoStatus('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se o status do módulo contador de pulso está desativado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoStatus('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getPulsoConfig', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulsoConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da configuração do contador de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoConfig('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do contador de pulso foi encontrada com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getPulsoConfig('{\"pulse\":{\"2\":{\"type\":4,\"repo_name\":\"reactive_repo\",\"var_name\":\"reactive\",\"port\":2,\"contact\":0,\"offset_Time\":0,\"send_time_repo\":60,\"send_time\":900,\"factor\":1,\"totalizador\":0,\"overflowTotaliz\":999999999,\"reactive\":true,\"repo\":true},\"1\":{\"type\":0,\"repo_name\":\"active_repo\",\"var_name\":\"active\",\"port\":1,\"contact\":0,\"offset_Time\":0,\"send_time_repo\":60,\"send_time\":900,\"factor\":1,\"totalizador\":0,\"overflowTotaliz\":999999999,\"reactive\":false,\"repo\":false}}}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do contador de pulso não foi encontrada ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoConfig('{\"pulse\":{}}')).toHaveBeenCalled;        

      });
    });

    describe('getPulsoEnd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulsoEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência do reset da configuração contador de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoEnd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do contador de pulso foi resetada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoEnd('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se houve falha ao resetar a configuração do contador de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoEnd('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;
      });
    });

    describe('getPulsoAdd', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulsoAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da inclusão de um contador de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoAdd('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do contador de pulso foi incluida com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getPulsoAdd('{\"action_log\":\"Sucesso: portas adicionadas\",\"time\":1738000868,\"action_success\":true}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do contador de pulso ativo e reativo foi incluida com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getPulsoAdd('{\"action_log\":\"Sucesso: porta ativa e reativa adicionadas\",\"time\":1737983230,\"action_success\":true}')).toHaveBeenCalled;
      });

      it('deve retornar se a configuração do contador de pulso foi configurada na porta errada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoAdd('{\"action_log\":\"Porta nao suportada\",\"time\":1737983230,\"action_success\":false}')).toHaveBeenCalled;        
      });

      it('deve retornar se a configuração do contador de pulso foi configurada na porta já utilizada', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoAdd('{\"action_log\":\"Porta nao pode ser inicializada\",\"time\":1737983230,\"action_success\":false}')).toHaveBeenCalled;        
      });

      it('deve retornar se a configuração do contador de pulso não foi configurado', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoAdd('{\"action_log\":\"\",\"time\":1737983230,\"action_success\":false}')).toHaveBeenCalled;        
      });      
    });

    describe('getPulsoReset', () => {

      it('deve retornar não liberado para procura da informação', () => {

        infoReleasedAllMQTT(0);
      
        expect(getPulsoReset('{\"time\":1729860083}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou a referência da exclusão de todos os contadores de pulso', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoReset('{\"time\":1729860083}')).toHaveBeenCalled;
      });
    
      it('deve retornar se a configuração do contador de pulso foi totalmente excluída com sucesso', () => {

        infoReleasedAllMQTT(1);

        expect(getPulsoReset('{\"action_success\":true,\"time\":1729860083}')).toHaveBeenCalled;
      });      

      it('deve retornar se a configuração do contador de pulso não foi foi totalmente excluída ', () => {

        infoReleasedAllMQTT(1);
      
        expect(getPulsoReset('{\"action_success\":false,\"time\":1729860083}')).toHaveBeenCalled;        
      });
    });

    describe('getKhomp', () => {
      it('deve retornar não liberado para procura das informações do khomp', () => {

        infoReleasedAllMQTT(0);
      
        expect(getKhomp('{\"bn\": \"F803320500034B92\", \"bt\": 1744376880}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou as informações do khomp', () => {

        infoReleasedAllMQTT(1);
      
        expect(getKhomp('{\"bn\": \"0000000000000000\", \"bt\": 1744376880}')).toHaveBeenCalled;
      });
      
      it('deve retornar se encontrou as informações do khomp', () => {

        infoReleasedAllMQTT(1);
      
        expect(getKhomp('{\"bn\": \"F803320500034B92\", \"bt\": 1744376880}')).toHaveBeenCalled;
      });      
    });

    describe('getApnConfig', () => {
      it('deve retornar não liberado para procura das informações das apns', () => {

        infoReleasedAllMQTT(0);
      
        expect(getApnConfig('{\"time\":1745073820,\"action_success\":false}')).toHaveBeenCalled;
      });

      it('deve retornar se não encontrou as informações da apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnConfig('{\"action_log\":\"No APN found\"}')).toHaveBeenCalled;
      });

      it('deve retornar se encontrou as informações da apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnConfig('{\"time\":1745155705,\"apn_list\":\"iot.datatemm.com.br(datatem, datatem)\"}')).toHaveBeenCalled;
      });      
    });

    describe('getApnAdd', () => {
      it('deve retornar não liberado para procura das informações da apn', () => {

        infoReleasedAllMQTT(0);
      
        expect(getApnAdd('{\"time\":1745073820,\"action_success\":false}')).toHaveBeenCalled;
      });

      it('deve retornar se não finalizou ao adicionar a apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnAdd('{\"action_log\":\"APN added\"}')).toHaveBeenCalled;
      });

      it('deve retornar se adicionou as informações da apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnAdd('{\"time\":1745073820,\"action_success\":true}')).toHaveBeenCalled;
      });      
    });

    describe('getApnRemove', () => {
      it('deve retornar não liberado para procura das informações da apn', () => {

        infoReleasedAllMQTT(0);
      
        expect(getApnRemove('{\"time\":1745073820,\"action_success\":false}')).toHaveBeenCalled;
      });

      it('deve retornar se não finalizou ao adicionar a apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnRemove('{\"action_log\":\"APN not found\"}')).toHaveBeenCalled;
      });

      it('deve retornar se adicionou as informações da apn', () => {

        infoReleasedAllMQTT(1);
      
        expect(getApnRemove('{\"time\":1745073820,\"action_success\":true}')).toHaveBeenCalled;
      });      
    });     

    describe('VerifyInfoReleseadDbgack', () => {
      it('deve chamar VerifyInfoReleseadDbgack quando todas as informações forem encontradas', async () => {
        // Mock dos dados de entrada        
        _testExports.infoReleasedDbgackMQTT(INFO_FINDED);

        // Execução da função
        await _testExports.VerifyInfoReleseadDbgack();

        // Verificações
        eventFindedDbgack(true);
        _testExports.infoReleasedDbgackMQTT(INFO_IDLE);
      });

      it('não deve chamar VerifyInfoReleseadDbgack quando nem todas as informações forem encontradas', async () => {
        // Mock dos dados de entrada        
        _testExports.infoReleasedDbgackMQTT(INFO_FINDED);

        // Execução da função
        await _testExports.VerifyInfoReleseadDbgack();

        // Verificações
        eventFindedDbgack(false);
        _testExports.infoReleasedDbgackMQTT(INFO_FINDED);
      });
    });

    describe('VerifyInfoReleseadSysConfig', () => {
      it('deve chamar VerifyInfoReleseadSysConfig quando todas as informações forem encontradas', async () => {
        // Mock dos dados de entrada        
        _testExports.infoReleasedSysConfigMQTT(INFO_FINDED);

        // Execução da função
        await _testExports.VerifyInfoReleseadSysConfig();

        // Verificações
        eventFindedSysConfig(true);
        _testExports.infoReleasedSysConfigMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadSysConfig quando nem todas as informações forem encontradas', async () => {
        // Mock dos dados de entrada        
        _testExports.infoReleasedSysConfigMQTT(INFO_FINDED);

        // Execução da função
        await _testExports.VerifyInfoReleseadSysConfig();

        // Verificações
        eventFindedSysConfig(false);
        _testExports.infoReleasedSysConfigMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadSystemReset', () => {
      it('deve chamar VerifyInfoReleseadSystemReset quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSystemReset();

        // Verificações
        eventReconnect(true);
        _testExports.infoReleasedSystemResetMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadSystemReset quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSystemReset();

        // Verificações
        eventReconnect(false);
        _testExports.infoReleasedSystemResetMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadSystemFormat', () => {
      it('deve chamar VerifyInfoReleseadSystemFormat quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSystemFormat();

        // Verificações
        eventFormat(true);
        _testExports.infoReleasedSystemFormatMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadSystemFormat quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSystemFormat();

        // Verificações
        eventFormat(false);
        _testExports.infoReleasedSystemFormatMQTT(INFO_NOT_FINDED);
      });
    });    
    
    describe('VerifyInfoReleseadModuleScan', () => {
      it('deve chamar VerifyInfoReleseadModuleScan quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadModuleScan();

        // Verificações
        eventModuleScan(10);
        _testExports.infoReleasedModuleScanMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModuleScan quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadModuleScan();

        // Verificações
        eventModuleScan(0);
        _testExports.infoReleasedModuleScanMQTT(INFO_NOT_FINDED);
      });
    });


    describe('VerifyInfoReleseadFormatMessage', () => {
      it('deve chamar VerifyInfoReleseadFormatMessage quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadFormatMessage();

        // Verificações
        eventFormatMessage(true);
        _testExports.infoReleasedFormatMessageMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadFormatMessage quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadFormatMessage();

        // Verificações
        eventFormatMessage(false);
        _testExports.infoReleasedFormatMessageMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadListFiles', () => {
      it('deve chamar VerifyInfoReleseadListFiles quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadListFiles();

        // Verificações
        eventListFiles([ { id: 0, name: 'file0', size:'10', timer:'1733414902'}]);
        _testExports.infoReleasedListFilesMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadListFiles quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadListFiles();

        // Verificações
        eventListFiles([]);
        _testExports.infoReleasedListFilesMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadFormatFile', () => {
      it('deve chamar VerifyInfoReleseadFormatFile quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadFormatFile();

        // Verificações
        eventFormatFile(true);
        _testExports.infoReleasedFormatFileMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadFormatFile quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadFormatFile();

        // Verificações
        eventFormatFile(false);
        _testExports.infoReleasedFormatFileMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadWifi', () => {
      it('deve chamar VerifyInfoReleseadWifi quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadWifi();

        // Verificações
        eventWifi(MockProbeSettings());
        _testExports.infoReleasedWifiMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadWifi quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadWifi();

        // Verificações
        eventWifi(MockProbeSettings());
        _testExports.infoReleasedWifiMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadGSM', () => {
      it('deve chamar VerifyInfoReleseadGSM quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadGSM();

        // Verificações
        eventGSM(MockProbeSettings());
        _testExports.infoReleasedGSMMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadGSM quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadGSM();

        // Verificações
        eventGSM(MockProbeSettings());
        _testExports.infoReleasedGSMMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadEthernet', () => {
      it('deve chamar VerifyInfoReleseadEthernet quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadEthernet();

        // Verificações
        eventEthernet(MockProbeSettings());
        _testExports.infoReleasedEthernetMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadFormatFile quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadEthernet();

        // Verificações
        eventEthernet(MockProbeSettings());
        _testExports.infoReleasedEthernetMQTT(INFO_NOT_FINDED);
      });
    });  
    
    describe('VerifyInfoReleseadSaveWifi', () => {
      it('deve chamar VerifyInfoReleseadSaveWifi quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSaveWifi();

        // Verificações
        eventSaveWifi(true);
        _testExports.infoReleasedSaveWifiMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadSaveWifi quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSaveWifi();

        // Verificações
        eventSaveWifi(false);
        _testExports.infoReleasedSaveWifiMQTT(INFO_NOT_FINDED);
      });
    });    

    describe('VerifyInfoReleseadSaveEthernet', () => {
      it('deve chamar VerifyInfoReleseadSaveEthernet quando todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSaveEthernet();

        // Verificações
        eventSaveEthernet(true);
        _testExports.infoReleasedSaveEthernetMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadSaveEthernet quando nem todas as informações forem encontradas', async () => {

        // Execução da função
        await _testExports.VerifyInfoReleseadSaveEthernet();

        // Verificações
        eventSaveEthernet(false);
        _testExports.infoReleasedSaveEthernetMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusMaster', () => {
      it('deve chamar VerifyInfoReleseadModbusMaster quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusMaster();

        // Verificações
        eventModbusMaster(true);
        _testExports.infoReleasedModbusMasterMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusMaster quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusMaster();

        // Verificações
        eventModbusMaster(false);
        _testExports.infoReleasedModbusMasterMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusConfig', () => {
      it('deve chamar VerifyInfoReleseadModbusConfig quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusConfig();

        // Verificações
        eventModbusConfig(MockModbusSettings());
        _testExports.infoReleasedModbusConfigMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusConfig quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusConfig();

        // Verificações
        eventModbusConfig([]);
        _testExports.infoReleasedModbusConfigMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusReset', () => {
      it('deve chamar VerifyInfoReleseadModbusReset quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusReset();

        // Verificações
        eventModbusReset(true);
        _testExports.infoReleasedModbusResetMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusReset quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusReset();

        // Verificações
        eventModbusReset(false);
        _testExports.infoReleasedModbusResetMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusAddBeginIni', () => {
      it('deve chamar VerifyInfoReleseadModbusAddBeginIni quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddBeginIni();

        // Verificações
        eventModbusAddBeginIni(true);
        _testExports.infoReleasedModbusAddBeginIniMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddBeginIni quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddBeginIni();

        // Verificações
        eventModbusAddBeginIni(false);
        _testExports.infoReleasedModbusAddBeginIniMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusAddDevice', () => {
      it('deve chamar VerifyInfoReleseadModbusAddDevice quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddDevice();

        // Verificações
        eventModbusAddDevice(true);
        _testExports.infoReleasedModbusAddDeviceMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddDevice quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddDevice();

        // Verificações
        eventModbusAddDevice(false);
        _testExports.infoReleasedModbusAddDeviceMQTT(INFO_NOT_FINDED);
      });
    });    

    describe('VerifyInfoReleseadModbusAddSlave', () => {
      it('deve chamar VerifyInfoReleseadModbusAddSlave quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddSlave();

        // Verificações
        eventModbusAddSlave(true);
        _testExports.infoReleasedModbusAddSlaveMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddSlave quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddSlave();

        // Verificações
        eventModbusAddSlave(false);
        _testExports.infoReleasedModbusAddSlaveMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusAddVariable', () => {
      it('deve chamar VerifyInfoReleseadModbusAddVariable quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddVariable();

        // Verificações
        eventModbusAddVariable(true);
        _testExports.infoReleasedModbusAddVariableMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddVariable quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddVariable();

        // Verificações
        eventModbusAddVariable(false);
        _testExports.infoReleasedModbusAddVariableMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusAddMean', () => {
      it('deve chamar VerifyInfoReleseadModbusAddMean quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddMean();

        // Verificações
        eventModbusAddMean(true);
        _testExports.infoReleasedModbusAddMeanMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddMean quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddMean();

        // Verificações
        eventModbusAddMean(false);
        _testExports.infoReleasedModbusAddMeanMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadModbusAddBeginFim', () => {
      it('deve chamar VerifyInfoReleseadModbusAddBeginFim quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddBeginFim();

        // Verificações
        eventModbusAddBeginFim(true);
        _testExports.infoReleasedModbusAddBeginFimMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadModbusAddBeginFim quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadModbusAddBeginFim();

        // Verificações
        eventModbusAddBeginFim(false);
        _testExports.infoReleasedModbusAddBeginFimMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadCodiStatus', () => {
      it('deve chamar VerifyInfoReleseadCodiStatus quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiStatus();

        // Verificações
        eventCodiStatus(true);
        _testExports.infoReleasedCodiStatusMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadCodiStatus quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiStatus();

        // Verificações
        eventCodiStatus(false);
        _testExports.infoReleasedCodiStatusMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadCodiConfig', () => {
      it('deve chamar VerifyInfoReleseadCodiConfig quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiConfig();

        // Verificações
        eventCodiConfig(MockCodiSettings());
        _testExports.infoReleasedCodiConfigMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadCodiConfig quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiConfig();

        // Verificações
        eventCodiConfig([]);
        _testExports.infoReleasedCodiConfigMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadCodiEnd', () => {
      it('deve chamar VerifyInfoReleseadCodiEnd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiEnd();

        // Verificações
        eventCodiEnd(true);
        _testExports.infoReleasedCodiEndMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadCodiEnd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiEnd();

        // Verificações
        eventCodiEnd(false);
        _testExports.infoReleasedCodiEndMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadCodiAdd', () => {
      it('deve chamar VerifyInfoReleseadCodiAdd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiAdd();

        // Verificações
        eventAddCodi(true);
        _testExports.infoReleasedCodiAddMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadCodiAdd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadCodiAdd();

        // Verificações
        eventAddCodi(false);
        _testExports.infoReleasedCodiAddMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadOTARollback', () => {
      it('deve chamar VerifyInfoReleseadOTARollback quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadOTARollback();

        // Verificações
        eventOTARollback('H3.0_Z2.0.30');
        _testExports.infoReleasedOTARollbackMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadOTARollback quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadOTARollback();

        // Verificações
        eventOTARollback('');
        _testExports.infoReleasedOTARollbackMQTT(INFO_NOT_FINDED);
      });
    });    

    describe('VerifyInfoReleseadUpdateFirmware', () => {
      it('deve chamar VerifyInfoReleseadUpdateFirmware quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadUpdateFirmware();

        // Verificações
        eventOTAUpdate('H3.0_Z2.0.31');
        _testExports.infoReleasedOTAUpdateMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadUpdateFirmware quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadUpdateFirmware();

        // Verificações
        eventOTAUpdate('');
        _testExports.infoReleasedOTAUpdateMQTT(INFO_NOT_FINDED);
      });
    });    

    describe('VerifyInfoReleseadIonStatus', () => {
      it('deve chamar VerifyInfoReleseadIonStatus quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonStatus();

        // Verificações
        eventIonStatus(true);
        _testExports.infoReleasedIonStatusMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadIonStatus quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonStatus();

        // Verificações
        eventIonStatus(false);
        _testExports.infoReleasedIonStatusMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadIonConfig', () => {
      it('deve chamar VerifyInfoReleseadIonConfig quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonConfig();

        // Verificações
        eventIonConfig(MockIonSettings());
        _testExports.infoReleasedIonConfigMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadIonConfig quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonConfig();

        // Verificações
        eventIonConfig([]);
        _testExports.infoReleasedIonConfigMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadIonEnd', () => {
      it('deve chamar VerifyInfoReleseadIonEnd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonEnd();

        // Verificações
        eventIonEnd(true);
        _testExports.infoReleasedIonEndMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadIonEnd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonEnd();

        // Verificações
        eventIonEnd(false);
        _testExports.infoReleasedIonEndMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadIonAdd', () => {
      it('deve chamar VerifyInfoReleseadIonAdd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonAdd();

        // Verificações
        eventIonAdd(true);
        _testExports.infoReleasedIonAddMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadIonAdd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonAdd();

        // Verificações
        eventIonAdd(false);
        _testExports.infoReleasedIonAddMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadIonAutoSearch', () => {
      it('deve chamar VerifyInfoReleseadIonAutoSearch quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonAutoSearch();

        // Verificações
        eventIonAutoSearch(true);
        _testExports.infoReleasedIonAutoSearchMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadIonAutoSearch quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadIonAutoSearch();

        // Verificações
        eventIonAutoSearch(false);
        _testExports.infoReleasedIonAutoSearchMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadPulsoStatus', () => {
      it('deve chamar VerifyInfoReleseadPulsoStatus quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoStatus();

        // Verificações
        eventPulsoStatus(true);
        _testExports.infoReleasedPulsoStatusMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoStatus quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoStatus();

        // Verificações
        eventPulsoStatus(false);
        _testExports.infoReleasedPulsoStatusMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadPulsoConfig', () => {
      it('deve chamar VerifyInfoReleseadPulsoConfig quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoConfig();

        // Verificações
        eventPulsoConfig(MockPulsoSettings());
        _testExports.infoReleasedPulsoConfigMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoConfig quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoConfig();

        // Verificações
        eventPulsoConfig([]);
        _testExports.infoReleasedPulsoConfigMQTT(INFO_NOT_FINDED);
      });
    });

    describe('VerifyInfoReleseadPulsoReset', () => {
      it('deve chamar VerifyInfoReleseadPulsoReset quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoReset();

        // Verificações
        eventPulsoReset(true);
        _testExports.infoReleasedPulsoResetMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoReset quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoReset();

        // Verificações
        eventPulsoReset(false);
        _testExports.infoReleasedPulsoResetMQTT(INFO_NOT_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoReset quando as informações não estaão sendo procuradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoReset();

        // Verificações
        eventPulsoReset(false);
        _testExports.infoReleasedPulsoResetMQTT(INFO_IDLE);
      });

    });    

    describe('VerifyInfoReleseadPulsoEnd', () => {
      it('deve chamar VerifyInfoReleseadPulsoEnd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoEnd();

        // Verificações
        eventPulsoEnd(true);
        _testExports.infoReleasedPulsoEndMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoEnd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoEnd();

        // Verificações
        eventPulsoEnd(false);
        _testExports.infoReleasedPulsoEndMQTT(INFO_NOT_FINDED);
      });
    });    

    describe('VerifyInfoReleseadPulsoAdd', () => {
      it('deve chamar VerifyInfoReleseadPulsoAdd quando todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoAdd();

        // Verificações
        eventPulsoAdd(0);
        _testExports.infoReleasedPulsoAddMQTT(INFO_FINDED);
      });

      it('não deve chamar VerifyInfoReleseadPulsoAdd quando nem todas as informações forem encontradas', () => {

        // Execução da função
        _testExports.VerifyInfoReleseadPulsoAdd();

        // Verificações
        eventPulsoAdd(4);
        _testExports.infoReleasedPulsoAddMQTT(INFO_NOT_FINDED);
      });
    });

    describe('MQTT - Funcoes', () => {

      describe('isMQTTConnected', () => {
        test('deve retornar se mqtt status de conexão do mqtt', () => {
          expect(isMQTTConnected()).toBe(false);
        });
      });
      

    });

    describe('padTo2Digits - Funcoes', () => {

        test('deve adicionar zero à esquerda para números menores que 10', () => {
            expect(padTo2Digits(5)).toBe('05');
        });

        test('não deve adicionar zero à esquerda para números maiores ou iguais a 10', () => {
            expect(padTo2Digits(10)).toBe('10');
            expect(padTo2Digits(25)).toBe('25');
        });

        test('deve retornar "00" para 0', () => {
            expect(padTo2Digits(0)).toBe('00');
        });
    });

    describe('DataCompleta - Funcoes', () => {
        test('deve formatar a data corretamente', () => {
        
            // Cria uma nova data para o teste
            const date = new Date(2025, 1, 7, 14, 25, 0); // 7 de Fevereiro de 2025, 14:25:00
  
            // Chama a função com a data de teste
            const formattedDate = DataCompleta(date);
  
            // Compara o resultado com o formato esperado
            expect(formattedDate).toBe('07/02/2025 14:25:00');
        });
    });

    describe('Data - Funcoes', () => {
        it('deverá formatar a data corretamente', () => {
          const date = new Date(2023, 0, 1); // 1 de Janeiro de 2023
          const formattedDate = Data(date);
          expect(formattedDate).toBe('01/01/2023');
        });
      
        it('deve lidar com dias e meses de um dígito', () => {
          const date = new Date(2023, 5, 9); // 9 de Junho de 2023
          const formattedDate = Data(date);
          expect(formattedDate).toBe('09/06/2023');
        });
      
        it('deve lidar com dias e meses de dois dígitos', () => {
          const date = new Date(2023, 10, 20); // 20 de Novembro de 2023
          const formattedDate = Data(date);
          expect(formattedDate).toBe('20/11/2023');
        });
    });

    describe('Hora - Funcoes', () => {
        it('devera retornar a hora no formato HH:MM:SS', () => {
          const date = new Date('2025-02-07 12:34:56');
          const result = Hora(date);
          expect(result).toBe('12:34:56');
        });
      
        it('deve preencher horas, minutos e segundos de um dígito com um zero à esquerda', () => {
          const date = new Date('2025-02-07 03:04:05');
          const result = Hora(date);
          expect(result).toBe('03:04:05');
        });
    });

    describe('DataTimeStamp - Funcoes', () => {
          
        it('converte timestamp para uma data em string', () => {
            // Exemplo de timestamp para 5 de março de 2023
            const timestamp = 1678012800;  // 05/03/2023 em segundos desde 01/01/1970
            const result = DataTimeStamp(timestamp);
            expect(result).toBe('05/03/2023');
        });        
    });

    describe('HoraTimeStamp - Funcoes', () => {
        test('deverá retornar a hora correta, mostrando os 04 segundos', () => {
            const timestamp = 946695604; // timestamp que representa 2000-01-01 01:00:04 UTC
            const show_second = true;
            const result = HoraTimeStamp(timestamp, show_second);
            expect(result).toBe('03:00:04'); // dependendo do seu fuso horário
        });
    
        test('deverá retornar a hora correta sem mostrar os segundos', () => {
            const timestamp = 946695604; // timestamp que representa 2000-01-01 01:00:04 UTC
            const show_second = false;
            const result = HoraTimeStamp(timestamp, show_second);
            expect(result).toBe('03:00'); // dependendo do seu fuso horário
        });    
    });
    
    describe('MascaraIP - Funcoes', () => { 
        it('deve remover caracteres não numéricos', () => { 
            const input = 'abc123def456ghi789jkl012'; 
            const output = MascaraIP(input); 
            expect(output).toBe('***************'); 
        }); 
    
        it('deve adicionar pontos nos lugares corretos', () => { 
            const input = '123456789012'; 
            const output = MascaraIP(input); 
            expect(output).toBe('***************'); 
        }); 
    
        it('deve limitar o comprimento a 15 caracteres', () => { 
            const input = '12345678901234567890'; 
            const output = MascaraIP(input); 
            expect(output).toBe('***************'); 
        }); 
    
        it('deve verificar se cada segmento é menor ou igual a 255', () => { 
            const input = '123456789255300400500'; 
            const output = MascaraIP(input); 
            expect(output).toBe('***************'); 
        }); 
    
        it('deve lidar com entradas curtas', () => { 
            const input = '123'; 
            const output = MascaraIP(input); 
            expect(output).toBe('123'); 
        }); 
    
        it('deve lidar com entradas vazias', () => { 
            const input = ''; 
            const output = MascaraIP(input); 
            expect(output).toBe(''); 
        }); 
    });

    describe('FormataTimer - Funcoes', () => {
        // Teste para verificar se a função formata corretamente para 00:00:00
        it('deve formatar 0 segundos como 00:00:00', () => {
          expect(FormataTimer(0)).toBe('00:00:00');
        });
      
        // Teste para verificar a formatação de 1 segundo
        it('deve formatar 1 segundo como 00:00:01', () => {
          expect(FormataTimer(1)).toBe('00:00:01');
        });
      
        // Teste para verificar a formatação de 1 minuto (60 segundos)
        it('deve formatar 60 segundos como 00:01:00', () => {
          expect(FormataTimer(60)).toBe('00:01:00');
        });
      
        // Teste para verificar a formatação de 1 hora (3600 segundos)
        it('deve formatar 3600 segundos como 01:00:00', () => {
          expect(FormataTimer(3600)).toBe('01:00:00');
        });
      
        // Teste para verificar a formatação de 1 hora, 1 minuto e 1 segundo (3661 segundos)
        it('deve formatar 3661 segundos como 01:01:01', () => {
          expect(FormataTimer(3661)).toBe('01:01:01');
        });
      
        // Teste para verificar a formatação de mais de 1 hora e alguns minutos e segundos
        it('deve formatar 3723 segundos como 01:02:03', () => {
          expect(FormataTimer(3723)).toBe('01:02:03');
        });
    });    

    describe('isNumber - Funcoes', () => {
        it('deve retornar true para uma string numérica', () => {
          expect(isNumber('12345')).toBe(true);
        });
      
        it('deve retornar false para uma string não numérica', () => {
          expect(isNumber('abcde')).toBe(false);
        });
      
        it('deve retornar false para uma string mista', () => {
          expect(isNumber('123abc')).toBe(false);
        });
      
        it('deve retornar false para uma string vazia', () => {
          expect(isNumber('')).toBe(false);
        });
    });  

    describe('isObject - Funcoes', () => {
        it('deverá retornar true se for object', () => {
            expect(isObject({})).toBe(true);
            expect(isObject([])).toBe(true);
            expect(isObject(new Date())).toBe(true);
        });
    
        it('deverá retornar false se não for object', () => {
            expect(isObject(42)).toBe(false);
            expect(isObject('string')).toBe(false);
            expect(isObject(null)).toBe(false);
            expect(isObject(undefined)).toBe(false);
            expect(isObject(true)).toBe(false);
            expect(isObject(false)).toBe(false);
        });
    });

    describe('getIndexParidade - Funcoes', () => {
        it('Deve retornar o id correto quando a paridades são encontradas', () => {
            expect(getIndexParidade('8N1')).toBe(0);
            expect(getIndexParidade('8E1')).toBe(2);
            expect(getIndexParidade('8O2')).toBe(5);
        });
      
        it('Deve retornar 0 quando a paridade não é encontrada', () => {
          expect(getIndexParidade('8N')).toBe(0);
        });
    });  
    
    describe('getIndexBaudRate - Funcoes', () => {
        it('Deve retornar o id correto quando os baud rate são encontrados', () => {
            expect(getIndexBaudRate(600)).toBe(1);
            expect(getIndexBaudRate(9600)).toBe(5);
            expect(getIndexBaudRate(19200)).toBe(7);
        });

        it('Deve retornar 5 quando a descricao não é encontrada', () => {
          expect(getIndexBaudRate(100)).toBe(5);
        });
    });

    describe('getIndexTempoEnvio - Funcoes', () => {
        it('Deve retornar o id correto quando os tempos de envio são encontrados', () => {
            expect(getIndexTempoEnvio(60)).toBe(0);
            expect(getIndexTempoEnvio(300)).toBe(1);
            expect(getIndexTempoEnvio(900)).toBe(2);
            expect(getIndexTempoEnvio(3600)).toBe(3);
        });

        it('Deve retornar 2 quando a descricao não é encontrada', () => {
            expect(getIndexTempoEnvio(100)).toBe(2);
        });
    });

    describe('ValidarIP - Funcoes', () => {

      it('deve retornar true para IP válido', () => {
        const ip = '*************';
        expect(ValidarIP(ip)).toBe(true);
      });
    
      it('deve retornar false para IP inválido', () => {
        const ip = '500.168.000.100';
        expect(ValidarIP(ip)).toBe(false);
      });

      it('deve retornar false para IP inválido', () => {
        const ip = '500.168.000';
        expect(ValidarIP(ip)).toBe(false);
      });      
    });    
    
    describe('getPercentualSinal', () => {
      test('deve retornar 0 se o sinal for menor ou igual ao minSinal', () => {
        expect(getPercentualSinal(5, 10, 20)).toBe(0);
      });
    
      test('deve retornar 100 se o sinal for maior ou igual ao maxSinal', () => {
        expect(getPercentualSinal(25, 10, 20)).toBe(100);
      });
    
      test('deve calcular o percentual corretamente para um sinal intermediário', () => {
        expect(getPercentualSinal(15, 10, 20)).toBe(50);
        expect(getPercentualSinal(12.5, 10, 20)).toBe(25);
        expect(getPercentualSinal(17.5, 10, 20)).toBe(75);
      });
    
      test('deve garantir que o valor esteja entre 0% e 100%', () => {
        expect(getPercentualSinal(5, 10, 20)).toBe(0);
        expect(getPercentualSinal(25, 10, 20)).toBe(100);
        expect(getPercentualSinal(15, 10, 20)).toBeGreaterThanOrEqual(0);
        expect(getPercentualSinal(15, 10, 20)).toBeLessThanOrEqual(100);
      });
    });

    describe('formatJson', () => {
      test('deve retornar se contem o topico se for uma string', () => {
        expect(formatJson("{\"usr\":\"App\",\"variable\":\"dbgack\"}", "action/10061C1D4998")).toContain("action/10061C1D4998");
      });

      test('deve retornar se contem o topico se for um objeto', () => {
        expect(formatJson(JSON.parse("{\"usr\":\"App\",\"variable\":\"dbgack\"}"), "action/10061C1D4998")).toContain("action/10061C1D4998");
      }); 
      it('deve retornar uma string vazia quando nenhum parâmetro é passado', () => {
        const resultado = formatJson('', '');
        
        expect(resultado).toBe(''); // Verifica se retorna vazio
      });
    
    });    


    describe('data', () => {
      describe('AddDevice', () => {
  
        // Teste básico para verificar se a função retorna o objeto correto
        it('deve retornar as configurações do dispositivo corretamente', () => {
          const id = 1;
          const nome = 'Dispositivo Teste';
          const endianess = 0;
        
          const resultadoEsperado = {
            id_device: id,
            nameDevice: nome,
            endianessDevice: endianess,
            slaves: [],
          };
        
          const resultado = AddDevice(id, nome, endianess);
        
          // Verificar se o resultado é igual ao resultado esperado
          expect(resultado).toEqual(resultadoEsperado);
        });
              
      });    

      describe('DetalhesConcluidoOSHoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = DetalhesConcluidoOSHoje();
      
          expect(detalhes_os).toEqual(MockDetalhesConcluidoOSHoje());
        });
      });
      

      describe('EtapasInstalacaoFro1Hoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = EtapasInstalacaoFro1Hoje();
      
          expect(detalhes_os).toEqual(MockEtapasInstalacaoFro1Hoje());
        });
      });

      describe('EtapasTrocaFro2Hoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = EtapasTrocaFro2Hoje ();
      
          expect(detalhes_os).toEqual(MockEtapasTrocaFro2Hoje());
        });
      });
      
      describe('EtapasManutencaoHoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = EtapasManutencaoHoje();
      
          expect(detalhes_os).toEqual(MockEtapasManutencaoHoje());
        });
      });

      describe('EtapasVisitaHoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = EtapasVisitaHoje();
      
          expect(detalhes_os).toEqual(MockEtapasVisitaHoje());
        });
      });

      describe('EtapasRetiradaFro3Hoje', () => {
        it('deve retornar os detalhes esperados da OS', () => {
          const detalhes_os = EtapasRetiradaFro3Hoje();
      
          expect(detalhes_os).toEqual(MockEtapasRetiradaFro3Hoje());
        });
      });

    });

  describe('Storages', () => {    

    jest.mock('@react-native-async-storage/async-storage', () => ({      
        setItem: jest.fn(),
        getItem: jest.fn(),
        clear: jest.fn(),
    
    }));
      
    describe('setStorageJson', () => {  
      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });
            
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = { test: 'value' };
        const error = new Error('Test error');
              
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));              
        console.error = jest.fn();          
        await setStorageJson(key, value);          
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageJson', () => {
      it('deve retornar um json quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(JSON.stringify({"test": "value"}));      
        const result = await getStorageJson('key');
        expect(result).toStrictEqual({"test": "value"});
      });

      it('deve retornar um null', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(null);    
        const result = await getStorageJson('key');
        expect(result).toBe(null);
      });      

      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });      
        
        const result = await getStorageJson('key');
        expect(result).toBe('');
      });          
    });      

    describe('setStorageString', () => {
      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });
  
      it('deve salvar o dado como string no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = 'value';
        await setStorageString(key, value);  
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, value);
      });     
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = 'value';
        const error = new Error('Test error');              
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));              
        console.error = jest.fn();          
        await setStorageString(key, value);          
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageString', () => {
      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });      
      it('deve retornar uma string quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue('value');      
        const result = await getStorageString('key');
        expect(result).toBe('value');
      });        
      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });      
        const result = await getStorageString('key');
        expect(result).toBe('');
      });    
    });
  
    describe('setStorageBoolean', () => {  
      it('deve salvar um valor booleano no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = true;      
        await setStorageBoolean(key, value);            
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, JSON.stringify(value));
      });            
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = 'testKey';
        const value = false;
        const error = new Error('Test error');                
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));                
        console.error = jest.fn();            
        await setStorageBoolean(key, value);            
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageBoolean ', () => {
      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });    
      it('deve retornar uma string quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(true);      
        const result = await getStorageBoolean ('key');
        expect(result).toBe(true);
      });        
      it('deve retornar um null', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(null);      
        const result = await getStorageBoolean('key');
        expect(result).toBe(null);
      }); 
      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });      
        const result = await getStorageBoolean ('key');
        expect(result).toBe(false);
      });    
    });
    
    describe('deleteStorage', () => {
      afterEach(() => {
        jest.clearAllMocks();
      });
                
      it('deve registrar um erro se a remoção falhar', async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error');
        const key = 'testKey';
        const error = new Error('Erro ao remover item');            
        const value = 'value';
        await setStorageString(key, value);  
        AsyncStorage.removeItem = jest.fn(() => Promise.reject(error));        
        await deleteStorage(key);
        expect(consoleErrorSpy).toHaveBeenCalledWith(`Erro ao remover ${key}: `, error);
      });
    });    
  
    describe('clearStorage', () => {          
      beforeEach(() => {
        // Limpa todos os mocks antes de cada teste
        jest.clearAllMocks(); 
      });        
        
      it('deve lidar com erros e chamar console.error', async () => {
        const error = new Error('Erro ao limpar os dados');          
        AsyncStorage.clear = jest.fn(() => Promise.reject(error));            
        console.error = jest.fn(); // Mock do console.error        
        await clearStorage();
        expect(console.error).toHaveBeenCalledWith('Erro ao limpar os dados', error);
      });
    });    
  });

  describe('MQTT Client', () => {

    // Mock das dependências internas
    jest.mock('sp-react-native-mqtt');
    const setStorageString = jest.fn();

    describe('connectMQTT', () => {

      let mqttClientMock: IMqttClient;
            
      beforeEach(() => {

        // Substituímos manualmente o método createClient por um mock
        MQTT.createClient = jest.fn().mockImplementation(() => Promise.resolve(mqttClientMock));      
        mqttClientMock = {
          on: jest.fn(),
          connect: jest.fn(),
          subscribe: jest.fn(),
          publish: jest.fn(),
          disconnect: jest.fn(),
          unsubscribe: jest.fn(),
          reconnect:jest.fn(),
          isConnected: jest.fn(),
        };
      });

      it('deve inicializar corretamente e conectar ao cliente MQTT HMG', async () => {
        await connectMQTT(true);

        infoReleasedAllMQTT(INFO_IDLE);
        expect(MQTT.createClient).toHaveBeenCalledWith({
          uri: BrokerHmg.URI,
          auth: BrokerHmg.AUTH,
          user: BrokerHmg.USER,
          pass: BrokerHmg.PASS,
          clientId: BrokerHmg.CLIENTE_ID,
          keepalive: 120,
          automaticReconnect: true,
        });
        expect(mqttClientMock.on).toHaveBeenCalledWith('connect', expect.any(Function));
        expect(mqttClientMock.connect).toHaveBeenCalled();
      
      });

      it('deve inicializar corretamente e conectar ao cliente MQTT PROD', async () => {
        await connectMQTT();

        infoReleasedAllMQTT(INFO_IDLE);
        expect(MQTT.createClient).toHaveBeenCalledWith({
          uri: BrokerProd.URI,
          auth: BrokerProd.AUTH,
          user: BrokerProd.USER,
          pass: BrokerProd.PASS,
          clientId: BrokerProd.CLIENTE_ID,
          keepalive: 120,
          automaticReconnect: true,
        });
        expect(mqttClientMock.on).toHaveBeenCalledWith('connect', expect.any(Function));
        expect(mqttClientMock.connect).toHaveBeenCalled();
      });

      it('deve lidar com o evento de fechar conexão', async () => {
        await connectMQTT();
        // Simula o evento 'connect'
        const onConnectCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'closed')[1];        
        onConnectCallback();
        eventIsConnected(false);
      });

      it('deve lidar com o evento de conexão', async () => {
        await connectMQTT();
        // Simula o evento 'connect'
        const onConnectCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'connect')[1];
        onConnectCallback();
        eventIsConnected(true);
      });

      it('deve lidar com erros no cliente MQTT', async () => {
        await connectMQTT();
        const onErrorCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'error')[1];
        onErrorCallback('Test Error');
        eventIsConnected(false);    
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister = INFO_IDLE', async () => {
        await connectMQTT(false, INFO_IDLE);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        const mockMessage = { data: 'mockMessageData' };
        onMessageCallback(mockMessage);
        _testExports.getMensagem(JSON.stringify(mockMessage), 'topico');      
        eventIsConnected(true);
      });
    
      it('deve lidar com mensagens recebidas quando eventRegister = INFO_FINDED', async () => {        
        await connectMQTT(false, INFO_FINDED);      
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        const mockMessage = '#';
        onMessageCallback(mockMessage);
        _testExports.getMensagem(JSON.stringify(mockMessage), 'topico');            
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_CONFIG] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(34, INFO_FINDED);                
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_RESET] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(35, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_ADD_INI] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(36, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_DEVICE] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(37, INFO_FINDED);                
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_SLAVE] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(38, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_VARIABLE] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(39, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_MEAN] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(40, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_ADD_FIM] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(41, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[MODBUS_DATA] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(42, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com mensagens recebidas quando eventRegister[CODI_STATUS] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(43, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[CODI_CONFIG] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(44, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[CODI_END] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(45, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[CODI_ADD] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(46, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[OTA_ROLLBACK] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(47, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[OTA_UPDATE] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(48, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com mensagens recebidas quando eventRegister[ION_STATUS] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(49, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[ION_CONFIG] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(50, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[ION_END] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(51, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[ION_ADD] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(52, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com mensagens recebidas quando eventRegister[ION_SEARCH] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(53, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[ION_DATA] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(54, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[PULSO_STATUS] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(55, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[PULSO_CONFIG] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(56, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[PULSO_RESET] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(57, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[PULSO_END] = INFO_FINDED', async () => {        
        await connectMQTT();        
        _testExports.infoReleasedMQTT(58, INFO_FINDED);        
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });

      it('deve lidar com mensagens recebidas quando eventRegister[PULSO_ADD] = INFO_FINDED', async () => {        
        await connectMQTT();                
        _testExports.infoReleasedMQTT(59, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister[KHOMP] = INFO_FINDED', async () => {        
        await connectMQTT();                
        _testExports.infoReleasedMQTT(60, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister[APN_CONFIG] = INFO_FINDED', async () => {        
        await connectMQTT();                
        _testExports.infoReleasedMQTT(61, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister[APN_ADD] = INFO_FINDED', async () => {        
        await connectMQTT();                
        _testExports.infoReleasedMQTT(62, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });
      
      it('deve lidar com mensagens recebidas quando eventRegister[APN_REMOVE] = INFO_FINDED', async () => {        
        await connectMQTT();                
        _testExports.infoReleasedMQTT(63, INFO_FINDED);
        const onMessageCallback = (mqttClientMock.on as jest.Mock).mock.calls.find(call => call[0] === 'message')[1];
        onMessageCallback('#');
        eventIsConnected(true);
      });      

      it('deve lidar com a publish do mqtt ', async () => {
        
        await connectMQTT(false, INFO_FINDED);        

        eventIsConnected(true);

        publishMQTT('12345678', 'dbgack');
        publishMQTT('12345678', 'sys_config', 'all');
        publishMQTT('12345678', 'sys_config', 'modbus');
        publishMQTT('12345678', 'sys_config', 'codi');
        publishMQTT('12345678', 'sys_config', 'ion');
        publishMQTT('12345678', 'sys_config', 'pulse');
        publishMQTT('12345678', 'system_reset');
        publishMQTT('12345678', 'system_format');
        publishMQTT('12345678', 'module_scan');
        publishMQTT('12345678', 'pers_array_format', 'message_manager');
        publishMQTT('12345678', 'system_files');
        publishMQTT('12345678', 'pers_format');
        publishMQTT('12345678', 'sys_config_wifi');
        publishMQTT('12345678', 'sys_config_gsm');
        publishMQTT('12345678', 'sys_config_eth');
        publishMQTT('12345678', 'wifi_reset');
        publishMQTT('12345678', 'ethernet_reset_config');
        publishMQTT('12345678', 'wifi_config', JSON.stringify({pass:'senha', ssid:'user'}));
        publishMQTT('12345678', 'ethernet_config', JSON.stringify({dns:'*************', gw:'***********01', ip:'***********02', mask:'*************'}));
        publishMQTT('12345678', 'modbus_master');
        publishMQTT('12345678', 'modbus_reset');
        publishMQTT('12345678', 'modbus_add_begin', JSON.stringify({device_size:1, mean_size:1, slave_size:1, value:true}));
        publishMQTT('12345678', 'modbus_add_begin', JSON.stringify({value:false}));
        publishMQTT('12345678', 'modbus_add_device', JSON.stringify({dev_name:'Device', endianness:3}));
        publishMQTT('12345678', 'modbus_add_slave', JSON.stringify({ dev_name:'Device', slave_name:'Slave', slave_id:1, send_time:900, protocol:0, baud:600, uart: '8N1', port_id:0}));
        publishMQTT('12345678', 'modbus_add_slave', JSON.stringify({ dev_name:'Device', slave_name:'Slave', slave_id:2, send_time:900, protocol:1, port:502, conn: 0, slave_ip:'*************'}));
        publishMQTT('12345678', 'modbus_add_variable', JSON.stringify({dev_name:'Device', address:1, data_type:2, factor:1.0, function:2, env:true, mean: false, var_name:'active'}));
        publishMQTT('12345678', 'modbus_add_mean', JSON.stringify({slave_name:'Slave', var_name:'active'}));
        publishMQTT('12345678', 'mdb_refresh_data', JSON.stringify({value:1}));
        publishMQTT('12345678', 'codi');
        publishMQTT('12345678', 'codi_end', JSON.stringify({port:1}));
        publishMQTT('12345678', 'codi_add', JSON.stringify({port:1, protocolo:'normal', replicate:false, reverse:false, baud: 600, conf: '8N1', repo: false }));
        publishMQTT('12345678', 'system_rollback');
        publishMQTT('12345678', 'ota', JSON.stringify({modo:0, url:'http://www.site.com', timeout_s:10, reverse:false}));
        publishMQTT('12345678', 'ota', JSON.stringify({modo:1, url:'http://www.site.com', timeout_s:10, reverse:false, stable: true}));
        publishMQTT('12345678', 'ota', JSON.stringify({modo:2, url:'http://www.site.com', timeout_s:10, reverse:false, ultra_stable: true}));
        publishMQTT('12345678', 'ion');
        publishMQTT('12345678', 'ion_end', JSON.stringify({port:1, slave_id: 100}));
        publishMQTT('12345678', 'ion_add', JSON.stringify({port:1, slave_id: 100, baud:100,uart:'8N1'}));
        publishMQTT('12345678', 'ion_auto_search', JSON.stringify({port:1, cancel: true}));
        publishMQTT('12345678', 'ion_auto_search', JSON.stringify({port:1, cancel: false, basic:true,extended:0}));
        publishMQTT('12345678', 'ion_data');
        publishMQTT('12345678', 'pulse');
        publishMQTT('12345678', 'pulse_reset');
        publishMQTT('12345678', 'pulse_end', JSON.stringify({port:1}));
        publishMQTT('12345678', 'pulse_add', JSON.stringify({port:1,type:'eflow',contact:'nc', repo: false, send_time_repo:10,send_time:900}));
        publishMQTT('12345678', 'pulse_add', JSON.stringify({port:1,type:'eflow',contact:'##', repo: false, send_time_repo:'##',send_time:900}));
        publishMQTT('12345678', 'khomp');
        publishMQTT('12345678', 'get_apns');
        publishMQTT('12345678', 'add_apn', JSON.stringify({apn:'apn.teste.com.br', user:'teste', pass: 'teste'}));
        publishMQTT('12345678', 'remove_apn', JSON.stringify({apn:'apn.teste.com.br'}));

        publishMQTT('12345678', '');
      });      

      it('deve lidar com a subscribe do mqtt ', async () => {
        
        await connectMQTT(false, INFO_FINDED);        

        eventIsConnected(true);

        subscribeMQTT('12345678', 'dbgack');
        subscribeMQTT('12345678', 'sys_config');
        subscribeMQTT('12345678', 'sys_config');
        subscribeMQTT('12345678', 'system_reset');
        subscribeMQTT('12345678', 'system_format');
        subscribeMQTT('12345678', 'module_scan');
        subscribeMQTT('12345678', 'pers_array_format');
        subscribeMQTT('12345678', 'system_files');
        subscribeMQTT('12345678', 'pers_format');
        subscribeMQTT('12345678', 'sys_config_wifi');
        subscribeMQTT('12345678', 'sys_config_gsm');
        subscribeMQTT('12345678', 'sys_config_eth');
        subscribeMQTT('12345678', 'wifi_reset');
        subscribeMQTT('12345678', 'ethernet_reset_config');
        subscribeMQTT('12345678', 'wifi_config');
        subscribeMQTT('12345678', 'ethernet_config');
        subscribeMQTT('12345678', 'modbus_master');
        subscribeMQTT('12345678', 'modbus_reset');
        subscribeMQTT('12345678', 'modbus_add_begin');
        subscribeMQTT('12345678', 'modbus_add_device');
        subscribeMQTT('12345678', 'modbus_add_slave');
        subscribeMQTT('12345678', 'modbus_add_variable');
        subscribeMQTT('12345678', 'modbus_add_mean');
        subscribeMQTT('12345678', 'mdb_refresh_data', JSON.stringify({value:"mdb_rtu_001"}));
        subscribeMQTT('12345678', 'codi');
        subscribeMQTT('12345678', 'codi_end');
        subscribeMQTT('12345678', 'codi_add');
        subscribeMQTT('12345678', 'system_rollback');
        subscribeMQTT('12345678', 'ota');
        subscribeMQTT('12345678', 'ion');
        subscribeMQTT('12345678', 'ion_end');
        subscribeMQTT('12345678', 'ion_add');
        subscribeMQTT('12345678', 'ion_auto_search');
        subscribeMQTT('12345678', 'ion_data', JSON.stringify({port:1, id:100}));
        subscribeMQTT('12345678', 'pulse');
        subscribeMQTT('12345678', 'pulse_reset');
        subscribeMQTT('12345678', 'pulse_end');
        subscribeMQTT('12345678', 'pulse_add');        
        subscribeMQTT('F803320500034B92', 'khomp');
        subscribeMQTT('F803320B00034B92', 'khomp');
        subscribeMQTT('12345678', 'get_apns');
        subscribeMQTT('12345678', 'add_apn');
        subscribeMQTT('12345678', 'remove_apn');
        subscribeMQTT('12345678', '');
      });

      it('deve lidar com o unsubscribe do mqtt ', async () => {
        
        await connectMQTT(false, INFO_FINDED);        

        eventIsConnected(true);

        unsubscribeMQTT('12345678', 'dbgack');
        unsubscribeMQTT('12345678', 'sys_config');
        unsubscribeMQTT('12345678', 'sys_config');
        unsubscribeMQTT('12345678', 'system_reset');
        unsubscribeMQTT('12345678', 'system_format');
        unsubscribeMQTT('12345678', 'module_scan');
        unsubscribeMQTT('12345678', 'pers_array_format');
        unsubscribeMQTT('12345678', 'system_files');
        unsubscribeMQTT('12345678', 'pers_format');
        unsubscribeMQTT('12345678', 'sys_config_wifi');
        unsubscribeMQTT('12345678', 'sys_config_gsm');
        unsubscribeMQTT('12345678', 'sys_config_eth');
        unsubscribeMQTT('12345678', 'wifi_reset');
        unsubscribeMQTT('12345678', 'ethernet_reset_config');
        unsubscribeMQTT('12345678', 'wifi_config');
        unsubscribeMQTT('12345678', 'ethernet_config');
        unsubscribeMQTT('12345678', 'modbus_master');
        unsubscribeMQTT('12345678', 'modbus_reset');
        unsubscribeMQTT('12345678', 'modbus_add_begin');
        unsubscribeMQTT('12345678', 'modbus_add_device');
        unsubscribeMQTT('12345678', 'modbus_add_slave');
        unsubscribeMQTT('12345678', 'modbus_add_variable');
        unsubscribeMQTT('12345678', 'modbus_add_mean');
        unsubscribeMQTT('12345678', 'mdb_refresh_data', JSON.stringify({value:"mdb_rtu_001"}));
        unsubscribeMQTT('12345678', 'codi');
        unsubscribeMQTT('12345678', 'codi_end');
        unsubscribeMQTT('12345678', 'codi_add');
        unsubscribeMQTT('12345678', 'system_rollback');
        unsubscribeMQTT('12345678', 'ota');
        unsubscribeMQTT('12345678', 'ion');
        unsubscribeMQTT('12345678', 'ion_end');
        unsubscribeMQTT('12345678', 'ion_add');
        unsubscribeMQTT('12345678', 'ion_auto_search');
        unsubscribeMQTT('12345678', 'ion_data', JSON.stringify({port:1, id:100}));
        unsubscribeMQTT('12345678', 'pulse');
        unsubscribeMQTT('12345678', 'pulse_reset');
        unsubscribeMQTT('12345678', 'pulse_end');
        unsubscribeMQTT('12345678', 'pulse_add');        
        unsubscribeMQTT('F803320500034B92', 'khomp');
        unsubscribeMQTT('F803320B00034B92', 'khomp');
        unsubscribeMQTT('12345678', 'get_apns');
        unsubscribeMQTT('12345678', 'add_apn');
        unsubscribeMQTT('12345678', 'remove_apn');
        unsubscribeMQTT('12345678', '');
      });      

      it('desconecta com sucesso', () => {
        // Simulando a desconexão bem-sucedida
        disconnectMQTT();
        
        expect(isMQTTConnected()).toBe(false);
      });

      test('lida com erro durante a desconexão', () => {
    
        disconnectMQTT();
        expect(isMQTTConnected()).toBe(false);
      }); 
      
    });
  });

});


