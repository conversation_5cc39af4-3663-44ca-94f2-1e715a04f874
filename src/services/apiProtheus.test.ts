import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import apiProtheus from './apiProtheus';

jest.mock('./apiProtheus');

describe('apiProtheus - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiProtheus.defaults.baseURL).toBe('https://api-hmg-protheus.comerc.com.br/doc88');
        expect(apiProtheus.defaults.responseType).toBe('json');
        expect(apiProtheus.defaults.withCredentials).toBe(true);
    });    
});
