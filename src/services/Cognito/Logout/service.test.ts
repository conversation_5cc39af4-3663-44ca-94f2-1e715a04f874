// CognitoSignInService.test.js
import { 
    COGNITO_LOGIN_BASE_PROD_URL, 
    COGNITO_CLIENT_ID_PROD, 
    COGNITO_CALLBACK_URL_LOGOUT_PROD 
} from '@env';

import { CognitoLogoutService } from './service';

// constantes
import {URL_SIGNOUT} from '../constants'

describe('CognitoLogoutService', () => {
  it('deve retornar a URL de logout correta com os valores reais', () => {
    const expectedUrl = `${COGNITO_LOGIN_BASE_PROD_URL}${URL_SIGNOUT}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=${COGNITO_CALLBACK_URL_LOGOUT_PROD}`;
    const result = CognitoLogoutService.SignOut();
    expect(result).toBe(expectedUrl);
  });
});