// CognitoSignInService.test.js
import { 
  COGNITO_LOGIN_BASE_PROD_URL, 
  COGNITO_CLIENT_ID_PROD, 
  COGNITO_CALLBACK_URL_LOGIN_PROD 
} from '@env';

import { CognitoSignInService } from './service';

// constantes
import {URL_SIGNIN} from '../constants'

describe('CognitoSignInService', () => {
  it('deve retornar a URL de login correta com os valores reais', () => {
    const expectedUrl = `${COGNITO_LOGIN_BASE_PROD_URL}${URL_SIGNIN}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=${COGNITO_CALLBACK_URL_LOGIN_PROD}`;
    const result = CognitoSignInService.signIn();
    expect(result).toBe(expectedUrl);
  });
});
