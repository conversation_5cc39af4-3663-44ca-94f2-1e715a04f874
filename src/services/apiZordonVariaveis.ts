import apiZordon from "./apiZordon";

export const apiZordonVariaveis  = async (dispositivo: string) => {

  try {

    // seta o parametro
    let parametro = `/tabelas/${dispositivo}/`;
    
    const result = await apiZordon(parametro, {
      method:"GET",      
    });

    return result;
  }
  catch(error: any) {      
      return error.response?.data;
  }
}

export default apiZordonVariaveis;
