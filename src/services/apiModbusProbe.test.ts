import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import apiGestal from './apiGestal';
import { MockModbusJson } from '../../__mocks__/ModbusJsonMock';
import apiModbusProbe from './apiModbusProbe';

import * as ApiGestal from './apiGestal';
const spyApiGestal = jest.spyOn(ApiGestal, 'default');

jest.mock('./apiGestal');

describe('apiVariaveisProbe - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiGestal.defaults.baseURL).toBe('http://ws.gestal.srv.br/api/v1');
        expect(apiGestal.defaults.responseType).toBe('json');
        expect(apiGestal.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/modbus').reply(200, data);

        apiGestal.get = jest.fn().mockReturnValueOnce({data:MockModbusJson});

        const response = await apiGestal.get('/modbus');
        expect(response.data).toEqual(MockModbusJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiGestal.mockRejectedValue({response:{data:'erro'}})
        const response = await apiModbusProbe('/modbus');

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiGestal.mockResolvedValue({data:MockModbusJson})
        const response = await apiModbusProbe();
        expect(response.data).toEqual(MockModbusJson);    
    });    
});
