import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import apiGestal from './apiGestal';
import { MockVariableJson } from '../../__mocks__/VariableJsonMock';
import apiVariaveisProbe from './apiVariaveisProbe';

import * as ApiGestal from './apiGestal';
const spyApiGestal = jest.spyOn(ApiGestal, 'default');

jest.mock('./apiGestal');

describe('apiVariaveisProbe - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiGestal.defaults.baseURL).toBe('http://ws.gestal.srv.br/api/v1');
        expect(apiGestal.defaults.responseType).toBe('json');
        expect(apiGestal.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/modbus/tabelas/EN_KRON_MULTK120/').reply(200, data);

        apiGestal.get = jest.fn().mockReturnValueOnce({data:MockVariableJson});

        const response = await apiGestal.get('/modbus/tabelas/EN_KRON_MULTK120/');
        expect(response.data).toEqual(MockVariableJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiGestal.mockRejectedValue({response:{data:'erro'}})
        const response = await apiVariaveisProbe('/modbus/tabelas/EN_KRON_MULTK120/');

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiGestal.mockResolvedValue({data:MockVariableJson})
        const response = await apiVariaveisProbe('/modbus/tabelas/EN_KRON_MULTK120/');
        expect(response.data).toEqual(MockVariableJson);    
    });    
});
