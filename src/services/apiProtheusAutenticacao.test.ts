import axios from "axios";
import <PERSON>ck<PERSON><PERSON>pter from "axios-mock-adapter";
import api<PERSON>rotheus from './apiProtheus';
import apiProtheusAutenticacao from "./apiProtheusAutenticacao";

// Mock da função
jest.mock('./apiProtheus');

const mockedApiProtheus = apiProtheus as unknown as jest.Mock;

describe("apiProtheusAutenticacao", () => {
  let mock: MockAdapter;

  beforeEach(() => {
    mock = new MockAdapter(axios);
  });

  afterEach(() => {
    mock.reset();
  });

 it('deve retornar resultado quando a requisição for bem-sucedida', async () => {
    const mockResponse = { access_token: 'token123' };
    mockedApiProtheus.mockResolvedValue(mockResponse);

    const result = await apiProtheusAutenticacao();

    expect(mockedApiProtheus).toHaveBeenCalledWith("/api/oauth2/v1/token", {
      method: "POST",
      headers: {
        'Content-Type': "aplication/json",
      },
      params: {
        grant_type: 'password',
        username: 'api_appinstalador',
        password: 'dza_umr1fna!HBE6xtz',
      },
    });

    expect(result).toEqual(mockResponse);
  });

  it('deve retornar erro quando a requisição falhar', async () => {
    const mockError = {
      response: {
        data: {
          error: 'invalid_grant',
        }
      }
    };

    mockedApiProtheus.mockRejectedValue(mockError);

    const result = await apiProtheusAutenticacao();

    expect(result).toEqual({ error: 'invalid_grant' });
  });

});