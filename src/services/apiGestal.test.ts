import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import apiGestal from './apiGestal';

jest.mock('./apiGestal');

describe('apiVariaveisProbe - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiGestal.defaults.baseURL).toBe('http://ws.gestal.srv.br/api/v1');
        expect(apiGestal.defaults.responseType).toBe('json');
        expect(apiGestal.defaults.withCredentials).toBe(true);
    });    
});
