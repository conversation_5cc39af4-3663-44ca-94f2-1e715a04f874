import { EventRegister } from 'react-native-event-listeners';

import {
    <PERSON>rokerHmg, Broke<PERSON><PERSON>rod,
    INFO_FINDED,
    INFO_IDLE,
    INFO_NOT_FINDED,
    InfoProbeMQTT,
    ListaBaudRateProbe,
    ListaParidadeProbe,
    ListaProtocoloCodi485,
    ListaTiposPulso,
    ProtocoloProbe,
    TempoSegundosEnvioProbe,
    TipoFirmwareModo,
    TipoMedicaoPulso,
    TiposCODI,
    TiposComunicacaoION
} from '../constantes';

import { getStorageString, KEY, setStorageString } from '../storages';

import {
    AddIon,
    AddPulso,
    ApnSettings,
    codiSettings,
    DeviceSettingsInicial,
    FilesProbe,
    ionSettings,
    modbusSettings,
    ProbeSettings,
    ProbeSettingsInicial,
    pulsoSettings,
    SlavesSettingsInicial
} from '../data';

import MQTT, { IMqttClient } from 'sp-react-native-mqtt';
import { DataCompleta, format<PERSON>son, getIndexBaudRate, getIndexParidade, getIndexTempoEnvio, isNumber, parseStringApn } from '../funcoes';

const qOS_action = 0;
const qOS_state = 1;

let mqttClient: IMqttClient;
let isConnected = false;
let modbusPayload: string[] = [];

// lista de informações vindas do mqtt, inicialmente a procura esta parada
let eventRegister = new Array<number>(InfoProbeMQTT.TOTAL).fill(INFO_IDLE);

// evento indicando que esta conectado ao broker mqtt
export const eventIsConnected = (is_connected: boolean) => { EventRegister.emit('isConnectedMQTT', is_connected); };

// evento indicando que veio nova mensagem
export const eventMessage = (mensagem: string) => { EventRegister.emit('messageMQTT', mensagem); };

// evento indicando que as informações do dbgack foram encontradas
export const eventFindedDbgack = (finded: boolean) => { EventRegister.emit('findDbgackMQTT', finded); };

// evento indicando que as informações do sys_config foram encontradas
export const eventFindedSysConfig = (finded: boolean) => { EventRegister.emit('findSysConfigMQTT', finded); };

// evento indicando que encontrou a data e hora atual
export const eventFirmware = (firmware: string) => { eventRegister[InfoProbeMQTT.FIRMWARE] = INFO_FINDED; EventRegister.emit('firmwareMQTT', firmware); };

// evento indicando que encontrou a data e hora atual
export const eventDateTime = (date_time: number) => { eventRegister[InfoProbeMQTT.DATAHORA] = INFO_FINDED; EventRegister.emit('dateTimeMQTT', date_time); };

// evento indicando a data de instalação do firmware
export const eventFirmwareBoot = (firmware_boot: number) => { eventRegister[InfoProbeMQTT.FIRMWARE_BOOT] = INFO_FINDED; EventRegister.emit('firmwareBootMQTT', firmware_boot); };

// evento indicando o total de resets da probe
export const eventResets = (resets: number) => { eventRegister[InfoProbeMQTT.RESETS] = INFO_FINDED; EventRegister.emit('resetsMQTT', resets); };

// evento indicando o total de exceções (bug) que fizeram a probe dar reset
export const eventExceptions = (exceptions: number) => { eventRegister[InfoProbeMQTT.EXCECOES] = INFO_FINDED; EventRegister.emit('exceptionsMQTT', exceptions); };

// evento indicando o total de memoria heap livre
export const eventHeap = (heap: number) => { eventRegister[InfoProbeMQTT.HEAP] = INFO_FINDED; EventRegister.emit('heapMQTT', heap); };

// evento indicando que encontrou o nivel de bateria
export const eventBattery = (battery: number) => { eventRegister[InfoProbeMQTT.NIVEL_BATERIA] = INFO_FINDED; EventRegister.emit('batteryMQTT', battery); };

// evento indicando que encontrou a tensão da bateria
export const eventVBattery = (v_battery: number) => { eventRegister[InfoProbeMQTT.TENSAO_BATERIA] = INFO_FINDED; EventRegister.emit('vBatteryMQTT', v_battery); };

// evento indicando que encontrou o status de tensão da probe
export const eventStatusVProbe = (v_probe: number) => { eventRegister[InfoProbeMQTT.STATUS_TENSAO] = INFO_FINDED; EventRegister.emit('vStatusProbeMQTT', v_probe); };

// evento indicando que encontrou o sinal do gsm ou wifi
export const eventSignal = (signal: number) => { eventRegister[InfoProbeMQTT.SINAL] = INFO_FINDED; EventRegister.emit('signalMQTT', signal); };

// evento indicando que encontrou o tipo de operadora
export const eventOperadora = (operadora: number) => { eventRegister[InfoProbeMQTT.TIPO_OPERADORA] = INFO_FINDED; EventRegister.emit('operadoraMQTT', operadora); };

// evento indicando que encontrou o tipo de conexão
export const eventTypeConnection = (type_connection: number) => { eventRegister[InfoProbeMQTT.TIPO_CONEXAO] = INFO_FINDED; EventRegister.emit('typeConnectionMQTT', type_connection); };

// evento indicando que a configuração do sistema esta habilitado
export const eventSistema = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.SISTEMA] = INFO_FINDED; EventRegister.emit('sistemaMQTT', habilitado); };

// evento indicando que a configuração da rede esta habilitado
export const eventRede = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.REDE] = INFO_FINDED; EventRegister.emit('redeMQTT', habilitado); };

// evento indicando que configuração da modbus esta habilitado
export const eventModbus = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.MODBUS] = INFO_FINDED; EventRegister.emit('modbusMQTT', habilitado); };

// evento indicando que configuração do CODI esta habilitado
export const eventCodi = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.CODI] = INFO_FINDED; EventRegister.emit('codiMQTT', habilitado); };

// evento indicando que configuração do ION esta habilitado
export const eventION = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.ION] = INFO_FINDED; EventRegister.emit('ionMQTT', habilitado); };

// evento indicando que configuração do Contador de Pulsos esta habilitado
export const eventPulso = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.PULSO] = INFO_FINDED; EventRegister.emit('pulsoMQTT', habilitado); };

// evento indicando que configuração do OTA esta habilitado
export const eventOTA = (habilitado: boolean) => { eventRegister[InfoProbeMQTT.OTA] = INFO_FINDED; EventRegister.emit('otaMQTT', habilitado); };

// evento indicando o ip da probe
export const eventIP = (ip: string) => { eventRegister[InfoProbeMQTT.IP] = INFO_FINDED; EventRegister.emit('ipMQTT', ip); };

// evento indicando o total de memoria flash disponivel
export const eventTotalMemory = (memory: number) => { eventRegister[InfoProbeMQTT.MEMORIA_TOTAL] = INFO_FINDED; EventRegister.emit('totalMemoryMQTT', memory); };

// evento indicando o total de memoria utilizada
export const eventUsedMemory = (memory: number) => { eventRegister[InfoProbeMQTT.MEMORIA_USADA] = INFO_FINDED; EventRegister.emit('usedMemoryMQTT', memory); };

// evento indicando o ip gateway da probe
export const eventGateway = (gateway: string) => { EventRegister.emit('gatewayMQTT', gateway); };

// evento indicando que a probe foi reconectada
export const eventReconnect = (reconnect: boolean) => { eventRegister[InfoProbeMQTT.RECONECTADO] = INFO_FINDED; EventRegister.emit('reconnectMQTT', reconnect); };

// evento indicando que a probe foi formatada
export const eventFormat = (format: boolean) => { eventRegister[InfoProbeMQTT.FORMATADO] = INFO_FINDED; EventRegister.emit('formatMQTT', format); };

// evento indicando que a probe foi escaneada
export const eventModuleScan = (scan: number) => { eventRegister[InfoProbeMQTT.MODULO_SCAN] = INFO_FINDED; EventRegister.emit('moduleScanMQTT', scan); };

// evento indicando que as mensagens da probe foram formatadas
export const eventFormatMessage = (format: boolean) => { eventRegister[InfoProbeMQTT.MSG_FORMATADA] = INFO_FINDED; EventRegister.emit('formatMessageMQTT', format); };

// evento indicando que os arquivos da probe foram listados
export const eventListFiles = (files: FilesProbe[]) => { eventRegister[InfoProbeMQTT.LISTA_ARQUIVOS] = INFO_FINDED; EventRegister.emit('listFilesMQTT', files); };

// evento indicando que o arquivo da probe foi formatado
export const eventFormatFile = (format: boolean) => { eventRegister[InfoProbeMQTT.FORMATA_ARQUIVO] = INFO_FINDED; EventRegister.emit('formatFileMQTT', format); };

// evento indicando que as informações da rede wifi foram encontradas
export const eventWifi = (probe: ProbeSettings) => { eventRegister[InfoProbeMQTT.REDE_WIFI] = INFO_FINDED; EventRegister.emit('wifiMQTT', probe); };

// evento indicando que as informações da rede gsm foram encontradas
export const eventGSM = (probe: ProbeSettings) => { eventRegister[InfoProbeMQTT.REDE_GSM] = INFO_FINDED; EventRegister.emit('gsmMQTT', probe); };

// evento indicando que as informações da rede ethernet foram encontradas
export const eventEthernet = (probe: ProbeSettings) => { eventRegister[InfoProbeMQTT.REDE_ETHERNET] = INFO_FINDED; EventRegister.emit('ethernetMQTT', probe); };

// evento indicando que as informações da rede wifi foram salvas
export const eventSaveWifi = (save: boolean) => { eventRegister[InfoProbeMQTT.REDE_WIFI_SALVA] = INFO_FINDED; EventRegister.emit('saveWifiMQTT', save); };

// evento indicando que as informações da rede ethernet foram salvas
export const eventSaveEthernet = (save: boolean) => { eventRegister[InfoProbeMQTT.REDE_ETHERNET_SALVA] = INFO_FINDED; EventRegister.emit('saveEthernetMQTT', save); };

// evento indicando que as informações do módulo modbus foram alteradas
export const eventModbusMaster = (save: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_STATUS] = INFO_FINDED; EventRegister.emit('modbusMasterMQTT', save); };

// evento indicando que as informações de configuração do modbus foram alteradas
export const eventModbusConfig = (modbus: modbusSettings[]) => { eventRegister[InfoProbeMQTT.MODBUS_CONFIG] = INFO_FINDED; EventRegister.emit('modbusConfigMQTT', modbus); };

// evento indicando que as informações de configuração do modbus foram resetadas
export const eventModbusReset = (reset: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_RESET] = INFO_FINDED; EventRegister.emit('modbusResetMQTT', reset); };

// evento indicando que as informações de configuração do modbus foram reservadas
export const eventModbusAddBeginIni = (begin: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_ADD_INI] = INFO_FINDED; EventRegister.emit('modbusAddBeginIniMQTT', begin); };

// evento indicando que as informações de configuração do modbus do device foram configuradas
export const eventModbusAddDevice = (device: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_DEVICE] = INFO_FINDED; EventRegister.emit('modbusAddDeviceMQTT', device); };

// evento indicando que as informações de configuração do modbus do slave foram configuradas
export const eventModbusAddSlave = (slave: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_SLAVE] = INFO_FINDED; EventRegister.emit('modbudAddSlaveMQTT', slave); };

// evento indicando que as informações de configuração do modbus da variavel do slave foram configuradas
export const eventModbusAddVariable = (variable: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_VARIABLE] = INFO_FINDED; EventRegister.emit('modbusAddVariableMQTT', variable); };

// evento indicando que as informações de configuração do modbus da variavel do slave foram configuradas
export const eventModbusAddMean = (variable: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_MEAN] = INFO_FINDED; EventRegister.emit('modbusAddMeanMQTT', variable); };

// evento indicando que as informações de configuração do modbus foi finalizada
export const eventModbusAddBeginFim = (begin: boolean) => { eventRegister[InfoProbeMQTT.MODBUS_ADD_FIM] = INFO_FINDED; EventRegister.emit('modbusAddBeginFimMQTT', begin); };

// evento indicando as informações do slave modbus
export const eventModbusData = (data: string[]) => { EventRegister.emit('modbusDataMQTT', data); };

// evento indicando que as informações do módulo codi foram alteradas
export const eventCodiStatus = (save: boolean) => { eventRegister[InfoProbeMQTT.CODI_STATUS] = INFO_FINDED; EventRegister.emit('codiStatusMQTT', save); };

// evento indicando que as informações de configuração do codi foram alteradas
export const eventCodiConfig = (codi: codiSettings[]) => { eventRegister[InfoProbeMQTT.CODI_CONFIG] = INFO_FINDED; EventRegister.emit('codiConfigMQTT', codi); };

// evento indicando que as informações de configuração do codi foram alteradas
export const eventCodiEnd = (end: boolean) => { eventRegister[InfoProbeMQTT.CODI_END] = INFO_FINDED; EventRegister.emit('codiEndMQTT', end); };

// evento indicando que as informações de configuração do codi foram alteradas
export const eventAddCodi = (add: boolean) => { eventRegister[InfoProbeMQTT.CODI_ADD] = INFO_FINDED; EventRegister.emit('codiAddMQTT', add); };

// evento indicando que as informações de configuração do firmware foi alterada
export const eventOTARollback = (version: string) => { eventRegister[InfoProbeMQTT.OTA_ROLLBACK] = INFO_FINDED; EventRegister.emit('otaRollbackMQTT', version); };

// evento indicando que as informações de configuração de atualização firmware foi alterada
export const eventOTAUpdating = (update: string) => { EventRegister.emit('OTAupdatingMQTT', update); };

// evento indicando que as informações de configuração de atualização firmware foi alterada
export const eventOTAUpdate = (update: string) => { eventRegister[InfoProbeMQTT.OTA_UPDATE] = INFO_FINDED; EventRegister.emit('otaUpdateMQTT', update); };

// evento indicando que as informações de configuração do ion foi alterada
export const eventIonStatus = (status: boolean) => { eventRegister[InfoProbeMQTT.ION_STATUS] = INFO_FINDED; EventRegister.emit('ionStatusMQTT', status); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonConfig = (ion: ionSettings[]) => { eventRegister[InfoProbeMQTT.ION_CONFIG] = INFO_FINDED; EventRegister.emit('ionConfigMQTT', ion); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonEnd = (end: boolean) => { eventRegister[InfoProbeMQTT.ION_END] = INFO_FINDED; EventRegister.emit('ionEndMQTT', end); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonAdd = (add: boolean) => { eventRegister[InfoProbeMQTT.ION_ADD] = INFO_FINDED; EventRegister.emit('ionAddMQTT', add); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonAutoSearch = (search: boolean) => { eventRegister[InfoProbeMQTT.ION_SEARCH] = INFO_FINDED; EventRegister.emit('ionAutoSearchMQTT', search); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonAutoSearching = (search: string) => { EventRegister.emit('ionAutoSearchingMQTT', search); };

// evento indicando que as informações de configuração do ion foram alteradas
export const eventIonData = (data: string[]) => { EventRegister.emit('ionDataMQTT', data); };

// evento indicando que as informações de configuração do contador de pulsos foi alterada
export const eventPulsoStatus = (status: boolean) => { eventRegister[InfoProbeMQTT.PULSO_STATUS] = INFO_FINDED; EventRegister.emit('pulsoStatusMQTT', status); };

// evento indicando que as informações de configuração do contador de pulsos foram alteradas
export const eventPulsoConfig = (ion: pulsoSettings[]) => { eventRegister[InfoProbeMQTT.PULSO_CONFIG] = INFO_FINDED; EventRegister.emit('pulsoConfigMQTT', ion); };

// evento indicando que as informações de configuração do contador de pulsos foram resetadas
export const eventPulsoReset = (reset: boolean) => { eventRegister[InfoProbeMQTT.PULSO_RESET] = INFO_FINDED; EventRegister.emit('pulsoResetMQTT', reset); };

// evento indicando que as informações de configuração do contador de pulso foram alteradas
export const eventPulsoEnd = (end: boolean) => { eventRegister[InfoProbeMQTT.PULSO_END] = INFO_FINDED; EventRegister.emit('pulsoEndMQTT', end); };

// evento indicando que as informações de configuração do contador de pulso foram alteradas
export const eventPulsoAdd = (add: number) => { eventRegister[InfoProbeMQTT.PULSO_ADD] = INFO_FINDED; EventRegister.emit('pulsoAddMQTT', add); };

// evento indicando que as informações de configuração do khomp foram encontradas
export const eventKhomp = (payload: string) => { eventRegister[InfoProbeMQTT.KHOMP] = INFO_FINDED; EventRegister.emit('khompMQTT', payload);  };

// evento indicando que as informações de apn's foram foram encontradas
export const eventApnConfig = (apn: ApnSettings[]) => { eventRegister[InfoProbeMQTT.APN_CONFIG] = INFO_FINDED; EventRegister.emit('apnConfigMQTT', apn);  };

// evento indicando que as informações de apn's foram foram encontradas
export const eventApnAdd = (apn: boolean) => { eventRegister[InfoProbeMQTT.APN_ADD] = INFO_FINDED; EventRegister.emit('apnAddMQTT', apn);  };

// evento indicando que as informações de apn's foram foram encontradas
export const eventApnRemove = (apn: boolean) => { eventRegister[InfoProbeMQTT.APN_REMOVE] = INFO_FINDED; EventRegister.emit('apnRemoveMQTT', apn);  };



// verifica se a informação esta liberada para ser encontrada
export function InfoReleased(indice: number) {

    // se indice maior que a quantidade de itens
    if (indice > eventRegister.length) {
        return false;
    }

    // se informação parada ou encontrada
    if ((eventRegister[indice] === INFO_IDLE) || (eventRegister[indice] === INFO_FINDED))
        return false;
    else
        return true;
}

// pega a mensagem
async function getMensagem(mensagem: string, topico: string) {

    try {

        // pega a mensagem e armazena em ordem descrescente
        getStorageString(KEY.historicoDebugDesc).then((value) => {
            setStorageString(KEY.historicoDebugDesc, '\n' + formatJson(mensagem, topico) + '\n' + value);
        });

        // pega a mensagem e armazena em ordem crescente
        getStorageString(KEY.historicoDebugAsc).then((value) => {
            setStorageString(KEY.historicoDebugAsc, value + '\n' + formatJson(mensagem, topico) + '\n');
        });
    }
    catch (error) { }

    eventMessage(mensagem)
}

// retorna o firmware da probe
export async function getFirmware(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.FIRMWARE))
        return;

    // se não encontrou informação
    if (payload.indexOf("version") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.version)
        eventFirmware(response.version);
}

// retorna a data e hora da probe
export async function getDataHora(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.DATAHORA))
        return;

    // se não encontrou informação 
    if (payload.indexOf("time") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.time)
        eventDateTime(response.time);
}

// retorna a data de instalação do firmware na Probe
export async function getFirmwareBoot(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.FIRMWARE_BOOT))
        return;

    // se não encontrou info
    if (payload.indexOf("firmware_boot") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.firmware_boot)
        eventFirmwareBoot(response.firmware_boot);
}

// retorna o total de resets da Probe
export async function getResets(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.RESETS))
        return;

    // se não encontrou info
    if (payload.indexOf("sys_resets") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.sys_resets)
        eventResets(response.sys_resets);
}

// retorna o total de exceções (bug) que ocasionaram resets da probe
export async function getExcecoes(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.EXCECOES))
        return;

    // se não encontrou info
    if (payload.indexOf("sys_exceptions") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    eventExceptions(response.sys_exceptions);
}

// retorna o total de memória heap livre
export async function getHeap(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.HEAP))
        return;

    // se não encontrou info
    if (payload.indexOf("sys_heap") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.sys_heap)
        eventHeap(response.sys_heap);
}

// retorna o nivel da bateria
export async function getNivelBateria(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.NIVEL_BATERIA))
        return;

    // se não encontrou informação
    if (payload.indexOf("pbatt") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload);

    eventBattery(response.pbatt);
}

// retorna a tensao da bateria
export async function getTensaoBateria(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.TENSAO_BATERIA))
        return;

    // se não encontrou informação
    if (payload.indexOf("vbatt") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)
    eventVBattery(response.vbatt);
}

// retorna o status de tensão da probe
export async function getStatusTensaoProbe(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.STATUS_TENSAO))
        return;

    // verifica se existe tensão da bateria
    if (payload.indexOf("voltalarm") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    // se voltalarm ativo
    if (response.voltalarm) {
        eventStatusVProbe(0); // conectada pela bateria
    }
    else {
        eventStatusVProbe(1); // conectada pela alimentação 
    }
}

// retorna o nivel de sinal do wi-fi ou gsm
export async function getSinal(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.SINAL))
        return;

    // se encontrar sinal wi-fi
    if (payload.indexOf("rssi") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        if (response.rssi)
            eventSignal(response.rssi);
    }

    // se encontrar sinal gsm
    if (payload.indexOf("siglvl") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        if (response.siglvl)
            eventSignal(response.siglvl);
    }
}

// retorna o tipo de conexão da probe
export async function getTipoConexaoProbe(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.TIPO_CONEXAO))
        return;

    // se usa wifi
    if (payload.indexOf("wifi") !== -1) {

        // conteudo da resposta
        const response = JSON.parse(payload);

        if (response.rede.wifi.enabled === true) {
            eventTypeConnection(0);
            return;
        }
    }

    // se usa o gsm
    if ((payload.indexOf("gsm") !== -1)) {

        // conteudo da resposta
        const response = JSON.parse(payload);

        // se for gsm
        if (response.rede.gsm.enabled === true) {
            eventTypeConnection(1);
            return;
        }
    }

    // se usa ethernet
    if ((payload.indexOf("eth") !== -1)) {

        // conteudo da resposta
        const response = JSON.parse(payload);

        // se for eth
        if (response.rede.eth.enabled === true) {
            eventTypeConnection(2);
            return;
        }
    }
}

// retorna o tipo de operadora
export async function getTipoOperadora(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.TIPO_OPERADORA))
        return;

    // conteudo da resposta
    const response = JSON.parse(payload);

    // operadora default
    let operadora: number;

    // se for gsm
    if (response.rede?.gsm?.enabled === true) {

        // verifica o tipo de operador
        switch (response.rede.gsm.oper) {
            case 'Vivo': operadora = 1; break;
            case 'Tim': operadora = 2; break;
            case 'Claro': operadora = 3; break;
            case 'Oi': operadora = 4; break;
            case 'Nextel': operadora = 5; break;
            case 'Algar': operadora = 6; break;
            case 'Sercomtel': operadora = 7; break;
            default: operadora = 0; break;
        }

        // se for narrow band
        if (response.rede.gsm.rede === 'NB')
            eventOperadora(operadora + 90);
        else
            eventOperadora(operadora);
        return;
    }

    // se for eth
    if (response.rede?.eth?.enabled === true) {
        eventOperadora(0);
        return;
    }

    // se for wifi
    if (response.rede?.wifi?.enabled === true) {
        eventOperadora(0);
    }
}

// retorna se sistema habilitado
export async function getSistema(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.SISTEMA))
        return;

    // se não encontrou informação
    if (payload.indexOf("system") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.system === true)
        eventSistema(true);
    else
        eventSistema(false);
}

// retonar se rede habilitado
export async function getRede(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE))
        return;

    // se não encontrou informação
    if (payload.indexOf("rede") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.rede === true)
        eventRede(true);
    else
        eventRede(false);
}

// retorna se modbus habilitado
export async function getModbus(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS))
        return;

    // se não encontrou informação
    if (payload.indexOf("modbus_master") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.modbus_master === true)
        eventModbus(true);
    else
        eventModbus(false);
}

// retornar se codi habilitado
export async function getCODI(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.CODI))
        return;

    // se não encontrou informação
    if (payload.indexOf("codi") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.codi === true)
        eventCodi(true);
    else
        eventCodi(false);
}

// retornar se ion habilitado
export async function getION(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION))
        return;

    // se não encontrou informação
    if (payload.indexOf("ion") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.ion === true)
        eventION(true);
    else
        eventION(false);
}

// retornar se constado de pulsos habilitado
export async function getPulso(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO))
        return;

    // se não encontrou informação
    if (payload.indexOf("pulse") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.modules.pulse === true)
        eventPulso(true);
    else
        eventPulso(false);
}

// retorna se ota habilitado
export async function getOTA(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.OTA))
        return;

    // se não encontrou informação
    if (payload.indexOf("ota") === -1)
        return;

    eventOTA(true);
}

// retonar ip da probe
export async function getIP(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.IP))
        return;


    if (payload.indexOf("wifi") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        // ip conexao wifi
        if (response.rede.wifi.enabled === true) {
            eventIP(response.rede.wifi.ip);
            return;
        }
    }

    if (payload.indexOf("eth") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        // ip conexao eth
        if (response.rede.eth.enabled === true) {
            eventIP(response.rede.eth.ip_use);
            return;
        }
    }

    if (payload.indexOf("gsm") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        // ip conexao gsm
        if (response.rede.gsm.enabled === true) {
            eventIP(response.rede.gsm.ip);
            return;
        }
    }
}

// retorna o total de memória flash disponivel
export async function getTotalMemory(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MEMORIA_TOTAL))
        return;

    // se não encontrou info
    if (payload.indexOf("total_memory") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.sys.info.total_memory)
        eventTotalMemory(response.sys.info.total_memory);
}

// retorna o total de memória flash utilizada
export async function getUsedMemory(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MEMORIA_USADA))
        return;

    // se não encontrou info
    if (payload.indexOf("used_memory") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.sys.info.used_memory)
        eventUsedMemory(response.sys.info.used_memory);
}

// retorna se a probe foi reconectada
export async function getReconectada(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.RECONECTADO))
        return;

    // se não encontrou info
    if (payload.indexOf("reconn") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if ((response.reconn === 'Ethernet') || (response.reconn === 'WiFi') || (response.reconn === 'GSM') || (response.reconn === 'NB-IoT'))
        eventReconnect(true);
    else
        eventReconnect(false);
}

// retorna se a probe foi formatado
export async function getFormatado(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.FORMATADO))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventFormat(true);
    else
        eventFormat(false);
}

// retorna se o total de tarefas executadas apos o escaneamento
export async function getModulosEscaneados(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODULO_SCAN))
        return;

    // se não encontrou info
    if (payload.indexOf("module_tasks_running") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.module_tasks_running)
        eventModuleScan(response.module_tasks_running);
}

// retorna se as mensagens da probe foram formatadas
export async function getMensagensFormatado(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MSG_FORMATADA))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventFormatMessage(true);
    else
        eventFormatMessage(false);
}

// retorna se os arquivos da probe foram listados
export async function getListarArquivos(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.LISTA_ARQUIVOS))
        return;

    let filesProbeArray: FilesProbe[] = [];

    // se não existem arquivos
    if (payload.indexOf("action_success\":false") !== -1) {
        eventListFiles(filesProbeArray);
        return;
    }

    // se não encontrou info
    if (payload.indexOf("sysfiles") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    // percorre json para encontrar os arquivos
    for (const key in response.sysfiles.files) {

        if (response.sysfiles.files.hasOwnProperty(key) && key !== "Final") {
            const file = response.sysfiles.files[key];
            const fileProbe: FilesProbe = {
                id: parseInt(key),
                name: file.name,
                size: file.size.trim(),
                timer: file.time
            };

            filesProbeArray.push(fileProbe);
        }
    }

    // arquivos encontrados
    eventListFiles(filesProbeArray);
}

// retorna se o arquivo da probe foi formatado
export async function getFormataArquivo(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.FORMATA_ARQUIVO))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventFormatFile(true);
    else
        eventFormatFile(false);
}

// retorna se foi encontrado as informações da rede ethernet
export async function getRedeEthernet(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE_ETHERNET))
        return;

    // se não encontrou info
    if (payload.indexOf("eth") === -1)
        return;

    // inicializa
    let _probe: ProbeSettings = ProbeSettingsInicial();

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.rede.eth.ip_use)
        _probe.ethernetIP = response.rede.eth.ip_use;

    if (response.rede.eth.msk_use)
        _probe.ethernetMascara = response.rede.eth.msk_use;

    if (response.rede.eth.gw_use)
        _probe.ethernetGateway = response.rede.eth.gw_use;

    if (response.rede.eth.dns_use)
        _probe.ethernetDNS = response.rede.eth.dns_use;

    if (response.rede.eth.mac)
        _probe.ethernetMAC = response.rede.eth.mac;

    if ((response.rede.eth.ip_use === '0.0.0.0') && (response.rede.eth.ip === '0.0.0.0'))
        _probe.ethernetDHCP = 1; // dhcp
    else if (response.rede.eth.ip_use === response.rede.eth.ip)
        _probe.ethernetDHCP = 0; // ip fixo
    else
        _probe.ethernetDHCP = 1; // dhcp

    eventEthernet(_probe);
}

// retorna se foi encontrado as informações da rede wifi
export async function getRedeWifi(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE_WIFI))
        return;

    // se não encontrou info
    if (payload.indexOf("wifi") === -1)
        return;

    // inicializa
    let _probe: ProbeSettings = ProbeSettingsInicial();

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.rede.wifi.ssid)
        _probe.wifiNome = response.rede.wifi.ssid;

    if (response.rede.wifi.pass)
        _probe.wifiSenha = response.rede.wifi.pass;

    if (response.rede.wifi.rssi)
        _probe.wifiSinal = response.rede.wifi.rssi;

    if (response.rede.wifi.ip)
        _probe.wifiIP = response.rede.wifi.ip;

    if (response.rede.wifi.msk)
        _probe.wifiMascara = response.rede.wifi.msk;

    if (response.rede.wifi.gw)
        _probe.wifiGateway = response.rede.wifi.gw;

    if (response.rede.wifi.dns)
        _probe.wifiDNS = response.rede.wifi.dns;

    if (response.rede.wifi.mac)
        _probe.wifiMAC = response.rede.wifi.mac;

    eventWifi(_probe);
}

// retorna se foi encontrado as informações da rede gsm
export async function getRedeGSM(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE_GSM))
        return;

    // se não encontrou info
    if (payload.indexOf("gsm") === -1)
        return;

    // inicializa
    let _probe: ProbeSettings = ProbeSettingsInicial();

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.rede.gsm.rede)
        _probe.gsmRede = response.rede.gsm.rede;

    if (response.rede.gsm.modem)
        _probe.gsmModem = response.rede.gsm.modem;

    if (response.rede.gsm.imei)
        _probe.gsmIMEI = response.rede.gsm.imei;

    if (response.rede.gsm.imsi)
        _probe.gsmIMSI = response.rede.gsm.imsi;

    if (response.rede.gsm.iccid)
        _probe.gsmICCID = response.rede.gsm.iccid;

    if (response.rede.gsm.rssi)
        _probe.gsmSinal = response.rede.gsm.rssi;

    if (response.rede.gsm.oper)
        _probe.gsmOperadora = response.rede.gsm.oper;

    if (response.rede.gsm.apn)
        _probe.gsmAPN = response.rede.gsm.apn;

    if (response.rede.gsm.ip)
        _probe.gsmIP = response.rede.gsm.ip;

    if (response.rede.gsm.msk)
        _probe.gsmMascara = response.rede.gsm.msk;

    if (response.rede.gsm.gw)
        _probe.gsmGateway = response.rede.gsm.gw;

    if (response.rede.gsm.lat)
        _probe.gsmLatitude = response.rede.gsm.lat;

    if (response.rede.gsm.lng)
        _probe.gsmLongitude = response.rede.gsm.lng;

    eventGSM(_probe);
}

// retorna se a configuração wifi foi salva
export async function getSalvaWifi(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE_WIFI_SALVA))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventSaveWifi(true);
    else
        eventSaveWifi(false);
}

// retorna se a configuração ethernet foi salva
export async function getSalvaEthernet(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.REDE_ETHERNET_SALVA))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventSaveEthernet(true);
    else
        eventSaveEthernet(false);
}

// retorna se o módulo modbus foi ativado ou não
export async function getModbusMaster(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_STATUS))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusMaster(true);
    else
        eventModbusMaster(false);
}

// pega as informações de devices do modbus
function getModbusConfig_Devices(payload: string[], modbus_devices: modbusSettings[], total_devices: number): modbusSettings[] {

    let modbusDevices: modbusSettings[] = modbus_devices;

    // percorre os devices para pegar suas informações
    for (let idDevice = 0; idDevice < total_devices; idDevice++) {

        // monta string auxiliar
        const aux = `{"modbus":{"device":{"${idDevice}":`;

        // pega o indice onde se encontra a informação do device
        const indice = payload.findIndex(item => item.includes(aux));

        // se indice invalido não continua
        if (indice < 0)
            continue;

        // conteudo da resposta
        const response = JSON.parse(payload[indice]);

        // atualiza estrutura de configuração da probe
        const deviceAtualizado = {
            ...{ ...modbusDevices[idDevice] },
            id_device: idDevice,
            nameDevice: response.modbus.device[idDevice].dev_name,
            endianessDevice: response.modbus.device[idDevice].endianness,
        };

        // atualiza com as configurações do device
        modbusDevices.splice(idDevice, 1, deviceAtualizado);
    }

    return modbusDevices;
}

/**
 * pega as informações de slaves do modbus
 * @param payload 
 * @param modbus_devices 
 * @param total_devices 
 * @param total_slaves 
 * @returns 
 */
function getModbusConfig_Slaves(payload: string[], modbus_devices: modbusSettings[], total_devices: number, total_slaves: number ) : modbusSettings[] {
    
    let payload_atual : string[] =  payload;
    let modbusDevices: modbusSettings[] = modbus_devices;
    let idSlave : number = 0;
    let indice : number = 0

    /* percorre todos os slaves possíveis para pegar as informações */
    while (idSlave < total_slaves) {
     
        /* monta string auxiliar para comparação */ 
        const aux = `{"modbus":{"slave":{"${idSlave}":`;        

        /* pega o indice onde se encontra a informação do slave */
        indice = payload_atual.findIndex(item => item.includes(aux));

        /* se indice invalido retorna para continuar o próximo slave */
        if(indice < 0) {
            idSlave++;
            continue;
        }
            
        const response = JSON.parse(payload_atual[indice]);

        /* verifica se existe determinada chave */
        if(Object.keys(response.modbus.slave[idSlave]).length < 8) {

            /* remove o payload que não possui a informação do dev_name na posição especificada */
            payload_atual.splice(indice, 1); 
            continue;
        }

        /* percorre todos os devices possíveis */
        for(let idDevice = 0; idDevice < total_devices; idDevice++) {

            /* verifica se o nome do device é igual ao contido no slave */
            if(modbusDevices[idDevice].nameDevice === response.modbus.slave[idSlave].dev_name ) {

                /* adiciona um novo slave ao device */
                modbusDevices[idDevice].slaves?.push(SlavesSettingsInicial()[0]);

                /* pega o indice para armazenar o novo id slave */
                const indiceSlave = (modbusDevices[idDevice].slaves?.length || 1) - 1;

                /* atualiza estrutura do slave a ser adicionado */
                const slaveAtualizado = { 
                                            ...{...modbusDevices[idDevice]?.slaves?.[indiceSlave]},
                                            id_slave: indiceSlave,
                                            statusSlave: false,
                                            nomeSlave: response.modbus.slave[idSlave].slave_name,
                                            enderecoSlave: response.modbus.slave[idSlave].slave.toString(),  
                                            tempoEnvioSlave: getIndexTempoEnvio(response.modbus.slave[idSlave].slave_name),
                                            protocoloSlave: response.modbus.slave[idSlave].protocol,
                                            paridadeSlave: getIndexParidade(response.modbus.slave[idSlave].uart ?? -1),
                                            baudeRateSlave: getIndexBaudRate(response.modbus.slave[idSlave].baud ?? -1), 
                                            conexaoSlave: Number(response.modbus.slave[idSlave].conn ?? -1),
                                            portSlave: response.modbus.slave[idSlave].port ?? 0,
                                            ipSlave: response.modbus.slave[idSlave].slave_ip ?? '',
                                        };
   
                /* atualiza com as configurações do device */
                modbusDevices[idDevice].slaves?.splice(indiceSlave, 1, slaveAtualizado);
            }                       
        }
    
        idSlave++;
    }
    
    return modbusDevices;    
}

// pega as informações das variables do modbus
function getModbusConfig_Variables(payload: string[], modbus_devices: modbusSettings[], total_devices: number,): modbusSettings[] {

    let modbusDevices: modbusSettings[] = modbus_devices;

    // percorre os devices
    for (let idDevice = 0; idDevice < total_devices; idDevice++) { // 14

        // percorre as variaveis para pegar as informações
        for (let idVariable = 0; idVariable < 30; idVariable++) {

            // monta string auxiliar
            const aux = `{"modbus":{"variable":{"${idVariable}":`;

            // pega o indice onde se encontra a informação do slave
            const indice = payload.findIndex(item => item.includes(aux));

            // se indice invalido não continua
            if (indice < 0)
                continue;

            // conteudo da resposta
            const response = JSON.parse(payload[indice]);

            // verifica se o nome do device é igual ao da variavel
            if (modbusDevices[idDevice].nameDevice === response.modbus.variable[idVariable].dev_name) {

                // atualiza estrutura da variavel
                const variableAtualizado = {
                    ...{ ...modbusDevices[idDevice].slaves?.[0].variables?.[idVariable] },
                    nomeVariable: response.modbus.variable[idVariable].var_name,
                    descricaoVariable: response.modbus.variable[idVariable].var_name,
                    valorMediaVariable: response.modbus.variable[idVariable].mean,
                    valorUltimaVariable: response.modbus.variable[idVariable].send,
                    funcaoVariable: response.modbus.variable[idVariable].function,
                    fatorVariable: Number(response.modbus.variable[idVariable].factor),
                    enderecoVariable: response.modbus.variable[idVariable].address,
                    formatoVariable: response.modbus.variable[idVariable].data_type,
                };

                // atualiza as variáveis dos slaves do dispositivo
                modbusDevices[idDevice].slaves?.forEach(slave => {
                    slave.variables?.splice(idVariable, 1, variableAtualizado);
                });
            }
        }
    }

    return modbusDevices;
}

// retorna se a configuração modbus
export async function getModbusConfig(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_CONFIG))
        return;

    // acrescenta o payload a lista
    modbusPayload.push(payload);

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // estrutura modbus
    let modbusDevices: modbusSettings[] = [];

    // totalizadores
    let totalDevices = 0;
    let totalSlaves = 0;

    // procura os payloads coletados em busca do total de devices e slaves
    for (let i = 0; i < modbusPayload.length; i++) {

        // se info incluida
        if (modbusPayload[i].includes('{"config":')) {

            // conteudo da resposta
            const response = JSON.parse(modbusPayload[i]);

            // pega o total de devices
            totalDevices = response.config.device;

            // pega o total de slaves
            totalSlaves = response.config.slaves;

            // finaliza o loop
            break;
        }
    }

    // inicializa as configurações modbus
    modbusDevices = Array.from({ length: totalDevices }, () => DeviceSettingsInicial()[0]);

    // pega os devices
    modbusDevices = getModbusConfig_Devices(modbusPayload, [...modbusDevices], totalDevices);

    // pega os slaves
    modbusDevices = getModbusConfig_Slaves(modbusPayload, [...modbusDevices], totalDevices, totalSlaves);

    // pega as variables
    modbusDevices = getModbusConfig_Variables(modbusPayload, [...modbusDevices], totalDevices);

    // finaliza 
    eventModbusConfig(modbusDevices);
    
    // reinicia
    modbusPayload = [];
}

// retorna se a configuração modbus foi limpa
export async function getModbusReset(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_RESET))
        return;

    // se não encontrou info
    //if( payload.indexOf("modbus_reset:success") === -1)
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusReset(true);
    else
        eventModbusReset(false);
}

// retorna se a configuração modbus inicial foi feita
export async function getModbusAddBeginIni(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_ADD_INI))
        return;

    // se não encontrou info    
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddBeginIni(true);
    else
        eventModbusAddBeginIni(false);
}

// retorna se a configuração modbus final foi feita
export async function getModbusAddBeginFim(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_ADD_FIM))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddBeginFim(true);
    else
        eventModbusAddBeginFim(false);
}

// retorna se a configuração modbus do device foi configurado
export async function getModbusAddDevice(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_DEVICE))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddDevice(true);
    else
        eventModbusAddDevice(false);
}

// retorna se a configuração modbus do slave do device foi configurado
export async function getModbusAddSlave(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_SLAVE))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddSlave(true);
    else
        eventModbusAddSlave(false);
}

// retorna se a configuração modbus da variavel do slave do device foi configurado
export async function getModbusAddVariable(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_VARIABLE))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddVariable(true);
    else
        eventModbusAddVariable(false);
}

// retorna se a configuração modbus da variavel media do slave do device foi configurado
export async function getModbusAddMean(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_MEAN))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventModbusAddMean(true);
    else
        eventModbusAddMean(false);
}

// retorna as informações do slave modbus
export async function getModbusData(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.MODBUS_DATA))
        return;

    // se não encontrou info
    if ((payload.indexOf("action_success") === -1) && (payload.indexOf("variable") === -1)) {

        // acrescenta o payload a lista
        modbusPayload.push(payload);

        eventModbusData(modbusPayload);
    }
}

// retorna se o módulo codi foi ativado ou não
export async function getCodiStatus(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.CODI_STATUS))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventCodiStatus(true);
    else
        eventCodiStatus(false);
}

// retorna a configuração do codi
export async function getCodiConfig(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.CODI_CONFIG))
        return;

    // se não encontrou info
    if (payload.indexOf('"codi":') === -1)
        return;

    // estrutura codi
    let codiDevices: codiSettings[] = [];

    // conteudo da resposta
    const response = JSON.parse(payload)

    // percorre as portas disponiveis
    for (let porta = 0; porta < 4; porta++) {

        // monta o texto de identificação da porta
        let auxiliar = `"${porta + 1}":`;

        // verifica se a porta está contida no payload
        if (payload.indexOf(auxiliar) !== -1) {

            try {

                // monta um novo codi para ser inserido
                let codiDevice: codiSettings = {
                    id_codi: Number(response.codi[porta + 1].port),
                    codi: TiposCODI[porta],
                    replicar: response.codi[porta + 1].replicate,
                    invertido: response.codi[porta + 1].reverse,
                    protocolo: ListaProtocoloCodi485.find(x => x.descricao === response.codi[porta + 1].protocolo).id,
                    paridade: ListaParidadeProbe.find(x => x.descricao === response.codi[porta + 1].conf).id,
                    baudRate: ListaBaudRateProbe.find(x => x.descricao == response.codi[porta + 1].baud).id,
                    repo: response.codi[porta + 1].repo
                }


                // adiciona um novo codi
                codiDevices.push(codiDevice);
            }
            catch (error) {
                // não faz nada
            }

        }
    }

    // configuração codi
    eventCodiConfig(codiDevices);
}

// retorna se as configurações codi foram resetadas
export async function getCodiEnd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.CODI_END))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success) {

        eventCodiEnd(true);
    }
    // evita erro se mandar apagar codi que não existe
    else if ((response.action_log === 'Erro: nao existe nenhuma codi nessa porta') || (response.action_log === 'Erro: nao existe codi nessa porta'))
        eventCodiEnd(true);
    else
        eventCodiEnd(false);
}

// retorna se as configurações codi foram adicionadas
export async function getCodiAdd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.CODI_ADD))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventAddCodi(true);
    else
        eventAddCodi(false);
}

// retorna se as configurações de firmware fora alteradas
export async function getSystemOTARollback(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.OTA_ROLLBACK))
        return;

    // se não encontrou info
    if (payload.indexOf("version") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.version)
        eventOTARollback(response.version);
}

// retorna se as configurações de atualização de firmware fora alteradas
export async function getSystemOTAUpdate(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.OTA_UPDATE))
        return;

    // se encontrou info pela modo estavel
    if (payload.indexOf("ota_status") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        if (payload.indexOf("Sucesso: Ota finalizado") === -1)
            eventOTAUpdating(response.ota_status);
        else
            eventOTAUpdate(response.ota_status);
    }

    // se encontrou info pela modo ultra estavel
    if (payload.indexOf("version") !== -1) {
        // conteudo da resposta
        const response = JSON.parse(payload)

        eventOTAUpdate(response.version);
    }
}

// retorna se as configurações de ion fora alteradas
export async function getIonStatus(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_STATUS))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventIonStatus(true);
    else
        eventIonStatus(false);
}

// retorna a configuração do ion
export async function getIonConfig(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_CONFIG))
        return;

    // se não encontrou info
    if ((payload.indexOf('{"ion":{"codi_fix_repo"') === -1) && (payload.indexOf('{"ion":{"ion_fix_repo"') === -1))
        return;

    // estrutura devices
    let ionDevices: ionSettings[] = [];

    // conteudo da resposta
    const response = JSON.parse(payload)

    // pega a lista de ids
    const lista_ids = Object.keys(response.ion);

    // percorre as portas disponiveis
    for (let id = 0; id < lista_ids.length; id++) {

        // verifica se eh numero
        if (!isNumber(lista_ids[id]))
            continue;

        // verifica se a porta está contida no payload
        if (payload.indexOf(lista_ids[id]) !== -1) {

            try {
                // adiciona um novo ion
                ionDevices.push(AddIon(Number(response.ion[lista_ids[id]].slave_id),
                    TiposComunicacaoION[response.ion[lista_ids[id]].port],
                    response.ion[lista_ids[id]].port,
                    ListaParidadeProbe.find(x => x.descricao === response.ion[lista_ids[id]].uartconf).id,
                    ListaBaudRateProbe.find(x => x.descricao == response.ion[lista_ids[id]].baud).id
                ));
            }
            catch (error) {
                // não faz nada
            }

        }
    }

    // configuração ion
    eventIonConfig(ionDevices);
}

// retorna se as configurações ion foram resetadas
export async function getIonEnd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_END))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success) {
        eventIonEnd(true);
    }
    else {

        // evita erro se mandar apagar codi que não existe
        if (response.action_log === 'Erro: nao existe nenhum ION nessa porta') {
            eventIonEnd(true);
        }
        else {
            eventIonEnd(false);
        }
    }
}

// retorna se as configurações ion foram adicionadas
export async function getIonAdd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_ADD))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventIonAdd(true);
    else
        eventIonAdd(false);
}

// retorna se encontrou algum ion na rede
export async function getIonAutoSearch(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_SEARCH))
        return;

    // se não encontrou info     
    if (payload.indexOf("ion_status") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.ion_status === 'Comando cancelado')
        eventIonAutoSearch(false);
    else
        eventIonAutoSearching(response.ion_status)
}

// retorna se encontrou algum dado coletado do ion na rede
export async function getIonData(payload: string) {
    
    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.ION_DATA))
        return;

    // acrescenta o payload a lista
    modbusPayload.push(payload);

    eventIonData(modbusPayload);
}

// retorna se as configurações do contador de pulsos fora alteradas
export async function getPulsoStatus(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO_STATUS))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventPulsoStatus(true);
    else
        eventPulsoStatus(false);
}

// retorna a configuração do contador de pulso
export async function getPulsoConfig(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO_CONFIG))
        return;

    // se não encontrou info
    if (payload.indexOf('{\"pulse\":') === -1)
        return;

    // estrutura devices
    let pulsoDevices: pulsoSettings[] = [];

    // conteudo da resposta
    const response = JSON.parse(payload)

    // percorre as portas disponiveis
    for (let porta = 0; porta < 2; porta++) {

        // monta o texto de identificação da porta
        let auxiliar = `"${porta + 1}":`;

        // verifica se a porta está contida no payload
        if (payload.indexOf(auxiliar) !== -1) {

            try {

                // adiciona um novo contador de pulso
                pulsoDevices.push(AddPulso(ListaTiposPulso[porta].descricao,
                    Number(response.pulse[porta + 1].port),
                    response.pulse[porta + 1].type,
                    (response.pulse[porta + 1].type === TipoMedicaoPulso.CONTATO) ? response.pulse[porta + 1].contact : -1,
                    response.pulse[porta + 1].reactive,
                    response.pulse[porta + 1].repo,
                    (response.pulse[porta + 1].repo) ? response.pulse[porta + 1].send_time_repo : 60,
                    parseFloat(response.pulse[porta + 1].factor),
                    Number(Object.keys(TempoSegundosEnvioProbe).find(key => TempoSegundosEnvioProbe[key] === response.pulse[porta + 1].send_time)),
                ));
            }
            catch (error) {
                // não faz nada
            }

        }
    }

    // configuração do contador de pulsos
    eventPulsoConfig(pulsoDevices);
}

// retorna se as configurações contador de pulso foram excluidas
export async function getPulsoEnd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO_END))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventPulsoEnd(true);
    else
        eventPulsoEnd(false);
}

// retorna se as configurações contador de pulso foram adicionadas
export async function getPulsoAdd(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO_ADD))
        return;

    // se não encontrou info
    if (payload.indexOf("action_log") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    switch (response.action_log) {
        case 'Sucesso: portas adicionadas':
        case 'Sucesso: porta adicionada':
            eventPulsoAdd(0); 
            break;
        case 'Sucesso: porta ativa e reativa adicionadas': 
            eventPulsoAdd(1); 
            break;
        case 'Porta nao suportada': 
            eventPulsoAdd(2); 
            break;
        case 'Porta nao pode ser inicializada': 
            eventPulsoAdd(3); 
            break;
        default: 
            eventPulsoAdd(4);
            break;
    }
}

// retorna se a configuração do contador de pulsos foi limpa
export async function getPulsoReset(payload: string) {

    // se não liberado
    if (!InfoReleased(InfoProbeMQTT.PULSO_RESET))
        return;

    // se não encontrou info
    if (payload.indexOf("action_success") === -1)
        return;

    // conteudo da resposta
    const response = JSON.parse(payload)

    if (response.action_success)
        eventPulsoReset(true);
    else
        eventPulsoReset(false);
}

/**
 * retorna as informações do khomp quando encontradas
 * @param payload 
 * @returns 
 */
export async function getKhomp(payload: string) {

    /* se não liberado verificar informações */
    if (!InfoReleased(InfoProbeMQTT.KHOMP))
        return;

    /* encontrou info */
    if(payload.includes("F803") || payload.includes("f803")) {

        eventKhomp(payload);        
    }        
}

/**
 * retorna as informações do get_apns foram quando encontradas
 * @param payload 
 * @returns 
 */
export async function getApnConfig(payload: string) {

    /* se não liberado verificar informações */
    if (!InfoReleased(InfoProbeMQTT.APN_CONFIG))
        return;

    // estrutura devices
    let apns: ApnSettings[] = [];

    /* se encontrou não encontrou apns */
    if(payload.indexOf("No APN found") !== -1) {

        /* retorna a lista de apns vazia */ 
        eventApnConfig([]);        
    }

    /* se encontrou algo na lista */
    if(payload.indexOf("apn_list") !== -1) {

        /* conteudo da resposta */
        const response = JSON.parse(payload);

        /* dividir as entradas principais com base na vírgula fora dos parênteses */        
        const entries = parseStringApn(response.apn_list);

        // processar cada entrada
        entries.forEach((entry: string) => {

            // dividi o texto com base nos parênteses
            const partes = entry.split(/[()]/);

            // extrair os textos
            const apn = partes[0];
            const user = partes[1].split(",")[0].trim();
            const pass = partes[1].split(",")[1].trim();

            // adiciona à lista
            apns.push({apn: apn, user: user, pass: pass});

        });

        /* retorna a lista de apns encontradas */       
        eventApnConfig(apns);        
    }
}

/**
 * retorna as informações do add_apn foram quando encontradas
 * @param payload 
 * @returns 
 */
export async function getApnAdd(payload: string) {

    /* se não liberado verificar informações */
    if (!InfoReleased(InfoProbeMQTT.APN_ADD))
        return;

    /* se não encontrou info final */
    if(payload.indexOf("action_success") === -1) 
        return;

    /* conteudo da resposta */
    const response = JSON.parse(payload)

    eventApnAdd(response.action_success);
}

/**
 * retorna as informações do remove_apn foram quando encontradas
 * @param payload 
 * @returns 
 */
export async function getApnRemove(payload: string) {

    /* se não liberado verificar informações */
    if (!InfoReleased(InfoProbeMQTT.APN_REMOVE))
        return;

    /* se não encontrou info final */
    if(payload.indexOf("action_success") === -1) 
        return;

    /* conteudo da resposta */
    const response = JSON.parse(payload)

    eventApnRemove(response.action_success);
}



/**
 * conectar o cliente ao servidr MQTT
 */
const connectMQTT = (brokerHmg: boolean = false, status: number = INFO_IDLE) => { 
   
    // não libera buscar informações
    infoReleasedAllMQTT(status);

    // limpa historico de debug
    setStorageString(KEY.historicoDebugDesc, `\n` + DataCompleta(new Date()) + '\n' + 'Conectou MQTT' + `\n`);
    setStorageString(KEY.historicoDebugAsc, DataCompleta(new Date()) + '\n' + `Conectou MQTT` + `\n`);

    // cria o cliente mqtt
    MQTT.createClient({
        uri: (brokerHmg) ? BrokerHmg.URI : BrokerProd.URI,
        auth: (brokerHmg) ? BrokerHmg.AUTH : BrokerProd.AUTH,
        user: (brokerHmg) ? BrokerHmg.USER : BrokerProd.USER,
        pass: (brokerHmg) ? BrokerHmg.PASS : BrokerProd.PASS,
        clientId: (brokerHmg) ? BrokerHmg.CLIENTE_ID : BrokerProd.CLIENTE_ID,
        keepalive: 120,
        automaticReconnect: true,

    }).then(function (client) {

        // recebe o cliente criado
        mqttClient = client;

        client.on('closed', function () {

            // liberar quando quiser fazer o debug das mensagens // console.log('mqtt closed')

            isConnected = false;            
            eventIsConnected(false);
        });

        client.on('error', function (msg) {

            // liberar quando quiser fazer o debug das mensagens // console.log('mqtt error', msg)

            isConnected = false;
            eventIsConnected(false);
        });

        client.on('message', async function (msg) {

            // liberar quando quiser fazer o debug das mensagens // console.log('mqtt message', msg);

            // pega a mensagem
            getMensagem(msg.data, msg.topic);

            isConnected = true;
            eventIsConnected(true);

            try {

                // dbgack
                getFirmware(msg.data);
                getDataHora(msg.data);
                getFirmwareBoot(msg.data);
                getResets(msg.data);
                getExcecoes(msg.data);
                getHeap(msg.data);
                getNivelBateria(msg.data);
                getTensaoBateria(msg.data);
                getStatusTensaoProbe(msg.data);
                getSinal(msg.data);

                // verifica se todas as informações dbgack encontradas
                VerifyInfoReleseadDbgack();

                // --------------------------------------------------------------

                // sys_config all

                getTipoOperadora(msg.data);
                getTipoConexaoProbe(msg.data);
                getSistema(msg.data);
                getRede(msg.data);
                getModbus(msg.data);
                getCODI(msg.data);
                getION(msg.data);
                getPulso(msg.data);
                getOTA(msg.data);
                getIP(msg.data);
                getTotalMemory(msg.data);
                getUsedMemory(msg.data);

                // verifica se todas as informações 'sys_config' encontradas
                VerifyInfoReleseadSysConfig();

                // --------------------------------------------------------------

                // system_reset
                getReconectada(msg.data);

                // verifica se todas as informações 'system_reset' encontradas
                VerifyInfoReleseadSystemReset();

                // --------------------------------------------------------------

                // system_format
                getFormatado(msg.data);

                // verifica se todas as informações 'system_format' encontradas
                VerifyInfoReleseadSystemFormat();

                // --------------------------------------------------------------

                // module_scan
                getModulosEscaneados(msg.data);

                // verifica se todas as informações 'module_scan' encontradas
                VerifyInfoReleseadModuleScan();

                // --------------------------------------------------------------

                // pers_array_format
                getMensagensFormatado(msg.data);

                // verifica se todas as informações 'pers_array_format' 'message_manager' encontradas
                VerifyInfoReleseadFormatMessage();

                // --------------------------------------------------------------

                // system_files
                getListarArquivos(msg.data);

                // verifica se todas as informações 'system_files' encontradas
                VerifyInfoReleseadListFiles();

                // --------------------------------------------------------------

                // pers_format
                getFormataArquivo(msg.data);

                // verifica se todas as informações 'pers_format' encontradas
                VerifyInfoReleseadFormatFile();

                // --------------------------------------------------------------

                // sys_config eth
                getRedeEthernet(msg.data);

                // verifica se todas as informações do 'sys_config eth' foram encontradas
                VerifyInfoReleseadEthernet();

                // --------------------------------------------------------------

                // sys_config wifi
                getRedeWifi(msg.data);

                // verifica se todas as informações do 'sys_config wifi' foram encontradas
                VerifyInfoReleseadWifi();

                // --------------------------------------------------------------

                // sys_config wifi
                getRedeGSM(msg.data);

                // verifica se todas as informações do 'sys_config gsm' foram encontradas
                VerifyInfoReleseadGSM();

                // --------------------------------------------------------------

                // wifi_config
                getSalvaWifi(msg.data);

                // verifica se todas as informações do 'wifi_config' foram encontradas
                VerifyInfoReleseadSaveWifi();

                // --------------------------------------------------------------

                // ethernet_config
                getSalvaEthernet(msg.data);

                // verifica se todas as informações do 'ethernet_config' foram encontradas
                VerifyInfoReleseadSaveEthernet();

                // --------------------------------------------------------------

                // modbus_master
                getModbusMaster(msg.data);

                // verifica se todas as informações do 'modbus_master' foram encontradas
                if (VerifyInfoReleseadModbusMaster())
                    return;

                // --------------------------------------------------------------

                getModbusConfig(msg.data);

                // verifica se todas as informações do 'sys_config' 'modbus' foram encontradas
                if (VerifyInfoReleseadModbusConfig())
                    return;

                // --------------------------------------------------------------

                getModbusReset(msg.data);

                // verifica se todas as informações do 'modbus_reset' foram encontradas
                if (VerifyInfoReleseadModbusReset())
                    return;

                // --------------------------------------------------------------

                getModbusAddBeginIni(msg.data);

                // verifica se todas as informações do 'modbus_add_begin' foram encontradas
                if (VerifyInfoReleseadModbusAddBeginIni())
                    return;

                // --------------------------------------------------------------

                getModbusAddDevice(msg.data);

                // verifica se todas as informações do 'modbus_add_device' foram encontradas
                if (VerifyInfoReleseadModbusAddDevice())
                    return;

                // --------------------------------------------------------------

                getModbusAddSlave(msg.data);

                // verifica se todas as informações do 'modbus_add_slave' foram encontradas
                if (VerifyInfoReleseadModbusAddSlave())
                    return;

                // --------------------------------------------------------------

                getModbusAddVariable(msg.data);

                // verifica se todas as informações do 'modbus_add_variable' foram encontradas
                if (VerifyInfoReleseadModbusAddVariable())
                    return;

                // --------------------------------------------------------------

                getModbusAddMean(msg.data);

                // verifica se todas as informações do 'modbus_add_mean' foram encontradas
                if (VerifyInfoReleseadModbusAddMean())
                    return;

                // --------------------------------------------------------------

                getModbusAddBeginFim(msg.data);

                // verifica se todas as informações do 'modbus_add_begin' foram encontradas
                if (VerifyInfoReleseadModbusAddBeginFim())
                    return;


                // --------------------------------------------------------------

                getModbusData(msg.data);

                // verifica se todas as informações do 'modbus_data_refresh' foram encontradas
                if (VerifyInfoReleseadModbusData())
                    return;

                // --------------------------------------------------------------

                getCodiStatus(msg.data);

                // verifica se todas as informações do 'codi' foram encontradas
                if (VerifyInfoReleseadCodiStatus())
                    return;

                // --------------------------------------------------------------

                getCodiConfig(msg.data);

                // verifica se todas as informações do 'sys_config' 'codi' foram encontradas
                if (VerifyInfoReleseadCodiConfig())
                    return;

                // --------------------------------------------------------------

                getCodiEnd(msg.data);

                // verifica se todas as informações 'pers_array_format' 'codi' encontradas
                if (VerifyInfoReleseadCodiEnd())
                    return;

                // --------------------------------------------------------------

                getCodiAdd(msg.data);

                // verifica se todas as informações 'codi_add' encontradas
                if (VerifyInfoReleseadCodiAdd())
                    return;

                // --------------------------------------------------------------

                getSystemOTARollback(msg.data);

                // verifica se todas as informações 'system_rollback' encontradas
                if (VerifyInfoReleseadOTARollback())
                    return;

                // --------------------------------------------------------------

                getSystemOTAUpdate(msg.data);

                // verifica se todas as informações 'system_rollback' encontradas
                if (VerifyInfoReleseadUpdateFirmware())
                    return;

                // --------------------------------------------------------------

                getIonStatus(msg.data);

                // verifica se todas as informações 'ion' encontradas
                if (VerifyInfoReleseadIonStatus())
                    return;

                // --------------------------------------------------------------

                getIonConfig(msg.data);

                // verifica se todas as informações do 'sys_config' 'ion' foram encontradas
                if (VerifyInfoReleseadIonConfig())
                    return;

                // --------------------------------------------------------------

                getIonEnd(msg.data);

                // verifica se todas as informações 'ion_end' encontradas
                if (VerifyInfoReleseadIonEnd())
                    return;

                // --------------------------------------------------------------

                getIonAdd(msg.data);

                // verifica se todas as informações 'ion_add' encontradas
                if (VerifyInfoReleseadIonAdd())
                    return;

                // --------------------------------------------------------------

                getIonAutoSearch(msg.data);

                // verifica se todas as informações 'ion_auto_search' encontradas
                if (VerifyInfoReleseadIonAutoSearch())
                    return;

                // --------------------------------------------------------------

                getIonData(msg.data);

                // verifica se todas as informações 'ion_data' encontradas
                if (VerifyInfoReleseadIonData())
                    return;
                
                // --------------------------------------------------------------

                getPulsoStatus(msg.data);

                // verifica se todas as informações 'pulse' 'module_start/module_end' encontradas
                if (VerifyInfoReleseadPulsoStatus())
                    return;

                // --------------------------------------------------------------

                getPulsoConfig(msg.data);

                // verifica se todas as informações do 'sys_config' 'pulse' foram encontradas
                if (VerifyInfoReleseadPulsoConfig())
                    return;

                // --------------------------------------------------------------

                getPulsoReset(msg.data);

                // verifica se todas as informações do 'pulse_reset ' foram encontradas
                if (VerifyInfoReleseadPulsoReset())
                    return;

                // --------------------------------------------------------------

                getPulsoEnd(msg.data);

                // verifica se todas as informações do 'pulse_end ' foram encontradas
                if (VerifyInfoReleseadPulsoEnd())
                    return;

                // --------------------------------------------------------------

                getPulsoAdd(msg.data);

                // verifica se todas as informações do 'pulse_add' foram encontradas
                if (VerifyInfoReleseadPulsoAdd())
                    return;

                // --------------------------------------------------------------

                getKhomp(msg.data);

                // verifica se todas as informações do 'khomp' foram encontradas
                if (VerifyInfoReleseadKhomp())
                    return;
                
                /* -------------------------------------------------------------- */

                getApnConfig(msg.data);

                /* verifica se todas as informações do 'get_apns' foram encontradas */
                if (VerifyInfoReleseadApnConfig())
                    return;
                
                /* -------------------------------------------------------------- */

                getApnAdd(msg.data);

                /* verifica se todas as informações do 'add_apn' foram encontradas */
                if (VerifyInfoReleseadApnAdd())
                    return;

                /* -------------------------------------------------------------- */

                getApnRemove(msg.data);

                /* verifica se todas as informações do 'remove_apn' foram encontradas */
                if (VerifyInfoReleseadApnRemove())
                    return;                
            }
            catch (error) {
                // liberar quando quiser fazer o debug das mensagens
                //console.log(`Error MQTT : ${error}`);
            }

        });

        client.on('connect', function () {

            // liberar quando quiser fazer o debug das mensagens
            //console.log('mqtt connected');

            isConnected = true;
            eventIsConnected(true);
        });

        client.connect();

    }).catch(function (err) {

        // liberar quando quiser fazer o debug das mensagens
        //console.log(err);

        isConnected = false;
        eventIsConnected(false);
    });
}

// solicita o dbgack
function publishMQTT_dbgack(id_probe: string) {
    // libera a busca de informações solicitadas pelo dbgack
    infoReleasedDbgackMQTT(INFO_NOT_FINDED);

    // public no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'dbgack' }), qOS_action, false);
}

// solicita o sys_config e todas os seus auxiliares
function publishMQTT_sys_config(id_probe: string, auxiliar: string = '') {

    switch (auxiliar) {

        case 'all':

            // libera a busca de informações solicitadas pelo 'sys_config all'
            infoReleasedSysConfigMQTT(INFO_NOT_FINDED);

            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'all' }), qOS_action, false);

            break;
        case 'modbus':

            // inicializa
            modbusPayload = [];

            // libera a busca de informações solicitadas pelo 'sys_config modbus'
            infoReleasedModbusConfigMQTT(INFO_NOT_FINDED);

            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'modbus' }), qOS_action, false);

            break;
        case 'codi':

            // libera a busca de informações solicitadas pelo 'sys_config codi'
            infoReleasedCodiConfigMQTT(INFO_NOT_FINDED);

            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'codi' }), qOS_action, false);

            break;
        case 'ion':

            // libera a busca de informações solicitadas pelo 'sys_config' 'ion'
            infoReleasedIonConfigMQTT(INFO_NOT_FINDED);

            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'ion' }), qOS_action, false);

            break;
        case 'pulse':

            // libera a busca de informações solicitadas pelo 'sys_config' 'pulse'
            infoReleasedPulsoConfigMQTT(INFO_NOT_FINDED);

            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'pulse' }), qOS_action, false);

            break;
    }
}

// solicita o system_reset
function publishMQTT_system_reset(id_probe: string) {
    // libera a busca de informações solicitadas pelo 'system_reset'
    infoReleasedSystemResetMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'system_reset' }), qOS_action, false);
}

// solicita o system_format
function publishMQTT_system_format(id_probe: string) {
    // libera a busca de informações solicitadas pelo 'system_format'
    infoReleasedSystemFormatMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'system_format' }), qOS_action, false);
}

// solicita o module_scan
function publishMQTT_module_scan(id_probe: string) {
    // libera a busca de informações solicitadas pelo 'module_scan'
    infoReleasedModuleScanMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'module_scan' }), qOS_action, false);
}

// solicita o pers_array_format
function publishMQTT_pers_array_format(id_probe: string) {
    // libera a busca de informações solicitadas pelo 'pers_array_format' 'codi'
    infoReleasedFormatMessageMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ path: 'message_manager', usr: 'App', variable: 'pers_array_format' }), qOS_action, false);
}

// solicita o system_files
function publishMQTT_system_files(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'system_files'
    infoReleasedListFilesMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'system_files' }), qOS_action, false);
}

// solicita o pers_format
function publishMQTT_pers_format(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'pers_format'
    infoReleasedFormatFileMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'pers_format', path: auxiliar }), qOS_action, false);
}

// solicita o sys_config para o wifi
function publishMQTT_sys_config_wifi(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'system_config wifi'
    infoReleasedWifiMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'rede' }), qOS_action, false);
}

// solicita o sys_config para o gsm
function publishMQTT_sys_config_gsm(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'system_config gsm'
    infoReleasedGSMMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'rede' }), qOS_action, false);
}

// solicita o sys_config para o eth
function publishMQTT_sys_config_eth(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'system_config eth'
    infoReleasedEthernetMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'sys_config', update: 'rede' }), qOS_action, false);
}

// solicita o wifi_reset
function publishMQTT_sys_reset(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'wifi_reset'
    infoReleasedWifiMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'wifi_reset' }), qOS_action, false);
}

// solicita o ethernet_reset_config
function publishMQTT_ethernet_reset_config(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'ethernet_reset_config'            
    infoReleasedSaveEthernetMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'ethernet_reset_config' }), qOS_action, false);
}

// solicita o wifi_config
function publishMQTT_wifi_config(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'wifi_config'
    infoReleasedSaveWifiMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const wifi = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ pass: wifi.pass, ssid: wifi.ssid, usr: 'App', variable: 'wifi_config' }), qOS_action, false);
}

// solicita o ethernet_config
function publishMQTT_ethernet_config(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'ethernet_config'
    infoReleasedSaveEthernetMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const ethernet = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        dns: ethernet.dns,
        gw: ethernet.gw,
        ip: ethernet.ip,
        mask: ethernet.mask,
        usr: 'App',
        variable: 'ethernet_config'
    }), qOS_action, false);
}

// solicita o modbus_master
function publishMQTT_modbus_master(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'modbus_master'
    infoReleasedModbusMasterMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ name: 'modbus_master', usr: 'App', variable: auxiliar }), qOS_action, false);
}

// solicita o modbus_reset
function publishMQTT_modbus_reset(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'modbus_reset'
    infoReleasedModbusResetMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'modbus_reset' }), qOS_action, false);
}

// solicita o modbus_add_begin
function publishMQTT_modbus_add_begin(id_probe: string, auxiliar: string) {

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // se value = true o modbus vai começar a ser gravado
    if (json.value) {
        // libera a busca de informações solicitadas pelo 'modbus_add_begin'
        infoReleasedModbusAddBeginIniMQTT(INFO_NOT_FINDED);

        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({
            usr: 'App',
            variable: 'modbus_add_begin',
            device_size: json.device_size,
            mean_size: json.mean_size,
            slave_size: json.slave_size,
            value: json.value
        }), qOS_action, false);
    }
    else {
        // libera a busca de informações solicitadas pelo 'modbus_add_begin'
        infoReleasedModbusAddBeginFimMQTT(INFO_NOT_FINDED);

        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'modbus_add_begin', value: json.value }), qOS_action, false);
    }
}

// solicita o modbus_add_device
function publishMQTT_modbus_add_device(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'modbus_add_device'
    infoReleasedModbusAddDeviceMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'modbus_add_device',
        dev_name: json.dev_name,
        endianness: json.endianness
    }), qOS_action, false);
}

// solicita o modbus_add_slave
function publishMQTT_modbus_add_slave(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'modbus_add_slave'
    infoReleasedModbusAddSlaveMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // verifica o tipo de protocolo para enviar a solicitação
    if (json.protocol === ProtocoloProbe.RTU) {
        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({
            usr: 'App',
            variable: 'modbus_add_slave',
            dev_name: json.dev_name,
            slave_name: json.slave_name,
            slave_id: json.slave_id,
            send_time: json.send_time,
            protocol: json.protocol,
            baud: json.baud,
            uart: json.uart,
            port_id: json.port_id
        }), qOS_action, false);
    }
    else {
        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({
            usr: 'App',
            variable: 'modbus_add_slave',
            dev_name: json.dev_name,
            slave_name: json.slave_name,
            slave_id: json.slave_id,
            send_time: json.send_time,
            protocol: json.protocol,
            port: json.port,
            conn: json.conn,
            slave_ip: json.slave_ip
        }), qOS_action, false);
    }
}

// solicita o modbus_add_variable
function publishMQTT_modbus_add_variable(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'modbus_add_variable'
    infoReleasedModbusAddVariableMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'modbus_add_variable',
        dev_name: json.dev_name,
        address: json.address,
        data_type: json.data_type,
        factor: json.factor,
        function: json.function,
        mean: json.mean,
        var_name: json.var_name
    }), qOS_action, false);
}

// solicita o modbus_add_mean
function publishMQTT_modbus_add_mean(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'modbus_add_mean'
    infoReleasedModbusAddMeanMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'modbus_add_mean',
        slave_name: json.slave_name,
        var_name: json.var_name
    }), qOS_action, false);
}

// solicita o modbus_refresh_data
function publishMQTT_modbus_refresh_data(id_probe: string, auxiliar: string) {

    // inicializa
    modbusPayload = [];

    // libera a busca de informações solicitadas pelo 'modbus_refresh_data'
    infoReleasedModbusDataMQTT(INFO_NOT_FINDED);

    // parsing dos parametros
    const json = JSON.parse(auxiliar);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'mdb_refresh_data',
        value: Number(json.value)
    }), qOS_action, false);
}

// solicita o codi
function publishMQTT_codi(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'codi'
    infoReleasedCodiStatusMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: auxiliar, name: 'codi' }), qOS_action, false);
}

// solicita o codi_end
function publishMQTT_codi_end(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'codi_end'
    infoReleasedCodiEndMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'codi_end', port: json.port }), qOS_action, false);
}

// solicita o codi_add
function publishMQTT_codi_add(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'codi_add'
    infoReleasedCodiAddMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'codi_add',
        port: json.port,
        protocolo: json.protocolo,
        replicate: json.replicate,
        reverse: json.reverse,
        baud: json.baud,
        conf: json.conf,
        repo: json.repo
    }), qOS_action, false);
}

// solicita o system_rollback
function publishMQTT_system_rollback(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'system_rollback'
    infoReleasedOTARollbackMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'system_rollback' }), qOS_action, false);
}

// solicita o ota
function publishMQTT_ota(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'ota'
    infoReleasedOTAUpdateMQTT(INFO_NOT_FINDED);

    // verifica o tipo de modo
    switch (json.modo) {
        case TipoFirmwareModo.NORMAL:
            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({
                usr: 'App',
                variable: 'module_start',
                name: 'ota',
                url: json.url,
                timeout_s: json.timeout_s
            }), qOS_action, false);
            break;
        case TipoFirmwareModo.ESTAVEL:
            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({
                usr: 'App',
                variable: 'module_start',
                name: 'ota',
                url: json.url,
                timeout_s: json.timeout_s,
                stable: true
            }), qOS_action, false);
            break;
        case TipoFirmwareModo.ULTRA_ESTAVEL:
            // publica no mqtt
            mqttClient.publish(`action/${id_probe}`, JSON.stringify({
                usr: 'App',
                variable: 'module_start',
                name: 'ota',
                url: json.url,
                timeout_s: json.timeout_s,
                ultra_stable: true
            }), qOS_action, false);
            break;
    }
}

// solicita o ion
function publishMQTT_ion(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'ion'
    infoReleasedIonStatusMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ name: 'ion', usr: 'App', variable: auxiliar }), qOS_action, false);
}

// solicita o ion_end
function publishMQTT_ion_end(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'ion_end'
    infoReleasedIonEndMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'ion_end',
        slave_id: json.slave_id,
        port: json.port
    }), qOS_action, false);
}

// solicita o ion_add
function publishMQTT_ion_add(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'ion_add'
    infoReleasedIonAddMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'ion_add',
        slave_id: json.slave_id,
        port: json.port,
        baud: json.baud,
        uart: json.uart
    }), qOS_action, false);
}

// solicita o ion_auto_search
function publishMQTT_ion_auto_search(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'ion_auto_search'
    infoReleasedIonAutoSearchMQTT(INFO_NOT_FINDED);

    if (json.cancel) {
        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'ion_auto_search', port: json.port, cancel: true }), qOS_action, false);
    }
    else {
        // publica no mqtt
        mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'ion_auto_search', port: json.port, basic: json.basic, extended: json.extended }), qOS_action, false);
    }
}

/**
 * solicita os dados do ion
 */
function publishMQTT_ion_data() {

    // inicializa                    
    modbusPayload = [];
    
    /* libera a busca de informações solicitadas pelo 'ion_data' */
    infoReleasedIonDataMQTT(INFO_NOT_FINDED);
}

// solicita o pulse
function publishMQTT_pulse(id_probe: string, auxiliar: string) {

    // libera a busca de informações solicitadas pelo 'pulse'
    infoReleasedPulsoStatusMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ name: 'pulse', usr: 'App', variable: auxiliar }), qOS_action, false);
}

// solicita o pulse_reset
function publishMQTT_pulse_reset(id_probe: string) {

    // libera a busca de informações solicitadas pelo 'pulse_reset'
    infoReleasedPulsoResetMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr: 'App', variable: 'pulse_reset' }), qOS_action, false);
}

// solicita o pulse_end
function publishMQTT_pulse_end(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'pulse_end'
    infoReleasedPulsoEndMQTT(INFO_NOT_FINDED);

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({
        usr: 'App',
        variable: 'pulse_end',
        port: json.port
    }), qOS_action, false);
}

// solicita o pulse_add
function publishMQTT_pulse_add(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    // libera a busca de informações solicitadas pelo 'pulse_add'
    infoReleasedPulsoAddMQTT(INFO_NOT_FINDED);

    // cria o objeto inicial
    let obj: Record<string, any> = {
        usr: 'App',
        variable: 'pulse_add',
        port: json.port,
        type: json.type,
        send_time: json.send_time,
        repo: json.repo,
    };

    // Adicione a propriedade `contact` condicionalmente
    if (json.contact !== '##') {
        obj.contact = json.contact;
    }

    // Adicione a propriedade `send_time_repo` condicionalmente
    if (json.send_time_repo !== '##') {
        obj.send_time_repo = json.send_time_repo;
    }

    // publica no mqtt
    mqttClient.publish(`action/${id_probe}`, JSON.stringify(obj), qOS_action, false);
}

/* solicita configurações do khomp */
function publishMQTT_khomp() {

    // libera a busca de informações solicitadas pelo 'khomp'
    infoReleasedKhompMQTT(INFO_NOT_FINDED);
}

/**
 *  solicita a publicação do get_apns no servidor mqtt
 * @param id_probe 
 * @param auxiliar 
 */
function publishMQTT_apn_config(id_probe: string) {

    /* libera a busca de informações solicitadas pelo 'get_apns' */
    infoReleasedApnConfigMQTT(INFO_NOT_FINDED);

    /* publica no mqtt */
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr:'App', variable: 'get_apns'}), qOS_action, false);
}

/**
 *  solicita a publicação do add_apn no servidor mqtt
 * @param id_probe 
 * @param auxiliar 
 */
function publishMQTT_apn_add(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    /* libera a busca de informações solicitadas pelo 'add_apn' */
    infoReleasedApnAddMQTT(INFO_NOT_FINDED);

    /* publica no mqtt */
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr:'App', variable: 'add_apn', apn: json.apn, user:json.user, pass:json.pass}), qOS_action, false);
}

/**
 *  solicita a publicação do add_remove no servidor mqtt
 * @param id_probe 
 * @param auxiliar 
 */
function publishMQTT_apn_remove(id_probe: string, auxiliar: string) {

    const json = JSON.parse(auxiliar);

    /* libera a busca de informações solicitadas pelo 'add_remove' */
    infoReleasedApnRemoveMQTT(INFO_NOT_FINDED);

    /* publica no mqtt */
    mqttClient.publish(`action/${id_probe}`, JSON.stringify({ usr:'App', variable: 'remove_apn', apn: json.apn}), qOS_action, false);
}



//  le o MQTT
function publishMQTT(id_probe: string, variable: string, auxiliar: string = '') {

    // subscreve no action do mqtt
    mqttClient.subscribe(`action/${id_probe}`, qOS_action);

    // pega a variavel atual
    switch (variable) {
        case 'dbgack':
            publishMQTT_dbgack(id_probe);
            break;
        case 'sys_config':
            publishMQTT_sys_config(id_probe, auxiliar);
            break;
        case 'system_reset':
            publishMQTT_system_reset(id_probe);
            break;
        case 'system_format':
            publishMQTT_system_format(id_probe);
            break;
        case 'module_scan':
            publishMQTT_module_scan(id_probe);
            break;
        case 'pers_array_format':
            publishMQTT_pers_array_format(id_probe);
            break;
        case 'system_files':
            publishMQTT_system_files(id_probe);
            break;
        case 'pers_format':
            publishMQTT_pers_format(id_probe, auxiliar);
            break;
        case 'sys_config_wifi':
            publishMQTT_sys_config_wifi(id_probe);
            break;
        case 'sys_config_gsm':
            publishMQTT_sys_config_gsm(id_probe);
            break;
        case 'sys_config_eth':
            publishMQTT_sys_config_eth(id_probe);
            break;
        case 'wifi_reset':
            publishMQTT_sys_reset(id_probe);
            break;
        case 'ethernet_reset_config':
            publishMQTT_ethernet_reset_config(id_probe);
            break;
        case 'wifi_config':
            publishMQTT_wifi_config(id_probe, auxiliar);
            break;
        case 'ethernet_config':
            publishMQTT_ethernet_config(id_probe, auxiliar);
            break;
        case 'modbus_master':
            publishMQTT_modbus_master(id_probe, auxiliar);
            break;
        case 'modbus_reset':
            publishMQTT_modbus_reset(id_probe);
            break;
        case 'modbus_add_begin':
            publishMQTT_modbus_add_begin(id_probe, auxiliar);
            break;
        case 'modbus_add_device':
            publishMQTT_modbus_add_device(id_probe, auxiliar);
            break;
        case 'modbus_add_slave':
            publishMQTT_modbus_add_slave(id_probe, auxiliar);
            break;
        case 'modbus_add_variable':
            publishMQTT_modbus_add_variable(id_probe, auxiliar);
            break;
        case 'modbus_add_mean':
            publishMQTT_modbus_add_mean(id_probe, auxiliar);
            break;
        case 'mdb_refresh_data':
            publishMQTT_modbus_refresh_data(id_probe, auxiliar);
            break;
        case 'codi':
            publishMQTT_codi(id_probe, auxiliar);
            break;
        case 'codi_end':
            publishMQTT_codi_end(id_probe, auxiliar);
            break;
        case 'codi_add':
            publishMQTT_codi_add(id_probe, auxiliar);
            break;
        case 'system_rollback':
            publishMQTT_system_rollback(id_probe);
            break;
        case 'ota':
            publishMQTT_ota(id_probe, auxiliar);
            break;
        case 'ion':
            publishMQTT_ion(id_probe, auxiliar);
            break;
        case 'ion_end':
            publishMQTT_ion_end(id_probe, auxiliar);
            break;
        case 'ion_add':
            publishMQTT_ion_add(id_probe, auxiliar);
            break;
        case 'ion_auto_search':
            publishMQTT_ion_auto_search(id_probe, auxiliar);
            break;
        case 'ion_data':
            publishMQTT_ion_data();
            break;            
        case 'pulse':
            publishMQTT_pulse(id_probe, auxiliar);
            break
        case 'pulse_reset':
            publishMQTT_pulse_reset(id_probe);
            break;
        case 'pulse_end':
            publishMQTT_pulse_end(id_probe, auxiliar);
            break;
        case 'pulse_add':
            publishMQTT_pulse_add(id_probe, auxiliar);
            break;
        case 'khomp':
            publishMQTT_khomp();
        break;
        case 'get_apns':
            publishMQTT_apn_config(id_probe);
        break;
        case 'add_apn':
            publishMQTT_apn_add(id_probe, auxiliar);
        break;
        case 'remove_apn':
            publishMQTT_apn_remove(id_probe, auxiliar);
        break;
        
        default:
            return;
    }
}

//  subscribe o MQTT
function subscribeMQTT(id_probe: string, variable: string, auxiliar: string = '') {

    switch (variable) {
        case 'dbgack':
        case 'system_reset':
        case 'system_format':
        case 'module_scan':
        case 'pers_array_format':
        case 'modbus_master':
        case 'modbus_reset':
        case 'modbus_add_begin':
        case 'modbus_add_device':
        case 'modbus_add_slave':
        case 'modbus_add_variable':
        case 'modbus_add_mean':
        case 'codi_end':
        case 'codi_add':
        case 'system_rollback':
        case 'ota':
        case 'ion':
        case 'ion_end':
        case 'ion_add':
        case 'ion_auto_search':
        case 'pulse':
        case 'pulse_reset':
        case 'pulse_end':
        case 'pulse_add':
        case 'get_apns':
        case 'add_apn':
        case 'remove_apn':
            mqttClient.subscribe(`state/${id_probe}`, qOS_state);
            break;
        case 'sys_config':
        case 'system_files':
        case 'sys_config_wifi':
        case 'sys_config_gsm':
        case 'sys_config_eth':
        case 'wifi_reset':
        case 'ethernet_reset_config':
        case 'wifi_config':
        case 'ethernet_config':
            mqttClient.subscribe(`state/${id_probe}/cfg`, qOS_state);
            break;
        case 'khomp':
            if (id_probe.startsWith('F803320B') || id_probe.startsWith('f803320b'))
                mqttClient.subscribe(`probe/${id_probe}/data`, qOS_state);
            else
                mqttClient.subscribe(`telemetry/itg200/#/${id_probe}`, qOS_state);
            break;
        case 'mdb_refresh_data':
            mqttClient.subscribe(`state/${id_probe}/dbg/${JSON.parse(auxiliar).value}`, qOS_state);
            break;
        case 'ion_data':            
            mqttClient.subscribe(`state/${id_probe}_ion_${JSON.parse(auxiliar).port}_${JSON.parse(auxiliar).id}`, qOS_state);
            break;
        default:
            return;
    }
}

//  unsubscribe o MQTT
function unsubscribeMQTT(id_probe: string, variable: string, auxiliar: string = '') {

    switch (variable) {
        case 'dbgack':
        case 'system_reset':
        case 'system_format':
        case 'module_scan':
        case 'pers_array_format':
        case 'modbus_master':
        case 'modbus_reset':
        case 'modbus_add_begin':
        case 'modbus_add_device':
        case 'modbus_add_slave':
        case 'modbus_add_variable':
        case 'modbus_add_mean':
        case 'codi_end':
        case 'codi_add':
        case 'system_rollback':
        case 'ota':
        case 'ion':
        case 'ion_end':
        case 'ion_add':
        case 'ion_auto_search':
        case 'pulse':
        case 'pulse_reset':
        case 'pulse_end':
        case 'pulse_add':
        case 'get_apns':
        case 'add_apn':
        case 'remove_apn':
            mqttClient.unsubscribe(`state/${id_probe}`);
            break;
        case 'sys_config':
        case 'system_files':
        case 'sys_config_wifi':
        case 'sys_config_gsm':
        case 'sys_config_eth':
        case 'wifi_reset':
        case 'ethernet_reset_config':
        case 'wifi_config':
        case 'ethernet_config':
            mqttClient.unsubscribe(`state/${id_probe}/cfg`);
            break;
        case 'khomp':
            if (id_probe.startsWith('F803320B') || id_probe.startsWith('f803320b'))
                mqttClient.unsubscribe(`probe/${id_probe}/data`);
            else
                mqttClient.unsubscribe(`telemetry/itg200/#/${id_probe}`);
            break;
        case 'mdb_refresh_data':            
            mqttClient.unsubscribe(`state/${id_probe}/dbg/${JSON.parse(auxiliar).value}`);
            infoReleasedModbusDataMQTT(INFO_IDLE);
        break;
        case 'ion_data':
            mqttClient.unsubscribe(`state/${id_probe}_ion_${JSON.parse(auxiliar).port}_${JSON.parse(auxiliar).id}`);
            /* informações voltam a ficar paradas */
            infoReleasedIonDataMQTT(INFO_IDLE);
            break;
        default:
            return;
    }
}


// desconectar o cliente
function disconnectMQTT() {

    try {
        mqttClient.disconnect();
    }
    catch (error) {
        isConnected = false;
        eventIsConnected(false);
        return;
    }

    isConnected = false;
    eventIsConnected(false);
}

// função para verificar o status da conexão
function isMQTTConnected() {
    return isConnected;
}

// função para liberar ou bloquear a busca de uma determinada informação
function infoReleasedMQTT(id_info: number, status: number) {
    eventRegister[id_info] = status;
}

// função para liberar ou bloquear a busca de todas as informações
function infoReleasedAllMQTT(status: number) {
    eventRegister = new Array<number>(InfoProbeMQTT.TOTAL).fill(status);
}

// função para liberar ou bloquear a busca de as informações solicitadas pelo 'dbgack'
function infoReleasedDbgackMQTT(status: number) {

    // seta o status de [0]FIRMWARE à [8]STATUS_TENSAO
    for (let i = InfoProbeMQTT.FIRMWARE; i < InfoProbeMQTT.TIPO_OPERADORA; i++) {
        eventRegister[i] = status;
    }
}

// função para liberar ou bloquear a busca de as informações solicitadas pelo 'sys_config'
function infoReleasedSysConfigMQTT(status: number) {

    // seta o status de [10]TIPO_OPERADORA à [21]MEMORIA_USADA
    for (let i = InfoProbeMQTT.TIPO_OPERADORA; i < InfoProbeMQTT.RECONECTADO; i++) {
        eventRegister[i] = status;
    }
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'system_reset'
function infoReleasedSystemResetMQTT(status: number) {

    // seta o status de [22]RECONECTADO
    eventRegister[InfoProbeMQTT.RECONECTADO] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'system_format'
function infoReleasedSystemFormatMQTT(status: number) {

    // seta o status de [23]RECONECTADO
    eventRegister[InfoProbeMQTT.FORMATADO] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'module_scan'
function infoReleasedModuleScanMQTT(status: number) {

    // seta o status de [24]MODULO_SCAN
    eventRegister[InfoProbeMQTT.MODULO_SCAN] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'pers_array_format'
function infoReleasedFormatMessageMQTT(status: number) {

    // seta o status de [25]MSG_FORMATADA
    eventRegister[InfoProbeMQTT.MSG_FORMATADA] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'system_files'
function infoReleasedListFilesMQTT(status: number) {

    // seta o status de [26]LISTA_ARQUIVOS
    eventRegister[InfoProbeMQTT.LISTA_ARQUIVOS] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'pers_format'
function infoReleasedFormatFileMQTT(status: number) {

    // seta o status de [27]FORMATA_ARQUIVO
    eventRegister[InfoProbeMQTT.FORMATA_ARQUIVO] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config wifi'
function infoReleasedWifiMQTT(status: number) {

    // seta o status de [28]REDE_WIFI
    eventRegister[InfoProbeMQTT.REDE_WIFI] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config gsm'
function infoReleasedGSMMQTT(status: number) {

    // seta o status de [29]REDE_GSM
    eventRegister[InfoProbeMQTT.REDE_GSM] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config eth'
function infoReleasedEthernetMQTT(status: number) {

    // seta o status de [30]REDE_ETHERNET
    eventRegister[InfoProbeMQTT.REDE_ETHERNET] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'wifi_config'
function infoReleasedSaveWifiMQTT(status: number) {

    // seta o status de [31]REDE_WIFI_SALVA
    eventRegister[InfoProbeMQTT.REDE_WIFI_SALVA] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'ethernet_config'
function infoReleasedSaveEthernetMQTT(status: number) {

    // seta o status de [32]REDE_ETHERNET_SALVA
    eventRegister[InfoProbeMQTT.REDE_ETHERNET_SALVA] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_master'
function infoReleasedModbusMasterMQTT(status: number) {

    // seta o status de [33]MODBUS_STATUS
    eventRegister[InfoProbeMQTT.MODBUS_STATUS] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config' 'modbus'
function infoReleasedModbusConfigMQTT(status: number) {

    // seta o status de [34]MODBUS_CONFIG
    eventRegister[InfoProbeMQTT.MODBUS_CONFIG] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_reset'
function infoReleasedModbusResetMQTT(status: number) {

    // seta o status de [35] MODBUS_RESET
    eventRegister[InfoProbeMQTT.MODBUS_RESET] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_begin'
function infoReleasedModbusAddBeginIniMQTT(status: number) {

    // seta o status de [36] MODBUS_ADD_INI
    eventRegister[InfoProbeMQTT.MODBUS_ADD_INI] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_device'
function infoReleasedModbusAddDeviceMQTT(status: number) {

    // seta o status de [37] MODBUS_DEVICE
    eventRegister[InfoProbeMQTT.MODBUS_DEVICE] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_slave'
function infoReleasedModbusAddSlaveMQTT(status: number) {

    // seta o status de [38] MODBUS_SLAVE
    eventRegister[InfoProbeMQTT.MODBUS_SLAVE] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_variable'
function infoReleasedModbusAddVariableMQTT(status: number) {

    // seta o status de [39]MODBUS_VARIABLE
    eventRegister[InfoProbeMQTT.MODBUS_VARIABLE] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_mean'
function infoReleasedModbusAddMeanMQTT(status: number) {

    // seta o status de [40]MODBUS_MEAN
    eventRegister[InfoProbeMQTT.MODBUS_MEAN] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_add_mean'
function infoReleasedModbusAddBeginFimMQTT(status: number) {

    // seta o status de [41]MODBUS_ADD_FIM
    eventRegister[InfoProbeMQTT.MODBUS_ADD_FIM] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'modbus_refresh_data'
function infoReleasedModbusDataMQTT(status: number) {

    // seta o status de [42]MODBUS_DATA
    eventRegister[InfoProbeMQTT.MODBUS_DATA] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'codi'
function infoReleasedCodiStatusMQTT(status: number) {

    // seta o status de [43]CODI_STATUS
    eventRegister[InfoProbeMQTT.CODI_STATUS] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config' 'codi'
function infoReleasedCodiConfigMQTT(status: number) {

    // seta o status de [44]CODI_CONFIG
    eventRegister[InfoProbeMQTT.CODI_CONFIG] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'codi_end'
function infoReleasedCodiEndMQTT(status: number) {

    // seta o status de [45]CODI_END
    eventRegister[InfoProbeMQTT.CODI_END] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'codi_add'
function infoReleasedCodiAddMQTT(status: number) {

    // seta o status de [46]ADD_CODI
    eventRegister[InfoProbeMQTT.CODI_ADD] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'system_rollback'
function infoReleasedOTARollbackMQTT(status: number) {

    // seta o status de [47]OTA_ROLLBACK
    eventRegister[InfoProbeMQTT.OTA_ROLLBACK] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'module_start' 'ota'
function infoReleasedOTAUpdateMQTT(status: number) {

    // seta o status de [48]OTA_UPDATE
    eventRegister[InfoProbeMQTT.OTA_UPDATE] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'ion'
function infoReleasedIonStatusMQTT(status: number) {

    // seta o status de [49]ION_STATUS
    eventRegister[InfoProbeMQTT.ION_STATUS] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config' 'ion'
function infoReleasedIonConfigMQTT(status: number) {

    // seta o status de [50]ION_CONFIG
    eventRegister[InfoProbeMQTT.ION_CONFIG] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'ion_end'
function infoReleasedIonEndMQTT(status: number) {

    // seta o status de [51]ION_END
    eventRegister[InfoProbeMQTT.ION_END] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'ion_add'
function infoReleasedIonAddMQTT(status: number) {

    // seta o status de [52]ION_ADD
    eventRegister[InfoProbeMQTT.ION_ADD] = status;
}

// função para liberar ou bloquear a busca de informações solicitadas pelo 'ion_auto_search'
function infoReleasedIonAutoSearchMQTT(status: number) {

    // seta o status de [53]ION_SERCH
    eventRegister[InfoProbeMQTT.ION_SEARCH] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'ion_timer_dbg'
 * @param status 
 */
function infoReleasedIonDataMQTT(status: number) {

    // seta o status de [54]ION_DATA
    eventRegister[InfoProbeMQTT.ION_DATA] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'pulse'
 * @param status 
 */
function infoReleasedPulsoStatusMQTT(status: number) {

    // seta o status de [55]PULSO_STATUS
    eventRegister[InfoProbeMQTT.PULSO_STATUS] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'sys_config' 'pulse'
 * @param status 
 */
function infoReleasedPulsoConfigMQTT(status: number) {

    // seta o status de [56]PULSO_CONFIG
    eventRegister[InfoProbeMQTT.PULSO_CONFIG] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'pulse_reset'
 * @param status 
 */
function infoReleasedPulsoResetMQTT(status: number) {

    // seta o status de [57]PULSO_RESET
    eventRegister[InfoProbeMQTT.PULSO_RESET] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'pulse_end'
 * @param status 
 */
function infoReleasedPulsoEndMQTT(status: number) {

    // seta o status de [58]PULSO_END
    eventRegister[InfoProbeMQTT.PULSO_END] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'pulse_add'
 * @param status 
 */
function infoReleasedPulsoAddMQTT(status: number) {

    // seta o status de [59]PULSO_ADD
    eventRegister[InfoProbeMQTT.PULSO_ADD] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'khomp'
 * @param status 
 */
function infoReleasedKhompMQTT(status: number) {

    // seta o status de [60] KHOMP
    eventRegister[InfoProbeMQTT.KHOMP] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'get_apns'
 * @param status 
 */
function infoReleasedApnConfigMQTT(status : number) {  
    
    // seta o status de [61] APN_CONFIG
    eventRegister[InfoProbeMQTT.APN_CONFIG] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'add_apn'
 * @param status 
 */
function infoReleasedApnAddMQTT(status : number) {  
    
    // seta o status de [62] APN_ADD
    eventRegister[InfoProbeMQTT.APN_ADD] = status;
}

/**
 * função para liberar ou bloquear a busca de informações solicitadas pelo 'add_apn'
 * @param status 
 */
function infoReleasedApnRemoveMQTT(status : number) {  
    
    // seta o status de [63] APN_REMOVE
    eventRegister[InfoProbeMQTT.APN_REMOVE] = status;
}



// função para verificar se as funções solicitadas pelo 'dbgack' não esta liberadas
async function VerifyInfoReleseadDbgack() {

    // Cria um subarray do índice 0 ao 8
    // remove a informação de sinal, porque se a probe for ethernet essa informação não sera entregue 
    const dbgack = eventRegister.slice(InfoProbeMQTT.FIRMWARE, InfoProbeMQTT.STATUS_TENSAO);

    // cria um subarray do índice 0 ao 8 que verifica somente as informações solicitadas pelo dbgack
    if (dbgack.every(value => value === INFO_FINDED) === true) {

        // todas info encontradas
        eventFindedDbgack(true);

        // informações voltam a ficar paradas
        infoReleasedDbgackMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'sys_config all' não esta liberadas
async function VerifyInfoReleseadSysConfig() {

    // cria um subarray do índice 11 ao 21 que verifica somente as informações solicitadas pelo sys_config
    const sys_config = eventRegister.slice(InfoProbeMQTT.TIPO_OPERADORA, InfoProbeMQTT.MEMORIA_USADA);

    // verifica se todas as informações encontradas
    if (sys_config.every(value => value === INFO_FINDED) === true) {

        // todas info encontradas
        eventFindedSysConfig(true);

        // informações voltam a ficar paradas
        infoReleasedSysConfigMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'system_reset' não esta liberadas
async function VerifyInfoReleseadSystemReset() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.RECONECTADO] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedSystemResetMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'system_format' não esta liberadas
async function VerifyInfoReleseadSystemFormat() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.FORMATADO] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedSystemFormatMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'module_scan' não esta liberadas
async function VerifyInfoReleseadModuleScan() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODULO_SCAN] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModuleScanMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'pers_array_format' não esta liberadas
async function VerifyInfoReleseadFormatMessage() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MSG_FORMATADA] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedFormatMessageMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'system_files' não esta liberadas
async function VerifyInfoReleseadListFiles() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.LISTA_ARQUIVOS] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedListFilesMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'pers_format' não esta liberadas
async function VerifyInfoReleseadFormatFile() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.FORMATA_ARQUIVO] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedFormatFileMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'sys_config eth' não esta liberadas
async function VerifyInfoReleseadEthernet() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.REDE_ETHERNET] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedEthernetMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'sys_config wifi' não esta liberadas
async function VerifyInfoReleseadWifi() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.REDE_WIFI] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedWifiMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'sys_config gsm' não esta liberadas
async function VerifyInfoReleseadGSM() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.REDE_GSM] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedGSMMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'wifi_config' não esta liberadas
async function VerifyInfoReleseadSaveWifi() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.REDE_WIFI_SALVA] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedSaveWifiMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'ethernet_config' não esta liberadas
async function VerifyInfoReleseadSaveEthernet() {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.REDE_ETHERNET_SALVA] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedSaveEthernetMQTT(INFO_IDLE);
    }
}

// função para verificar se as funções solicitadas pelo 'modbus_master' não esta liberadas
function VerifyInfoReleseadModbusMaster(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_STATUS] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusMasterMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'sys_config' 'modbus' não esta liberadas
function VerifyInfoReleseadModbusConfig(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_CONFIG] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusConfigMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_reset' não esta liberadas
function VerifyInfoReleseadModbusReset(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_RESET] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusResetMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_begin' não esta liberadas
function VerifyInfoReleseadModbusAddBeginIni(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_ADD_INI] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddBeginIniMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_device' não esta liberadas
function VerifyInfoReleseadModbusAddDevice(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_DEVICE] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddDeviceMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_slave' não esta liberadas
function VerifyInfoReleseadModbusAddSlave(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_SLAVE] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddSlaveMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_variable' não esta liberadas
function VerifyInfoReleseadModbusAddVariable(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_VARIABLE] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddVariableMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_mean' não esta liberadas
function VerifyInfoReleseadModbusAddMean(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_MEAN] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddMeanMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_add_begin' não esta liberadas
function VerifyInfoReleseadModbusAddBeginFim(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_ADD_FIM] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusAddBeginFimMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'modbus_refresh_data' não esta liberadas
function VerifyInfoReleseadModbusData(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.MODBUS_DATA] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedModbusDataMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'codi' não esta liberadas
function VerifyInfoReleseadCodiStatus(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.CODI_STATUS] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedCodiStatusMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'sys_config' 'codi' não esta liberadas
function VerifyInfoReleseadCodiConfig(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.CODI_CONFIG] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedCodiConfigMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'codi_end' não esta liberadas
function VerifyInfoReleseadCodiEnd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.CODI_END] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedCodiEndMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'codi_add' não esta liberadas
function VerifyInfoReleseadCodiAdd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.CODI_ADD] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedCodiAddMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'system_rollback' não esta liberadas
function VerifyInfoReleseadOTARollback(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.OTA_ROLLBACK] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedOTARollbackMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'module_start' 'ota' não estão liberadas
function VerifyInfoReleseadUpdateFirmware(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.OTA_UPDATE] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedOTAUpdateMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'module_start'/'module_end' 'ion' não estão liberadas
function VerifyInfoReleseadIonStatus(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_STATUS] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonStatusMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'sys_config' 'ion' não estão liberadas
function VerifyInfoReleseadIonConfig(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_CONFIG] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonConfigMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'ion_end' não estão liberadas
function VerifyInfoReleseadIonEnd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_END] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonEndMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'ion_add' não estão liberadas
function VerifyInfoReleseadIonAdd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_ADD] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonAddMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'ion_auto_search' não estão liberadas
function VerifyInfoReleseadIonAutoSearch(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_SEARCH] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonAutoSearchMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'ion_data' não estão liberadas
function VerifyInfoReleseadIonData(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.ION_DATA] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedIonDataMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}


// função para verificar se as funções solicitadas pelo 'pulse' 'module_start/module_end' não estão liberadas
function VerifyInfoReleseadPulsoStatus(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.PULSO_STATUS] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedPulsoStatusMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'sys_config' 'pulse' não estão liberadas
function VerifyInfoReleseadPulsoConfig(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.PULSO_CONFIG] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedPulsoConfigMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'pulse_reset' não estão liberadas
function VerifyInfoReleseadPulsoReset(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.PULSO_RESET] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedPulsoResetMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'pulse_end' não estão liberadas
function VerifyInfoReleseadPulsoEnd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.PULSO_END] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedPulsoEndMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

// função para verificar se as funções solicitadas pelo 'pulse_add' não estão liberadas
function VerifyInfoReleseadPulsoAdd(): boolean {

    // verifica se foi encontrado 
    if (eventRegister[InfoProbeMQTT.PULSO_ADD] === INFO_FINDED) {

        // informações voltam a ficar paradas
        infoReleasedPulsoAddMQTT(INFO_IDLE);

        // indica que não precisa mais procurar informações
        return true;
    }

    return false;
}

/**
 * função para verificar se as informações solicitadas pelo 'khomp' não estão liberadas
 * @returns 
 */
function VerifyInfoReleseadKhomp() : boolean {
          
    /* verifica se foi encontrado */
    if(eventRegister[InfoProbeMQTT.KHOMP] === INFO_FINDED) {

        /* a solicitação das informações voltam a ficar bloqueadas */
        infoReleasedKhompMQTT(INFO_IDLE);

        /* indica que não precisa mais procurar informações */
        return true;
    }

    return false;
}

/**
 * função para verificar se as informações solicitadas pelo 'get_apns' não estão liberadas
 * @returns 
 */ 
function VerifyInfoReleseadApnConfig() : boolean {
          
    /* verifica se foi encontrado */
    if(eventRegister[InfoProbeMQTT.APN_CONFIG] === INFO_FINDED) {

        /* a solicitação das informações voltam a ficar bloqueadas */
        infoReleasedApnConfigMQTT(INFO_IDLE);

        /* indica que não precisa mais procurar informações */
        return true;
    }

    return false;
}

/**
 * função para verificar se as informações solicitadas pelo 'add_apn' não estão liberadas
 * @returns 
 */ 
function VerifyInfoReleseadApnAdd() : boolean {
          
    /* verifica se foi encontrado */
    if(eventRegister[InfoProbeMQTT.APN_ADD] === INFO_FINDED) {

        /* a solicitação das informações voltam a ficar bloqueadas */
        infoReleasedApnAddMQTT(INFO_IDLE);

        /* indica que não precisa mais procurar informações */
        return true;
    }

    return false;
}

/**
 * função para verificar se as informações solicitadas pelo 'remove_apn' não estão liberadas
 * @returns 
 */ 
function VerifyInfoReleseadApnRemove() : boolean {
          
    /* verifica se foi encontrado */
    if(eventRegister[InfoProbeMQTT.APN_REMOVE] === INFO_FINDED) {

        /* a solicitação das informações voltam a ficar bloqueadas */
        infoReleasedApnRemoveMQTT(INFO_IDLE);

        /* indica que não precisa mais procurar informações */
        return true;
    }

    return false;
}

export {
    connectMQTT, disconnectMQTT, infoReleasedAllMQTT, infoReleasedSysConfigMQTT,
    isMQTTConnected, publishMQTT, subscribeMQTT, unsubscribeMQTT
};

export const _testExports = {
    infoReleasedMQTT,
    VerifyInfoReleseadDbgack, infoReleasedDbgackMQTT,
    VerifyInfoReleseadSysConfig, infoReleasedSysConfigMQTT,
    VerifyInfoReleseadSystemReset, infoReleasedSystemResetMQTT,
    VerifyInfoReleseadSystemFormat, infoReleasedSystemFormatMQTT,
    VerifyInfoReleseadModuleScan, infoReleasedModuleScanMQTT,
    VerifyInfoReleseadFormatMessage, infoReleasedFormatMessageMQTT,
    VerifyInfoReleseadListFiles, infoReleasedListFilesMQTT,
    VerifyInfoReleseadFormatFile, infoReleasedFormatFileMQTT,
    VerifyInfoReleseadEthernet, infoReleasedEthernetMQTT,
    VerifyInfoReleseadWifi, infoReleasedWifiMQTT,
    VerifyInfoReleseadGSM, infoReleasedGSMMQTT,
    VerifyInfoReleseadSaveWifi, infoReleasedSaveWifiMQTT,
    VerifyInfoReleseadSaveEthernet, infoReleasedSaveEthernetMQTT,
    VerifyInfoReleseadModbusMaster, infoReleasedModbusMasterMQTT,
    VerifyInfoReleseadModbusConfig, infoReleasedModbusConfigMQTT,
    VerifyInfoReleseadModbusReset, infoReleasedModbusResetMQTT,
    VerifyInfoReleseadModbusAddBeginIni, infoReleasedModbusAddBeginIniMQTT,
    VerifyInfoReleseadModbusAddDevice, infoReleasedModbusAddDeviceMQTT,
    VerifyInfoReleseadModbusAddSlave, infoReleasedModbusAddSlaveMQTT,
    VerifyInfoReleseadModbusAddVariable, infoReleasedModbusAddVariableMQTT,
    infoReleasedModbusAddMeanMQTT, VerifyInfoReleseadModbusAddMean,
    VerifyInfoReleseadModbusAddBeginFim, infoReleasedModbusAddBeginFimMQTT,
    VerifyInfoReleseadCodiStatus, infoReleasedCodiStatusMQTT,
    VerifyInfoReleseadCodiConfig, infoReleasedCodiConfigMQTT,
    VerifyInfoReleseadCodiEnd, infoReleasedCodiEndMQTT,
    VerifyInfoReleseadCodiAdd, infoReleasedCodiAddMQTT,
    VerifyInfoReleseadOTARollback, infoReleasedOTARollbackMQTT,
    VerifyInfoReleseadUpdateFirmware, infoReleasedOTAUpdateMQTT,
    VerifyInfoReleseadIonStatus, infoReleasedIonStatusMQTT,
    VerifyInfoReleseadIonConfig, infoReleasedIonConfigMQTT,
    VerifyInfoReleseadIonEnd, infoReleasedIonEndMQTT,
    VerifyInfoReleseadIonAdd, infoReleasedIonAddMQTT,
    VerifyInfoReleseadIonAutoSearch, infoReleasedIonAutoSearchMQTT,
    VerifyInfoReleseadPulsoStatus, infoReleasedPulsoStatusMQTT,
    VerifyInfoReleseadPulsoConfig, infoReleasedPulsoConfigMQTT,
    VerifyInfoReleseadPulsoReset, infoReleasedPulsoResetMQTT,
    VerifyInfoReleseadPulsoEnd, infoReleasedPulsoEndMQTT,
    VerifyInfoReleseadPulsoAdd, infoReleasedPulsoAddMQTT,
    getMensagem,
};
