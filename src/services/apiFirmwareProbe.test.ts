import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import apiGestal from './apiGestal';
import { MockFirmwareJson } from '../../__mocks__/FirmwareJsonMocks';
import apiFirmwareProbe from './apiFirmwareProbe';

import * as ApiGestal from './apiGestal';
import { cleanup } from '@testing-library/react-native';
const spyApiGestal = jest.spyOn(ApiGestal, 'default');
cleanup();
jest.mock('./apiGestal');

describe('apiVariaveisProbe - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiGestal.defaults.baseURL).toBe('http://ws.gestal.srv.br/api/v1');
        expect(apiGestal.defaults.responseType).toBe('json');
        expect(apiGestal.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/firmware').reply(200, data);

        apiGestal.get = jest.fn().mockReturnValueOnce({data:MockFirmwareJson});

        const response = await apiGestal.get('/firmware');
        expect(response.data).toEqual(MockFirmwareJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiGestal.mockRejectedValue({response:{data:'erro'}})
        const response = await apiFirmwareProbe('/firmware');

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiGestal.mockResolvedValue({data:MockFirmwareJson})
        const response = await apiFirmwareProbe('/firmware');
        expect(response.data).toEqual(MockFirmwareJson);    
    });    
});
