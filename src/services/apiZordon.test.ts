import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import apiZordon from './apiZordon';

jest.mock('./apiZordon');

describe('apiZordon - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiZordon.defaults.baseURL).toBe('https://telemetria-api-hmg.comerc.com.br/v1/');
        expect(apiZordon.defaults.responseType).toBe('json');
        expect(apiZordon.defaults.withCredentials).toBe(true);
    });    
});
