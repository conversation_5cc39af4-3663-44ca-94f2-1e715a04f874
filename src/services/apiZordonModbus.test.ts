import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { MockModbusJson } from '../../__mocks__/ModbusJsonMock';
import apiZordon from './apiZordon';
import apiZordonModbus from './apiZordonModbus';

import * as ApiZordon from './apiZordon';
const spyApiZordon = jest.spyOn(ApiZordon, 'default');

jest.mock('./apiZordon');

describe('apiZordonModbus - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiZordon.defaults.baseURL).toBe('https://telemetria-api-hmg.comerc.com.br/v1/');
        expect(apiZordon.defaults.responseType).toBe('json');
        expect(apiZordon.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/modbus').reply(200, data);

        apiZordon.get = jest.fn().mockReturnValueOnce({data:MockModbusJson});

        const response = await apiZordon.get('/modbus');
        expect(response.data).toEqual(MockModbusJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiZordon.mockRejectedValue({response:{data:'erro'}})
        const response = await apiZordonModbus();

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiZordon.mockResolvedValue({data:MockModbusJson})
        const response = await apiZordonModbus();
        expect(response.data).toEqual(MockModbusJson);    
    });    
});
