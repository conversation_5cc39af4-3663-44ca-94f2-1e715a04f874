import { getStorageString, KEY, setStorageJson, setStorageString } from "../storages";
import apiProtheusAgenda from "./apiProtheusAgenda";
import apiProtheusAutenticacao from "./apiProtheusAutenticacao";
import apiProtheusMedidores from "./apiProtheusMedidores";

export class Protheus {

    /**
     * autenticação no servidor protheus
     * @returns 
     */
    async autenticarProtheus(): Promise<number> {
    
        try {

            const result = await apiProtheusAutenticacao();
      
            if (result.status === 201) {
                
                setStorageString(KEY.protheusToken, result.data.access_token);
                setStorageString(KEY.protheusRenovaToken, result.data.refresh_token);
            } 

            return (result.status);
        
        } catch (error) {
            
            return 0;
        }
    }

    /**
     * pega lista de medidores no servidor protheus
     * @returns 
     */
    async medidoresProtheus(): Promise<number> {
    
        try {

            const result = await apiProtheusMedidores();
      
            if (result.status === 200) {
                
                setStorageJson(KEY.modelosMedidorProtheus, result.data.Data);
            } 

            return (result.status);
            
        } catch (error) {
            
            return 0;
        }
    }
    
    /**
     * pega a agenda do instalador
     * @returns 
     */
    async agendaInstaladoresProtheus(): Promise<number> {
    
        try {

            const result = await apiProtheusAgenda(await getStorageString(KEY.cognitoEmail) || '' );
      
            if (result.status === 200) {
                
                setStorageJson(KEY.agendaProtheus, result.data.Data);
            } 
              
            return (result.status);
            
        } catch (error) {
            
            return 0;
        }
    }    
}