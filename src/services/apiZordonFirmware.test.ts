import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import { MockFirmwareJson } from '../../__mocks__/FirmwareJsonMocks';
import apiZordon from './apiZordon';
import apiZordonFirmware from './apiZordonFirmware';

import { cleanup } from '@testing-library/react-native';
import * as ApiZordon from './apiZordon';
const spyApiZordon = jest.spyOn(ApiZordon, 'default');
cleanup();
jest.mock('./apiZordon');

describe('apiVariaveisProbe - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiZordon.defaults.baseURL).toBe('https://telemetria-api-hmg.comerc.com.br/v1/');
        expect(apiZordon.defaults.responseType).toBe('json');
        expect(apiZordon.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/firmware').reply(200, data);

        apiZordon.get = jest.fn().mockReturnValueOnce({data:MockFirmwareJson});

        const response = await apiZordon.get('/firmware');
        expect(response.data).toEqual(MockFirmwareJson);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiZordon.mockRejectedValue({response:{data:'erro'}})
        const response = await apiZordonFirmware();

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiZordon.mockResolvedValue({data:MockFirmwareJson})
        const response = await apiZordonFirmware();
        expect(response.data).toEqual(MockFirmwareJson);    
    });    
});
