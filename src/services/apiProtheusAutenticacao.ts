
import {
  PROTHEUS_PASSWORD,
  PROTHEUS_USERNAME,
} from '@env';
import api<PERSON>rotheus from "./apiProtheus";

export const apiProtheusAutenticacao = async () => {

  try {
    const result = await apiProtheus("/api/oauth2/v1/token", {
      method:"POST",
      headers:{
        'Content-Type': "aplication/json"
      },
      params:{
        'grant_type': 'password',
        'username': PROTHEUS_USERNAME,    
        'password': PROTHEUS_PASSWORD
      },      
    });

    return result;
  }
  catch(error:any) {
      return error.response.data;
  }
}

export default apiProtheusAutenticacao;