
import { getStorageString, KEY, setStorageJson, setStorageString } from "../storages";
import apiProtheusAgenda from "./apiProtheusAgenda";
import apiProtheusAutenticacao from "./apiProtheusAutenticacao";
import apiProtheusMedidores from "./apiProtheusMedidores";

import { Protheus } from "./protheus";

// Mockando as dependências
jest.mock("../storages", () => ({
  setStorageString: jest.fn(),
  setStorageJson: jest.fn(),
  getStorageString: jest.fn().mockResolvedValue("mockEmail"),
  KEY: {
    protheusToken: "protheusToken",
    protheusRenovaToken: "protheusRenovaToken",
    modelosMedidorProtheus: "modelosMedidorProtheus",
    agendaProtheus: "agendaProtheus",
    cognitoEmail: "cognitoEmail",
  },
}));

jest.mock("./apiProtheus", () => jest.fn());
jest.mock("./apiProtheusAutenticacao", () => jest.fn());
jest.mock("./apiProtheusMedidores", () => jest.fn());
jest.mock("./apiProtheusAgenda", () => jest.fn());

describe("Protheus", () => {
  let protheus: Protheus;

  beforeEach(() => {
    protheus = new Protheus();
    jest.clearAllMocks(); // Limpa mocks antes de cada teste
  });

  it("deve autenticar no Protheus e armazenar os tokens se retorno OK 201", async () => {
    (apiProtheusAutenticacao as jest.Mock).mockResolvedValue({
      status: 201,
      data: { access_token: "mockAccessToken", refresh_token: "mockRefreshToken" },
    });

    const result = await protheus.autenticarProtheus();

    expect(result).toBe(201);
    expect(setStorageString).toHaveBeenCalledWith(KEY.protheusToken, "mockAccessToken");
    expect(setStorageString).toHaveBeenCalledWith(KEY.protheusRenovaToken, "mockRefreshToken");
  });

  it("deve autenticar no Protheus e retorno diferente de 201", async () => {
    (apiProtheusAutenticacao as jest.Mock).mockResolvedValue({
      status: 401,
      data: { access_token: "mockAccessToken", refresh_token: "mockRefreshToken" },
    });

    const result = await protheus.autenticarProtheus();

    expect(result).toBe(401);
    //expect(setStorageString).toHaveBeenCalledWith(KEY.protheusToken, "mockAccessToken");
    //expect(setStorageString).toHaveBeenCalledWith(KEY.protheusRenovaToken, "mockRefreshToken");
  });  

  it("deve retornar 0 em caso de erro", async () => {
    (apiProtheusAutenticacao as jest.Mock).mockRejectedValue(new Error("Erro de autenticação"));

    const result = await protheus.autenticarProtheus();

    expect(result).toBe(0);
  });

  it("deve buscar medidores e armazená-los corretamente", async () => {
    const mockData = { Data: [{"IdMedidor":'001', "DescMed":"Setorial", "VlrKE":"1.0" }] };

    (apiProtheusMedidores as jest.Mock).mockResolvedValue({
      status: 200,
      data: mockData,
    });

    const result = await protheus.medidoresProtheus();

    expect(result).toBe(200);
    expect(setStorageJson).toHaveBeenCalledWith(KEY.modelosMedidorProtheus, mockData.Data);
  });

  it("deve buscar medidores, mas retorna status diferente de 200", async () => {
    const mockData = { Data: [{"IdMedidor":'001', "DescMed":"Setorial", "VlrKE":"1.0" }] };

    (apiProtheusMedidores as jest.Mock).mockResolvedValue({
      status: 400,
      data: mockData,
    });

    const result = await protheus.medidoresProtheus();

    expect(result).toBe(400);    
  });  

  it("deve retornar 0 em caso de erro", async () => {
    (apiProtheusMedidores as jest.Mock).mockRejectedValue(new Error("Erro na API"));

    const result = await protheus.medidoresProtheus();

    expect(result).toBe(0);
  });

  it("deve buscar agenda e armazená-los corretamente", async () => {
    const mockData = { Data: [{ "Agendas" : [ {"Loja": "01", "Status": "2", "DescOcor": "Instalação", "Bairro": "DISTRITO INDUSTRIAL", "DtInicio": "01-01-2000", "HrInicio": "09:00" }] }] };

    (apiProtheusAgenda as jest.Mock).mockResolvedValue({
      status: 200,
      data: mockData,
    });

    const result = await protheus.agendaInstaladoresProtheus();

    expect(result).toBe(200);
    expect(setStorageJson).toHaveBeenCalledWith(KEY.agendaProtheus, mockData.Data);
  });

  it("deve buscar medidores, mas retorna status diferente de 200", async () => {

    const mockData = { Data: [{ "Agendas" : [ {}] }] };

    (apiProtheusAgenda as jest.Mock).mockResolvedValue({
      status: 400,
      data: mockData,
    });

    const result = await protheus.agendaInstaladoresProtheus();

    expect(result).toBe(400);    
  });  

  it("deve chamar apiProtheusAgenda com string vazia quando cognitoEmail não existir", async () => {
    (getStorageString as jest.Mock).mockResolvedValue(undefined); // Simulando valor não definido

    (apiProtheusAgenda as jest.Mock).mockResolvedValue({ status: 200, data: {} });

    await protheus.agendaInstaladoresProtheus();

    expect(apiProtheusAgenda).toHaveBeenCalledWith(""); // Verifica se string vazia foi passada
  });

  it("deve retornar 0 em caso de erro", async () => {
    (apiProtheusAgenda as jest.Mock).mockRejectedValue(new Error("Erro na API"));

    const result = await protheus.agendaInstaladoresProtheus();

    expect(result).toBe(0);
  });  


});