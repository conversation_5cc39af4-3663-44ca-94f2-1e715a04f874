import { getStorageString, KEY } from "../storages";
import apiProtheus from "./apiProtheus";

export const apiProtheusMedidores = async () => {

  try {

    const result = await apiProtheus("/ModelosdeMedidores", {
      method:"GET",
      headers:{        
        'usuario': 'api_appinstalador',    
        'senha': 'dza_umr1fna!HBE6xtz',
        'Authorization': `Bearer ${await getStorageString(KEY.protheusToken)}`
      },
      params:{        
        'TipoCarga' : 'C'
      },      
    });

    return result;
  }
  catch(error:any) {
      return error.response.data;
  }
}

export default apiProtheusMedidores;
