import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import apiProtheus from './apiProtheus';

import { cleanup } from '@testing-library/react-native';
import * as ApiProtheus from './apiProtheus';
import apiProtheusMateriais from './apiProtheusMateriais';
const spyApiProtheus = jest.spyOn(ApiProtheus, 'default');
cleanup();
jest.mock('./apiProtheus');

describe('apiProtheusMedidores - Services', () => {
    let mock: MockAdapter;

    beforeEach(() => {
        mock = new MockAdapter(axios);
    });

    afterEach(() => {
        mock.restore();
    });

    it('deverá ser criado com um baseURL e responseType correto', () => {
        expect(apiProtheus.defaults.baseURL).toBe('https://api-hmg-protheus.comerc.com.br/doc88');
        expect(apiProtheus.defaults.responseType).toBe('json');
        expect(apiProtheus.defaults.withCredentials).toBe(true);
    });

    it('deverá tratar uma simples requesição de API', async () => {
        const data = { message: 'success' };
        mock.onGet('/ProdutosPorTecnico').reply(200, data);

        apiProtheus.get = jest.fn().mockReturnValueOnce({data:[]});

        const response = await apiProtheus.get('/ProdutosPorTecnico');
        expect(response.data).toEqual([]);
    });

    it('deverá tratar uma simples requesição de API com erro', async () => {

        spyApiProtheus.mockRejectedValue({response:{data:'erro'}})
        const response = await apiProtheusMateriais();

        expect(response).toEqual('erro');    
    });    

    it('deverá tratar uma simples requesição de API', async () => {

        spyApiProtheus.mockResolvedValue({data:[]})
        const response = await apiProtheusMateriais();
        expect(response.data).toEqual([]);    
    });    
});
