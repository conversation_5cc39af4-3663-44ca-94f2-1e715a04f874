declare module '@env' {

  const COGNITO_LOGIN_BASE_DEV_URL: string;
  const COGNITO_LOGIN_BASE_HMG_URL: string;
  const COGNITO_LOGIN_BASE_PROD_URL: string;

  const COGNITO_CALLBACK_URL_LOGIN_DEV: string;
  const COGNITO_CALLBACK_URL_LOGOUT_DEV: string;

  const COGNITO_CALLBACK_URL_LOGIN_HMG: string;
  const COGNITO_CALLBACK_URL_LOGOUT_HMG: string;

  const COGNITO_CALLBACK_URL_LOGIN_PROD: string;
  const COGNITO_CALLBACK_URL_LOGOUT_PROD: string;

  const COGNITO_CLIENT_ID_DEV: string;
  const COGNITO_CLIENT_ID_HMG: string;
  const COGNITO_CLIENT_ID_PROD: string;

  const COGNITO_CLIENT_SECRET_DEV: string;
  const COGNITO_CLIENT_SECRET_HMG: string;
  const COGNITO_CLIENT_SECRET_PROD: string;

  const ZORDON_BASE_URL: string;

  const PROTHEUS_USERNAME: string;
  const PROTHEUS_PASSWORD: string;


}