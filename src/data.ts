// funções
import { Double } from "react-native/Libraries/Types/CodegenTypes";
import {AdicionaDiaDataAtual} from "./funcoes"

export const ListaOrdensServicoHoje = [

    {
      id: 0,
      cliente: "ABC Ltda",
      numero_os: "12839821701",
      tipo_os: 0,
      status_os: 0,
      endereco: "<PERSON>. <PERSON>. <PERSON>, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "08:00",
      contato: "<PERSON>abio <PERSON>",
      telefone: "(11) 93292-9345",
      numero_pontos: 4,
    },
    {
        id: 1,
        cliente: "DEF Ltda",
        numero_os: "239408932812",
        tipo_os: 0,
        status_os: 2,
        endereco: "R. <PERSON><PERSON><PERSON>, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: new Date().toLocaleDateString(),
        agenda_hora: "10:00",
        contato: "<PERSON><PERSON><PERSON>",
        telefone: "(11) 93292-9345",
        numero_pontos: 2,
    },
    {
        id: 2,
        cliente: "GHI Ltda",
        numero_os: "340519043923",
        tipo_os: 3,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: new Date().toLocaleDateString(),
        agenda_hora: "13:00",
        contato: "Fabio Amaral de Souza",
        telefone: "(11) 93292-9345",
        numero_pontos: 1,
    },
    {
        id: 3,
        cliente: "JKL Ltda",
        numero_os: "451620154034",
        tipo_os: 4,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: new Date().toLocaleDateString(),
        agenda_hora: "15:00",
        contato: "Fabio Amaral",
        telefone: "(11) 93292-9345",
        numero_pontos: 3,
    },
    {
        id: 4,
        cliente: "MNO Ltda",
        numero_os: "562731265145",
        tipo_os: 5,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: new Date().toLocaleDateString(),
        agenda_hora: "17:00",
        contato: "Amaral",
        telefone: "(11) 93292-9345",
        numero_pontos: 1,
    },
    {
      id: 5,
      cliente: "PQR Ltda",
      numero_os: "673842376256",
      tipo_os: 6,
      status_os: 1,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "18:00",
      contato: "Fabio",
      telefone: "(11) 8888-9999",
      numero_pontos: 1,
    },
    {
      id: 6,
      cliente: "PQR123 Ltda",
      numero_os: "673842888256",
      tipo_os: 3,
      status_os: 1,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "18:00",
      contato: "Fabio",
      telefone: "(11) 8888-9999",
      numero_pontos: 2,
    },    
    {
      id: 7,
      cliente: "STU123 Ltda",
      numero_os: "673842888256",
      tipo_os: 1,
      status_os: 1,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "18:00",
      contato: "Fabio",
      telefone: "(11) 8888-9999",
      numero_pontos: 2,
    }, 
    {
      id: 8,
      cliente: "VXZ123 Ltda",
      numero_os: "673842888256",
      tipo_os: 2,
      status_os: 1,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "18:00",
      contato: "Fabio",
      telefone: "(11) 8888-9999",
      numero_pontos: 2,
    },
    {
      id: 9,
      cliente: "Desconhecido Ltda",
      numero_os: "000000000000",
      tipo_os: 7,
      status_os: 1,
      endereco: "R.",
      bairro: "",
      cep: "00000-000",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "00:00",
      contato: "",
      telefone: "(00) 0000-0000",
      numero_pontos: 0,
    },         

];

export const ListaOrdensServicoAmanha = [
    {
      id: 0,
      cliente: "ABC Ltda",
      numero_os: "12839821701",
      tipo_os: 1,
      status_os: 2,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: new Date().toLocaleDateString(),
      agenda_hora: "10:00",
      contato: "Fabio Amaral",
      telefone: "(11) 93292-9345",
      numero_pontos: 4,
    },
    {
        id: 1,
        cliente: "DEF Ltda",
        numero_os: "239408932812",
        tipo_os: 1,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: new Date().toLocaleDateString(),
        agenda_hora: "13:00",
        contato: "Fabio Souza",
        telefone: "(11) 93292-9345",
        numero_pontos: 2,
      },
];

export const ListaOrdensServicoFuturas = [
    {
      id: 0,
      cliente: "ABC Ltda",
      numero_os: "12839821701",
      tipo_os: 1,
      status_os: 2,
      endereco: "R. Min. Jesuíno Cardoso, 531",
      bairro: "Vila Nova Conceição, São Paulo SP",
      cep: "04544-150",
      agenda_data: AdicionaDiaDataAtual(2),
      agenda_hora: "09:00",
      contato: "Fabio Amaral",
      telefone: "(11) 93292-9345",
      numero_pontos: 4,
    },
    {
        id: 1,
        cliente: "DEF Ltda",
        numero_os: "239408932812",
        tipo_os: 1,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: AdicionaDiaDataAtual(3),
        agenda_hora: "10:00",
        contato: "Fabio Souza",
        telefone: "(11) 93292-9345",
        numero_pontos: 2,
      },
      {
        id: 2,
        cliente: "GHI Ltda",
        numero_os: "340519043923",
        tipo_os: 1,
        status_os: 2,
        endereco: "R. Min. Jesuíno Cardoso, 531",
        bairro: "Vila Nova Conceição, São Paulo SP",
        cep: "04544-150",
        agenda_data: AdicionaDiaDataAtual(4),
        agenda_hora: "08:00",
        contato: "Fabio Amaral de Souza",
        telefone: "(11) 93292-9345",
        numero_pontos: 1,
      },
];

export interface EtapasInstalacao {
  id?: number,
  cliente?: string,
  numero_os?: string,
  tipo_os?: number,
  status_os?: number,
  horario_ini?: string,
  horario_fim?: string,
  latitude?: number,
  longitude?: number,
  descricao_problema?: string;
  codigo_probe?: string,
  tipo_conexao?: number,
  operadora?: number,
  ip?:string,
  mascara?:string,
  gateway?:string,
  ssid?:string,
  ssid_senha?:string,
  medidor_fabricante?: number,
  medidor_modelo?: number,
  medidor_ns?: string,
  teste_conexao?: boolean,
  imagem_local1?: string,
  imagem_texto_local1?: string,
  imagem_local2?: string,
  imagem_texto_local2?: string,
  imagem_local3?: string,
  imagem_texto_local3?: string,
  imagem_local4?: string,
  imagem_texto_local4?: string,
  imagem_local5?: string,
  imagem_texto_local5?: string,
  imagem_local6?: string,
  imagem_texto_local6?: string,
  imagem_local7?: string,
  imagem_texto_local7?: string,
  imagem_local8?: string,
  imagem_texto_local8?: string,
  totalMaterial1?: number,
  textoMaterial1?: string,
  totalMaterial2?: number,
  textoMaterial2?: string,
  totalMaterial3?: number,
  textoMaterial3?: string,
  totalMaterial4?: number,
  textoMaterial4?: string,
  totalMaterial5?: number,
  textoMaterial5?: string,
  totalMaterial6?: number,
  textoMaterial6?: string,
  materialExtra?: boolean,
  textoMaterialExtra?: string,  
  imagem_instalacao1?: string,
  imagem_texto_instalacao1?: string,
  imagem_instalacao2?: string,
  imagem_texto_instalacao2?: string,
  imagem_instalacao3?: string,
  imagem_texto_instalacao3?: string,
  imagem_instalacao4?: string,
  imagem_texto_instalacao4?: string,
  imagem_instalacao5?: string,
  imagem_texto_instalacao5?: string,
  imagem_instalacao6?: string,
  imagem_texto_instalacao6?: string,  
  imagem_retirada1?: string,
  imagem_texto_retirada1?: string,
  imagem_retirada2?: string,
  imagem_texto_retirada2?: string,
  imagem_retirada3?: string,
  imagem_texto_retirada3?: string,
  imagem_retirada4?: string,
  imagem_texto_retirada4?: string,  
  observacoes?: string,
  imagem_assinatura?: string,
  cliente_nome?: string,
  cliente_telefone?: string,  
  concorda?: boolean,
  step1?: boolean,
  step2?: boolean,
  step3?: boolean,
  step4?: boolean,
  step5?: boolean,
  step6?: boolean,
  step7?: boolean,
}

export const ListaMedidorFabricante =  [
  { id:  0, descricao: "ABB"},
  { id:  1, descricao: "Carlo Gavazzi"},  
  { id:  2, descricao: "Kron"},
  { id:  3, descricao: "Nansen"},
  { id:  4, descricao: "Polus"},
  { id:  5, descricao: "Schneider"},
  { id:  6, descricao: "Siemens"},
  { id:  7, descricao: "WEG"},
];

export const ListaMedidorModelo =  [
  { id: 0, id_fabricante: 0, descricao: "NEXUS 2"},

  { id: 0, id_fabricante: 1, descricao: "EM330"},
  { id: 1, id_fabricante: 1, descricao: "EM340"},
  { id: 2, id_fabricante: 1, descricao: "EM24"},
  { id: 3, id_fabricante: 1, descricao: "EM210"},
  { id: 4, id_fabricante: 1, descricao: "WM30"},
  
  { id: 0, id_fabricante: 2, descricao: "Multi K 05"},
  { id: 1, id_fabricante: 2, descricao: "Multi K 120"},
  { id: 2, id_fabricante: 2, descricao: "Multi K"},
  { id: 3, id_fabricante: 2, descricao: "TKE-01"},

  { id: 0, id_fabricante: 3, descricao: "SPECTRUM K"},

  { id: 0, id_fabricante: 4, descricao: "MT 100"},
  { id: 1, id_fabricante: 4, descricao: "PL-M2"},

  { id: 0, id_fabricante: 5, descricao: "PM 1200"},
  { id: 1, id_fabricante: 5, descricao: "PM 2100"},
  { id: 2, id_fabricante: 5, descricao: "ION 6800"},
  { id: 3, id_fabricante: 5, descricao: "ION 7650"},
  { id: 4, id_fabricante: 5, descricao: "ION 8600"},

  { id: 0, id_fabricante: 6, descricao: "PAC 1020"},
  { id: 1, id_fabricante: 6, descricao: "SMART 7KT"},
  { id: 2, id_fabricante: 6, descricao: "PAC 3100"},
  { id: 3, id_fabricante: 6, descricao: "PAC 3200"},

  { id: 0, id_fabricante: 7, descricao: "MMW03"},
  { id: 1, id_fabricante: 7, descricao: "MMW03 CH"},

];

// etapas de instalação
export const DetalhesConcluidoOSHoje = () => {

  var detalhes_os = {} as EtapasInstalacao; 

  detalhes_os = {
    cliente: 'ABC Ltda',
    numero_os: '12839821701',
    tipo_os : 0,
    status_os: 0,
    latitude: -23.5930874,
    longitude: -46.6828586,
    horario_ini: '08:15:23',
    horario_fim: '10:07:00',
    descricao_problema: '',
    codigo_probe: '4C752584CC2C',
    tipo_conexao: 0,
    operadora: 1,
    ip:'',
    mascara:'',
    gateway:'',
    ssid:'',
    ssid_senha:'',
    medidor_fabricante: 1,
    medidor_modelo: 1,            
    medidor_ns: '57678/888',
    teste_conexao: true,    
    imagem_local1: '',
    imagem_texto_local1: '',
    imagem_local2: '',
    imagem_texto_local2: '',
    imagem_local3: '',
    imagem_texto_local3: '',
    imagem_local4: '',
    imagem_texto_local4: '',
    imagem_local5: '',
    imagem_texto_local5: '',
    imagem_local6: '',
    imagem_texto_local6: '',
    imagem_local7: '',
    imagem_texto_local7: '',
    imagem_local8: '',
    imagem_texto_local8: '',
    totalMaterial1: 2,
    textoMaterial1: 'Antena',
    totalMaterial2: 0,
    textoMaterial2: 'Caixa Externa',
    totalMaterial3: 1,
    textoMaterial3: 'Duplicador',
    totalMaterial4: 1,
    textoMaterial4: 'Conversor',
    totalMaterial5: 0,
    textoMaterial5: 'Cabo Externo',
    totalMaterial6: 2,
    textoMaterial6: 'Cabo Interno',
    materialExtra: true,
    textoMaterialExtra:'2 metros de fio 3mm',    
    imagem_instalacao1: '',
    imagem_texto_instalacao1: '',
    imagem_instalacao2: '',
    imagem_texto_instalacao2: '',
    imagem_instalacao3: '',
    imagem_texto_instalacao3: '',
    imagem_instalacao4: '',
    imagem_texto_instalacao4: '',
    imagem_instalacao5: '',
    imagem_texto_instalacao5: '',
    imagem_instalacao6: '',
    imagem_texto_instalacao6: '',    
    imagem_retirada1: '',
    imagem_texto_retirada1: '',
    imagem_retirada2: '',
    imagem_texto_retirada2: '',
    imagem_retirada3: '',
    imagem_texto_retirada3: '',
    imagem_retirada4: '',
    imagem_texto_retirada4: '',
    observacoes: 'Nada consta.',
    imagem_assinatura: '',
    cliente_nome: '',
    cliente_telefone: '',
    concorda: true,
    step1: false,
    step2: false,
    step3: false,
    step4: false,
    step5: false,
    step6: false,
    step7: false,
  };

  return detalhes_os;

};



// etapas de instalação
export const EtapasInstalacaoFro1Hoje = () => {
        
  var etapasInstalacao = {} as EtapasInstalacao; 

  etapasInstalacao = {
    cliente: '',
    numero_os: '',
    tipo_os : 0,
    status_os: 0,
    latitude: -23.5930874,
    longitude: -46.6828586,
    horario_ini: '00:00:00',
    horario_fim: '00:00:00',
    descricao_problema: '',
    codigo_probe: '',
    tipo_conexao: 3,
    operadora: 4,
    ip:'',
    mascara:'',
    gateway:'',
    ssid:'',
    ssid_senha:'',    
    medidor_fabricante: 0,
    medidor_modelo: 0,            
    medidor_ns: '',
    teste_conexao: false,    
    imagem_local1: '',
    imagem_texto_local1: '',
    imagem_local2: '',
    imagem_texto_local2: '',
    imagem_local3: '',
    imagem_texto_local3: '',
    imagem_local4: '',
    imagem_texto_local4: '',
    imagem_local5: '',
    imagem_texto_local5: '',
    imagem_local6: '',
    imagem_texto_local6: '',
    imagem_local7: '',
    imagem_texto_local7: '',
    imagem_local8: '',
    imagem_texto_local8: '',
    totalMaterial1: 0,
    textoMaterial1: 'Antena',
    totalMaterial2: 0,
    textoMaterial2: 'Caixa Externa',
    totalMaterial3: 0,
    textoMaterial3: 'Duplicador',
    totalMaterial4: 0,
    textoMaterial4: 'Conversor',
    totalMaterial5: 0,
    textoMaterial5: 'Cabo Externo',
    totalMaterial6: 0,
    textoMaterial6: 'Cabo Interno',
    materialExtra: false,
    textoMaterialExtra:'',    
    imagem_instalacao1: '',
    imagem_texto_instalacao1: '',
    imagem_instalacao2: '',
    imagem_texto_instalacao2: '',
    imagem_instalacao3: '',
    imagem_texto_instalacao3: '',
    imagem_instalacao4: '',
    imagem_texto_instalacao4: '',
    imagem_instalacao5: '',
    imagem_texto_instalacao5: '',
    imagem_instalacao6: '',
    imagem_texto_instalacao6: '',    
    imagem_retirada1: '',
    imagem_texto_retirada1: '',
    imagem_retirada2: '',
    imagem_texto_retirada2: '',
    imagem_retirada3: '',
    imagem_texto_retirada3: '',
    imagem_retirada4: '',
    imagem_texto_retirada4: '',    
    imagem_assinatura: '',
    observacoes: '',
    cliente_nome: '',
    cliente_telefone: '',
    concorda: true,
    step1: false,
    step2: false,
    step3: false,
    step4: false,
    step5: false,
    step6: false,
    step7: false,
  };

  return etapasInstalacao;
}

// etapas de troca
export const EtapasTrocaFro2Hoje = () => {      
        
  var etapasTroca = {} as EtapasInstalacao; 

  etapasTroca = {
    cliente: '',
    numero_os: '',
    tipo_os : 0,
    status_os: 0,
    latitude: 0,
    longitude: 0,
    horario_ini: '00:00:00',
    horario_fim: '00:00:00',
    descricao_problema: 'Probe não está comunicando.',
    codigo_probe: '',
    tipo_conexao: 2,
    operadora: 4,
    ip:'',
    mascara:'',
    gateway:'',
    ssid:'',
    ssid_senha:'',    
    medidor_fabricante: 0,
    medidor_modelo: 0,            
    medidor_ns: '',
    teste_conexao: false,    
    imagem_local1: '',
    imagem_texto_local1: '',
    imagem_local2: '',
    imagem_texto_local2: '',
    imagem_local3: '',
    imagem_texto_local3: '',
    imagem_local4: '',
    imagem_texto_local4: '',
    imagem_local5: '',
    imagem_texto_local5: '',
    imagem_local6: '',
    imagem_texto_local6: '',
    imagem_local7: '',
    imagem_texto_local7: '',
    imagem_local8: '',
    imagem_texto_local8: '',
    totalMaterial1: 0,
    textoMaterial1: 'Antena',
    totalMaterial2: 0,
    textoMaterial2: 'Caixa Externa',
    totalMaterial3: 0,
    textoMaterial3: 'Duplicador',
    totalMaterial4: 0,
    textoMaterial4: 'Conversor',
    totalMaterial5: 0,
    textoMaterial5: 'Cabo Externo',
    totalMaterial6: 0,
    textoMaterial6: 'Cabo Interno',
    materialExtra: false,
    textoMaterialExtra:'',    
    imagem_instalacao1: '',
    imagem_texto_instalacao1: '',
    imagem_instalacao2: '',
    imagem_texto_instalacao2: '',
    imagem_instalacao3: '',
    imagem_texto_instalacao3: '',
    imagem_instalacao4: '',
    imagem_texto_instalacao4: '',
    imagem_instalacao5: '',
    imagem_texto_instalacao5: '',
    imagem_instalacao6: '',
    imagem_texto_instalacao6: '',    
    imagem_retirada1: '',
    imagem_texto_retirada1: '',
    imagem_retirada2: '',
    imagem_texto_retirada2: '',
    imagem_retirada3: '',
    imagem_texto_retirada3: '',
    imagem_retirada4: '',
    imagem_texto_retirada4: '',
    observacoes: '',
    imagem_assinatura: '',
    cliente_nome: '',
    cliente_telefone: '',    
    concorda: true,
    step1: false,
    step2: false,
    step3: false,
    step4: false,
    step5: false,
    step6: false,
    step7: false,
  };

  return etapasTroca;
}

// etapas de manutenção
export const EtapasManutencaoHoje = () => {
        
  var etapasManutencao = {} as EtapasInstalacao; 

  etapasManutencao = {
      cliente: '',
      numero_os: '',
      tipo_os : 0,
      status_os: 0,
      latitude: 0,
      longitude: 0,
      horario_ini: '00:00:00',
      horario_fim: '00:00:00',
      descricao_problema: 'Probe não comunica com o servidor desde o dia 10/06/2024 às 08:00.',
      codigo_probe: '',
      tipo_conexao: 0,
      operadora: 4,
      ip:'',
      mascara:'',
      gateway:'',
      ssid:'',
      ssid_senha:'',      
      medidor_fabricante: 0,
      medidor_modelo: 0,            
      medidor_ns: '1234888/999',
      teste_conexao: false,    
      imagem_local1: '',
      imagem_texto_local1: '',
      imagem_local2: '',
      imagem_texto_local2: '',
      imagem_local3: '',
      imagem_texto_local3: '',
      imagem_local4: '',
      imagem_texto_local4: '',
      imagem_local5: '',
      imagem_texto_local5: '',
      imagem_local6: '',
      imagem_texto_local6: '',
      imagem_local7: '',
      imagem_texto_local7: '',
      imagem_local8: '',
      imagem_texto_local8: '',
      totalMaterial1: 0,
      textoMaterial1: 'Antena',
      totalMaterial2: 0,
      textoMaterial2: 'Caixa Externa',
      totalMaterial3: 0,
      textoMaterial3: 'Duplicador',
      totalMaterial4: 0,
      textoMaterial4: 'Conversor',
      totalMaterial5: 0,
      textoMaterial5: 'Cabo Externo',
      totalMaterial6: 0,
      textoMaterial6: 'Cabo Interno',
      materialExtra: false,
      textoMaterialExtra:'',
      imagem_instalacao1: '',
      imagem_texto_instalacao1: '',
      imagem_instalacao2: '',
      imagem_texto_instalacao2: '',
      imagem_instalacao3: '',
      imagem_texto_instalacao3: '',
      imagem_instalacao4: '',
      imagem_texto_instalacao4: '',
      imagem_instalacao5: '',
      imagem_texto_instalacao5: '',
      imagem_instalacao6: '',
      imagem_texto_instalacao6: '',
      imagem_retirada1: '',
      imagem_texto_retirada1: '',
      imagem_retirada2: '',
      imagem_texto_retirada2: '',
      imagem_retirada3: '',
      imagem_texto_retirada3: '',
      imagem_retirada4: '',
      imagem_texto_retirada4: '',
      observacoes: '',
      imagem_assinatura: '',      
      cliente_nome: '',
      cliente_telefone: '',      
      concorda: true,
      step1: false,
      step2: false,
      step3: false,
      step4: false,
      step5: false,
      step6: false,
      step7: false,
  };

  return etapasManutencao;
}

// etapas da visita
export const EtapasVisitaHoje = () => {
        
  var etapasVisita = {} as EtapasInstalacao; 

  etapasVisita = {
      cliente: '',
      numero_os: '',
      tipo_os : 0,
      status_os: 0,
      latitude: 0,
      longitude: 0,
      horario_ini: '00:00:00',
      horario_fim: '00:00:00',
      descricao_problema: 'Procurar encarregado do setor para maiores informações',
      codigo_probe: '4C752584CC2C',
      tipo_conexao: 0,
      operadora: 2,
      ip:'',
      mascara:'',
      gateway:'',
      ssid:'',
      ssid_senha:'',      
      medidor_fabricante: 0,
      medidor_modelo: 0,            
      medidor_ns: '1234888/999',
      teste_conexao: false,
      imagem_local1: '',
      imagem_texto_local1: '',
      imagem_local2: '',
      imagem_texto_local2: '',
      imagem_local3: '',
      imagem_texto_local3: '',
      imagem_local4: '',
      imagem_texto_local4: '',
      imagem_local5: '',
      imagem_texto_local5: '',
      imagem_local6: '',
      imagem_texto_local6: '',
      imagem_local7: '',
      imagem_texto_local7: '',
      imagem_local8: '',
      imagem_texto_local8: '',
      totalMaterial1: 0,
      textoMaterial1: 'Antena',
      totalMaterial2: 0,
      textoMaterial2: 'Caixa Externa',
      totalMaterial3: 0,
      textoMaterial3: 'Duplicador',
      totalMaterial4: 0,
      textoMaterial4: 'Conversor',
      totalMaterial5: 0,
      textoMaterial5: 'Cabo Externo',
      totalMaterial6: 0,
      textoMaterial6: 'Cabo Interno',
      materialExtra: false,
      textoMaterialExtra:'',
      imagem_instalacao1: '',
      imagem_texto_instalacao1: '',
      imagem_instalacao2: '',
      imagem_texto_instalacao2: '',
      imagem_instalacao3: '',
      imagem_texto_instalacao3: '',
      imagem_instalacao4: '',
      imagem_texto_instalacao4: '',
      imagem_instalacao5: '',
      imagem_texto_instalacao5: '',
      imagem_instalacao6: '',
      imagem_texto_instalacao6: '',      
      imagem_retirada1: '',
      imagem_texto_retirada1: '',
      imagem_retirada2: '',
      imagem_texto_retirada2: '',
      imagem_retirada3: '',
      imagem_texto_retirada3: '',
      imagem_retirada4: '',
      imagem_texto_retirada4: '',
      observacoes: '',
      imagem_assinatura: '', 
      cliente_nome: '',
      cliente_telefone: '',           
      concorda:true,
      step1: false,
      step2: false,
      step3: false,
      step4: false,
      step5: false,
      step6: false,
      step7: false,
  };

  return etapasVisita;
}

// etapas de retirada
export const EtapasRetiradaFro3Hoje = () => {      
        
  var etapasRetirada = {} as EtapasInstalacao; 

  etapasRetirada = {
    cliente: '',
    numero_os: '',
    tipo_os : 0,
    status_os: 0,
    latitude: 0,
    longitude: 0,
    horario_ini: '00:00:00',
    horario_fim: '00:00:00',
    descricao_problema: '',
    codigo_probe: '4C752584CC2C',
    tipo_conexao: 0,
    operadora: 3,
    ip:'',
    mascara:'',
    gateway:'',
    ssid:'',
    ssid_senha:'',    
    medidor_fabricante: 0,
    medidor_modelo: 0,            
    medidor_ns: '888/999-00',
    teste_conexao: false,
    imagem_local1: '',
    imagem_texto_local1: '',
    imagem_local2: '',
    imagem_texto_local2: '',
    imagem_local3: '',
    imagem_texto_local3: '',
    imagem_local4: '',
    imagem_texto_local4: '',
    imagem_local5: '',
    imagem_texto_local5: '',
    imagem_local6: '',
    imagem_texto_local6: '',
    imagem_local7: '',
    imagem_texto_local7: '',
    imagem_local8: '',
    imagem_texto_local8: '',
    totalMaterial1: 0,
    textoMaterial1: 'Antena',
    totalMaterial2: 0,
    textoMaterial2: 'Caixa Externa',
    totalMaterial3: 0,
    textoMaterial3: 'Duplicador',
    totalMaterial4: 0,
    textoMaterial4: 'Conversor',
    totalMaterial5: 0,
    textoMaterial5: 'Cabo Externo',
    totalMaterial6: 0,
    textoMaterial6: 'Cabo Interno',
    materialExtra: false,
    textoMaterialExtra:'',
    imagem_instalacao1: '',
    imagem_texto_instalacao1: '',
    imagem_instalacao2: '',
    imagem_texto_instalacao2: '',
    imagem_instalacao3: '',
    imagem_texto_instalacao3: '',
    imagem_instalacao4: '',
    imagem_texto_instalacao4: '',
    imagem_instalacao5: '',
    imagem_texto_instalacao5: '',    
    imagem_texto_instalacao6: '',
    imagem_retirada1: '',
    imagem_texto_retirada1: '',
    imagem_retirada2: '',
    imagem_texto_retirada2: '',
    imagem_retirada3: '',
    imagem_texto_retirada3: '',
    imagem_retirada4: '',
    imagem_texto_retirada4: '',
    observacoes: '',
    imagem_assinatura: '',
    cliente_nome: '',
    cliente_telefone: '',    
    concorda:true,
    step1: false,
    step2: false,
    step3: false,
    step4: false,
    step5: false,
    step6: false,
  };

  return etapasRetirada;
}

// estrutura de configuração da probe
export interface ProbeSettings {

  user?: string,
  id?: string,
  firmware?: string,
  datetime?: number,
  sinal?: number,
  bateria?: number,
  vBateria?: number,
  tipoConexao?: number,
  tipoOperadora?: number,
  v_probe?: number,
  firmwareBoot : number,
  excecoes : number,
  resets: number,
  heap : number,
  totalMemory: number, 
  usedMemory: number,   
  sistema?: boolean,
  rede?: boolean,
  modbus?: boolean,
  codi?: boolean,
  ion?: boolean,
  pulso?: boolean,
  ota?: boolean,

  wifiNome?: string,
  wifiSenha?: string,
  wifiIP?: string,
  wifiGateway?: string,
  wifiMascara?: string,
  wifiDNS?: string,
  wifiSinal?: number,
  wifiMAC?: string,

  gsmRede?: string,
  gsmModem?: string,
  gsmIMEI?: string,
  gsmIMSI?: string,
  gsmICCID?: string,
  gsmSinal?: number,
  gsmOperadora?: string,
  gsmAPN?: string,
  gsmIP?: string,
  gsmGateway?: string,
  gsmMascara?: string,
  gsmLatitude?: number,
  gsmLongitude?: number,

  ethernetIP?: string,
  ethernetMascara?: string,
  ethernetGateway?: string,
  ethernetDNS?: string,
  ethernetMAC?: string,
  ethernetDHCP?: number,  

  modbusDevices?: modbusSettings[],

  codiDevices?: codiSettings[],

  ionDevices?: ionSettings[],

  pulsoDevices?: pulsoSettings[],
}


// estrutura inicial de configuração da probe
export const ProbeSettingsInicial = () => {      
        
  var probeSettings = {} as ProbeSettings;

  probeSettings = {

    user: 'App',
    id : '',
    firmware : 'Firmware',
    datetime : 946692000, // 01/01/2000 00:00    
    sinal : 9999,
    bateria : 9999,
    vBateria: 9999,
    tipoConexao : 9999,
    tipoOperadora : 9999,
    v_probe : 9999,    
    firmwareBoot : 946692000, // 01/01/2000 00:00 
    excecoes : 0,
    resets: 0,
    heap : 0,
    totalMemory: 0, 
    usedMemory: 0,
    sistema : false,
    rede : false,
    modbus : false,
    codi : false,
    ion : false,
    pulso: false,
    ota : false,

    wifiNome: '',
    wifiSenha: '',
    wifiIP: '0.0.0.0',
    wifiGateway: '0.0.0.0',
    wifiMascara: '0.0.0.0',
    wifiDNS: '0.0.0.0',
    wifiSinal: 0,
    wifiMAC: '00:00:00:00:00:00',
  
    gsmRede: '---',
    gsmModem: '---',
    gsmIMEI: '---',
    gsmIMSI: '---',
    gsmICCID: '---',
    gsmSinal: 0,
    gsmOperadora: '---',
    gsmAPN: '---',
    gsmIP: '0.0.0.0',
    gsmGateway: '0.0.0.0',
    gsmMascara: '0.0.0.0',
    gsmLatitude: 0,
    gsmLongitude: 0,

    ethernetIP: '0.0.0.0',
    ethernetMascara: '0.0.0.0',
    ethernetGateway: '0.0.0.0',
    ethernetDNS: '0.0.0.0',
    ethernetMAC: '00:00:00:00:00:00',
    ethernetDHCP: 0, 
    
    modbusDevices: [],

    codiDevices: [],

    ionDevices: [],

    pulsoDevices: [],
  }

  return probeSettings;
} 

// estrutura de configuração dos equipamentos configurados na probe
export type modbusSettings = {
  id_device?: number,
  nameDevice?: string,
  endianessDevice?: number,
  slaves?: SlavesSettings[],
}

// estrutura inicial de configuração dos equipamentos configurados na probe
export const DeviceSettingsInicial = () : modbusSettings[] => {

  var deviceSettings = {} as modbusSettings;

  deviceSettings = {
    id_device: -1,
    nameDevice: '',
    endianessDevice: -1,
    slaves: [],
  }

  return [deviceSettings];    
}

// insere um device as configurações da Probe
export const AddDevice = (id: number, nome:string, endianess: number) : modbusSettings => {

  var deviceSettings = {} as modbusSettings;
  
  deviceSettings = {
    id_device: id,
    nameDevice: nome,
    endianessDevice: endianess,
    slaves: [],
  }

  return deviceSettings;    
}

// estrutura de configuração das variaveis configuradas nos equipamentos
export type SlavesSettings = {

  id_slave?: number,
  statusSlave?: boolean,
  nomeSlave?: string,
  enderecoSlave?: string,  
  tempoEnvioSlave?: number,
  protocoloSlave?: number,
  paridadeSlave?: number,
  baudeRateSlave?: number,
  ipSlave?:string,
  portSlave?:number,
  conexaoSlave?:number,

  variables?: VariablesSettings[],
}

// estrutura inicial de configuração das variaveis configuradas nos equipamentos
export const SlavesSettingsInicial = () : SlavesSettings[] => {

  var slaveSettings = {} as SlavesSettings;

  slaveSettings = {
    id_slave: -1,
    statusSlave: false,
    nomeSlave: '',
    enderecoSlave: '',  
    tempoEnvioSlave: -1,
    protocoloSlave: -1,
    paridadeSlave: -1,
    baudeRateSlave: -1,
    ipSlave: '',
    portSlave: 0,
    conexaoSlave: -1,    

    variables: VariableSettingsInicial(),
  }
  
  return [slaveSettings];
}

// estrutura de configuração das variaveis configuradas nos equipamentos
export type VariablesSettings = {

  nomeVariable?: string,
  descricaoVariable?: string,
  valorMediaVariable?: boolean,
  valorUltimaVariable?: boolean,
  funcaoVariable?: number,
  fatorVariable?: number,
  enderecoVariable?: number,
  formatoVariable?: number,
}


// estrutura inicial de configuração das variaveis configuradas nos equipamentos
export const VariableSettingsInicial = () : VariablesSettings[] => {

  var variableSettings = {} as VariablesSettings;

  variableSettings = {

      nomeVariable: '',
      descricaoVariable: '',
      valorMediaVariable: false,
      valorUltimaVariable: false,
      funcaoVariable: 0,
      fatorVariable: 0,
      enderecoVariable: 0,
      formatoVariable: 0,
  }
  
  return [variableSettings];
}

// estrutura de configuração dos equipamentos CODI configurados na probe
export type codiSettings = {
  id_codi?: number,
  codi?: string,
  replicar?: boolean,  
  invertido?: boolean,
  protocolo?: number,
  paridade?: number,
  baudRate?: number,
  repo?: boolean,
}

// estrutura de configuração do equipamento ion configurados na probe
export type ionSettings = {
  id_ion?: number,
  ion?: string,
  port?:number,
  paridade?: number,
  baudRate?: number,
}

// insere um ion as configurações da Probe
export const AddIon = (id: number, tipo_ion:string, port_: number, paridade_: number, baudRate_: number) : ionSettings => {

  var ion_settings = {} as ionSettings;
    
  ion_settings = {
    id_ion: id,
    ion: tipo_ion,
    port: port_,    
    paridade: paridade_,
    baudRate: baudRate_,
  }

  return ion_settings;    
}

// estrutura de configuração dos contadores de pulso configurados na probe
export type pulsoSettings = {  
  pulso?: string,
  port?:number,
  type?: number,
  contact?: number,
  //edge?: number,
  reactive?: boolean,
  repo?: boolean,
  factor?: Double,
  offset_time?: number,
  //window_size?: number,
  send_time?: number,
  send_time_repo?: number,
  //repo_name?: string,
  //var_name?: string,
}

// insere um contador de pulso as configurações da Probe
export const AddPulso = ( tipo_pulso:string, 
                          port: number, 
                          tipo_medicao: number,
                          tipo_contato: number, 
                          reativo: boolean, 
                          repo: boolean,
                          tempo_repo: number,
                          fator: Double,
                          //deslocamento: number,
                          tempo_envio: number) : pulsoSettings => {

  var pulso_settings = {} as pulsoSettings;
    
  pulso_settings = {    
    pulso: tipo_pulso,
    port: port,
    type: tipo_medicao,
    contact: tipo_contato,
    reactive: reativo,
    repo: repo,
    factor: fator,
    //offset_time: deslocamento,    
    send_time: tempo_envio,
    send_time_repo: tempo_repo}

  return pulso_settings;    
}

export type VariablesOK = {
  device?: number,
  variable?: number,
}

export type VariablesMeanOK = {
  device?: number,
  slave?: number
  variable?: number,
}

export type SlavesOK = {
  device?: number,
  slave?: number,
}

export interface FilesProbe {
  id?: number,
  name?: string,
  size?: string,
  timer?: string,
};

/* estrutura de configuração das apn's */
export type ApnSettings = {  
  apn?: string,
  user?: string,
  pass?: string,
}

// estrutura inicial de configuração das apn's
export const ApnSettingsInicial = () : ApnSettings[] => {

  let apnSettings = {
                      apn: '',
                      user: '',
                      pass: '',
                    } as ApnSettings;
  
  return [apnSettings];
}
