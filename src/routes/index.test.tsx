import { NavigationContainer } from "@react-navigation/native";
import { render, screen } from "@testing-library/react-native";
import React from 'react';
import Routes from ".";

jest.mock('@react-native-community/netinfo', () => ({
    fetch: jest.fn(() => Promise.resolve({ isConnected: true })),
    addEventListener: jest.fn(callback => {
        // Dispara o callback com isConnected = null
        callback({ isConnected: null });
        // Dispara o callback novamente com isConnected = true
        callback({ isConnected: true });
        return jest.fn(); // Mock para unsubscribe
    }),

    useNetInfo: jest.fn()
}));

jest.mock('react-native-webview', () => 'WebView');
jest.mock('@react-navigation/drawer', () => 'createDrawerNavigator');
jest.mock('rn-slide-button', () => 'SlideButton');
jest.mock('expo-location', () => 'getCurrentPositionAsync');
jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');
jest.mock('sp-react-native-mqtt', () => 'MQTT');
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');
jest.mock('@shopify/react-native-skia', () => 'Canvas');
jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');
jest.mock('react-native-view-shot', () => 'ViewShot');
jest.mock('react-native-share', () => 'Share');

describe('Routes', () => {

    it('renderiza corretamente a tela', () => {

        const { unmount } = render(<NavigationContainer>
            <Routes />
        </NavigationContainer>);

        expect(screen).toBeTruthy();

        unmount(); // Garante que o componente seja desmontado no final do teste 
    });

});
