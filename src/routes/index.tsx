import { createNativeStackNavigator, NativeStackNavigationProp } from '@react-navigation/native-stack';
import React from 'react';

// telas
import { ErroAuth } from '../pages/ErroAuth';
import { <PERSON>rro<PERSON>ognito } from '../pages/ErroCognito';
import SignIn from '../pages/Login/SignIn';
import Logout from '../pages/Logout';
import MenuPrincipal from '../pages/MenuPrincipal/index';
import PrimeirosPassos from '../pages/PrimeirosPassos/index';
import Probe from '../pages/Probe/index';
import ProbeION from '../pages/Probe/Ion/index';
import Khomp from '../pages/Probe/Khomp/index';
import ProbeModbus from '../pages/Probe/Modbus/index';
import ProbeCODI from '../pages/Probe/ProbeCODI';
import ProbeID from '../pages/Probe/ProbeID';
import ProbeOTA from '../pages/Probe/ProbeOTA';
import ProbePulso from '../pages/Probe/ProbePulso';
import ProbeRede from '../pages/Probe/ProbeRede';
import ProbeSistema from '../pages/Probe/ProbeSistema';
import { SemAcesso } from '../pages/SemAcesso';
import { Splash } from '../pages/Splash';

// dados de configuração da probe
import { ProbeSettings } from '../data';

const Stack = createNativeStackNavigator();

// rotas de navegação
type StackNavigation = {
    Splash: undefined;
    SignIn: { hasInternet: boolean };
    Logout: undefined;
    SemAcesso: undefined;
    ErroCognito: undefined;
    ErroAuth: { erro: string };
    PrimeirosPassos: undefined;
    MenuPrincipal: undefined;

    Probe: { probeSettings: ProbeSettings };
    ProbeID: { id: string }
    ProbeSistema: { probeSettings: ProbeSettings };
    ProbeRede: { probeSettings: ProbeSettings };
    ProbeModbus: { probeSettings: ProbeSettings };
    ProbeCODI: { probeSettings: ProbeSettings };
    ProbeION: { probeSettings: ProbeSettings };
    ProbePulso: { probeSettings: ProbeSettings };
    ProbeOTA: undefined;
    Khomp: { probeSettings: ProbeSettings }
};

// exporta as rotas de navegação
export type StackTypes = NativeStackNavigationProp<StackNavigation>;

export default function Routes() {
    return (

        <Stack.Navigator>

            <Stack.Screen
                name="Splash"
                component={Splash}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="SignIn"
                component={SignIn}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="Logout"
                component={Logout}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="SemAcesso"
                component={SemAcesso}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ErroCognito"
                component={ErroCognito}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ErroAuth"
                component={ErroAuth}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="PrimeirosPassos"
                component={PrimeirosPassos}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="MenuPrincipal"
                component={MenuPrincipal}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="Probe"
                component={Probe}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeID"
                component={ProbeID}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeSistema"
                component={ProbeSistema}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeRede"
                component={ProbeRede}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeModbus"
                component={ProbeModbus}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeCODI"
                component={ProbeCODI}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeION"
                component={ProbeION}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbePulso"
                component={ProbePulso}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="ProbeOTA"
                component={ProbeOTA}
                options={{ headerShown: false }}
            />

            <Stack.Screen
                name="Khomp"
                component={Khomp}
                options={{ headerShown: false }}
            />

        </Stack.Navigator>
    )
}
