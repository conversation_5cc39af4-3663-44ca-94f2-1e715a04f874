
import React from "react";
import { Splash } from ".";
import { NavigationContainer } from "@react-navigation/native";
import { act, render, screen } from "@testing-library/react-native";

jest.mock('@react-native-community/netinfo', () => ({
    fetch: jest.fn(() => Promise.resolve({ isConnected: true })),
    addEventListener: jest.fn(callback => {
        // Dispara o callback com isConnected = false
        callback({ isConnected: null });
        // Dispara o callback novamente com isConnected = true
        callback({ isConnected: true });
        return jest.fn(); // Mock para unsubscribe
    }),

    useNetInfo: jest.fn()
}));


jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
      useFocusEffect: jest.fn(effect => {
        const cleanup = effect(); // Executa o efeito e armazena a função de limpeza
        return cleanup; // Retorna a função de limpeza simulada
    }),    
    };
});

jest.useFakeTimers();

describe('Tela Splash', () => {

    beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
    });

    it('renderiza corretamente a tela', async () => {
    
        
        const {unmount} = render(  <NavigationContainer>
                                <Splash />
                            </NavigationContainer> );

        // Simula avanço no tempo para disparar o timeout
        act(() => {
            jest.advanceTimersByTime(1500);
        });

        expect(screen).toBeTruthy();
        
        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('renderiza corretamente a tela e lida com isConnected=false', async () => {

        const {unmount} = render(
            <NavigationContainer>
                <Splash />
            </NavigationContainer>
        );

        // Simula avanço no tempo para disparar o timeout
        act(() => {
            jest.advanceTimersByTime(1500);
        });

        // Verifica se a tela foi renderizada
        expect(screen).toBeTruthy();

        // Simula mudança de estado da conexão
        act(() => {
            jest.advanceTimersByTime(500); // Simula a execução do callback do addEventListener
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });  

    it("verifica mudanças no estado da conexão", () => {
        // Renderiza o componente
        const {unmount} = render(
            <NavigationContainer>
                <Splash />
            </NavigationContainer>
        );

        // Avança o tempo para simular o timeout
        act(() => {
            jest.advanceTimersByTime(1500);
        });

        // Verifica se addEventListener foi chamado
        const mockAddEventListener = require("@react-native-community/netinfo").addEventListener;
        expect(mockAddEventListener).toHaveBeenCalled();
        expect(mockAddEventListener).toHaveBeenCalledTimes(1);

        unmount(); // Garante que o componente seja desmontado no final do teste
    });
        
});
