import React, { useEffect, useState } from 'react';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {View} from 'react-native';
import NetInfo from '@react-native-community/netinfo'

// logo Zordon
import LogoZordon from '../../assets/svg/logo_zordon-vertical.svg'

// Style
import { styles } from './layout';

// rotas
import { StackTypes } from '../../routes';


export function Splash() {

    const [hasInternet, setHasInternet] = useState<boolean>(false);
    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
  
    // navega para pagina login
    const goToSignin = (temInternet:boolean) => {
        
        navigation.navigate("SignIn", {hasInternet: temInternet})
    };  
    
    useFocusEffect(() => {
            
        const timeout = setTimeout(() => {
            goToSignin(hasInternet);
        }, 1500);

        return () => clearTimeout(timeout);
    });    

    // verifica se existe internet
    useEffect(() => {
    
        NetInfo.fetch().then(state => {
            if (state.isConnected !== null) {
                setHasInternet(state.isConnected);
            }
        });

        // Escuta as mudanças no estado da conexão
        const unsubscribe = NetInfo.addEventListener(state => {
            if (state.isConnected !== null) {
                setHasInternet(state.isConnected);
            }
        });

        // Limpa a escuta ao desmontar o componente
        return unsubscribe;
        
    }, []);

    return (
        <View style={styles.containerTela}>
            <LogoZordon />
        </View>
    );
}
