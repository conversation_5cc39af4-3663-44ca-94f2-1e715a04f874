import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({

    containerTela: {
      flex:1,
      paddingTop: 100,  
      backgroundColor: '#fff',
      alignItems: "center",
    },
  
    containerPrincipal:{
      padding:0,
      width:375,
      height:546,
      gap:32,
      alignItems:"center",
      backgroundColor: '#fff'
      
    },
  
    containerHeader:{
      width:343,
      height:128,    
      gap:0,
      alignItems: "center",
      backgroundColor: '#fff'
    },
  
    containerFooter:{
      width: 343,
      height: 22,
      gap: 4,
      justifyContent: "center",
      alignItems: "center",
      flexDirection: "row",
    },
  
    containerHeaderLogo:{
      width:343,
      height:64,
      alignItems: "center",
      justifyContent: "center"
    },  
  
    containerHeaderTexto:{
      width:343,
      height:64,    
      alignItems: "center",
    },  
  
    containerConteudo:{
      width:343,
      height:332,
      gap:24,
    },
  
    containerFormulario:{
      width:343,
      height:160,
      gap:20,
    }, 
  
    containerFormularioEmail:{
      width:343,
      height:70,
      gap:6,
    },
    
    containerFormularioSenha:{
      width:343,
      height:70,
      gap:6,
    },  
  
    containerOpcao:{
      width:343,
      height:20, 
      flexDirection:"row",
    },
  
    containerBotoes:{
      width:343,
      height:104,
      gap:16,
    },
  
    containerCheckbox:{
      width: 206,
      height: 20,
      gap: 8,
      flexDirection: "row", 
    },
  
    containerIcon:{
      width: 24,
      height:24,
      borderRadius:2,    
      flexDirection:"row"
    },
  
    containerIcon1:{
      width:5.5,
      height:5.5,
      backgroundColor: "#F35325",
    },
  
    containerIcon2:{
      width:5.5,
      height:5.5,
      backgroundColor: "#81BC06",
    },
  
    containerIcon3:{
      width:5.5,
      height:5.5,
      backgroundColor: "#05A6F0",
    },
  
    containerIcon4:{
      width:5.5,
      height:5.5,
      backgroundColor: "#FFBA08",
    },
  
    textoHeaderPrincipal:{
      fontFamily: 'Exo2_600SemiBold',
      fontSize:24,
      fontWeight: "600", 
      lineHeight: 32,   
      color: "#141414",
    },
  
    textoHeaderSub:{
      fontFamily: 'SourceSans3_400Regular',
      fontSize:16,
      fontWeight: "400",
      lineHeight: 24,
      color: "#525252",
    },  
  
    textoEmailFormulario:{
      fontFamily: 'SourceSans3_500Medium',
      fontSize:14,
      lineHeight: 20, 
      color: "#424242"
    },
  
    textoSenhaFormulario:{
      fontFamily: 'SourceSans3_500Medium',
      fontSize:14,
      lineHeight: 20, 
      color: "#424242"
    },  
  
    textoCheckBox:{
      fontFamily: 'SourceSans3_500Medium',
      fontSize:14,
      fontWeight:"500",
      lineHeight: 20, 
      color: "#424242"
    },    
  
    textoEsqueciSenha:{
      fontFamily:"Exo2_600SemiBold",
      fontWeight:"600",
      fontSize:14,
      lineHeight:20,
      color:"#2E8C1F",
    },  
  
    textoButtonEntrar:{
      fontFamily:"Exo2_600SemiBold",
      fontWeight:"600",
      fontSize:16,
      lineHeight:24,
      color:"#141515",
    },
  
    textoButtonMicrosoft:{
      fontFamily:"SourceSans3_600SemiBold",
      fontWeight:"600",
      fontSize:16,
      lineHeight:24,
      color:"#424242",
    },  
  
    textoFooter:{
      fontFamily:"SourceSans3_400Regular",
      fontWeight:"600",
      fontSize:14,
      lineHeight:20,
      color:"#525252",
    },
  
    textoButtonCliqueAqui:{
      fontFamily:"Exo2_600SemiBold",
      fontWeight:"600",
      fontSize:14,
      lineHeight:20,
      color:"#2E8C1F",
    },    
  
    inputEmailFormulario: {    
      width: 343,
      height:44,
      borderWidth: 1,
      borderRadius: 8,
      borderColor: "#D6D6D6",
      paddingHorizontal: 14,
      paddingVertical: 10,
      gap:8,
      backgroundColor: "#FFFFFF",
      shadowColor:"#101828",
      shadowOpacity: 5,
    },
  
    inputSenhaFormulario: {    
      width: 343,
      height:44,
      borderWidth: 1,
      borderRadius: 8,
      borderColor: "#D6D6D6",
      paddingHorizontal: 14,
      paddingVertical: 10,
      gap:8,
      backgroundColor: "#FFFFFF",
      shadowColor:"#101828",
      shadowOpacity: 5,
    },  
  
    buttonEsqueciSenha: {
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },
  
    buttonEntrar: {
      width:343,
      height:44,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: "#45D42E",
      borderRadius:8,
      borderColor:"#45d42E",
      paddingVertical:10,
      paddingHorizontal:16,
      gap:6,
      shadowColor:"#101828",
      shadowOpacity:5,
    },
  
    buttonMicrosoft: {
      width:343,
      height:44,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: "#FFFFFF",
      borderWidth:1,
      borderRadius:8,
      borderColor:"#D6D6D6",
      paddingVertical:10,
      paddingHorizontal:16,
      gap:12,
      shadowColor:"#101828",
      shadowOpacity:5,
      flexDirection: "row",
    },
    
    buttonCliqueAqui: {
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
    },  
  
  });