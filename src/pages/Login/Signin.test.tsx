import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { render, screen } from "@testing-library/react-native";
import SignIn from "./SignIn";

jest.mock('react-native-webview', () => 'WebView');
jest.mock('@react-native-community/netinfo', () => 'NetInfo');
jest.mock('@react-navigation/drawer', () => 'createDrawerNavigator');
jest.mock('rn-slide-button', () => 'SlideButton');
jest.mock('expo-location', () => 'LocationObject');
jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');
jest.mock('sp-react-native-mqtt', () => 'MQTT');
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');
jest.mock('@shopify/react-native-skia', () => 'Canvas');
jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');
jest.mock('react-native-view-shot', () => 'ViewShot');
jest.mock('react-native-share', () => 'Share');

const mockRoute = {
    params: { hasInternet: false}
};

describe('Signin', () => {

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <SignIn route={mockRoute} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    
});