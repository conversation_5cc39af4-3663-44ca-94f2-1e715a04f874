
import { act, fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import Medidores from '.';
import { MessageBoxProvider } from '../../contexts/MessageBoxContext';
import { Protheus } from "../../services/protheus";
import * as storage from '../../storages'; // mock do getStorageJson

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: jest.fn(),
      dispatch: jest.fn(),
      goback: jest.fn(),
    }),
  };
});


jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock do Protheus (opcional para evitar requests reais)
jest.mock('../../services/protheus', () => ({
  Protheus: jest.fn().mockImplementation(() => ({
    medidoresProtheus: jest.fn().mockResolvedValue(200),
  })),
}));

describe('MateriaisCadastrados', () => {

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });

  it('deve renderizar corretamente', () => {
    const { unmount, getByText } = render(
                                            <MessageBoxProvider>
                                              <Medidores />
                                            </MessageBoxProvider>
                                          );

    expect(getByText('MEDIDORES CADASTRADOS')).toBeTruthy();

    unmount();
  });

  it('deve exibir mensagem de erro quando atualiza materiais', async () => {
    const { unmount, getByTestId } = render(
                                              <MessageBoxProvider>
                                                <Medidores />
                                              </MessageBoxProvider>
                                          );
    
    // Simula a chamada para atualizar a lista de materiais
    fireEvent.press(getByTestId('update-button'));

    unmount();
  });

  it('deve exibir mensagem de erro quando atualiza materiais e volta a pagina', async () => {
    const { unmount, getByTestId } = render(
                                              <MessageBoxProvider>
                                                <Medidores />
                                              </MessageBoxProvider>
                                            );
  
    // Simula a chamada para atualizar a lista de materiais
    fireEvent.press(getByTestId('back-button'));

    unmount();
  });    

  it('solicita a lista de medidores cadastrados no protheus com sucesso', async () => {

    jest.spyOn(storage, 'getStorageJson').mockResolvedValue([
      { IdMedidor: '001', DescMed: 'MEDIDOR 1', VlrKE: '10' },
    ]);

    // Mocka Protheus retornando 200
    const mockMedidores = jest.fn().mockResolvedValue(200);
    (Protheus as jest.Mock).mockImplementation(() => ({
      medidoresProtheus: mockMedidores,
    }));

    const { unmount } = render(
                                <MessageBoxProvider>
                                  <Medidores />
                                </MessageBoxProvider>
                              );

    await act(async () => {
      jest.advanceTimersByTime(1000);
      await Promise.resolve(); // força a execução do .then da Promise
    });

    // Verifica se a autenticação foi chamada
    expect(mockMedidores).toHaveBeenCalled();

    unmount();
  });

  it('solicita a lista de medidores cadastrados no protheus com sucesso e clica no filtro', async () => {

    jest.spyOn(storage, 'getStorageJson').mockResolvedValue([
      { IdMedidor: '001', DescMed: 'MEDIDOR 1', VlrKE: '10' }, { IdMedidor: '002', DescMed: 'MEDIDOR 2', VlrKE: '11' }
    ]);

    // Mocka Protheus retornando 200
    const mockMedidores = jest.fn().mockResolvedValue(200);
    (Protheus as jest.Mock).mockImplementation(() => ({
      medidoresProtheus: mockMedidores,
    }));

    const { unmount, getByPlaceholderText } = render(
                                <MessageBoxProvider>
                                  <Medidores />
                                </MessageBoxProvider>
                              );

    await act(async () => {
      jest.advanceTimersByTime(1000);
      await Promise.resolve(); // força a execução do .then da Promise
    });

    const searchInput = getByPlaceholderText('Pesquisar'); // Seleciona pelo placeholder
    fireEvent.changeText(searchInput, 'MEDIDOR 2'); // Simula digitação

    unmount();
  });

  it('solicita a lista de medidores cadastrados no protheus com falha', async () => {
    // Mocka Protheus retornando 200
    const mockMedidores = jest.fn().mockResolvedValue(400);
    (Protheus as jest.Mock).mockImplementation(() => ({
      medidoresProtheus: mockMedidores,
    }));

    const { unmount } = render(
                                <MessageBoxProvider>
                                  <Medidores />
                                </MessageBoxProvider>
                              );

    await act(async () => {
      jest.advanceTimersByTime(1000);
      await Promise.resolve(); // força a execução do .then da Promise
    });

    // Verifica se a autenticação foi chamada
    expect(mockMedidores).toHaveBeenCalled();

    unmount();
  });  

  it('solicita a lista de medidores cadastrados no protheus com erro de exceção', async () => {
    // Mocka Protheus retornando 200
    const mockMedidores =  jest.fn().mockRejectedValue(new Error('Erro de rede'));
    (Protheus as jest.Mock).mockImplementation(() => ({
      medidoresProtheus: mockMedidores,
    }));

    const { unmount } = render(
                                <MessageBoxProvider>
                                  <Medidores />
                                </MessageBoxProvider>
                              );

    await act(async () => {
      jest.advanceTimersByTime(1000);
      await Promise.resolve(); // força a execução do .then da Promise
    });

    // Verifica se a autenticação foi chamada
    expect(mockMedidores).toHaveBeenCalled();

    unmount();
  });

  it('deve exibir texto de dados não encontrados quando retorno for null', async () => {
    jest.spyOn(storage, 'getStorageJson').mockResolvedValue([
      { IdMedidor: '001', DescMed: 'MEDIDOR 1', VlrKE: '10' },
    ]);

    const { getByText } = render(
      <MessageBoxProvider>
        <Medidores />
      </MessageBoxProvider>
    );

    await waitFor(() => {
      expect(getByText('Falha ao solicitar dados de medidores')).toBeTruthy();
    });
  });

  it('solicita a lista de medidores cadastrados no protheus com sucesso mas gerou exceção ao ler do storage', async () => {

    jest.spyOn(storage, 'getStorageJson').mockRejectedValue(new Error('Erro de rede'));

    // Mocka Protheus retornando 200
    const mockMedidores = jest.fn().mockResolvedValue(200);
    (Protheus as jest.Mock).mockImplementation(() => ({
      medidoresProtheus: mockMedidores,
    }));

    const {unmount, getByText } = render(
      <MessageBoxProvider>
        <Medidores />
      </MessageBoxProvider>
    );

    await waitFor(() => {
      expect(getByText('Erro ao ler dados de medidores')).toBeTruthy();
    });

    unmount();
  });



 
});

