import { CommonActions, useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';

// estilos da pagina
import { styles } from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';

// rotas drawer de navegação
import { StackTypes } from '../MenuPrincipal/index';

// componentes
import CardMedidor from '../../componentes/CardMedidor';
import Filter from '../../componentes/Filter';
import Loading from '../../componentes/Loading';
import Space from '../../componentes/Space';

// armazena localmente no mobile
import { getStorageJson, KEY } from '../../storages';

/* funções do protheus */
import { Protheus } from '../../services/protheus';

// modal messages
import { useMessageBox } from '../../contexts/MessageBoxContext';


// Definindo a interface para a tipagem
interface Medidor {
  IdMedidor: string;
  DescMed: string;
  VlrKE: string;
}

const Medidores = () => {

  /* caixa de mensagens do tipo modal */
  const { showMessageBox } = useMessageBox();
    
  // navegação entre telas
  const navigation = useNavigation<StackTypes>();
    
  // lista de medidores
  const[listaMedidores, setListaMedidores] = useState<Medidor[]>([]);

  // texto de pesquisa
  const[pesquisar, setPesquisar] = useState<string>('');

  // indica que o loading não esta visivel
  const [loading, setLoading] = useState<boolean>(false);

  // texto material não encontrado
  const[texto, setTexto] = useState<string>('');


  /**
   * lista de medidores protheus
   */
  const ListaMedidores = async () : Promise<boolean> => {

    const protheus = new Protheus();

    try {
        /*  lista de medidores cadastrados no protheus */
        const status = await protheus.medidoresProtheus();

        if(status === 200) {
          return true;
        }          
        else {
          /* caixa de mensagens do tipo modal */
          showMessageBox ({title: 'Atenção', description: `Erro[${status}]: Protheus.\nNenhum registro encontrado`, textButton: 'OK', type:'error' });
          return false;
        }        
    }  catch (error) {  
        /* caixa de mensagens do tipo modal */
        showMessageBox ({title: 'Atenção', description: `Erro: Protheus.\nFalha ao solicitar a lista de medidores.`, textButton: 'OK', type:'error' });
        return false;
    }
  }  

  // atualiza lista de medidores
  const AtualizarListaMedidores = async () => {

    // inicia loading
    setLoading(true);

    if(await ListaMedidores()) {    
      
      try{
        const modelosMedidores = await getStorageJson(KEY.modelosMedidorProtheus);

        if(modelosMedidores !== null) 
          setListaMedidores(modelosMedidores);
        else
          setTexto(`Não existem dados de medidores`);
      } catch (error) {
        setTexto(`Erro ao ler dados de medidores`);      
      }
    }
    else {
      setTexto(`Falha ao solicitar dados de medidores`);  
    }

    // encerra o loading
    setLoading(false);
  }
  
  // executa a atualização da lista de medidores ao abrir a pagina
  useEffect( () => {

    AtualizarListaMedidores()

  }, []);

  // navegar de volta a tela de ordens de serviço
  const goBack = () => {
           
    // fecha a pagina atual retornando para pagina de ordens de serviço
    navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes:[
            { name: 'OrdensServico' },
          ],
        })
      );
  };   

  return (
      
    <>
      <Loading animating={loading} text='Atualizando lista de Medidores...'/>

      <View style = {styles.containerTela}>
    
        {/* header */}
        <View style={styles.containerHeader}>
  
            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goBack()} testID='back-button'>
                <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
              </TouchableOpacity>    
            </View>
  
            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeader}>MEDIDORES CADASTRADOS</Text>
            </View>
  
        </View>

        {/* content */}
        <View style = {styles.containerConteudo}>
          
          {
            (listaMedidores?.length <= 0)
            ?
              <View style = {{width: '100%', height: '100%', justifyContent:'center', alignItems:'center'}}>
                <Text style ={styles.textoNenhumMaterial}>{texto}</Text>
              </View>
            :            
              <View style = {{width: '100%', height: '100%', gap: 20}}>

                <Filter value={pesquisar}
                        placeHolder='Pesquisar'
                        onValueChange= {setPesquisar} />
                    
                <FlatList 
                  ItemSeparatorComponent={Space}              
                  data = {(pesquisar === '') ? listaMedidores : listaMedidores.filter(medidor => medidor.DescMed.includes(pesquisar.toUpperCase()) || medidor.VlrKE.includes(pesquisar))}
                  renderItem={ ({ item }) => <CardMedidor
                                                value = {item.DescMed}
                                                valueKE = {item.VlrKE}
                                                valueID = {item.IdMedidor}
                                            />
                              }
                />

            </View>            
          }

        </View>  

        {/* footer */}
        <View style={styles.containerFooter}>
  
          <TouchableOpacity style={styles.buttonAtualizar} onPress={() => AtualizarListaMedidores()} testID='update-button'>
            <Text style = {styles.textoButtonAtualizar}>ATUALIZAR</Text>
          </TouchableOpacity>                        

        </View>      
  
      </View>      

    </>

    
  );
  
};
  
export default Medidores;