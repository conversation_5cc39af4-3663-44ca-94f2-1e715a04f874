import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { fireEvent, render, screen, act } from "@testing-library/react-native";
import { ErroAuth } from ".";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        navigate: jest.fn(), // Simulando o comportamento de navigat
      }),
    };
});

const mockRoute = {
    params: { erro:'100'}
};

describe('Page ErroAuth', () => {

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                        <ErroAuth route={mockRoute} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela ao clicar no botão logout', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                        <ErroAuth route={mockRoute} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-logout'));
        });        
    });    
});