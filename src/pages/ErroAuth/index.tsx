import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {View, Text, TouchableOpacity} from 'react-native';

// icones
import IconErro from '../../assets/svg/icon_alert-circle.svg'

// Style
import { styles } from './layout';

// rotas
import { StackTypes } from '../../routes';


export function ErroAuth({route}) {

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
  
    return (
        <View style={styles.containerTela}>
            <IconErro color={"#A3A3A3"} width={60} height={60} />
            <Text style={styles.texto}>{`Erro de autenticação ${route.params?.erro}.\nNão foi possível realizar o login.`}</Text> 
            <TouchableOpacity style={styles.button}
                              onPress={ () => {navigation.navigate('Logout') }}
                              testID='button-logout'>
                                <Text style={styles.textoButton}>Tentar novamente</Text>
            </TouchableOpacity>
        </View>
    );
}
