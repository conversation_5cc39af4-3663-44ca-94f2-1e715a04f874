import { View, Text, TouchableOpacity, ScrollView, FlatList } from "react-native"
import React, { useEffect, useRef, useState } from "react";
import { EventRegister } from "react-native-event-listeners";

// bottom page
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import {styles} from './layout';

// rotas stack de navegação
import {StackTypes} from '../../routes/index'

// navegação de paginas
import {useNavigation} from "@react-navigation/native";

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import Icon3Dots from '../../assets/svg/icon_dots-vertical.svg';
import IconCPU from '../../assets/svg/icon_cpu.svg';
import IconInfo from '../../assets/svg/icon_info-circle.svg';
import IconHelp from '../../assets/svg/icon_help-circle.svg';
import IconX from '../../assets/svg/icon_x.svg';
import IconCopy from '../../assets/svg/icon_copy.svg'
// funções
import { DataTimeStamp, HoraTimeStamp } from "../../funcoes";

// constantes
import { HeapFreeMax, HeapTotal, TimeoutTipoConexao } from "../../constantes";

// modal messsages
import MessageBoxAtencao from "../../modal/messagebox/Atencao";
import MessageBoxPergunta from "../../modal/messagebox/Pergunta";
import MessageBoxSucesso from "../../modal/messagebox/Sucesso";
import MessageBoxErro from "../../modal/messagebox/Erro";

// funções mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT } from "../../services/mqtt";

// componentes
import Loading from "../../componentes/Loading";
import ListFiles from "../../componentes/ListFiles";

// estruturas
import { FilesProbe } from "../../data";
import Clipboard from "@react-native-clipboard/clipboard";

const ProbeSistema = ({route}) => {

    // referencia para o bottom sheet
    const refRBSheetMessage = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetFiles = useRef<{ open: () => void, close: () => void }>(null);

    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');
    
    // mensagens modal
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageAtencao, setShowMessageAtencao] = useState<boolean>(false);
    const [showMessagePergunta, setShowMessagePergunta] = useState<boolean>(false);
    const [textoMessage, setTextoMessage] = useState<string>('');
    
    // controle timeout loading
    let timeoutWait : number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);

    // seta o tipo de mensagem a ser enviada
    const [tipoMensagem, setTipoMensagem] = useState<number>(0);
    
    // informações da probe
    let id : string =  route.params?.probeSettings.id;
    let firmware : string = route.params?.probeSettings.firmware;
    let dateTime : number = route.params?.probeSettings.datetime;
    let vBateria : string = route.params?.probeSettings.vBateria;
    let pBateria : string = route.params?.probeSettings.bateria;
    let firmwareBoot : number = route.params?.probeSettings.firmwareBoot;
    let resets :string = route.params?.probeSettings.resets;
    let excecoes: string = route.params?.probeSettings.excecoes;
    let heap : number = route.params?.probeSettings.heap;
    let pHeap : number = (route.params?.probeSettings.heap / HeapTotal) * 100;

    // informações da memória da probe
    let totalMemory : number = route.params?.probeSettings.totalMemory;
    let usedMemory : number = route.params?.probeSettings.usedMemory;
    let percMemory : number = (totalMemory <= 0) ? 0 :(usedMemory/totalMemory) * 100;

    // indica se a probe foi reconectada após solicitação de reset
    const [reconectado, setReconectado] = useState<boolean>(false);

    // indica se a probe foi reconectada após solicitação de reset
    const [formatado, setFormatado] = useState<boolean>(false);

    // indica se a probe foi escaneada após solicitação de scan
    const [escaneado, setEscaneado] = useState<boolean>(false);

    // indica se a probe listou os arquivos após solicitação 
    const [listado, setListado] = useState<boolean>(false);

    // arquivos encontrados
    const [files, setFiles] = useState<FilesProbe[]>([]);

    // arquivos encontrados
    const [nameFile, setNameFile] = useState<string>('');

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();                
    }; 

    // solicitação para o MQTT
    const EscreveMQTT = (variable:string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error){}
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable:string) => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable);
        }
        catch(error) {}
    }       

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {

        // se timeout não alarmado
        if(!timeoutMessage)
            return

        switch(tipoMensagem)
        {
            case 0: // system_reset

                if(!reconectado) {
                    setLoading(false);
                    setTextoMessage('Probe demorou muito tempo para responder a reconexão.')
                    setShowMessageErro(true);                        
                }

                break;
            case 1: // system_format

                if(!formatado) {
                    setLoading(false);
                    setTextoMessage('Probe demorou muito tempo para responder a solicitação de formatação.')
                    setShowMessageErro(true);                        
                }            

                break;
            case 2: // module_scan

                if(!escaneado) {
                    setLoading(false);
                    setTextoMessage('Probe demorou muito tempo para responder a solicitação de escaneamento das tarefas.')
                    setShowMessageErro(true);                        
                }
        
                break;
            case 3: // module_scan

                if(!formatado) {
                    setLoading(false);
                    setTextoMessage('Probe demorou muito tempo para responder a solicitação de formatação das mensagens');
                    setShowMessageErro(true);                        
                }
        
                break;
            case 4: // system_files

                if(!listado) {
                    setLoading(false);
                    setTextoMessage('Probe demorou muito tempo para responder a solicitação de listar os arquivos.');
                    setShowMessageErro(true);                        
                }
        
            break;
        }

        // timeout não alarmado
        setTimeoutMessage(false);

    }, [timeoutMessage]);



    // executa sempre que a variavel 'reconnectMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('reconnectMQTT', mqtt => {
            
            // verifica se foi reconectado
            if(mqtt)
            {
                setReconectado(true);

                setLoading(false);
                setTextoMessage('Probe foi reconectada.');
                setShowMessageSucesso(true); 
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao reiniciar a Probe`);
                setShowMessageErro(true);
            }            

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'formatMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('formatMQTT', mqtt => {                            
            
            // verifica se foi formatada
            if(mqtt)
            {
                setFormatado(true);

                setLoading(false);
                setTextoMessage('Memória da Probe foi formatada.');
                setShowMessageSucesso(true); 
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao formatar a memória da Probe`);
                setShowMessageErro(true);
            }            

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'moduleScanMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('moduleScanMQTT', mqtt => {                                    
            // verifica se foi escaneado
            if(mqtt)
            {
                setEscaneado(true);

                setLoading(false);
                setTextoMessage((mqtt === 1) ? `Módulos escaneados: Existe ${mqtt} tarefa em funcionamento.` : `Módulos escaneados: Existem ${mqtt} tarefas em funcionamento.`);
                setShowMessageSucesso(true); 
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Não existem módulos a serem escaneados`);
                setShowMessageErro(true);
            }                        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'formatMessageMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('formatMessageMQTT', mqtt => {                                    
            
            // verifica se foi formatada
            if(mqtt)
            {
                setFormatado(true);

                setLoading(false);
                setTextoMessage('As mensagens da Probe foram formatadas.');
                setShowMessageSucesso(true); 
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao formatar as mensagens da Probe`);
                setShowMessageErro(true);
            }            

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'listFilesMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('listFilesMQTT', mqtt => {                                    
            
            // pega os arquivos
            setFiles(mqtt);            
            
            // se existem arquivos
            if(mqtt.length)
                refRBSheetFiles.current?.open();

            // indica que foi listado os arquivos
            setListado(true);

            setLoading(false);
            setTextoMessage(`Total de arquivos listados na Probe: ${mqtt.length}`);
            setShowMessageSucesso(true);             
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'formatFileMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('formatFileMQTT', async mqtt => {                                    
        
            // indica se foi formatado
            setFormatado(true);            
        
            // encerra o loading
            setLoading(false);

            // se foi formatado
            if(mqtt) {

                setFiles(files.filter(item => item.name !== nameFile));
                setTextoMessage(`Arquivo ${nameFile} excluido.`);
                setShowMessageSucesso(true);                 
            }
            else {
                setTextoMessage(`Não foi possível exlcuir o arquivo ${nameFile}.`);
                setShowMessageErro(true);                 
            }
            
    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [nameFile]);    


    // verifica o tipo de mensagem a ser enviada
    const mensagemMQTT = (mensagem: number) => {

        // verifica o tipo de mensagem
        switch(mensagem) {
         
            case 0:
                resetMQTT();
                break;
            case 1:
                FormatMQTT();
                break;
            case 2:
                TaskScanMQTT();
                break;
            case 3: 
                FormatarMensagensMQTT();
                break;
            case 4:
                SolicitarArquivosMQTT();
                break;
        }        
    }

    // comando de reset
    const resetMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // seta como não reconectada
            setReconectado(false);

            // inicia loading
            setLoading(true);
            setTextoLoading('Inicializando a Probe...');

            // envio de solicitação de reinicializaçõa da probe
            EscreveMQTT('system_reset');

            // leitura da solicitação 
            LerMQTT('system_reset');

            // loading vai durar 60 segundos
            setTimeout(function() {                
                // verifica se não se reconectou e envia mensagem para o usuario
                setTimeoutMessage(true);
            }, timeoutWait);    
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    // comando de format
    const FormatMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // seta como não formatada
            setFormatado(false);

            // inicia loading
            setLoading(true);
            setTextoLoading('Formatando os arquivos da Probe...');

            // envio de solicitação de formatação da probe
            EscreveMQTT('system_format');

            // leitura da solicitação 
            LerMQTT('system_format');

            // loading vai durar 60 segundos
            setTimeout(function() {
                // verifica se não formatou e envia mensagem para o usuario
                setTimeoutMessage(true);
            }, timeoutWait);
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };    

    // comando de format
    const TaskScanMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // seta como não escaneada
            setEscaneado(false);

            // inicia loading
            setLoading(true);
            setTextoLoading('Analisando as tarefas em funcionamento...');

            // envio de solicitação de verificação das tarefas sendo executadas pela probe
            EscreveMQTT('module_scan');

            // leitura da solicitação 
            LerMQTT('module_scan');

            // loading vai durar 60 segundos
            setTimeout(function() {
                // verifica se não escaneou e envia mensagem para o usuario
                setTimeoutMessage(true);
            }, timeoutWait);                      
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    // comando de format messages
    const FormatarMensagensMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // seta como não formatada
            setFormatado(false);

            // inicia loading
            setLoading(true);
            setTextoLoading('Excluindo mensagens da Probe...');

            // envio de solicitação de exclusão das mensagens da memória da probe
            EscreveMQTT('pers_array_format','message_manager');

            // leitura da solicitação 
            LerMQTT('pers_array_format');

            // loading vai durar 60 segundos
            setTimeout(function() {
               // verifica se não formatou e envia mensagem para o usuario
               setTimeoutMessage(true);
            }, timeoutWait);      
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    // comando de solicitação de listagens dos arquivos
    const SolicitarArquivosMQTT = () => {
        
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // seta como não listado
            setListado(false);

            // inicia loading
            setLoading(true);
            setTextoLoading('Solicitando os arquivos da Probe...');

            // envio de solicitação de dos arquivos da probe
            EscreveMQTT('system_files');

            // leitura da solicitação 
            LerMQTT('system_files');

            // loading vai durar 60 segundos
            setTimeout(function() {
               // verifica se não listou os arquivos e envia mensagem para o usuario
               setTimeoutMessage(true);
            }, timeoutWait);      
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };    

    // copia o id da probe
    const copyToID = () => {
        Clipboard.setString(id);
    };

    // copia o firmware da probe
    const copyToFirmware = () => {
        Clipboard.setString(id);
    };

    return(

        <>
            <Loading animating={loading} text={textoLoading}/>

            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}/>

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}/>

            <MessageBoxAtencao 
                visivel={showMessageAtencao} 
                titulo="Ajuda" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageAtencao(false)}/>

            <MessageBoxPergunta
                visivel={showMessagePergunta} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                                        setShowMessagePergunta(false);
                                        refRBSheetMessage.current?.close();
                                        mensagemMQTT(tipoMensagem); 
                                    }}
                onCancel={() => { setShowMessagePergunta(false) }}                
            />

            <View style={styles.containerTela}>

                {/* header */}
                <View style={{...styles.containerHeaderProbeSistema}}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID="button-back">
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderProbeSistema}>SISTEMA</Text>
                    </View>

                </View>

                {/* botões */}
                <View style={styles.containerBotoesProbeSistema}>
                    <TouchableOpacity onPress={ () => refRBSheetMessage.current?.open() } testID="button-sistema">
                        <Icon3Dots color={"#737373"} width={30} height={30}/>
                    </TouchableOpacity>                
                </View>

                <ScrollView contentContainerStyle={styles.containerConteudoProbeSistema}>

                    {/* informações da probe */}
                    <View style = {styles.containerInfoProbe}>

                        <View style = {{height:'10%', width:'100%', flexDirection:'row'}}>
                            <View style = {{height:'100%', width:'90%'}}>
                                <Text style={styles.textoTitle}>ID</Text>
                                <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToID} testID='button-copy-id'>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{id}</Text>
                                    <IconCopy color={"#737373"} width={20} height={20}/>
                                </TouchableOpacity>                                
                            </View>
                            <View style = {{height:'100%', width:'10%', alignItems:'flex-end'}}>
                              <IconInfo color={"#737373"} width={30} height={30}/>
                            </View>                        
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Firmware</Text>
                            <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToFirmware} testID='button-copy-firmware'>
                                <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{firmware}</Text>
                                <IconCopy color={"#737373"} width={20} height={20}/>
                            </TouchableOpacity>
                            
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Data e hora</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${DataTimeStamp(dateTime)} ${HoraTimeStamp(dateTime, false)}`}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Total de resets</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{resets}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Tensão da Bateria</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${vBateria} v`}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Carga da Bateria</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${pBateria} %`}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Data de instalação do Firmware</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${DataTimeStamp(firmwareBoot)} ${HoraTimeStamp(firmwareBoot, false)}`}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Total de exceções</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{excecoes}</Text>
                        </View>
                        <View style = {{height:'10%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Memória Heap livre atual</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10, color: (heap <= HeapFreeMax) ? '#FF0000' : '#000000'}}> 
                                { (heap > HeapFreeMax) ? `${heap} bytes (${pHeap.toFixed(2)} %)` : `${heap} bytes (${pHeap.toFixed(2)} %) [Crítico]` } 
                            </Text>
                        </View>

                    </View>

                    {/* informações de memória da probe */}
                    <View style = {styles.containerInfoMemoria}>

                        <View style = {{height:'50%', width:'100%', flexDirection:'row'}}>
                            <View style = {{height:'100%', width:'90%'}}>
                                <Text style={styles.textoTitle}>Memória utilizada</Text>
                                <Text style={{...styles.textoSubtitle, paddingLeft: 10, color: (percMemory > 90) ? '#FF0000' : '#000000'}}>
                                    { (percMemory > 90) ? `${usedMemory} bytes (${percMemory.toFixed(2)} %) [Crítico]` : `${usedMemory} bytes (${percMemory.toFixed(2)} %)` }
                                </Text>
                            </View>
                            <View style = {{height:'100%', width:'10%', alignItems:'flex-end'}}>
                              <IconCPU color={"#737373"} width={30} height={30}/>
                            </View>                        
                        </View>
                        <View style = {{height:'50%', width:'80%'}}>
                            <Text style={styles.textoTitle}>Memória total</Text>
                            <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${totalMemory} bytes`}</Text>
                        </View>
                    </View>

                </ScrollView>

                <RBSheet ref={refRBSheetMessage}  height={500} data-testid='rbSheet-message'>

                    <View style={{flex:1, padding: 20, alignItems:'center', gap: 17}}>        
              
                        <View style={{height:60, width:'100%', justifyContent:'center', alignItems:'center'}}>
                            <Text style={styles.textoMensagem}>Opções de mensagens da Probe </Text>
                        </View>

                        {/* botão reset */}
                        <View style={styles.containerBotaoMensagem}>
                            <TouchableOpacity style={ styles.botaoMensagem} 
                                              onPress={() => { 
                                                                setTipoMensagem(0);
                                                                setTextoMessage('Deseja realmente reinicializar a Probe?');
                                                                setShowMessagePergunta(true); }}
                                              testID="button-reset">
                                <Text style = {styles.textoBotaoMensagem}>Reniciar</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.botaoMensagemHelp} 
                                              onPress={() => { 
                                                                setTextoMessage('Essa opção solicita a reinicialização da Probe, simulando uma falha de energia.');
                                                                setShowMessageAtencao(true); }}
                                              testID="button-reset-help">
                                <IconHelp color={"#737373"} width={40} height={40}/>
                            </TouchableOpacity>
                        </View>

                        {/* botão format */}
                        <View style={styles.containerBotaoMensagem}>
                            <TouchableOpacity style={ styles.botaoMensagem} 
                                              onPress={() => { 
                                                                setTipoMensagem(1);
                                                                setTextoMessage('Deseja realmente formatar todos os arquivos da Probe?');
                                                                setShowMessagePergunta(true); }}
                                              testID="button-format">
                                <Text style = {styles.textoBotaoMensagem}>Formatar</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.botaoMensagemHelp} 
                                              onPress={() => { 
                                                                setTextoMessage('Essa opção solicita a formatação dos arquivos da Probe, para configurações de liberação.');
                                                                setShowMessageAtencao(true); }}
                                              testID="button-format-help">
                                <IconHelp color={"#737373"} width={40} height={40}/>
                            </TouchableOpacity>
                        </View>

                        {/* botão task scan */}
                        <View style={styles.containerBotaoMensagem}>
                            <TouchableOpacity style={ styles.botaoMensagem} 
                                              onPress={() => { 
                                                                setTipoMensagem(2);
                                                                setTextoMessage('Deseja realmente verificar as tarefas em funcionamento na Probe?');
                                                                setShowMessagePergunta(true);
                                                              }}
                                              testID="button-scan">
                                <Text style = {styles.textoBotaoMensagem}>Escanear Tarefas</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.botaoMensagemHelp} 
                                              onPress={() => { 
                                                                setTextoMessage('Essa opção solicita a verificação do que está em funcionamento na Probe.');
                                                                setShowMessageAtencao(true); }}
                                              testID="button-scan-help">
                                <IconHelp color={"#737373"} width={40} height={40}/>
                            </TouchableOpacity>
                        </View>
                    
                        {/* botão formatar mensagens */}
                        <View style={styles.containerBotaoMensagem}>
                            <TouchableOpacity style={ styles.botaoMensagem} 
                                              onPress={() => { 
                                                                setTipoMensagem(3);
                                                                setTextoMessage('Deseja realmente excluir todas as mensagens da Probe?');
                                                                setShowMessagePergunta(true); }}
                                              testID="button-format-messages">
                                <Text style = {styles.textoBotaoMensagem}>Formatar Mensagens</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.botaoMensagemHelp} 
                                              onPress={() => { 
                                                                setTextoMessage('Essa opção solicita a exclusão das mensagens da memória da Probe.');
                                                                setShowMessageAtencao(true); }}
                                              testID="button-format-messages-help">
                                <IconHelp color={"#737373"} width={40} height={40}/>
                            </TouchableOpacity>
                        </View>
                    
                        {/* botão listar arquivos */}
                        <View style={styles.containerBotaoMensagem}>
                            <TouchableOpacity style={ styles.botaoMensagem} 
                                              onPress={() => { 
                                                                setTipoMensagem(4);
                                                                setTextoMessage('Deseja realmente listar os arquivos para exclusão da Probe?');
                                                                setShowMessagePergunta(true);
                                                            }}
                                              testID="button-files">
                                <Text style = {styles.textoBotaoMensagem}>Formatar Arquivos</Text>
                            </TouchableOpacity>
                            <TouchableOpacity style={styles.botaoMensagemHelp} 
                                              onPress={() => { 
                                                                setTextoMessage('Essa opção solicita a lista de arquivos que podem ser excluídos da Probe');
                                                                setShowMessageAtencao(true); }}
                                              testID="button-files-help">
                                <IconHelp color={"#737373"} width={40} height={40}/>
                            </TouchableOpacity>
                        </View>
                                            
                    </View>
            
                </RBSheet>

                {/* lista de arquivos */}
                <RBSheet ref={refRBSheetFiles}  height={600}>

                    <View style={{flex:1, padding: 20, alignItems:'center', gap: 5}}>        
              
                        <View style={{height:20, width: '100%', alignItems:'flex-end'}}>
                            <TouchableOpacity style={ {height:'100%', width:40}} onPress={() => refRBSheetFiles.current?.close()} testID="button-close-files">
                                <IconX color={"#2E8C1F"} width={30} height={30}/>
                            </TouchableOpacity>                                
                        </View>

                        <View style={{height:30, width:'100%', justifyContent:'center', alignItems:'center'}}>
                            <Text style={styles.textoMensagem}>Arquivos da Probe </Text>
                        </View>
                        
                        <FlatList style={{marginTop: 20}}
                            ItemSeparatorComponent={() => <Text> </Text>}              
                            data = {files}
                            renderItem={ ({ item }) => <ListFiles 
                                                            height={80}
                                                            timer={item.timer}
                                                            name={item.name}
                                                            size={item.size}
                                                            onDelete = {() => {                                                                 
                                                                            setTipoMensagem(5);
                                                                            setNameFile(item.name ?? '');
                                                                            setTextoMessage(`Deseja realmente excluir o arquivo ${item.name}`);
                                                                            setShowMessagePergunta(true); 
                                                                        }}
                                                       />
                                        }
                        />

                    </View>
            
                </RBSheet>                

            </View>
        </>

    )
}

export default ProbeSistema;