import React, { useRef, useState } from 'react';
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native';

// navegação das telas
import { CommonActions, useNavigation } from '@react-navigation/native';

// estilos da pagina
import { screenHeight, styles } from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconCameraRotate from '../../assets/svg/icon_solid-camera-rotate.svg';
import IconFlashOff from '../../assets/svg/icon_solid-flash-slash.svg';
import IconFlash from '../../assets/svg/icon_solid-flash.svg';

// modal messages
import MessageBoxErro from '../../modal/messagebox/Erro';

// rotas drawer de navegação
import { StackTypes } from '../../routes/index';

// dados de instalação
import { ProbeSettings, ProbeSettingsInicial } from "../../data";

// uso do qrcode scanner
import { RNCamera } from 'react-native-camera';
import QRCodeScanner from 'react-native-qrcode-scanner';

const PassoID = ({route}) => {

    const qrCodeRef = useRef(null);

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // se esta usando qr code para obter o id da probe
    const [usaQrCode, setUsaQrCode] = useState<boolean>(true);

    // seta o id codigo da probe
    const [codigo, setCodigo] = useState<string>(''); 
                                                        /* 10061C1D4998 - #FABIO */
                                                        /* BANCADA (HMG)         */
                                                        /* E86BEA02A68C - #10    */

  // habilita o flash da camera
  const [flash, setFlash] = useState<boolean>(false);
  const [cameraFrontal, setCameraFrontal] = useState<boolean>(false);

  const [textoMessageBoxErro, setTextoMessageBoxErro] = useState("");
  const [showMessageBoxErro, setShowMessageBoxErro] = useState(false);

    // verifica o codigo digitado
    const SeCodigoOK = () => {

        // se foi digitado algum e-mail ou e-mail incorreto
        if (codigo == "") {

            setTextoMessageBoxErro("Digite o código da Probe");
            onMessageBoxErroAbrir();
  
            return false;
        }
  
        // se foi digitado id probe incorreto
        if (codigo.length > 16) {
  
            setTextoMessageBoxErro("O código da Probe possui até 16 caracteres alfanuméricos.");
            onMessageBoxErroAbrir();
  
          return false;
        }    

        return true
    };

    // navega para pagina principal de configuração da Probe
    const goToProbe = (id_probe: string) => {

        if(!SeCodigoOK())
            return;
        
        // estrutura inicial de configuração da probe
        const _probe: ProbeSettings = ProbeSettingsInicial();

        // atualiza estrutura de configuração da probe
        const probeAtualizada = { ..._probe, id: id_probe };

        // navega de volta para a pagina dos módulos da probe
        navigation.dispatch(
            CommonActions.navigate({
                name: 'Probe',
                params: { probeSettings: probeAtualizada },
            })
        );
            
    }; 
  
    // navega para pagina principal de configuração da Probe
    const goBackProbe = (id_probe: string) => {

        // estrutura inicial de configuração da probe
        const _probe: ProbeSettings = ProbeSettingsInicial();

        // atualiza estrutura de configuração da probe
        const probeAtualizada = { ..._probe, id: id_probe };

        // fecha a pagina de configuração da probe
        navigation.dispatch(
            CommonActions.navigate({
              name: 'Probe',
              params: { probeSettings: probeAtualizada },
            })
        );

    };

    const onMessageBoxErroAbrir = () => { setShowMessageBoxErro(true); };
    const onMessageBoxErroFechar = () => { setShowMessageBoxErro(false); };

    const onFlash = () => { setFlash(!flash); };
    const onCameraFrontal = () => { setCameraFrontal(!cameraFrontal); };
  
    return (
    
    <>

        <MessageBoxErro 
            visivel={showMessageBoxErro} 
            titulo="Erro ao vincular Probe" 
            descricao={textoMessageBoxErro} 
            textoBotao="OK" 
            onFechar={onMessageBoxErroFechar}
        />    

        <View style = {styles.containerTela}>

            {/* header */}
            <View style={styles.containerHeaderProbeID}>

                {/* botão voltar */}
                <View style={styles.containerBotaoVoltar}>
                    <TouchableOpacity onPress={()=> goBackProbe(route.params?.id)} testID='button-back'>
                        <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                    </TouchableOpacity>    
                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoHeaderProbeID}>
                    <Text style={styles.textoHeaderProbeID}>VINCULAR PROBE</Text>
                </View>

            </View>

            {
                (usaQrCode) 
                ?            
                    <QRCodeScanner
                        ref={qrCodeRef}
                        containerStyle={styles.containerConteudoQRCode}
                        cameraStyle={styles.cameraQRCode}
                        onRead={({data}) => {setCodigo(data)}}
                        flashMode={(flash) ? RNCamera.Constants.FlashMode.on : RNCamera.Constants.FlashMode.off}
                        cameraType= {(cameraFrontal) ? 'front' : 'back'}
                        topContent={
                            <View style={{marginTop: -80, justifyContent:'center', alignItems:'center'}}>
                                <Text style={styles.textoDescricaoQRCode} >Aponte a câmera do celular para o QR Code</Text>
                                <Text style={styles.textoDescricaoQRCode} >localizado na parte superior da Probe</Text>
                            </View>                
                        }
                        bottomContent= {

                            <View style={{flex:1, justifyContent:'space-between'}}>

                                <View style={{marginTop: -(screenHeight-240), gap:40, justifyContent:'space-between', alignItems:'center'}}>

                                    <Image style={{}}
                                        source={require('../../assets/png/scan-mask.png')}                    
                                    />

                                    <View style={{width:'80%', flexDirection:'row', justifyContent:'space-between'}} >
                                        <TouchableOpacity onPress={() => onFlash()} testID='button-flash'>
                                            {
                                                // se flash ligado
                                                (flash)                            
                                                ?
                                                    <IconFlash color={"#FAFAFA"} width={40} height={40}/>
                                                :
                                                    <IconFlashOff color={"#FAFAFA"} width={40} height={40}/>
                                            }                          
                                        </TouchableOpacity>

                                        <TouchableOpacity onPress={() => onCameraFrontal()} testID='button-camera'>
                                            <IconCameraRotate color={"#FAFAFA"} width={40} height={40}/>
                                        </TouchableOpacity>                  
                                    </View>
                
                                </View>

                                <View style={{height: 150, justifyContent:'flex-start', gap: 10}}>
                                    { 
                                        (codigo) &&
                                            <TouchableOpacity style={styles.buttonContinuar} onPress={() => goToProbe(codigo)} testID='button-code'>
                                                <Text style = {styles.textoButtonContinuar}>{codigo}</Text>
                                            </TouchableOpacity>
                                    }

                                    <TouchableOpacity style={{...styles.buttonContinuar, backgroundColor:'#737373'}} onPress={ () => setUsaQrCode(false)} testID='button-input'>
                                        <Text style = {styles.textoButtonContinuar}>DIGITAR CÓDIGO</Text>
                                    </TouchableOpacity>                                          
                                </View>


                            </View>
                        }
                    />          
                :
                    // usa o teclado para entrar com o id da probe *** ('1006 1C1D 4998') // Probe - 2 Pavimento -> 7990 7216 6467 78
                    <View style = {styles.containerConteudoTelaProbeID}>

                        <View style={{gap:5}}>
          
                            <Text style={styles.textoDigiteCodigo}>Digite o código</Text>

                            <View style={styles.containerInputCodigo}>
          
                                <TextInput
                                    style={styles.textoInputCodigo}
                                    autoCapitalize = {'none'}
                                    placeholder="                " 
                                    keyboardType='ascii-capable'                                
                                    value={codigo}
                                    maxLength={16}                  
                                    onChangeText={(texto) => setCodigo(texto.replace(/[^a-z0-9]/gi, ''))}
                                    testID='input-code' 
                                />

                            </View>

                        </View>

                        <View style={{height:80, justifyContent:'flex-start'}}>
                            <TouchableOpacity style={styles.buttonContinuar} onPress={() => goToProbe(codigo)} testID='button-continue'>
                                <Text style = {styles.textoButtonContinuar}>CONTINUAR</Text>
                            </TouchableOpacity>
                        </View>

                    </View>        
            }

        </View>

    </>
    );

};

export default PassoID;