import { getModelo<PERSON>homp } from "./functions";

describe('functions', () => {

    it('retorna o modelo ITC100 da khomp ', () => {
    
        const modeloITC100 = getModeloKhomp('F803320500000000');
        const modeloITE11L = getModeloKhomp('F803320600000000');
        const modeloITC200 = getModeloKhomp('F803320B00000000');
        const modeloDesconhecido = getModeloKhomp('F803320C00000000');
        
        expect(modeloITC100).toBe('Modelo ITC100');
        expect(modeloITE11L).toBe('Modelo ITE11L');
        expect(modeloITC200).toBe('Modelo ITC200');
        expect(modeloDesconhecido).toBe('Modelo desconhecido');
    });
})