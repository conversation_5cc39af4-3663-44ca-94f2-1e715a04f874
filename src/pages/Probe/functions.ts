/**
 * retorna o modelo do equipamento khomp
 * @param id 
 */
export function getModeloKhomp(id: string): string {

    // pega o identificador do modelo
    const identificador = id.substring(0, 8); 
    
    switch(identificador){
        case 'F8033205':
        case 'f8033205':
            return ('Modelo ITC100');
        case 'F8033206':
        case 'f8033206':
            return ('Modelo ITE11L');
        case 'F803320B':
        case 'f803320b':
            return ('Modelo ITC200');
        default:
            return ('Modelo desconhecido');
    }
}