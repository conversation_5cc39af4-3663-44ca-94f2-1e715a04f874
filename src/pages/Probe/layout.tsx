import { Dimensions, Platform, StatusBar, StyleSheet } from "react-native";

export const screenHeight = Dimensions.get('screen').height;
export const screenWidth = Dimensions.get('screen').width;

export const statusBarHeight = (Platform.OS === 'android') ? StatusBar.currentHeight : 30;


// 20% da largura da tela
const width20 = screenWidth * 0.2;

// 80% da largura da tela
const width80 = screenWidth * 0.8;

// area header
export const heightHeader = 80;

// altura padrão dos botões
export const heightButton = 60;

export const styles = StyleSheet.create({

    containerTela: {
        flex:1,
        backgroundColor:'#FFFFFF'
    },

    containerBotaoUser:{
        width: width20, 
        justifyContent:'center',
        alignItems:'center',
        zIndex:1        
    },    
    
    containerBotaoVoltar:{
        width: width20, 
        justifyContent:'center',
        alignItems:'center',        
    },    
    
    containerTextoHeader:{
        width:'100%',
        marginStart:- width20,
        justifyContent:'center',
        alignItems:'center',        
    },

    textoHeader:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#FFFFFF', 
    },
    
    textoHeaderProbe:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 17,        
        color:'#EAECF0', 
    },

    textoHeaderFirmware:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 14,        
        color:'#D6D6D6', 
    },    
    
    textoHeaderTituloData:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 16,        
        color:'#EAECF0', 
    },    

    textoHeaderTituloHora:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#D6D6D6', 
    },    

    textoHeaderTempo:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 15,        
        color:'#D6D6D6', 
    },
    
    textoConectar:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 18,        
        color:'#EAECF0', 
    },

    textoButtonContinuar:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',        
    },

    textoLoading:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:16,
        fontWeight: "400",
        lineHeight: 24,
        color: "#737373",
    },    
    
    textoUser:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:20,
        fontWeight: "400",
        lineHeight: 24,
        color: "#737373",
    },

    textoLogout:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:20,
        fontWeight: "400",
        lineHeight: 24,
        color: "#FFFFFF",
    },

    textoVersao:{
        fontFamily: 'Exo2_400Regular',
        fontSize:12,
        fontWeight: "400",
        color: "#737373",
        textAlign:'right'
    },

    botaoInfo:{
        justifyContent:'center', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'transparent', 
        backgroundColor:'transparent',        
    },

    botaoAtualizar:{
        justifyContent:'center', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'transparent', 
        backgroundColor:'transparent',
        
    },

    botaoConectar:{
        height:'70%', 
        width:'100%', 
        alignItems:'center', 
        justifyContent:'center', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'transparent', 
        backgroundColor:'#2E8C1F',        
    },

    buttonContinuar:{        
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },
    
    buttonLogout:{
        height:heightButton,
        width:'80%', 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },    

    textoSwitchMqtt:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:24,
        alignSelf:'center',
        fontWeight: "600",
        color: "#424242",
    },

    textoSwitch:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:20,
        fontWeight: "400",
        lineHeight: 24,
        color: "#A3A3A3",
    },

    /********** TELA LEITURA DO QR CODE DO ID DA PROBE **********/

    containerHeaderProbeID: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerTextoHeaderProbeID:{
        width:'100%',
        marginStart:- width20,
        justifyContent:'center',
        alignItems:'center',        
    },

    containerConteudoQRCode:{
        paddingTop: 100,
        width: screenWidth, 
        height: '100%',
        //justifyContent:'space-between'
    },

    containerConteudoTelaProbeID:{
        width: screenWidth, 
        height: screenHeight - heightHeader - (statusBarHeight ?? 0), 
        paddingHorizontal: 30, 
        paddingVertical: 20, 
        justifyContent:'space-between',        
    },    

    containerInputCodigo:{
        width:'100%', 
        height:60, 
        backgroundColor:'#FFFFFF', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'#525252',
        justifyContent:'center',
        alignItems:'center'
    },

    textoHeaderProbeID:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },

    textoDescricaoQRCode:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,        
        color:'#737373', 
        textAlignVertical:'center',
    },

    textoDigiteCodigo:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        color:'#2E8C1F',
    },
    
    textoInputCodigo:{
        width:'100%',
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 26,
        fontWeight: '800',
        color:'#737373',
        textAlign: 'center',        
    },    

    cameraQRCode:{
        height:'100%',
        width:'100%',
    },

    /********** TELA LEITURA DO QR CODE DO ID DA PROBE **********/

    textoTitleDebug:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },    

    /********** TELA DE INFORMAÇÕES DA PROBE **********/

    containerHeaderProbeSistema: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerBotoesProbeSistema: {
        height:50, 
        width:'100%', 
        paddingHorizontal: 20, 
        justifyContent:'flex-end', 
        flexDirection:'row', 
        gap: 30
    },

    containerConteudoProbeSistema: {
        flexGrow: 1,
        height:screenHeight - (heightHeader + 50),
        width:'100%',
        paddingHorizontal: 20,
        gap: 20,
    },

    containerInfoProbe: {
        height:400, 
        width:'100%', 
        padding: 15, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5',
        gap: 2
    },

    containerInfoEmpresa: {
        height:150, 
        width:'100%', 
        padding: 15, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5'
    },

    containerInfoMemoria: {
        height:120, 
        width:'100%', 
        padding: 15, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5'  
    },

    containerBotaoMensagem: {
        width:'100%', 
        flexDirection:'row', 
        alignItems:'center'
    },

    textoHeaderProbeSistema: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },
    
    textoTitle: {
        fontFamily: 'Exo2_400Regular',
        fontSize: 12,
        color: '#737373'
    },

    textoSubtitle: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 14,
        color: '#000000'
    },

    textoMensagem: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 21,
        color: '#525252'
    },

    textoBotaoMensagem: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,
        color: '#FFFFFF'
    },    

    botaoMensagem:{
        height: heightButton, 
        width:'80%', 
        justifyContent:'center', 
        alignItems:'center', 
        borderRadius: 8, 
        borderWidth:1,
        borderColor: '#D6D6D6',
        backgroundColor:'#2E8C1F'
    },

    botaoMensagemHelp: {
        width:'20%', 
        alignItems:'flex-end'
    },

    /********** TELA DE CONFIGURAÇÃO DA REDE DA PROBE **********/

    containerHeaderProbeRede: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerBotoesProbeRede: {
        height:80, 
        width:'100%', 
        paddingHorizontal: 20, 
        justifyContent:'flex-end',
        alignItems:'center' ,
        flexDirection:'row', 
        gap: 30
    },

    containerInfoWifi: {
        height:290, 
        width:'100%', 
        padding: 15,
        marginTop: 20, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5',
        gap: 2
    },    

    containerInfoGSM: {
        height:570, 
        width:'100%', 
        padding: 15,
        marginTop: 0, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5',
        gap: 2
    },

    containerInfoEthernet: {
        height:70, 
        width:'100%', 
        padding: 15,
        marginTop: 20, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5',
        gap: 2
    },

    textoHeaderProbeRede: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },

    /********** TELA DE CONFIGURAÇÃO DO MODBUS **********/

    containerHeaderProbeModbus: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerBotoesProbeModbus: {
        height:80, 
        width:'100%', 
        paddingHorizontal: 20, 
        justifyContent:'space-between',
        alignItems:'center' ,
        flexDirection:'row',
        paddingTop:10 
    },

    containerTextoEquipamentos:{
        width:'100%',
        height: 60,
        paddingHorizontal: 20,
        flexDirection:'row',
        justifyContent:'space-between',
        alignItems:'center',
        backgroundColor:'#737373'
    },

    containerListaEquipamentos: {
        height: screenHeight - heightHeader - 80 - 60, 
        width:'100%', 
        paddingHorizontal: 20,
        paddingVertical: 30,  
        justifyContent:'flex-start',
        alignItems:'center' ,
    },
    
    textoHeaderProbeModbus: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },    

    textoEquipamentos: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#FFFFFF', 
    },
    
    textoSlave: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#737373',        
    },

    textoStatus: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 18,        
        color:'#2E8C1F',        
    }, 

    textoItensSelecao: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 22,        
        color:'#FFFFFF',        
    },

    textoButtonSalvarMedicao:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',
    }, 

    buttonSalvarMedicao:{
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },

    /********** TELA DE CONFIGURAÇÃO DO CODI **********/

    containerHeaderProbeCODI: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },
  
    containerListaCodi: {
        height: screenHeight - heightHeader - 80 - 60, 
        width:'100%', 
        paddingHorizontal: 20,
        paddingVertical: 30,  
        justifyContent:'flex-start',
        alignItems:'center' ,
    },

    buttonSalvarCodi:{
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    }, 
    
    textoButtonSalvarCodi:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',
    },    

    /********** TELA DE CONFIGURAÇÃO DO OTA **********/

    containerHeaderProbeOTA: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },
    
    textoHeaderProbeOTA: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 18,        
        color:'#2E8C1F', 
    },

    containerTextoFirmwareAtual:{
        width:'100%',
        height: 60,
        paddingHorizontal: 20,
        paddingVertical: 0,
        flexDirection:'column',
        justifyContent:'center',
        alignItems:'flex-start',
        backgroundColor:'#737373'
    },

    containerBotoesProbeOTA: {
        height:80, 
        width:'100%', 
        paddingHorizontal: 20, 
        justifyContent:'space-between',
        alignItems:'center' ,
        flexDirection:'row',
        paddingTop:10 
    },

    textoFirmwareAtualTitulo: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 12,        
        color:'#FFFFFF', 
    },

    textoFirmwareAtual: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        width:'100%',
        textAlign:'center',
        color:'#FFFFFF',        
    },

    textoButtonPROD:{
        fontFamily: 'Exo2_400Regular',
        fontSize:15,
        textAlign: 'center',
        color:'#FFFFFF',
    },    

    textoButtonHMG:{
        fontFamily: 'Exo2_400Regular',
        fontSize:15,
        textAlign: 'center',
        color:'#FFFFFF',
    },

    buttoPROD:{
        height:heightButton,
        width:'45%',
        backgroundColor:'#737373',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },

    buttoHMG:{
        height:heightButton,
        width:'45%', 
        backgroundColor:'#737373',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    }, 

    /********** TELA DE CONFIGURAÇÃO DO ION **********/

    containerHeaderProbeION: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerListaIon: {
        height: screenHeight - heightHeader - 80 - 60, 
        width:'100%', 
        paddingHorizontal: 20,
        paddingVertical: 30,  
        justifyContent:'flex-start',
        alignItems:'center' ,
    },    

    buttonSalvarIon:{
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    }, 
    
    textoButtonSalvarIon:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',
    },
    
    buttonBuscarIon:{
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    }, 
    
    textoButtonBuscarIon:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',
    },

    textoLoadingBusca:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:18,
        textAlign:'center',
        fontWeight: "400",
        lineHeight: 24,
        color: '#A3A3A3',
    },
      
    textoLoadingBuscaInfo:{
        fontFamily: 'Exo2_400Regular',
        fontSize:16,
        textAlign:'center',
        fontWeight: "400",        
        color: '#737373',
    },

    textoDados: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 22,        
        color:'#2E8C1F',        
    },    

    /********** TELA DE CONFIGURAÇÃO DO Pulso **********/

    containerHeaderProbePulso: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },
  
    containerListaPulso: {
        height: screenHeight - heightHeader - 80 - 60, 
        width:'100%', 
        paddingHorizontal: 20,
        paddingVertical: 30,  
        justifyContent:'flex-start',
        alignItems:'center' ,
    },

    textoHeaderProbePulso: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },    
})