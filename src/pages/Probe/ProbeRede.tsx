import React, { useEffect, useRef, useState } from "react";
import { View, Text, TouchableOpacity, FlatList } from "react-native"
import { EventRegister } from "react-native-event-listeners";
import Clipboard from "@react-native-clipboard/clipboard";
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import {screenHeight, styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconRefresh from '../../assets/svg/icon_refresh.svg';
import IconSave from '../../assets/svg/icon_save-01.svg';
import IconSetting from '../../assets/svg/icon_settings-01.svg';
import IconSettingDefault from '../../assets/svg/icon_refresh-default.svg';
import IconCopy from '../../assets/svg/icon_copy.svg';
import IconApn from '../../assets/svg/icon_gsm.svg';
import IconX from '../../assets/svg/icon_x.svg';
import IconPlus from '../../assets/svg/icon_plus.svg';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import {StackTypes} from '../../routes/index'

// componentes
import ComboBox from "../../componentes/ComboBox";
import TextEntry from "../../componentes/TextEntry";
import Loading from "../../componentes/Loading";
import Divider from "../../componentes/Divider";
import CardApn from "../../componentes/CardApn";
import Space from "../../componentes/Space";

// constantes
import {ListaModosEthernet, ListaTiposConexao, TimeoutTipoConexao, TipoConexao } from "../../constantes";

// funcoes mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT } from "../../services/mqtt";

// modal messages
import MessageBoxSucesso from "../../modal/messagebox/Sucesso";
import MessageBoxErro from "../../modal/messagebox/Erro";
import MessageBoxPergunta from "../../modal/messagebox/Pergunta";

// estrutura
import { ApnSettings, ProbeSettings } from "../../data";

const ProbeRede = ({route}) => {
    
   // referencia para o bottom sheet
    const refRBSheetApn = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetConfigApn = useRef<{ open: () => void, close: () => void }>(null);

    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');

    // controle timeout loading
    const timeoutWait = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);    

    // mensagens modal
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);    
    const [showMessagePerguntaDefault, setShowMessagePerguntaDefault] = useState<boolean>(false);
    const [showMessagePerguntaSave, setShowMessagePerguntaSave] = useState<boolean>(false);
    const [showMessagePerguntaSaveApn, setShowMessagePerguntaSaveApn] = useState<boolean>(false);
    const [showMessagePerguntaDelApn, setShowMessagePerguntaDelApn] = useState<boolean>(false);
    const [textoMessage, setTextoMessage] = useState<string>('');

    // tipo de conexão
    const [tipoConexao, setTipoConexao] = useState<number>(route.params?.probeSettings.tipoConexao);

    // informações WIFI
    const [wifiNome, setWifiNome] = useState<string>('');
    const [wifiSenha, setWifiSenha] = useState<string>('');
    const [wifiIP, setWifiIP] = useState<string>('0.0.0.0');
    const [wifiMascara, setWifiMascara] = useState<string>('0.0.0.0');
    const [wifiGateway, setWifiGateway] = useState<string>('0.0.0.0');
    const [wifiDNS, setWifiDNS] = useState<string>('0.0.0.0');
    const [wifiSinal, setWifiSinal] = useState<number>(0);
    const [wifiMAC, setWifiMAC] = useState<string>('00:00:00:00:00:00');

    // informações gsm
    const [gsmRede, setGsmRede] = useState<string>('---');
    const [gsmModem, setGsmModem] = useState<string>('---');
    const [gsmIMEI, setGsmIMEI] = useState<string>('---');
    const [gsmIMSI, setGsmIMSI] = useState<string>('---');
    const [gsmICCID, setGsmICCID] = useState<string>('---');
    const [gsmSinal, setGsmSinal] = useState<number>(0);
    const [gsmOperadora, setGsmOperadora] = useState<string>('---');
    const [gsmAPN, setGsmAPN] = useState<string>('---');
    const [gsmIP, setGsmIP] = useState<string>('0.0.0.0');
    const [gsmGateway, setGsmGateway] = useState<string>('0.0.0.0');
    const [gsmMascara, setGsmMascara] = useState<string>('0.0.0.0');
    const [gsmLatitude, setGsmLatitude] = useState<number>(0);
    const [gsmLongitude, setGsmLongitude] = useState<number>(0);

    // informações ETHERNET
    const [ethernetIP, setEthernetIP] = useState<string>('0.0.0.0');
    const [ethernetMascara, setEthernetMascara] = useState<string>('0.0.0.0');
    const [ethernetGateway, setEthernetGateway] = useState<string>('0.0.0.0');
    const [ethernetDNS, setEthernetDNS] = useState<string>('0.0.0.0');
    const [ethernetMAC, setEthernetMAC] = useState<string>('00:00:00:00:00:00');
    const [ethernetDHCP, setEthernetDHCP] = useState<number>(route.params?.probeSettings.ethernetDHCP);
    const [modoAtual, setModoAtual] = useState<number>(route.params?.probeSettings.ethernetDHCP);
    const [ipAtual, setIpAtual] = useState<string>('0.0.0.0');
    const [mascaraAtual, setMascaraAtual] = useState<string>('0.0.0.0');
    const [gatewayAtual, setGatewayAtual] = useState<string>('0.0.0.0');
    const [dnsAtual, setDnsAtual] = useState<string>('0.0.0.0');

    /* recebe as configurações das apn's */
    const [apnAtual, setApnAtual] = useState<number>(0);
    const [apn, setApn] = useState<ApnSettings[]>([]);
    const [nameApn, setNameApn] = useState<string>('');
    const [userApn, setUserApn] = useState<string>('');
    const [passApn, setPassApn] = useState<string>('');

    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
        
    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();
    };
    
   // solicitação para o MQTT
   const EscreveMQTT = (variable:string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error){}
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable:string) => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable);
        }
        catch(error) {}
    }        

    // comando de retornar as configurações do tipo de conexão atual
    const getConexaoMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // inicia loading
            setLoading(true);
            setTextoLoading('Informações da rede atual...');

            switch(tipoConexao){
                case TipoConexao.WIFI:

                    // envio de solicitação de configuração da rede ethernet
                    EscreveMQTT('sys_config_wifi');

                    // leitura da solicitação 
                    LerMQTT('sys_config_wifi');

                break;
                case TipoConexao.GSM:

                    // envio de solicitação de configuração da rede ethernet
                    EscreveMQTT('sys_config_gsm');

                    // leitura da solicitação 
                    LerMQTT('sys_config_gsm');

                break;                    
                case TipoConexao.ETHERNET:

                    // envio de solicitação de configuração da rede ethernet
                    EscreveMQTT('sys_config_eth');

                    // leitura da solicitação 
                    LerMQTT('sys_config_eth');

                break;
            }

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);                
            }, timeoutWait);  
            
            setTimeoutIDMessage(Number(timeout_id)); 
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    // comando de retornar as configurações de fabrica do tipo de conexão
    const setDefaultConexaoMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // inicia loading
            setLoading(true);
            setTextoLoading('Configurações de fábrica...');

            // envio de solicitação de configuração da rede ethernet
            EscreveMQTT('wifi_reset');

            // leitura da solicitação 
            LerMQTT('wifi_reset');            
            
            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    // comando de salvar as configurações do tipo de conexão
    const setSaveConexaoMQTT = () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {      

            // inicia loading
            setLoading(true);
            setTextoLoading('Salvando as configurações...');

            switch(tipoConexao){
                case TipoConexao.WIFI:

                    // envio de solicitação de configuração da rede ethernet
                    EscreveMQTT('wifi_config', JSON.stringify({pass:wifiSenha, ssid:wifiNome}));

                    // leitura da solicitação 
                    LerMQTT('wifi_config');

                break;
                case TipoConexao.ETHERNET:                    

                    if (ethernetDHCP) {

                        // envio de solicitação de configuração da rede ethernet
                        EscreveMQTT('ethernet_reset_config');

                        // leitura da solicitação 
                        LerMQTT('ethernet_reset_config');
                    }
                    else {
                        // envio de solicitação de configuração da rede ethernet
                        EscreveMQTT('ethernet_config', JSON.stringify({dns:ethernetDNS, gw:ethernetGateway, ip:ethernetIP, mask:ethernetMascara}));

                        // leitura da solicitação 
                        LerMQTT('ethernet_config');                        
                    }

                break;
            }

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);
            }, timeoutWait);  
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));

        } 
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };
    
    /**
     * solicita ao servidor mqtt as apn's configuradas na probe
     */
    const getApnMQTT = () => {

        /* se conectado com o servidor mqtt */
        if(isMQTTConnected()) {      

            /* inicia loading */
            setLoading(true);
            setTextoLoading("APN's configuradas");

                /* envio de solicitação das apn's configuradas */
                EscreveMQTT('get_apns');

                /* inscrição no tópico de para leitura das configurações */
                LerMQTT('get_apns');

            /* loading vai durar até 60 segundos */
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);                
            }, timeoutWait);  
            
            setTimeoutIDMessage(Number(timeout_id)); 
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    /**
     * salvar as configurações da apn via mqtt
     */
    function setSaveApnMQTT (name_apn: string, user_apn: string, pass_apn: string) {

        /* se foi inserido a apn */
        if(name_apn.length <= 0)  {
            setTextoMessage('Nenhuma APN foi inserido.');
            setShowMessageErro(true);
            return;
        }

        /* se foi inserido o usuario da apn */
        if(user_apn.length <= 0)  {
            setTextoMessage('Nenhum usuário da APN foi inserido.');
            setShowMessageErro(true);
            return;
        }        

        /* se foi inserido a senha da apn */
        if(pass_apn.length <= 0)  {
            setTextoMessage('Nenhuma senha de usuário da APN foi inserido.');
            setShowMessageErro(true);
            return;
        }

        /* se conectado com o servidor mqtt */
        if(isMQTTConnected()) {      

            /* inicia loading */
            setLoading(true);
            setTextoLoading('Salvando a configuração...');

            /* envio de solicitação para salvar a apn */
            EscreveMQTT('add_apn', JSON.stringify({apn:name_apn, user:user_apn, pass: pass_apn}));

            // leitura da solicitação 
            LerMQTT('add_apn');

            /* loading vai durar 60 segundos */
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);
            }, timeoutWait);  
            
            /* pega o id do timeout atual */
            setTimeoutIDMessage(Number(timeout_id));
        } 
        else {
            /* finaliza o loading */
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    };

    /**
     * remove apn selecionada da configuração
     * @param apn_atual 
     */
    function setRemoveApnMQTT (apn_atual: number) {

        /* se conectado com o servidor mqtt */
        if(isMQTTConnected()) {      

            /* inicia loading */
            setLoading(true);
            setTextoLoading('Excluindo a APN...');

            /* envio de solicitação ao servidor mqtt para excluir a apn */
            EscreveMQTT('remove_apn', JSON.stringify({apn:apn[apn_atual].apn}));

            // leitura da solicitação 
            LerMQTT('remove_apn');

            /* loading vai durar 60 segundos */
            const timeout_id = setTimeout(function() {
                setTimeoutMessage(true);
            }, timeoutWait);  
            
            /* pega o id do timeout atual */
            setTimeoutIDMessage(Number(timeout_id));

        } 
        else {
            /* finaliza o loading */
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    /* remove uma determinada apn da lista */
    function AddApn(apn: ApnSettings[], apn_name: string, apn_user: string, apn_pass: string)
    {        
        /* adiciona a apn a lista */
        apn.push({apn: apn_name, user: apn_user, pass: apn_pass});        

        /* atualiza lista de apn's */
        setApn(apn);
    }

    /* remove uma determinada apn da lista */
    function RemoveApn(apn: ApnSettings[], apn_atual: number)
    {
        /* exclui a apn selecionada da lista */
        apn?.splice(apn_atual, 1);

        /* atualiza lista de apn's */
        setApn(apn);
    }


    // executa sempre que a variavel timeout das mensagens for acionada
    useEffect(() => {

        // se timeout não alarmado
        if(!timeoutMessage)
            return
        
        // verifica se o loading esta carregando
        if(!loading)
            return;

        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);
    
        // timeout não alarmado
        setTimeoutMessage(false);

    }, [timeoutMessage]);    

    // executa uma vez sempre que iniciar a tela
    useEffect(() => {
        
        // pega as informações da rede atual
        getConexaoMQTT();

    }, []);

    
    // executa uma vez sempre que iniciar a tela
    useEffect(() => {

        if ( (modoAtual === 0) && (ethernetDHCP === 1) ) {

            // pega as informações da rede atual
            setEthernetIP('---.---.---.---');
            setEthernetMascara('---.---.---.---');
            setEthernetGateway('---.---.---.---');
            setEthernetDNS('---.---.---.---');
        }
        else if(ethernetIP === '---.---.---.---') {

            setEthernetIP(ipAtual);
            setEthernetMascara(mascaraAtual);
            setEthernetGateway(gatewayAtual);
            setEthernetDNS(dnsAtual);
        }

    }, [ethernetDHCP]);    
    

    // executa sempre que a variavel 'wifiMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('wifiMQTT', mqtt => {                                    
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // recebe as informações da rede ethernet
            let _probe: ProbeSettings = mqtt;
            
            setWifiNome(_probe.wifiNome ?? '');
            setWifiSenha(_probe.wifiSenha ?? '');
            setWifiIP(_probe.wifiIP ?? '0.0.0.0');
            setWifiMascara(_probe.wifiMascara ?? '0.0.0.0');
            setWifiGateway(_probe.wifiGateway ?? '0.0.0.0');
            setWifiDNS(_probe.wifiDNS ?? '0.0.0.0');
            setWifiSinal(_probe.wifiSinal ?? 0);
            setWifiMAC(_probe.wifiMAC ?? '00:00:00:00:00:00');

            setLoading(false);                   
                    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'gsmMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('gsmMQTT', mqtt => {                                    
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // recebe as informações da rede ethernet
            let _probe: ProbeSettings = mqtt;
            
            setGsmRede(_probe.gsmRede ?? '---');
            setGsmModem(_probe.gsmModem ?? '---');
            setGsmIMEI(_probe.gsmIMEI ?? '---');
            setGsmIMSI(_probe.gsmIMSI ?? '---');
            setGsmICCID(_probe.gsmICCID ?? '---');
            setGsmSinal(_probe.gsmSinal ?? 0);
            setGsmOperadora(_probe.gsmOperadora ?? '---');
            setGsmAPN(_probe.gsmAPN ?? '---');
            setGsmIP(_probe.gsmIP ?? '0.0.0.0');
            setGsmMascara(_probe.gsmMascara ?? '0.0.0.0');
            setGsmGateway(_probe.gsmGateway ?? '0.0.0.0');            
            setGsmLatitude(_probe.gsmLatitude ?? 0);
            setGsmLongitude(_probe.gsmLongitude ?? 0);

            setLoading(false);        
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'ethernetMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ethernetMQTT', mqtt => {                                    
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // recebe as informações da rede ethernet
            let _probe: ProbeSettings = mqtt;
            
            setEthernetIP(_probe.ethernetIP ?? '0.0.0.0');
            setEthernetMascara(_probe.ethernetMascara ?? '0.0.0.0');
            setEthernetGateway(_probe.ethernetGateway ?? '0.0.0.0');
            setEthernetDNS(_probe.ethernetDNS ?? '0.0.0.0');
            setEthernetMAC(_probe.ethernetMAC ?? '0.0.0.0');
            setModoAtual(_probe.ethernetDHCP ?? 0);
            setEthernetDHCP(_probe.ethernetDHCP ?? 0);
            
            setIpAtual(_probe.ethernetIP ?? '0.0.0.0');
            setMascaraAtual(_probe.ethernetMascara ?? '0.0.0.0');
            setGatewayAtual(_probe.ethernetGateway ?? '0.0.0.0');
            setDnsAtual(_probe.ethernetDNS ?? '0.0.0.0');

            setLoading(false);
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'saveWifiMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('saveWifiMQTT', mqtt => {                                    
                
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            setLoading(false);

            // se esta salvando
            if(mqtt){                
                setTextoMessage('As configurações da rede Wi-Fi foram salvas. Necessário reiniciar a Probe.');
                setShowMessageSucesso(true);
            }
            else {
                setTextoMessage('Não foi possível salvar as configurações da rede Wi-Fi.')
                setShowMessageErro(true);
            }
                        
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'saveEthernetMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('saveEthernetMQTT', mqtt => {                                    
              
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            setLoading(false);

            // se esta salvando
            if(mqtt){                
                setTextoMessage('As configurações da rede Ethernet foram salvas. Necessário reiniciar a Probe.');
                setShowMessageSucesso(true);
            }
            else {
                setTextoMessage('Não foi possível salvar as configurações da rede Ethernet.')
                setShowMessageErro(true);
            }            
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    /**
     * executa sempre que a variavel 'apnConfigMQTT' for ouvida
     */
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('apnConfigMQTT', mqtt => {                                    
            
            /* timeout message não alarmado */
            clearTimeout(timeoutIDMessage);

            /* recebe as informações das apns configuradas */
            setApn(mqtt);
            
            setLoading(false);                   
                    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    /**
     * executa sempre que a variavel 'apnAddMQTT' for ouvida
     */
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('apnAddMQTT', mqtt => {                                    
            
            /* timeout message não alarmado */
            clearTimeout(timeoutIDMessage);

            /* verifica se apn foi adicionada */
            if(mqtt) {

                /* depois de configurada adiciona a apn a lista*/
                AddApn([...apn], nameApn, userApn, passApn);

                setTextoMessage('APN adicionada com sucesso.');
                setShowMessageSucesso(true);                
            }
            else {
                setTextoMessage('Não foi possível adicionar a APN.');
                setShowMessageErro(true);                
            }            
            
            setLoading(false);                   
                    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);    

    /**
     * executa sempre que a variavel 'apnRemoveMQTT' for ouvida
     */
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('apnRemoveMQTT', mqtt => {                    

            /* timeout message não alarmado */
            clearTimeout(timeoutIDMessage);

            /* verifica se apn foi removida */
            if(mqtt) {

                /* exclui a apn selecionada */
                RemoveApn([...apn], apnAtual);

                setTextoMessage('APN removida com sucesso.');
                setShowMessageSucesso(true);                
            }
            else {
                setTextoMessage('Não foi possível remover a APN.');
                setShowMessageErro(true);                
            }            
            
            setLoading(false);                   
                    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]); 

    /**
     * copia o imei do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmIMEI = () => {
        Clipboard.setString(gsmIMEI);
    };
    
    /**
     * copia o imsi do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmIMSI = () => {
        Clipboard.setString(gsmIMSI);
    };

    /**
     * copia o iccid do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmICCID = () => {
        Clipboard.setString(gsmICCID);
    };

    /**
     * copia a operadora e o sinal do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmOperadora = () => {
        Clipboard.setString(`Operadora: ${gsmOperadora}\nSinal: ${gsmSinal} db`);
    };    
        
    /**
     * copia o apn do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmAPN = () => {
        Clipboard.setString(gsmAPN);
    };
    
    /**
     * copia o ip completo do gsm para utilizar em qualquer outro aplicativo
     */
    const copyToGsmIP = () => {
        Clipboard.setString(`IP: ${gsmIP}\nMáscara: ${gsmMascara}\nGateway: ${gsmGateway}\n`);
    };
    
    /**
     * copia o ip completo do eth para utilizar em qualquer outro aplicativo
     */
    const copyToEthIP = () => {
        Clipboard.setString(`IP: ${ethernetIP}\nMáscara: ${ethernetMascara}\nGateway: ${ethernetGateway}\nDNS: ${ethernetDNS}\nMAC: ${ethernetMAC}\n`);
    };    
          
    /**
     * copia o ip completo do wifi para utilizar em qualquer outro aplicativo
     */
    const copyToWifiIP = () => {
        Clipboard.setString(`SSID: ${wifiNome}\nSenha: ${wifiSenha}\nIP: ${wifiIP}\nMáscara: ${wifiMascara}\nGateway: ${wifiGateway}\nDNS: ${wifiDNS}\nMAC: ${wifiMAC}\n`);
    };  
    
    /**
     * apresenta tela de configuração das apn's e solicita as configurações existentes
     */
    const getApn = () => {

        /* apresenta a tela de configuração */
        refRBSheetApn.current?.open();

        /* loading vai durar até 60 segundos */
        setTimeout(function() {
            /* solcita as configurações */
            getApnMQTT();
        }, 500);        
    }

    return(

        <>
            <Loading animating={loading} text={textoLoading}/>
            
            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}/>

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}/>

            <MessageBoxPergunta
                visivel={showMessagePerguntaDefault} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                                setShowMessagePerguntaDefault(false);                                        
                                setDefaultConexaoMQTT(); 
                            }}
                onCancel={() => { setShowMessagePerguntaDefault(false) }}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSave} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                                setShowMessagePerguntaSave(false);                                        
                                setSaveConexaoMQTT(); 
                            }}
                onCancel={() => { setShowMessagePerguntaSave(false) }}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSaveApn} 
                titulo="Atenção" 
                descricao="Deseja realmente salvar a configuração da APN?"
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                                setShowMessagePerguntaSaveApn(false);
                                refRBSheetConfigApn.current?.close();
                                setSaveApnMQTT(nameApn, userApn, passApn); 
                            }}
                onCancel={() => { setShowMessagePerguntaSaveApn(false) }}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaDelApn} 
                titulo="Atenção" 
                descricao={`Deseja realmente excluir a APN ${apn?.[apnAtual]?.apn ?? ''}?`}
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                                setShowMessagePerguntaDelApn(false);
                                setRemoveApnMQTT(apnAtual); 
                            }}
                onCancel={() => { setShowMessagePerguntaDelApn(false) }}
            />

            <View style={styles.containerTela}>

                
                {/* header */}
                <View style={{...styles.containerHeaderProbeRede}}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID="button-back">
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderProbeRede}>REDE</Text>
                    </View>

                </View>

                {/* tipo de conexão */} 
                <View style={{paddingHorizontal:20, height:80, justifyContent:'flex-end'}}>

                    <ComboBox
                        value={tipoConexao}
                        onValueChange={setTipoConexao}                  
                        data={ListaTiposConexao}
                        height={60}
                        width={'100%'}
                        backgroundColor={'#FFFFFF'}
                        focusColor={'#2E8C1F'}
                        borderColor={'#737373'}
                        borderRadius={5}
                        borderWidth={1.5}
                        textoPlaceHolder='Selecione o tipo de conexão'
                        textoLabel='Tipo de Conexão'
                        textoCor={'#2E8C1F'}
                    />

                </View>

                {/* botões */}
                <View style={styles.containerBotoesProbeRede}>

                    {                          
                        (tipoConexao === TipoConexao.WIFI) &&
                            <TouchableOpacity onPress={() => { 
                                                        setTextoMessage(`Deseja realmente retornar a rede Wi-Fi as configurações de fábrica?`);
                                                        setShowMessagePerguntaDefault(true);
                                                    }}
                                              testID="button-factory">
                                <IconSettingDefault color={"#737373"} width={34} height={34}/>
                            </TouchableOpacity>
                    }
                        
                    {
                        (tipoConexao === TipoConexao.WIFI) &&
                            <TouchableOpacity onPress={() => { 
                                                        setTextoMessage(`Deseja realmente salvar as configurações feitas para a rede Wi-Fi?`);
                                                        setShowMessagePerguntaSave(true);
                                                    }}
                                              testID="button-save-wifi">
                                <IconSave color={"#737373"} width={30} height={30}/>
                            </TouchableOpacity>
                    }

                    {
                        (tipoConexao === TipoConexao.WIFI) &&
                            <TouchableOpacity onPress={copyToWifiIP}
                                              testID="button-copy-wifi">
                                <IconCopy color={"#737373"} width={30} height={30}/>
                            </TouchableOpacity>                            
                    }                    

                    {
                        (tipoConexao === TipoConexao.ETHERNET) &&
                            <TouchableOpacity onPress={() => { 
                                                        setTextoMessage((ethernetDHCP) ? `Deseja realmente alterar a rede Ethernet para DHCP ?` 
                                                                                       : `Deseja realmente salvar as configurações de IP Fixo da rede Ethernet ?`);
                                                        setShowMessagePerguntaSave(true);
                                                    }}
                                              testID="button-eth">
                                <IconSave color={"#737373"} width={30} height={30}/>
                            </TouchableOpacity>                            
                    }

                    {
                        (tipoConexao === TipoConexao.ETHERNET) &&
                            <TouchableOpacity onPress={copyToEthIP}
                                              testID="button-copy-eth">
                                <IconCopy color={"#737373"} width={30} height={30}/>
                            </TouchableOpacity>                            
                    }

                    {
                        ((tipoConexao === TipoConexao.GSM) || (tipoConexao === TipoConexao.NARROW_BAND)) &&
                            <TouchableOpacity onPress={() =>getApn()}
                                              testID="button-apn">
                                <IconApn color={"#737373"} width={30} height={30}/>
                            </TouchableOpacity>                            
                    }                                        

                    <TouchableOpacity onPress={() => getConexaoMQTT() } testID="button-refresh">
                        <IconRefresh color={"#737373"} width={30} height={30}/>
                    </TouchableOpacity>
                </View>

                {
                    // WI-FI
                    (tipoConexao == TipoConexao.WIFI) &&
                        <View style = {{paddingHorizontal:20, gap:10}}>
                            <TextEntry
                                value={wifiNome}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='Rede Wi-Fi'
                                textoLabel= 'Rede Wi-Fi'
                                type='texto'
                                onValueChange= {setWifiNome}
                            />
                            
                            <TextEntry
                                value={wifiSenha}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='Senha'
                                textoLabel= 'Senha'
                                type='texto'
                                onValueChange= {setWifiSenha}
                            />     

                            {/* informações do wifi da probe */}
                            <View style = {styles.containerInfoWifi}>

                                <View style = {{height:40, width:'100%', flexDirection:'row'}}>
                                    <View style = {{height:'100%', width:'90%'}}>
                                        <Text style={styles.textoTitle}>IP</Text>
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{wifiIP}</Text>
                                    </View>
                                    <View style = {{height:'100%', width:'10%', alignItems:'flex-end'}}>
                                        <IconSetting color={"#737373"} width={30} height={30}/>
                                    </View>                        
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Máscara</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{wifiMascara}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Gateway</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${wifiGateway}`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>DNS</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${wifiDNS}`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Sinal</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${wifiSinal} db`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>MAC Address</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${wifiMAC}`}</Text>
                                </View>
                            </View>

                        </View>
                }

                {
                    // GSM ou NARROW BAND
                    ( (tipoConexao == TipoConexao.GSM) || (tipoConexao == TipoConexao.NARROW_BAND))&&
                        <View style = {{paddingHorizontal:20, gap:10}}>

                            {/* informações do gsm da probe */}
                            <View style = {styles.containerInfoGSM}>

                                <View style = {{height:40, width:'100%', flexDirection:'row'}}>
                                    <View style = {{height:'100%', width:'90%'}}>
                                        <Text style={styles.textoTitle}>Rede</Text>
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{gsmRede}</Text>
                                    </View>
                                    <View style = {{height:'100%', width:'10%', alignItems:'flex-end'}}>
                                        <IconSetting color={"#737373"} width={30} height={30}/>
                                    </View>                        
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Modem</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{gsmModem}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>IMEI</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmIMEI} testID="button-copy-imei">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmIMEI}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>IMSI</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmIMSI} testID="button-copy-imsi">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmIMSI}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>ICCID</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmICCID} testID="button-copy-iccid">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmICCID}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>
                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Operadora</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmOperadora} testID="button-copy-operadora">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmOperadora}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Sinal</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmOperadora}  testID="button-copy-sinal">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmSinal} db`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>                                        
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>APN</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmAPN}  testID="button-copy-apn">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmAPN}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>
                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>IP</Text>
                                    <TouchableOpacity style={{flexDirection:'row', gap: 15}} onPress={copyToGsmIP}  testID="button-copy-ip">
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmIP}`}</Text>
                                        <IconCopy color={"#737373"} width={20} height={20}/>
                                    </TouchableOpacity>
                                    
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Gateway</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmGateway}`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Máscara</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmMascara}`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Latitude</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmLatitude}`}</Text>
                                </View>
                                <View style = {{height:40, width:'80%'}}>
                                    <Text style={styles.textoTitle}>Longitude</Text>
                                    <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{`${gsmLongitude}`}</Text>
                                </View>                                

                            </View>

                        </View>
                }

                {
                    // ETHERNET
                    (tipoConexao == TipoConexao.ETHERNET) &&
                        <View style = {{paddingHorizontal:20, gap:10}}>

                            <ComboBox
                                disable={false}
                                height={60} 
                                width={'100%'}
                                value={ethernetDHCP}
                                onValueChange={setEthernetDHCP}
                                data={ListaModosEthernet}
                                textoLabel={'Modo'}
                                fontSize={16}
                                textoPlaceHolder={'Modo'}
                                fontSizePlaceHolder={14}
                                textoCor={'#737373'}
                                borderRadius={8}
                                backgroundColor={'#FFFFFF'}
                            />

                            <View style={{height:0, width:'100%', justifyContent:'center'}}>
                                <Divider />
                            </View>                            
                            
                            <TextEntry
                                value={ethernetIP}
                                editable={!ethernetDHCP}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='IP'
                                textoLabel= 'IP'
                                type='ip'
                                onValueChange= {setEthernetIP}
                            />
                            
                            <TextEntry
                                value={ethernetMascara}
                                editable={!ethernetDHCP}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='Máscara'
                                textoLabel= 'Máscara'
                                type='ip'
                                onValueChange= {setEthernetMascara}
                            />     

                            <TextEntry
                                value={ethernetGateway}
                                editable={!ethernetDHCP}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='Gateway'
                                textoLabel= 'Gateway'
                                type='ip'
                                onValueChange= {setEthernetGateway}
                            />

                            <TextEntry
                                value={ethernetDNS}
                                editable={!ethernetDHCP}
                                height={60}
                                width={'100%'}
                                backgroundColor={'#FFFFFF'}
                                placeHolder='DNS'
                                textoLabel= 'DNS'
                                type='ip'
                                onValueChange= {setEthernetDNS}
                            />                            

                            {/* informações da ethernet da probe */}
                            <View style = {styles.containerInfoEthernet}>

                                <View style = {{height:40, width:'100%', flexDirection:'row'}}>
                                    <View style = {{height:'100%', width:'90%'}}>
                                        <Text style={styles.textoTitle}>MAC Address</Text>
                                        <Text style={{...styles.textoSubtitle, paddingLeft: 10}}>{ethernetMAC}</Text>
                                    </View>
                                    <View style = {{height:'100%', width:'10%', alignItems:'flex-end'}}>
                                        <IconSetting color={"#737373"} width={30} height={30}/>
                                    </View>                        
                                </View>
                            </View>                        

                        </View>

                }                

            </View>
            <RBSheet ref={refRBSheetApn}  height={screenHeight} data-testid='rbSheet-apn'>

                <View  style={{height:screenHeight}}>

                    <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#FFFFFF'}}>                                                            
                        <TouchableOpacity onPress={() => refRBSheetApn.current?.close()} testID='button-close-apn'>
                            <IconX color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                    <View style={{height:30, width:'100%', justifyContent:'center', alignItems:'center'}}>
                        <Text style={styles.textoMensagem}>CONFIGURAÇÃO DA APN</Text>
                    </View>

                    {/* botões */}
                    <View style={styles.containerBotoesProbeModbus}>

                        <View style={{height:'100%', width:'100%', justifyContent:'flex-end', alignItems:'center', flexDirection:'row', gap: 20}}>
                        
                        <TouchableOpacity onPress={() => getApn()} testID="button-refresh-apn">
                            <IconRefresh color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoEquipamentos}>
                    <Text style={styles.textoEquipamentos}>Apn's</Text>
                        <TouchableOpacity onPress={ () => {
                                                            refRBSheetConfigApn.current?.open();
                                                            setNameApn('');
                                                            setUserApn('');
                                                            setPassApn('');
                                                        }}
                                                testID="button-plus-apn">
                            <IconPlus color={"#FFFFFF"} width={34} height={34}/>
                        </TouchableOpacity>
                </View>

                {/* lista de apn's */}
                <View style={styles.containerListaIon}>
                    <FlatList style={{marginTop: 10}}
                            ItemSeparatorComponent={Space}
                            data = {apn ?? []}
                            renderItem= { ({ item }) => 
                                            <CardApn
                                                apn={item.apn}
                                                user={item.user}
                                                password={item.pass}
                                                width={'100%'}                                                
                                                onPressDelApn= { () => {
                                                        setApnAtual(apn.findIndex(x => x.apn === item.apn) ?? 0);
                                                        setShowMessagePerguntaDelApn(true);
                                                }}                                                
                                            />
                                        }
                    />
                </View>

            </View>                
            </RBSheet> 
            {/* tela de configuração das apn's */}
            <RBSheet ref={refRBSheetConfigApn}  height={screenHeight} data-testid='rbSheet-config-apn'>

                <View  style={{height:screenHeight, paddingHorizontal:20, gap:20}}>

                    <View style={{height:60, width:'100%', flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#FFFFFF'}}>                                                            
                        <TouchableOpacity onPress={() => refRBSheetConfigApn.current?.close()} testID='button-close-config-apn'>
                            <IconX color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                    <View style={{height:50, width:'100%', alignItems:'center'}}>
                        <TextEntry 
                            value={nameApn}
                            maxLength={30}
                            height={'100%'}
                            width={'100%'}
                            editable={true}
                            placeHolder='Entre com a Apn'
                            onValueChange={setNameApn}
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'Apn'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-apn"                           
                        /> 
                    </View>

                    <View style={{height:50, width:'100%', alignItems:'center'}}>
                        <TextEntry 
                            value={userApn}
                            maxLength={20}
                            height={'100%'}
                            width={'100%'}
                            editable={true}
                            placeHolder='Entre com a usuário da Apn'
                            onValueChange={setUserApn}
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'Usuário'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-user-apn"
                        /> 
                    </View>

                    <View style={{height:50, width:'100%', alignItems:'center'}}>
                        <TextEntry 
                            value={passApn}
                            maxLength={20}
                            height={'100%'}
                            width={'100%'}
                            editable={true}
                            placeHolder='Entre com a senha do usuário da Apn'
                            onValueChange={setPassApn}
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'Senha'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-pass-apn"                           
                        /> 
                    </View>                    

                    <View style={{marginTop:30}}>
                        <TouchableOpacity style={styles.buttonSalvarIon} onPress={() => { setShowMessagePerguntaSaveApn(true); }} testID="button-apn-save">
                            <Text style = {styles.textoButtonSalvarIon}>{'SALVAR'}</Text>
                        </TouchableOpacity>
                    </View>

                </View>                

            </RBSheet>
        </>

    )
}

export default ProbeRede;