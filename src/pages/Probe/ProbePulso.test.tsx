import React from "react";
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import <PERSON>bePulso from "./ProbePulso";
import * as spyMQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import { NavigationContainer } from "@react-navigation/native";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";
import { MockPulsoSettings } from "../../../__mocks__/PulsoSettingsMock";
import { EventRegister } from "react-native-event-listeners";

jest.mock('sp-react-native-mqtt', () => 'MQTT');

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings }
    },
};

const mockRouteStatusON1 = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       pulso : true,
                       pulsoDevices: MockPulsoSettings,
                    }
    },
};

jest.useFakeTimers();

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

describe('ProbeION', () => {    

    beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
    });

    it('renderiza corretamente com a rota fornecida e modulo off', () => {
    
        const {unmount} = render(  <NavigationContainer>
                                <ProbePulso route={mockRoute} />
                            </NavigationContainer> );
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });

        expect(screen).toBeTruthy();

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('renderiza corretamente com a rota fornecida e modulo on', () => {
    
        const {unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                </NavigationContainer> );    

        expect(screen).toBeTruthy();

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {getByTestId} = render(   <NavigationContainer>
                                            <ProbePulso route={mockRoute}/>
                                        </NavigationContainer> );
        
        act(() => {
            fireEvent.press(getByTestId('button-back'));
        });
    });

    it('renderiza corretamente ao clicar no botão para ativar e desativar o modulo pulso', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbePulso route={mockRouteStatusON1} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });''    
    });
    
    it('renderiza corretamente ao clicar no botão de ativar modulo pulso e cancelar', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });        
    });
    
    it('renderiza corretamente ao clicar no botão para desativar o modulo pulso e mqtt conectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });

    it('renderiza corretamente ao clicar no botão para ativar o modulo pulso e mqtt conectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });
    
    it('renderiza corretamente ao clicar no botão para ativar o modulo pulso e mqtt desconectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });
    
    it('renderiza corretamente ao clicar no botão para limpar configuração pulso e modulo ON e clica no botão Não', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração pulso e modulo ON e clica no botão Sim, mas mqtt desconectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração pulso e modulo ON e clica no botão Sim e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração pulso e modulo ON e clica no botão Sim, mas módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

    });    

    it('renderiza corretamente ao clicar no botão para ler a configuração pulso e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });        
    });    

    it('renderiza corretamente ao clicar no botão para ler a configuração pulso e modulo ON e MQTT desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });

    it('renderiza corretamente ao clicar no botão para ler a configuração pulso e modulo ON e MQTT conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });     

    it('renderiza corretamente ao clicar no botão para adicionar um pulso e módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });
    });

    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado e fecha escolha', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbePulso route={mockRouteStatusON1} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-close-tipo-pulso'));
            });
        });        
    });


    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado mas fecha tela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('PULSO 1'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-pulso-close'));
            });
        });
    });


    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado, adiciona e cancela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('PULSO 1'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-pulso-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Não'));
            });
        });                        
    }); 
    
    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado, adiciona, confirma mas não existe tipo de medição', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('PULSO 1'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-pulso-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });

    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado, adiciona, confirma mas tipo de contato inválido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbePulso route={ mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('PULSO 1'));
            });
        });

        act(() => {
            fireEvent(getByTestId('combo-medicao'), 'change', { nativeEvent: { value: { id: 4, descricao: 'Contato' }}})
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-pulso-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });
    
    /*
    it('renderiza corretamente ao clicar no botão para adicionar um pulso, módulo ativado, adiciona, confirma mas tipo de contato inválido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText, queryByText} = render(<NavigationContainer>
                                                    <ProbePulso route={ mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('PULSO 1'));
            });
        });

        const mockState = { id: 4, descricao: 'Contato' };

        await act( async() => {
            fireEvent(getByTestId('combo-medicao'), 'change', mockState)            
        });

        expect(getByTestId('combo-medicao').props.value).toEqual(mockState);        

        await act( async () => {
            fireEvent(getByTestId('combo-tipo'), 'press', { nativeEvent: { value: { id: 0, descricao: 'Fechado' }}})
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-pulso-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });    
    */

    it('renderiza corretamente ao clicar no botão para salvar mas módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para salvar mas módulo ativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbePulso route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));
            });
        });
    });


    it('deve executar o fluxo esperado ao ler o status do modulo do pulso = false', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoStatusMQTT') {
            callback((false)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                  </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler o status do modulo do pulso = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoStatusMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                  </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });     
    
    it('deve executar o fluxo esperado ao ler a configuração do pulso, mas veio vazio', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'pulsoConfigMQTT') {
                callback(([])); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRoute} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do pulso', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'pulsoConfigMQTT') {
                callback((MockPulsoSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRoute} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
    
    it('deve executar o fluxo esperado ao deletar toda configuração com erro', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoResetMQTT') {
            callback((false)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                  </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });  
    
    it('deve executar o fluxo esperado ao deletar toda configuração com sucesso', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoResetMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                  </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao deletar toda configuração com sucesso, ao salvar contador de pulso e cancelou', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoResetMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbePulso route={mockRouteStatusON1} />
                                                        </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-save'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao deletar toda configuração com sucesso, ao salvar contador de pulso, confirmou e mqtt desconectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoResetMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbePulso route={mockRouteStatusON1} />
                                                        </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-save'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao configurar toda configuração com sucesso, ao salvar contador de pulso, confirmou e mqtt conectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoConfigMQTT') {
            callback((MockPulsoSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbePulso route={mockRouteStatusON1} />
                                                        </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-save'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });   
    
    it('deve executar o fluxo esperado ao adicionar toda configuração com sucesso', async () => {

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoAddMQTT') {
            callback((0)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar toda configuração com falha', async () => {

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoAddMQTT') {
            callback((3)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao remover uma determinada configuração com sucesso', async () => {

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoEndMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoEndMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao remover uma determinada configuração com falha', async () => {

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoEndMQTT') {
            callback((false)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbePulso route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoEndMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
});