import React from "react";
import * as spyMQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import ProbeCODI from "./ProbeCODI";
import { NavigationContainer } from "@react-navigation/native";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";
import { MockCodiSettings } from "../../../__mocks__/CodiSettingsMock";
import { EventRegister } from "react-native-event-listeners";

jest.mock('sp-react-native-mqtt', () => 'MQTT');

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
  }));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.useFakeTimers();

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                    }
    },
};

const mockRouteStatusON = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       codi : true,
                       codiDevices: MockCodiSettings,
                    }
    },
};

const mockRouteStatusON2 = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       codi : true,
                       codiDevices: [
                        {
                          id_codi: 0,
                          codi: 'CODI 1',
                          replicar: false,
                          invertido: false,
                          protocolo: 0,
                          paridade: 0,
                          baudRate: 5,
                          repo: true,
                        },
                        {
                          id_codi: 1, // Alterei o ID para diferenciar
                          codi: 'CODI 2', // Nome diferente para exemplificar
                          replicar: true, // Apenas um exemplo de variação
                          invertido: false,
                          protocolo: 1,
                          paridade: 1,
                          baudRate: 5,
                          repo: false,
                        },
                      ],                
                    }
    },
};

const mockRouteStatusON3 = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       codi : true,
                       codiDevices: {...MockCodiSettings, baudRate: -1 },
                    }
    },
};

describe('ProbeCODI', () => {
    it('renderiza corretamente com a rota fornecida e modulo off', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeCODI route={mockRoute} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente com a rota fornecida e modulo on e mqtt conectado', () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {} = render(  <NavigationContainer>
                                <ProbeCODI route={mockRouteStatusON} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente com a rota fornecida e modulo on e mqtt desconectado', () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {} = render(  <NavigationContainer>
                                <ProbeCODI route={mockRouteStatusON} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    
    
    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {getByTestId} = render(   <NavigationContainer>
                                            <ProbeCODI route={mockRoute}/>
                                        </NavigationContainer> );
        
        await act( () => {         
            fireEvent.press(getByTestId('button-back'));
        });
    }); 
    
    
    it('renderiza corretamente ao clicar no botão para ativar e desativar o modulo codi', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });
    
    }); 

    it('renderiza corretamente ao clicar no botão de ativar modulo codi e cancelar', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });        
    });


    it('renderiza corretamente ao clicar no botão para ativar o modulo codi e mqtt conectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });

    it('renderiza corretamente ao clicar no botão para ativar o modulo codi e mqtt desconectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });    
    
    it('renderiza corretamente ao clicar no botão para limpar configuração codi e modulo ON', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });
    });


    it('renderiza corretamente ao clicar no botão para limpar configuração codi e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });            
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração codi e modulo ON mqtt conectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão de para limpar configuração codi e cancelar', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });


    });

    it('renderiza corretamente ao clicar no botão para salvar configuração codi e modulo ON', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));
            });
        });
    }); 

    
    it('renderiza corretamente ao clicar no botão para salvar configuração e modulo ON, mas mqtt desconectado', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para salvar configuração e modulo ON', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });    


    it('renderiza corretamente ao clicar no botão para salvar configuração e cancelar', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });        
    });    
    
    it('renderiza corretamente ao clicar no botão para salvar configuração codi e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para ler a configuração codi e modulo ON', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });
    });       

    it('renderiza corretamente ao clicar no botão para ler a configuração codi e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo ON', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });
    });
    
    it('deve executar o fluxo esperado ao habilitar e desabilitar o status do módulo e mqtt conectado', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiStatusMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao habilitar e desabilitar o status do módulo e mqtt não conectado', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiStatusMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração do codi e retornou codiSettings', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do codi e retornou codiSettings vazio', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback([]); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado ao excluir o codi = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiEndMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                                <ProbeCODI route={mockRouteStatusON} />
                                                            </NavigationContainer> );

        await act(async () => {      
            fireEvent.press(getByTestId('button-save'));
        });    

        await act(async () => {      
            fireEvent.press(getByText('Sim'));
        });


        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiEndMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao excluir o codi = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiEndMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiEndMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });


    it('deve executar o fluxo esperado ao adicionar o codi =  true, mas mqtt desconectado', () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiAddMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON2} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar o codi =  true', () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiAddMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON2} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar o codi =  false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiAddMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo ON e insere um codi da lista', async () => {
    
        const {getByTestId,getByText} = render(  <NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });


        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('CODI 1');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo ON e insere um codi da lista e adiciona', async () => {
    
        const {getByTestId,getByText} = render(  <NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });


        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('CODI 1');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });

        act(() => {
            fireEvent(getByTestId('combo-protocolo'), 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
        });


        await act(async () => {
            fireEvent.press(getByTestId('button-save-add'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        
    });

    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo ON e fecha a tela', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeCODI route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });


        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(getByTestId('button-close'));
            });
        });
    });

    it('renderiza corretamente ao clicar no botão para adicionar um  codi e modulo ON e insere um codi da lista e adiciona', async () => {
    
        const {getByTestId,getByText} = render(  <NavigationContainer>
                                                    <ProbeCODI route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-add'));
            });
        });


        // Simula o clique no botão "Não" dentro do modal
        const botaoEscolha = getByText('CODI 1');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoEscolha);
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoAdd = getByText('ADICIONAR');       

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoAdd);
            });
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');       

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });
        
        // Simula o clique no botão "OK" dentro do modal
        const botaoOK = getByText('OK');       

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoOK);
            });
        });        
    });  
    
    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para deletar configuração e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('delete-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para deletar configuração e concorda', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('delete-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para editar configuração e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                                <ProbeCODI route={mockRouteStatusON} />
                                                            </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para editar configuração e concorda e depois fecha', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                                <ProbeCODI route={mockRouteStatusON} />
                                                            </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        }); 

        await act(async () => {
            fireEvent.press(getByTestId('button-close-add'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para editar configuração e concorda, depois salva e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        }); 

        await act(async () => {
            fireEvent.press(getByTestId('button-save-add'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    


    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para editar configuração e concorda, depois salva e aceita', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                        <ProbeCODI route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        }); 

        await act(async () => {
            fireEvent.press(getByTestId('button-save-add'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do codi, clica para editar configuração e concorda, depois salva e aceita - baud rate = -1', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiConfigMQTT') {
            callback((MockCodiSettings())); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                                <ProbeCODI route={mockRouteStatusON3} />
                                                            </NavigationContainer> );
    
        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        }); 
                
        act(() => {
            fireEvent(getByTestId('combo-protocolo'), 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-save-add'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

});