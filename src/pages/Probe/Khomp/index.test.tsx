import React from "react";
import { Navigation<PERSON>ontainer, RouteProp } from "@react-navigation/native";
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import Khomp from ".";
import { MockProbeSettings } from "../../../../__mocks__/ProbeSettingsMock";
import { ProbeSettings } from "../../../data";
import { EventRegister } from "react-native-event-listeners";

jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');

jest.mock('sp-react-native-mqtt', () => 'MQTT');

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.useFakeTimers();

describe('khomp', () => {

    const routeMock = {
        params: {
            probeSettings: { MockProbeSettings },
        },
    } as unknown as RouteProp<{ params: ProbeSettings }, 'params'>;

    const routeMockITC100 = {
        params: {
            probeSettings: { ...MockProbeSettings,
                            id:'F803320500000000'
            },
        },
    } as unknown as RouteProp<{ params: ProbeSettings }, 'params'>;

    const routeMockITC200 = {
        params: {
            probeSettings: { ...MockProbeSettings,
                            id:'f803320b00000000'
            },
        },
    } as unknown as RouteProp<{ params: ProbeSettings }, 'params'>;    

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renderiza corretamente passando o id vazio', async () => {
    
        const {unmount, queryByText, getByText} = render(   <NavigationContainer>
                                                                <Khomp route={routeMock} />
                                                            </NavigationContainer> );
        
        act( () => {  
            // acelera o tempo simulado
            jest.advanceTimersByTime(60000);
        });

        await waitFor(() => {
            expect(queryByText('OK')).toBeTruthy();
        });

        act( () => {         
            fireEvent.press(getByText('OK'));
        });        

        unmount();
    });

    it('renderiza corretamente passando o id de um ITC100', async () => {
    
        const {unmount, queryByText, getByText} = render(   <NavigationContainer>
                                                                <Khomp route={routeMockITC100} />
                                                            </NavigationContainer> );
        
        act( () => {  
            // acelera o tempo simulado
            jest.advanceTimersByTime(60000);
        });

        await waitFor(() => {
            expect(queryByText('OK')).toBeTruthy();
        });

        act( () => {         
            fireEvent.press(getByText('OK'));
        });        

        unmount();
    });
    
    it('renderiza corretamente passando o id de um ITC200', async () => {
    
        const {unmount, queryByText, getByText} = render(   <NavigationContainer>
                                                                <Khomp route={routeMockITC200} />
                                                            </NavigationContainer> );
        
        act( () => {  
            // acelera o tempo simulado
            jest.advanceTimersByTime(60000);
        });

        await waitFor(() => {
            expect(queryByText('OK')).toBeTruthy();
        });

        act( () => {         
            fireEvent.press(getByText('OK'));
        });        

        unmount();
    }); 
    
    it('deve executar o fluxo esperado ao solicitar as informações do khomp, mas o retorno é vazio', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'khompMQTT') {
            callback(''); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Khomp route={routeMockITC100} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('khompMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar as informações do khomp, mas o retorno é o esperado', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'khompMQTT') {
            callback('[{\"bn\": \"F803320500034B92\", \"bt\": 1744376880}, {\"n\": \"model\", \"vs\": \"itc100\"}, {\"n\": \"version\", \"vs\": \"2.2.4.0\"}]'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Khomp route={routeMockITC100} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('khompMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
})