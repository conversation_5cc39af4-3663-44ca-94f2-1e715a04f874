import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import Header from "./Header";
import { act, fireEvent, render, screen } from "@testing-library/react-native";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

describe('Header - khomp', () => {
    it('renderiza corretamente', () => {
    
        const {} = render(  <NavigationContainer>
                                <Header />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente ao clicar no botão de voltar', () => {
    
        const {getByTestId} = render(   <NavigationContainer>
                                            <Header />
                                        </NavigationContainer> );
        
        act( () => {         
            fireEvent.press(getByTestId('button-back'));
        });
    });    
})