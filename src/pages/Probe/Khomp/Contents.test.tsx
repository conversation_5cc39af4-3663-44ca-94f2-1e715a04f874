import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import Contents from "./Contents";
import { render, screen } from "@testing-library/react-native";

describe('Contents - khomp', () => {
    it('renderiza corretamente quando payload vazio', () => {
    
        const {} = render(  <NavigationContainer>
                                <Contents payload="" />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente quando payload esta preenchido', () => {
    
        const response = '[{\"bn\": \"F803320500034B92\", \"bt\": 1744376880}, {\"n\": \"model\", \"vs\": \"itc100\"}, {\"n\": \"version\", \"vs\": \"2.2.4.0\"}]';

        const {} = render(  <NavigationContainer>
                                <Contents payload={response} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    

})