import React, { useEffect, useState } from "react";
import { View } from "react-native";

// estilos para a page
import {styles} from './layout';

// componentes
import Loading from "../../../componentes/Loading";

// estruturas
import { ProbeSettings } from "../../../data";

// navegação
import { RouteProp } from "@react-navigation/native";

// registro o evento lido
import { EventRegister } from "react-native-event-listeners";

// modal
import MessageBoxAtencao from "../../../modal/messagebox/Atencao";

// componentes da tela
import Header from "./Header";
import ButtonsFeatures from "./ButtonsFeatures";
import Contents from "./Contents";
import ContentsITC200 from "./ContentsITC200";
import { getModeloKhomp } from "../functions";


// Definindo as propriedades do componente Header
type HeaderProps = {
    route: RouteProp<{ params: ProbeSettings }, 'params'>; // Aqui, params é do tipo ProbeSettings
};

const Khomp : React.FC<HeaderProps> = ({route}) => {

    // recebe os valores da rota
    const probe = JSON.parse(JSON.stringify(route.params));

    // loading
    const [loading, setLoading] = useState<boolean>(false);

    // modals
    const [showMessage, setShowMessage] = useState<boolean>(false);

    // timeout
    const [timeoutLoading, setTimeoutLoading] = useState<number>(0);
    
    const [payload, setPayload] = useState<string>('');

    // executa sempre que a variavel 'khompMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('khompMQTT', mqtt => {                                    
        
            // timeout loading não alarmado
            clearTimeout(timeoutLoading);

            // se existe payload
            if(mqtt.length) {
            
                setPayload(mqtt);

                // finaliza o loading
                setLoading(false);
            }
    
        }, );
        return () => { EventRegister.removeEventListener(eventListener); };
    }, );

    useEffect( () => {

        // se loading ativo
        if(loading) {
                
            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() { 

                // indica que a mensagem demorou muito tempo para responder
                setShowMessage(true);
                setLoading(false);

            }, 60000);

            // pega o id do timeout atual
            setTimeoutLoading(Number(timeout_id));
        }

    }, [loading])

    return(

        <>

            <Loading animating={loading} text={'Informações Khomp'}/>

            <MessageBoxAtencao
                visivel={showMessage} 
                titulo="Atenção" 
                descricao={'Demorou muito tempo para responder.'} 
                textoBotao="OK" 
                onFechar={() => setShowMessage(false)}/>

            {/* area da tela */}
            <View style={styles.containerTela}>

                {/* area do header */}
                <Header/>

                {/* area dos botões de recursos*/}
                <ButtonsFeatures id = {probe.probeSettings.id} 
                                 payload = {payload} 
                                 setLoading = {setLoading}/>

                {
                    (getModeloKhomp(probe.probeSettings.id ?? '') === 'Modelo ITC200')
                    ?
                        /* area de conteudo para ITC200*/
                        <ContentsITC200 payload={payload}/>
                    :
                        /* area de conteudo para ITC200*/
                        <Contents payload={payload}/>
                }

            </View>
        </>

    );
}

export default Khomp;