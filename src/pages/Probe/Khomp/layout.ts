import { Dimensions, StatusBar, StyleSheet } from "react-native";

// altura da tela
const screenHeight = Dimensions.get('screen').height;

// largura da tela
const screenWidth = Dimensions.get('screen').width;

// altura do status bar (nivel de bateria, sinal gsm/wifi, etc)
const heightStatusBar = StatusBar.currentHeight ?? 30;

// area header
const heightHeader = 80;

// area dos botões
const heightButtonFeatures = 50;

// area de segurança para não ultrapassar o limite da tela
const heightFooterSecurity = 20;

// 20% da largura da tela
const width20 = screenWidth * 0.2;

export const styles = StyleSheet.create({

    containerTela: {
        flex:1,
        backgroundColor:'#FFFFFF'
    },

    containerHeader: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerBotaoVoltar:{
        width: width20, 
        justifyContent:'center',
        alignItems:'center',        
    },    
    
    containerTextoHeader:{
        width:'100%',
        marginStart:- width20,
        justifyContent:'center',
        alignItems:'center',        
    },   

    containerButtonsFeature: {
        height:heightButtonFeatures, 
        width:'100%', 
        paddingHorizontal: 20, 
        justifyContent:'flex-end', 
        flexDirection:'row', 
        gap: 30
    },

    containerContent: {        
        height:screenHeight - (heightHeader + heightButtonFeatures + heightStatusBar + heightFooterSecurity),
        width:'100%',
        paddingHorizontal: 20,        
    },
    
    containerScrollview: {        
        flexGrow: 1, 
        height:screenHeight - (heightHeader + heightButtonFeatures + heightStatusBar + heightFooterSecurity),
        width:'100%', 
        padding: 15, 
        borderRadius:10, 
        borderWidth:1, 
        borderColor:'#D6D6D6', 
        backgroundColor:'#F5F5F5',
        gap: 2
    },

    containerTopic: {
        marginVertical: 10,
        padding: 10, 
        backgroundColor: '#D6D6D6',
        borderRadius: 8
    },

    textoHeader: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },  
    
    textoTitle: {
        fontFamily: 'Exo2_400Regular',
        fontSize: 12,
        color: '#737373'
    },

    textoSubtitle: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 14,
        color: '#000000'
    }, 
    
    textoTopic: { 
        fontFamily: 'Exo2_400Regular',
        fontSize: 16, 
        color: '#333'
    },

    textoTitleList: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 18,        
        marginBottom: 10
    }
});