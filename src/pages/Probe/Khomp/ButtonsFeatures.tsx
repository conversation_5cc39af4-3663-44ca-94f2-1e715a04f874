
import React, { useEffect } from 'react';
import { TouchableOpacity, View } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';

// estilos para a page
import {styles} from './layout';

// imagens vetoriais
import IconRefresh from '../../../assets/svg/icon_refresh.svg';
import IconCopy from  '../../../assets/svg/icon_copy.svg'
import { publishMQTT, subscribeMQTT } from '../../../services/mqtt';

// Definindo as propriedades do componente Header
type ButtonsFeaturesProps = {
    id: string,
    payload: string 
    setLoading: React.Dispatch<React.SetStateAction<boolean>>; // Função de atualização de estado (useState)
};

const ButtonsFeatures : React.FC<ButtonsFeaturesProps> = ({id, payload, setLoading}) => {
    
    // permite a copia das informações do payload e o envio para algum aplicativo (WhatsApp, word, email, etc)
    const copyPayload = () => {
        Clipboard.setString(JSON.stringify(payload, null, 2));
    };

    // envio de uma solicitação para a probe atraves do mqtt
    const EscreveMQTT = (variable:string) => {

        try {            
            publishMQTT(id, variable);
        }
        catch(error){}
    }
        
    // se inscreve no topico para receber a leitura do payload pelo mqtt
    const LerMQTT = (variable:string) => {

        try {            
            subscribeMQTT(id, variable);
        }
        catch(error) {}
    }
        
    const refreshPayload = () => {

        // altera o status do loading
        setLoading(prevLoading => !prevLoading);

        // envio de solicitação ao mqtt
        EscreveMQTT('khomp');

        // se inscreve no topico para leitura das informações do khomp
        LerMQTT('khomp');        
    }
        
    // executa 1 vez quando iniciado
    useEffect(() => {
        refreshPayload();
    },[] );

    return (

        <View style={styles.containerButtonsFeature}>

            <TouchableOpacity onPress={copyPayload} testID="button-copy">
                <IconCopy color={"#737373"} width={30} height={30}/>
            </TouchableOpacity>

            <TouchableOpacity onPress={refreshPayload} testID="button-refresh">
                <IconRefresh color={"#737373"} width={30} height={30}/>
            </TouchableOpacity>
                            
        </View>
    );
};

export default ButtonsFeatures;
