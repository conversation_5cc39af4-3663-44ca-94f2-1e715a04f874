import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { render, screen } from "@testing-library/react-native";
import ContentsITC200 from "./ContentsITC200";

describe('Contents ITC200 - khomp', () => {
    it('renderiza corretamente quando payload vazio', () => {
    
        const {} = render(  <NavigationContainer>
                                <ContentsITC200 payload="" />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente quando payload esta preenchido mas payload incorreto', () => {
    
        const response = '[{\"bn\": \"F803320500034B92\", \"bt\": 1744376880}, {\"n\": \"model\", \"vs\": \"itc100\"}, {\"n\": \"version\", \"vs\": \"2.2.4.0\"}]';

        const {} = render(  <NavigationContainer>
                                <ContentsITC200 payload={response} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });
    
    it('renderiza corretamente quando payload esta preenchido corretamente', () => {
    
        const response = '{\"deduplicationId\":\"424909fa-dca2-4d09-96ce-80fcfd55c752\",\"time\":\"2025-04-11T16:29:27.015452898+00:00\",\"deviceInfo\":{\"tenantId\":\"52f14cd4-c6f1-4fbd-8f87-4025e1d49242\",\"tenantName\":\"Comerc\",\"applicationId\":\"0543e93f-ab83-4af9-bf33-c305ab83b91f\",\"applicationName\":\"Teste_Gestal\",\"deviceProfileId\":\"00baa44f-b0c4-45c0-850b-37967cb7d2c3\",\"deviceProfileName\":\"ITC 200\",\"deviceName\":\"node_Terada\",\"devEui\":\"f803320b00035514\"},\"devAddr\":\"000fb09d\",\"dr\":1,\"fCnt\":1925,\"fPort\":16,\"confirmed\":true,\"data\":\"AQACfwFIADzKNG4AAAAAAAA=\",\"object\":{\"device\":[{	\"v\":\"ITC 201\",\"n\":\"model\"},{\"v\":\"0.1.4.8\",	\"n\":\"firmware_version\"},{\"n\":\"uplink_interval\",\"v\":60.0,\"u\":\"minutes\"}],\"sensors\":[{\"n\":\"battery_voltage\",\"u\":\"V\",\"v\":3.02},{\"u\":\"°C\",\"n\":\"internal_temperature\",\"v\":26.0},{\"v\":55.0,\"u\":\"%RH\",\"n\":\"internal_relative_humidity\"},{\"v\":\"Single flux\",\"n\":\"operation_mode\"},{\"u\":\"L/pulse\",\"n\":\"meter_resolution\",\"v\":1.0},{\"n\":\"counter_flux_a\",\"v\":0.0},{\"v\":0.0,\"n\":\"total_volume_meter_a\",\"u\":\"m³\"}]},\"rxInfo\":[{\"gatewayId\":\"f8033202c0180000\",\"uplinkId\":58524,\"rssi\":-49,\"snr\":9.5,\"channel\":6,\"rfChain\":1,\"location\":{},\"context\":\"P4rMTA==\",\"metadata\":{\"region_common_name\":\"AU915\",\"region_name\":\"au915_0\"}},{\"gatewayId\":\"f803320327ca0000\",\"uplinkId\":12685,\"rssi\":-59,\"snr\":8.8,\"channel\":6,\"rfChain\":1,\"location\":{},\"context\":\"H1buPA==\",\"metadata\":{\"region_name\":\"au915_0\",\"region_common_name\":\"AU915\"}}],\"txInfo\":{\"frequency\":916400000,\"modulation\":{\"lora\":{\"bandwidth\":125000,\"spreadingFactor\":11,\"codeRate\":\"CR_4_5\"}}}}';

        const {} = render(  <NavigationContainer>
                                <ContentsITC200 payload={response} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    

})