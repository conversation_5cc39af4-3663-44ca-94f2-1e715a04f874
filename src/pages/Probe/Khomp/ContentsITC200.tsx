import React from "react";
import { FlatList, Text, View } from "react-native";

// estilos para a page
import {styles} from './layout';

// Definindo as propriedades do componente Header
type HeaderProps = {    
    payload: string,     
};

const ContentsITC200 : React.FC<HeaderProps> = ({payload}) => {
          
    const data = payload ? JSON.parse(payload) : [];
    const device = payload ? data.object?.device : [];
    const sensors = payload ? data.object?.sensors : [];

    // Definir a interface para os itens da FlatList
    interface Item {
        [key: string]: string; // Defina os tipos possíveis para os valores
    }
  

    const renderItem = ({ item }: { item: Item }) => (

        <View style={styles.containerTopic}>          
        {
            Object.entries(item).map(([key, value]) => (
                <Text key={key} style={styles.textoTopic}>
                    {key}: {value}
                </Text>
            ))
        }
        </View>
    );

    return (

        <View style = {styles.containerContent}>

            <FlatList
                data={sensors}
                keyExtractor={(item, index) => index.toString()}
                renderItem={renderItem}
                ListHeaderComponent= {
                    <View>
                        <Text style={styles.textoTitleList}>Informações do Dispositivo:</Text>
                        <Text>Empresa: {data?.deviceInfo?.tenantName}</Text>
                        <Text>Dispositivo: {data?.deviceInfo?.deviceName}</Text>                        
                        <Text>Aplicação: {data?.deviceInfo?.applicationName}</Text>
                        
                        <FlatList
                            data={device}
                            renderItem={renderItem}
                            keyExtractor={(item, index) => index.toString()}
                        />
                    </View>
                }
            />


        </View>
    );
};

export default ContentsITC200;
