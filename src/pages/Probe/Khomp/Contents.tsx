import React from "react";
import { FlatList, Text, View } from "react-native";

// estilos para a page
import {styles} from './layout';
import { DataTimeStamp, HoraTimeStamp } from "../../../funcoes";

// Definindo as propriedades do componente Header
type HeaderProps = {    
    payload: string,     
};

const Contents : React.FC<HeaderProps> = ({payload}) => {
          
    const data: Item[] = payload ? JSON.parse(payload) : [];

    // Definir a interface para os itens da FlatList
    interface Item {
        [key: string]: string; // Defina os tipos possíveis para os valores
    }
  
    const renderItem = ({ item }: { item: Item }) => (

        <View style={styles.containerTopic}>          
        {
            Object.entries(item).map(([key, value]) => (
                <Text key={key} style={styles.textoTopic}>
                    {key}: {(key === 'bt') ? `${DataTimeStamp(Number(value))} ${HoraTimeStamp(Number(value), true)}` : value}
                </Text>
            ))
        }
        </View>
    );
      
    return (

        <View style = {styles.containerContent}>

            <FlatList <Item>
                data={data}
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
                ListHeaderComponent= {
                    <View>
                        <Text style={styles.textoTitleList}>Informações do Dispositivo:</Text>
                    </View>
                }                
            />

        </View>
    );
};

export default Contents;
