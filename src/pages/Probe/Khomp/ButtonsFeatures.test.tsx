import React, { Dispatch, SetStateAction } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import ButtonsFeatures from "./ButtonsFeatures";

jest.mock('@react-native-clipboard/clipboard', () => ({
    setString: jest.fn(),
    getString: jest.fn().mockResolvedValue('Texto copiado'),
}));

// Mock do MQTT
jest.mock('sp-react-native-mqtt', () => {
    return {
        MQTT: () => null,
    };
});


describe('Buttons Features - khomp', () => {
    it('renderiza corretamente quando payload vazio', () => {
    
        const {} = render(  <NavigationContainer>
                                <ButtonsFeatures id={'ABCDEFGHIJKLMN'} payload={''} setLoading={jest.fn()} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente quando clica no botão de copia das informações', () => {
    
        const {getByTestId} = render(   <NavigationContainer>
                                            <ButtonsFeatures id={'ABCDEFGHIJKLMN'} payload={'valor a ser copiado'} setLoading={jest.fn()} />
                                        </NavigationContainer> );
        

        act( () => {         
            fireEvent.press(getByTestId('button-copy'));
        });
    });
    
    it('renderiza corretamente quando clica no botão de refresh das informações', () => {
    
        let loadingState = false;

        const mockSetLoading: Dispatch<SetStateAction<boolean>> = (updateFn) => {
            // se vier um valor direto (true/false), aplica direto
            if (typeof updateFn === 'function') {
              loadingState = updateFn(loadingState);
            } else {
              loadingState = updateFn;
            }
          };

        const {getByTestId} = render(   <NavigationContainer>
                                            <ButtonsFeatures id={'ABCDEFGHIJKLMN'} payload={'valor a ser copiado'} setLoading={mockSetLoading} />
                                        </NavigationContainer> );
        

        act( () => {         
            fireEvent.press(getByTestId('button-refresh'));
        });
    });    
})