import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';

// imagens vetoriais
import IconVoltar from '../../../assets/svg/icon_arrow-left.svg';


// estilos para a page
import {styles} from './layout';

// navegação
import { useNavigation } from '@react-navigation/native';

// rotas
import { StackTypes } from '../../../routes';

const Header = () => {

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();           
        
      
    // navegar de volta para tela principal
    const goBack = () => {
           
        navigation.goBack();
    };

    return (

        <View style={styles.containerHeader}>

            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
                <TouchableOpacity onPress={() => goBack()} testID="button-back">
                    <IconVoltar color={"#2E8C1F"} width={30} height={30} />
                </TouchableOpacity>
            </View>

            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
                <Text style={styles.textoHeader}>KHOMP</Text>
            </View>

        </View>
    );
};

export default Header;
