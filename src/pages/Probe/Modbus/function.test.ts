import { MockModbusSettings, MockModbusSettingsNoVariables, MockModbusSettingsSlaves, MockModbusSettingsVariables } from "../../../../__mocks__/ModbusSettingsMock";
import { MockProbeSettings } from "../../../../__mocks__/ProbeSettingsMock";
import { MockVariableJson } from "../../../../__mocks__/VariableJsonMock";
import { ProbeSettings } from "../../../data";
import {
    getDevice,
    getEndianness,
    getFabricantes,
    getGrandezas,
    getMedidores,
    getModbusAddDevice,
    getModbusAddSlave,
    getModbusAddVariable,
    getModbusAddVariableMean,
    getModbusDataSlave,
    getTotalSlaves,
    getTotalVariables,
    getTotalVariablesMean,
    getVariaveis,
    heightConfiguracoes,
    heightVariables,
    SetNameSlave
} from "./functions";

describe('functions - Probe Modbus', () => {

    it('retorna a altura correta para as configurações do slave', () => {

        const height_0 = heightConfiguracoes(-1);
        const height_1 = heightConfiguracoes(0);
        const height_2 = heightConfiguracoes(1);

        expect(height_0).toBe(250);
        expect(height_1).toBe(300);
        expect(height_2).toBe(380);
    });

    it('retorna a altura correta para as configurações das variaveis ', () => {

        const height_0 = heightVariables(-1, 1000);
        const height_1 = heightVariables(0, 1000);
        const height_2 = heightVariables(1, 1000);

        expect(height_0).toBe(520);
        expect(height_1).toBe(470);
        expect(height_2).toBe(390);
    });

    it('retorna as grandezas encontradas no json ', async () => {

        const grandeza_0 = await getGrandezas('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}');
        const grandeza_1 = await getGrandezas('{"modbusx": { "Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}');
        const grandeza_2 = await getGrandezas(JSON.parse('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}'));

        expect(grandeza_0).toEqual(["Energia"]);
        expect(grandeza_1).toEqual([]);
        expect(grandeza_2).toEqual(["Energia"]);
    });

    it('retorna os fabricantes encontradas no json ', async () => {

        const fabricante_0 = await getFabricantes('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia");
        const fabricante_1 = await getFabricantes('{"modbusx": { "Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia");
        const fabricante_2 = await getFabricantes(JSON.parse('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}'), "Energia");

        expect(fabricante_0).toEqual(["Gauge Tech"]);
        expect(fabricante_1).toEqual([]);
        expect(fabricante_2).toEqual(["Gauge Tech"]);
    });

    it('retorna os medidores encontradas no json ', async () => {

        const medidor_0 = await getMedidores('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia", "Gauge Tech");
        const medidor_1 = await getMedidores('{"modbusx": { "Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia", "Gauge Tech");
        const medidor_2 = await getMedidores(JSON.parse('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}'), "Energia", "Gauge Tech");

        expect(medidor_0).toEqual(["Shark 100"]);
        expect(medidor_1).toEqual([]);
        expect(medidor_2).toEqual(["Shark 100"]);
    });

    it('retorna o device encontrado no json ', async () => {

        const device_0 = await getDevice('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia", "Gauge Tech", "Shark 100");
        const device_1 = await getDevice('{"modbusx": { "Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}', "Energia", "Gauge Tech", "Shark 100");
        const device_2 = await getDevice(JSON.parse('{"modbus": {	"Energia": {"Gauge Tech":{"Shark 100": "EN_GTECH_SHARK100"}}}}'), "Energia", "Gauge Tech", "Shark 100");

        expect(device_0).toEqual("EN_GTECH_SHARK100");
        expect(device_1).toEqual('Desconhecido');
        expect(device_2).toEqual("EN_GTECH_SHARK100");
    });

    it('retorna o endianness encontrado no json ', async () => {

        const endianness_0 = await getEndianness('{"dev_name": "EN_GTECH_SHARK100","endianness": 3}');
        const endianness_1 = await getEndianness('{"dev_name": "EN_GTECH_SHARK100","endiannesss": 3}');
        const endianness_2 = await getEndianness(JSON.parse('{"dev_name": "EN_GTECH_SHARK100","endianness": 3}'));

        expect(endianness_0).toEqual(3);
        expect(endianness_1).toEqual(-1);
        expect(endianness_2).toEqual(3);
    });

    it('retorna o endianness encontrado uma exceção no json ', async () => {

        // Mock de JSON.parse para lançar um erro
        jest.spyOn(JSON, 'parse').mockImplementation(() => {
            throw new Error('Erro de parse!');
        });

        const endianness_3 = await getEndianness("{ invalid: 'json' }");

        expect(endianness_3).toEqual(-1);

        // Restaura a implementação original de JSON.parse
        jest.restoreAllMocks();
    });

    it('retorna as variaveis encontradas no json ', async () => {

        const variaveis_0 = await getVariaveis(JSON.stringify(MockVariableJson));
        const variaveis_1 = await getVariaveis(JSON.parse(JSON.stringify(MockVariableJson)));
        const variaveis_2 = await getVariaveis(MockVariableJson.toString());
        const variaveis_3 = await getVariaveis('{"dev_name": "EN_KRON_MULTK120","endianness": 2,"variables": {"Tensão A-N (V)":{"var_name": "voltan","function": 4,"address": 16,"factor": 1,"send": true,"mean": false,"data_type": 6}}');

        expect(variaveis_0.length).toEqual(22);
        expect(variaveis_1.length).toEqual(22);
        expect(variaveis_2.length).toEqual(0);
        expect(variaveis_3.length).toEqual(0);
    });

    describe('getTotalSlaves', () => {
        let mockSetSlavesOK: jest.Mock;

        beforeEach(() => {
            mockSetSlavesOK = jest.fn(); // Cria uma função mock para `setSlavesOK`
        });

        it('deve retornar 0 quando não há dispositivos Modbus', () => {
            const probe_settings: ProbeSettings = MockProbeSettings();

            const totalSlaves = getTotalSlaves(probe_settings, mockSetSlavesOK);

            expect(totalSlaves).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetSlavesOK).toHaveBeenCalledWith([]); // `setSlavesOK` deve ser chamado com um array vazio
        });

        it('deve retornar 0 quando não há slaves', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettings()
            }

            const totalSlaves = getTotalSlaves(probe_settings, mockSetSlavesOK);

            expect(totalSlaves).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetSlavesOK).toHaveBeenCalledWith([]); // `setSlavesOK` deve ser chamado com um array vazio
        });

        it('deve retornar quando há slaves Modbus', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettingsSlaves()
            };

            const totalSlaves = getTotalSlaves(probe_settings, mockSetSlavesOK);

            expect(totalSlaves).toBe(3); // O número total de slaves deve ser 2
            expect(mockSetSlavesOK).toHaveBeenCalledWith([]); // `setSlavesOK` deve ser chamado com um array vazio
        });
    });

    describe('getTotalVariables', () => {
        let mockSetVariablesOK: jest.Mock;

        beforeEach(() => {
            mockSetVariablesOK = jest.fn(); // Cria uma função mock para `setSlavesOK`
        });

        it('deve retornar 0 quando não há dispositivos Modbus', () => {
            const probe_settings: ProbeSettings = MockProbeSettings();

            const totalVariables = getTotalVariables(probe_settings, mockSetVariablesOK);

            expect(totalVariables).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetVariablesOK).toHaveBeenCalledWith([]); // `mockSetVariablesOK` deve ser chamado com um array vazio
        });

        it('deve retornar 0 quando não há slaves', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettings()
            }

            const totalVariables = getTotalVariables(probe_settings, mockSetVariablesOK);

            expect(totalVariables).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetVariablesOK).toHaveBeenCalledWith([]); // `setSlavesOK` deve ser chamado com um array vazio
        });

        it('deve retornar quando há variaveis Modbus', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettingsSlaves()
            };

            const totalVariables = getTotalVariables(probe_settings, mockSetVariablesOK);

            expect(totalVariables).toBe(1); // O número total de variables deve ser 2
            expect(mockSetVariablesOK).toHaveBeenCalledWith([]); // `SetVariablesOK` deve ser chamado com um array vazio
        });

        it('deve retornar quando não há variaveis Modbus', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettingsVariables()
            };

            const totalVariables = getTotalVariables(probe_settings, mockSetVariablesOK);

            expect(totalVariables).toBe(0); // O número total de variables deve ser 2
            expect(mockSetVariablesOK).toHaveBeenCalledWith([]); // `SetVariablesOK` deve ser chamado com um array vazio
        });

    });

    describe('getTotalVariablesMean', () => {
        let mockSetVariablesMeanOK: jest.Mock;

        beforeEach(() => {
            mockSetVariablesMeanOK = jest.fn(); // Cria uma função mock para `setSlavesOK`
        });

        it('deve retornar 0 quando não há dispositivos Modbus', () => {
            const probe_settings: ProbeSettings = MockProbeSettings();

            const totalVariables = getTotalVariablesMean(probe_settings, mockSetVariablesMeanOK);

            expect(totalVariables).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetVariablesMeanOK).toHaveBeenCalledWith([]); // `setVariablesMeanOK` deve ser chamado com um array vazio
        });


        it('deve retornar 0 quando não há slaves', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettings()
            }

            const totalVariables = getTotalVariablesMean(probe_settings, mockSetVariablesMeanOK);

            expect(totalVariables).toBe(0); // O número total de slaves deve ser 0
            expect(mockSetVariablesMeanOK).toHaveBeenCalledWith([]); // `setVariablesMeanOK` deve ser chamado com um array vazio
        });


        it('deve retornar quando há variaveis Modbus', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettingsSlaves()
            };

            const totalVariables = getTotalVariablesMean(probe_settings, mockSetVariablesMeanOK);

            expect(totalVariables).toBe(2); // O número total de variables deve ser 1
            expect(mockSetVariablesMeanOK).toHaveBeenCalledWith([]); // `setVariablesMeanOK` deve ser chamado com um array vazio
        });

        it('deve retornar quando não há variaveis Modbus', () => {
            const probe_settings: ProbeSettings = {
                ...MockProbeSettings(),
                modbusDevices: MockModbusSettingsNoVariables()
            };

            const totalVariables = getTotalVariablesMean(probe_settings, mockSetVariablesMeanOK);

            expect(totalVariables).toBe(0); // O número total de variables deve ser 2
            expect(mockSetVariablesMeanOK).toHaveBeenCalledWith([]); // `setVariablesMeanOK` deve ser chamado com um array vazio
        });
    });

    it('retorna o json para adicionar o device ', async () => {

        const json = getModbusAddDevice(MockProbeSettings(), 0);

        expect(json).toEqual('{\"dev_name\":\"device\",\"endianness\":0}');
    });

    it('retorna o json para adicionar o slave com protocolo tcp', async () => {

        const probe_settings: ProbeSettings = {
            ...MockProbeSettings(),
            modbusDevices: MockModbusSettingsSlaves()
        }
        const json_0 = getModbusAddSlave(probe_settings, 0, 0);
        const json_1 = getModbusAddSlave(probe_settings, 0, 1);
        const json_2 = getModbusAddSlave(probe_settings, 0, 2);

        expect(json_0).toEqual('{\"dev_name\":\"Device 1\",\"slave_name\":\"Slave 1\",\"slave_id\":1,\"protocol\":0,\"baud\":9600,\"uart\":\"8N1\",\"port_id\":0}');
        expect(json_1).toEqual('{\"dev_name\":\"Device 1\",\"slave_name\":\"Slave 2\",\"slave_id\":2,\"protocol\":1,\"port\":1,\"conn\":0,\"slave_ip\":\"***********\"}');
        expect(json_2).toEqual('{\"dev_name\":\"Device 1\",\"slave_name\":\"Slave 3\",\"slave_id\":0,\"protocol\":1,\"port\":0,\"conn\":2,\"slave_ip\":\"0.0.0.0\"}');
    });

    it('retorna o json para adicionar o slave com protocolo rtu', async () => {

        const probe_settings: ProbeSettings = {
            ...MockProbeSettings(),
            modbusDevices: MockModbusSettings()
        }
        const json_0 = getModbusAddSlave(probe_settings, 0, 0);


        expect(json_0).toEqual('{\"dev_name\":\"device\",\"slave_name\":\"slave\",\"slave_id\":0,\"send_time\":900,\"protocol\":0,\"baud\":9600,\"uart\":\"8N1\",\"port_id\":0}');
    });

    it('retorna o json para adicionar a variavel ', async () => {

        const json = getModbusAddVariable(MockProbeSettings(), 0, 0, 0);

        expect(json).toEqual('{\"dev_name\":\"device\",\"address\":0,\"data_type\":0,\"factor\":0,\"function\":0,\"env\":false,\"mean\":false,\"var_name\":\"variable\"}');
    });

    it('retorna o json para adicionar a variavel média ', async () => {

        const probe_settings: ProbeSettings = {
            ...MockProbeSettings(),
            modbusDevices: MockModbusSettingsSlaves()
        }
        const json = getModbusAddVariableMean(probe_settings, 0, 0, 0);

        expect(json).toEqual('{\"slave_name\":\"Slave 1\",\"var_name\":\"active\"}');
    });

    it('retorna o json para solicitar os dados de um slave modbus ', async () => {

        const json_0 = getModbusDataSlave(MockModbusSettingsSlaves()[0], 0);
        const json_1 = getModbusDataSlave(MockModbusSettingsSlaves()[0], 1);
        const json_2 = getModbusDataSlave(MockModbusSettingsSlaves()[0], 2);

        expect(json_0).toEqual('{\"value\":\"mdb_rtu_001\"}');
        expect(json_1).toEqual('{\"value\":\"mdb_tcp_001_002\"}');
        expect(json_2).toEqual('{\"value\":\"mdb_tcp_000_000\"}');
    });
    
    it('retorna o novo nome para o slave  ', async () => {

        const slave_0 = SetNameSlave('slave', '1');
        const slave_1 = SetNameSlave('slave 1', '2');

        
        expect(slave_0).toBe('slave 2');
        expect(slave_1).toBe('slave 3');
    });    
})
