import { Dimensions, StatusBar, StyleSheet } from "react-native";

/* altura da tela */
export const screenHeight = Dimensions.get('screen').height;

/* largura da tela */
const screenWidth = Dimensions.get('screen').width;

/* altura do status bar (nivel de bateria, sinal gsm/wifi, etc) */
const heightStatusBar = StatusBar.currentHeight ?? 30;

/* area header */
const heightHeader = 80;

/* area de botões do header */
const heightHeaderButtons = 80;

/* area do header onde adiciona os devices */
const heightHeaderAddDevice = 60;

/* tamanho dos botões */
const heightButton = 60;

/* area de segurança para não ultrapassar o limite da tela */
const heightFooterSecurity = 20;

/* 20% da largura da tela */
const width20 = screenWidth * 0.2;

export const styles = StyleSheet.create({

    containerTela: {
        flex: 1,
        backgroundColor: '#FFFFFF'
    },
    containerHeader: {
        width: screenWidth,
        height: heightHeader,
        flexDirection: 'row',
        paddingTop: 40
    },
    containerBotaoVoltar: {
        width: width20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    containerTextoHeader: {
        width: '100%',
        marginStart: - width20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    containerBotoesProbeModbus: {
        height: 80,
        width: '100%',
        paddingHorizontal: 20,
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        paddingTop: 10
    },
    containerTextoDevices: {
        width: '100%',
        height: 60,
        paddingHorizontal: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#737373'
    },
    containerListaEquipamentos: {
        height: screenHeight - heightHeader - heightHeaderButtons - heightHeaderAddDevice - heightStatusBar, 
        width:'100%', 
        paddingHorizontal: 20,
        paddingVertical: 30,
        justifyContent: 'flex-start',
        alignItems: 'center',
    },
    containerTopic: {
        marginVertical: 10,
        padding: 10,
        backgroundColor: '#D6D6D6',
        borderRadius: 8
    },
    containerContent: {
        height: screenHeight - (heightHeader + heightStatusBar + heightFooterSecurity),
        width: '100%',
        paddingHorizontal: 20,
    },

    buttonSalvarMedicao: {
        height: heightButton,
        backgroundColor: '#2E8C1F',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8
    },

    textoHeaderProbeModbus: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        color: '#2E8C1F',
    },
    textoDevices: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,
        color: '#FFFFFF',
    },
    textoStatus: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 18,
        color: '#2E8C1F',
    },
    textoButtonSalvarMedicao: {
        fontFamily: 'Exo2_400Regular',
        fontSize: 20,
        color: '#FFFFFF',
    },
    textoItensSelecao: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 22,
        color: '#FFFFFF',
    },
    textoTopic: {
        fontFamily: 'Exo2_400Regular',
        fontSize: 16,
        color: '#333'
    },
    textoTitleList: {
        height:50,
        fontFamily: 'Exo2_600SemiBold',
        textAlignVertical:'center',
        fontSize: 18,        
    }
});