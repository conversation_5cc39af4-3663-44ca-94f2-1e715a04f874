import { ListaBaudRateProbe, ListaParidadeProbe, ProtocoloProbe, TempoSegundosEnvioProbe } from "../../../constantes";
import { modbusSettings, ProbeSettings, SlavesOK, VariableSettingsInicial, VariablesMeanOK, VariablesOK, VariablesSettings } from "../../../data";
import { isObject } from "../../../funcoes";

// Define os tipos para os setters
type Setter<T> = (value: T) => void;

/**
 * retorna a altura da tela de configurações em relação a escolha do tipo de protocolo
 * @param tipo_protocolo 
 * @param protocolo_slave 
 * @returns 
 */
export function heightConfiguracoes(tipo_protocolo: number): number {

    if (tipo_protocolo === ProtocoloProbe.RTU)
        return 300;
    else if (tipo_protocolo === ProtocoloProbe.TCP)
        return 380;
    else
        return 250;
}

/**
 * retorna a altura da tela de configuração das variaveis em relação a escolha do tipo de protocolo
 * @param tipo_protocolo 
 * @returns 
 */
export function heightVariables(tipo_protocolo: number, altura_tela: number): number {

    if (tipo_protocolo === ProtocoloProbe.RTU) {
        return altura_tela - 530;
    }

    if (tipo_protocolo === ProtocoloProbe.TCP) {
        return altura_tela - 610;
    }

    return altura_tela - 480;
}

/**
 * retorna a lista de grandezas depois de fazer um parsing do arquivo json lido
 * @param data
 * @returns 
 */
export async function getGrandezas(data: string): Promise<string[]> {

    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {

        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }

    // parsing 
    const json = JSON.parse(stringSemComentarios.trim());

    // se existe informações modbus
    if (json.modbus) {
        return Object.keys(json.modbus);
    }

    // retorna uma lista vazia
    return [];
}

/**
 * retorna a lista de fabricantes depois de fazer um parsing do arquivo json
 * @param data 
 * @param item 
 * @returns 
 */
export async function getFabricantes(data: string, item: string): Promise<string[]> {

    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }

    // parsing 
    const json = JSON.parse(stringSemComentarios.trim());

    // se existe informações modbus
    if (json.modbus) {
        return Object.keys(json.modbus[item]);
    }

    // retorna uma lista vazia
    return [];
}

/**
 * retorna a lista de medidores depois de fazer um parsing do arquivo json
 * @param data 
 * @param grandeza 
 * @param item 
 * @returns 
 */
export async function getMedidores(data: string, grandeza: string, item: string): Promise<string[]> {

    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }

    // parsing 
    const json = JSON.parse(stringSemComentarios.trim());

    // se existe informações modbus
    if (json.modbus) {
        return Object.keys(json.modbus[grandeza][item]);
    }

    // retorna uma lista vazia
    return [];
}

/**
 * retorna o device depois de fazer um parsing do arquivo json
 * @param data 
 * @param grandeza 
 * @param fabricante 
 * @param item 
 * @returns 
 */
export async function getDevice(data: string, grandeza: string, fabricante: string, item: string): Promise<string> {

    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }

    // parsing 
    const json = JSON.parse(stringSemComentarios.trim());

    // se existe informações modbus
    if (json.modbus) {
        return json.modbus[grandeza][fabricante][item];
    }

    // não encontrou device
    return 'Desconhecido';
}

/**
 * retorna o valor do endianness depois de fazer um parsing do arquivo json
 * @param data 
 */
export async function getEndianness(data: string): Promise<number> {

    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }

    try {
        // parsing 
        const json = JSON.parse(stringSemComentarios.trim());

        // se existe informações modbus
        if (json.endianness) {
            return (Number(json.endianness));
        }

        // não encontrou o endianness
        return (-1)
    }
    catch (error) {
        // não encontrou o endianess
        return (-1);
    }
}

// retorna as variaveis dispositivo
/**
 * retorna as variaveis depois de fazer o parsing do arquivo json
 * @param data 
 */
export async function getVariaveis(data: string): Promise<VariablesSettings[]> {

    // lista de variaveis a serem preenchidas
    let variaveis: VariablesSettings[] = [];
    let stringSemComentarios = '';

    // verifica se string é um objeto
    if (isObject(data)) {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = JSON.stringify(data).replace(/\/\/[^\n]*/g, '') // remove comentários de linha
            .replace(/\/\*[\s\S]*?\*\//g, ''); // remove comentários de bloco
    }
    else {
        // remove os comentarios do json, para evitar valores indefinidos
        stringSemComentarios = data.toString().replace(/\/\/[^\n]*/g, '') // remove comentários de linha 
            .replace(/\/\*[\s\S]*?\*\//g, '');
    }

    try {

        // parsing 
        const json = JSON.parse(stringSemComentarios.trim());

        // pega o nome da variaveis
        const items = Object.keys(json.variables);

        // percorre os items
        items.forEach(item => {

            // variavel auxiliar
            let variavel: VariablesSettings = VariableSettingsInicial()[0];

            variavel.descricaoVariable = item;

            if (json.variables[item].var_name)
                variavel.nomeVariable = json.variables[item]?.var_name;

            if (json.variables[item].send)
                variavel.valorUltimaVariable = json.variables[item].send;

            if (json.variables[item].mean)
                variavel.valorMediaVariable = json.variables[item].mean;

            if (json.variables[item].function)
                variavel.funcaoVariable = json.variables[item].function;

            if (json.variables[item].address)
                variavel.enderecoVariable = json.variables[item].address;

            if (json.variables[item].factor)
                variavel.fatorVariable = json.variables[item].factor;

            if (json.variables[item].data_type)
                variavel.formatoVariable = json.variables[item].data_type;

            // adiciona a variavel a lista de variaveis
            variaveis.push(variavel);
        });

        return ([...variaveis]);
    }
    catch (error) {
        return [];
    }
}

/**
 * retorna o total de slaves configurados
 * @param probe_settings 
 * @param setSlavesOK 
 * @returns 
 */
export function getTotalSlaves(probe_settings: ProbeSettings, setSlavesOK: Setter<SlavesOK[]>): number {

    // inicializa
    setSlavesOK([]);
    let num_slaves = 0;

    let var_aux = {} as SlavesOK;
    let slaves: SlavesOK[] = [];

    for (let device = 0; device < (probe_settings.modbusDevices?.length || 0); device++) {

        for (let slave = 0; slave < (probe_settings.modbusDevices?.[device]?.slaves?.length || 0); slave++) {

            // pega os valores da variavel encontrada
            var_aux = { device: device, slave: slave }
            slaves.push(var_aux);

            // incrementa o numero de slaves
            num_slaves += 1;
        }
    }

    // seta as variaveis encontradas
    setSlavesOK(slaves);

    // retorna o numero total de slaves configuradas
    return num_slaves;
}

/**
 * retorna o total de variaveis a serem configuradas
 * @param probe_ 
 * @returns 
 */
export function getTotalVariables(probe_settings: ProbeSettings, setVariablesOK: Setter<VariablesOK[]>) {
        
    // inicializa
    setVariablesOK([]);
    let num_var = 0;

    let var_aux = {} as VariablesOK;
    let var_ok: VariablesOK[] = [];
    
    // percorre os devices
    for (let device = 0; device < (probe_settings.modbusDevices?.length || 0); device++) {

        // percorre as variaveis dos slaves
        for (let variable = 0; variable < (probe_settings.modbusDevices?.[device].slaves?.[0]?.variables?.length || 0); variable++) {

            // verifica se slave configurado
            if (probe_settings.modbusDevices?.[device].slaves?.[0]?.variables?.[variable].valorUltimaVariable)
            {   
                // pega os valores da variavel encontrada
                var_aux = {device: device, variable: variable}                  
                var_ok.push(var_aux);

                // incrementa o numero de variaveis
                num_var += 1;
            }
        }
    }

    // seta as variaveis encontradas
    setVariablesOK(var_ok);

    // retorna o numero total de variaveis configuradas
    return num_var;
}

/**
 * retorna o total de variaveis em mean (média) configurados
 * @param probe_settings 
 * @param setVariablesMeanOK 
 * @returns 
 */
export function getTotalVariablesMean(probe_settings: ProbeSettings, setVariablesMeanOK: Setter<VariablesMeanOK[]>): number {

    // inicializa
    setVariablesMeanOK([]);
    let num_mean = 0;

    let var_aux = {} as VariablesMeanOK;
    let var_mean: VariablesMeanOK[] = [];

    // percorre os devices
    for (let device = 0; device < (probe_settings.modbusDevices?.length || 0); device++) {

        // percorre os slaves
        for (let slave = 0; slave < (probe_settings.modbusDevices?.[device].slaves?.length || 0); slave++) {

            // percorre as variaveis dos slaves
            for (let variable = 0; variable < (probe_settings.modbusDevices?.[device].slaves?.[slave]?.variables?.length || 0); variable++) {

                // verifica se slave configurado
                if (probe_settings.modbusDevices?.[device].slaves?.[slave].variables?.[variable].valorMediaVariable) {
                    // pega os valores da variavel encontrada
                    var_aux = { device: device, slave: slave, variable: variable }
                    var_mean.push(var_aux);

                    // incrementa o numero de medias
                    num_mean += 1;
                }
            }
        }
    }

    // seta as variaveis encontradas
    setVariablesMeanOK(var_mean);

    // retorna o total de de variaveis mean encontradas
    return num_mean;
}

/**
 * retorna o string json com as informações para a solicitação de adicionar um device na configuração do modbus
 * @param probe_settings 
 * @param device 
 * @returns 
 */
export function getModbusAddDevice(probe_settings: ProbeSettings, device: number): string {

    return JSON.stringify({
        dev_name: probe_settings.modbusDevices?.[device]?.nameDevice ?? 'device',
        endianness: probe_settings.modbusDevices?.[device]?.endianessDevice ?? 0
    })
}

/**
 * retorna o string json com as informações para a solicitação de adicionar um slave na configuração do modbus
 * @param probe_settings 
 * @param device 
 * @param slave 
 * @returns 
 */
export function getModbusAddSlave(probe_settings: ProbeSettings, device: number, slave: number): string {

    // informações comum para os 2 tipos de configuração do slave possiveis
    const tempo = probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.tempoEnvioSlave ?? 2;
    const dev_name = probe_settings.modbusDevices?.[device]?.nameDevice ?? 'device';
    const slave_name = probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.nomeSlave ?? 'slave';
    const slave_id = Number(probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.enderecoSlave ?? '0');
    const protocol = probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.protocoloSlave ?? 0;

    // verifica o tipo de protocolo para montar a solicitação para adicionar o slave do modbus
    if (protocol === ProtocoloProbe.RTU) {

        // informações especificas para um slave com protocolo RTU
        const baud = Number(ListaBaudRateProbe.find(x => x.id === probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.baudeRateSlave)?.descricao ?? '9600');
        const uart = ListaParidadeProbe.find(x => x.id === probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.paridadeSlave)?.descricao ?? '8N1';

        return JSON.stringify({
            dev_name: dev_name,
            slave_name: slave_name,
            slave_id: slave_id,
            send_time: TempoSegundosEnvioProbe[tempo as keyof typeof TempoSegundosEnvioProbe],
            protocol: protocol,
            baud: baud,
            uart: uart,
            port_id: 0
        });
    }

    // informações especificas para um slave com protocolo TCP
    const port = probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.portSlave ?? 0;
    const conn = (probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.conexaoSlave === 0) ? 0 : 2;
    const slave_ip = probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.ipSlave ?? '0.0.0.0';

    return JSON.stringify({
        dev_name: dev_name,
        slave_name: slave_name,
        slave_id: slave_id,
        send_time: TempoSegundosEnvioProbe[tempo as keyof typeof TempoSegundosEnvioProbe],
        protocol: protocol,
        port: port,
        conn: conn,
        slave_ip: slave_ip
    });
}

/**
 * retorna o string json com as informações para a solicitação de adicionar uma variable na configuração do modbus
 * @param probe_settings 
 * @param device 
 * @param slave 
 * @param variable 
 * @returns 
 */
export function getModbusAddVariable(probe_settings: ProbeSettings, device: number, slave: number, variable: number): string {

    return JSON.stringify({
        dev_name: probe_settings.modbusDevices?.[device]?.nameDevice ?? 'device',
        address: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.enderecoVariable ?? 0,
        data_type: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.formatoVariable ?? 0,
        factor: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.fatorVariable ?? 0,
        function: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.funcaoVariable ?? 0,
        env: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.valorUltimaVariable ?? false,
        mean: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.valorMediaVariable ?? false,
        var_name: probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.variables?.[variable]?.nomeVariable ?? 'variable'
    })
}

/**
 * retorna o string json com as informações para a solicitação de adicionar uma variavel do mean (média) do modbus
 * @param probe_settings 
 * @param device 
 * @param slave 
 * @param variable 
 * @returns 
 */
export function getModbusAddVariableMean(probe_settings: ProbeSettings, device: number, slave: number, variable: number): string {

    return JSON.stringify({
        slave_name: probe_settings.modbusDevices?.[device]?.slaves?.[slave].nomeSlave,
        var_name: probe_settings.modbusDevices?.[device]?.slaves?.[slave].variables?.[variable].nomeVariable
    });
}

/**
 * retorna o string json com as informações para a solicitação dos dados modbus do slave
 * @param device 
 * @param slave_atual 
 * @returns 
 */
export function getModbusDataSlave(device: modbusSettings, slave_atual: number): string {

    /* pega somente o id do slave */
    const id_slave = device.slaves?.[slave_atual]?.enderecoSlave ?? '0';

    if (device.slaves?.[slave_atual]?.protocoloSlave === ProtocoloProbe.RTU) {

        const value = `mdb_rtu_${id_slave.padStart(3, '0')}`;

        return JSON.stringify({ value: value });
    }

    /* pega o ip configurado no slave*/
    const ip = device.slaves?.[slave_atual]?.ipSlave ?? '0.0.0.0';

    /* divide o IP em um array de octetos */
    const octetos = ip.split(".");

    /* obtém o último octeto */
    const ultimoOcteto = octetos[octetos.length - 1];

    const value = `mdb_tcp_${ultimoOcteto.padStart(3, '0')}_${id_slave.padStart(3, '0')}`;

    return JSON.stringify({ value: value});

}

/**
 * monta o nome para o slave copiado
 * @param name 
 * @param address 
 * @returns 
 */
export function SetNameSlave(name: string, address: string) : string {

    /* divide a string em palavras */
    const palavras = name.split(" ");

    // Verifica se a última palavra é um número
    if (!isNaN(parseInt(palavras[palavras.length - 1]))) {

        /* remove o último número e adiciona o novo número */
        palavras.pop();
    }
   
    /* adiciona o novo número ao final da string */
    palavras.push(String(Number(address) + 1));
   
    /* unta as palavras novamente em uma string */
    return palavras.join(" ");   
}
