import { NavigationContainer } from "@react-navigation/native";
import { render, screen } from "@testing-library/react-native";
import React from "react";
import DataSlave from "./DataSlave";

describe('Data Slave', () => {
    it('renderiza corretamente quando payload vazio', () => {

        const { } = render(<NavigationContainer>
            <DataSlave payload={[]} slave={'Slave1'} />
        </NavigationContainer>);

        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente quando payload esta preenchido', () => {

        const response = ['{\"energy_act_forward\":60.428,\"time\":1744829100,\"reactive\":111.889,\"active\":167.475}'];

        const { } = render(<NavigationContainer>
            <DataSlave payload={response} slave={'Slave1'} />
        </NavigationContainer>);

        expect(screen).toBeTruthy();
    });

})