import React, { useEffect, useRef, useState } from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { EventRegister } from "react-native-event-listeners";
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import { screenHeight, styles } from './layout';

// imagens vetoriais
import IconVoltar from '../../../assets/svg/icon_arrow-left.svg';
import IconBroom from '../../../assets/svg/icon_broom.svg';
import IconPlus from '../../../assets/svg/icon_plus.svg';
import IconRefresh from '../../../assets/svg/icon_refresh.svg';
import IconSave from '../../../assets/svg/icon_save-01.svg';
import IconX from '../../../assets/svg/icon_x.svg';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import { StackTypes } from '../../../routes/index';

// componentes
import CardProbe from "../../../componentes/CardProbe";
import CardVariable from "../../../componentes/CardVariable";
import CheckBox from "../../../componentes/CheckBox";
import ComboBox from "../../../componentes/ComboBox";
import Divider from "../../../componentes/Divider";
import Loading from "../../../componentes/Loading";
import LoadingProgressBar from "../../../componentes/LoadingProgressBar";
import TextEntry from "../../../componentes/TextEntry";

// constantes
import {
    ListaBaudRateProbe,
    ListaEndianessProbe,
    ListaParidadeProbe,
    ListaProtocoloProbe,
    ListaTempoEnvioProbe,
    ListaTipoConexaoProbe,
    ProtocoloProbe,
    TimeoutTipoConexao
} from "../../../constantes";

// serviços api
import apiZordonModbus from '../../../services/apiZordonModbus';
import apiZordonVariaveis from "../../../services/apiZordonVariaveis";

// modal messages
import MessageBoxErro from "../../../modal/messagebox/Erro";
import MessageBoxPergunta from "../../../modal/messagebox/Pergunta";
import MessageBoxSucesso from "../../../modal/messagebox/Sucesso";

// data 
import {
    AddDevice,
    modbusSettings,
    ProbeSettings,
    SlavesOK,
    SlavesSettingsInicial,
    VariablesMeanOK,
    VariablesOK,
    VariablesSettings
} from "../../../data";

// funções
import { isNumber } from "../../../funcoes";

// funções mqtt
import Space from "../../../componentes/Space";
import { isMQTTConnected, publishMQTT, subscribeMQTT, unsubscribeMQTT } from "../../../services/mqtt";
import DataSlave from "./DataSlave";
import {
    getDevice,
    getEndianness,
    getFabricantes,
    getGrandezas,
    getMedidores,
    getModbusAddDevice,
    getModbusAddSlave,
    getModbusAddVariable,
    getModbusAddVariableMean,
    getModbusDataSlave,
    getTotalSlaves,
    getTotalVariables,
    getTotalVariablesMean,
    getVariaveis,
    heightConfiguracoes,
    heightVariables,
    SetNameSlave
} from "./functions";

const ProbeModbusX = ({ route }) => {

    const refRBSheetSlave = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetDispositivo = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetDataSlave = useRef<{ open: () => void, close: () => void }>(null);

    // indica o status atual do modbus
    const [modbusStatus, setModbusStatus] = useState<boolean>(route.params?.probeSettings.modbus);

    // definições da probe
    const [probe, setProbe] = useState<ProbeSettings>({ ...route.params?.probeSettings });
    const [variables, setVariables] = useState<VariablesSettings[]>([]);
    const [variablesAux, setVariablesAux] = useState<VariablesSettings[]>([]);

    const [deviceCount, setDeviceCount] = useState<number>(0);
    const [slaveCount, setSlaveCount] = useState<number>(0);

    const [slaveTotal, setSlaveTotal] = useState<number>(0);
    const [variableTotal, setVariableTotal] = useState<number>(0);
    const [meanTotal, setMeanTotal] = useState<number>(0);

    // inicia leitura da configuração modbus
    const [iniciaLeitura, setIniciaLeitura] = useState<boolean>(false);

    // mensagens modal
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);
    const [showMessagePerguntaDevice, setShowMessagePerguntaDevice] = useState<boolean>(false);
    const [showMessagePerguntaSlave, setShowMessagePerguntaSlave] = useState<boolean>(false);
    const [showMessagePerguntaData, setShowMessagePerguntaData] = useState<boolean>(false);
    const [showMessagePerguntaEdit, setShowMessagePerguntaEdit] = useState<boolean>(false);
    const [showMessagePerguntaSaveMedicao, setShowMessagePerguntaSaveMedicao] = useState<boolean>(false);
    const [showMessagePerguntaModbusStatus, setShowMessagePerguntaModbusStatus] = useState<boolean>(false);
    const [showMessagePerguntaSaveModbus, setShowMessagePerguntaSaveModbus] = useState<boolean>(false);
    const [showMessagePerguntaCleanModbus, setShowMessagePerguntaCleanModbus] = useState<boolean>(false);

    const [textoMessage, setTextoMessage] = useState<string>('');

    // informações modbus
    const [modbus, setModbus] = useState<string>('');
    const [grandezas, setGrandezas] = useState<string[]>([]);
    const [grandeza, setGrandeza] = useState<string>('');
    const [fabricantes, setFabricantes] = useState<string[]>([]);
    const [fabricante, setFabricante] = useState<string>('');
    const [medidores, setMedidores] = useState<string[]>([]);
    const [selecao, setSelecao] = useState<number>(0);
    const [dataSlave, setDataSlave] = useState<string[]>([])

    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');
    const [saving, setSaving] = useState<boolean>(false);

    // loadingProgress    
    const [loadingPBar, setLoadingPBar] = useState<boolean>(false);
    const [textoLoadingPBar, setTextoLoadingPBar] = useState<string>('');
    const [loadingPBar1, setLoadingPBar1] = useState<boolean>(false);
    const [currentPBar1, setCurrentPBar1] = useState<number>(0);
    const [maxPBar1, setMaxPBar1] = useState<number>(0);
    const [textMaxPBar1, setTextMaxPBar1] = useState<string>('');
    const [loadingPBar2, setLoadingPBar2] = useState<boolean>(false);
    const [currentPBar2, setCurrentPBar2] = useState<number>(0);
    const [maxPBar2, setMaxPBar2] = useState<number>(0);
    const [textMaxPBar2, setTextMaxPBar2] = useState<string>('');
    const [loadingPBar3, setLoadingPBar3] = useState<boolean>(false);
    const [currentPBar3, setCurrentPBar3] = useState<number>(0);
    const [maxPBar3, setMaxPBar3] = useState<number>(0);
    const [textMaxPBar3, setTextMaxPBar3] = useState<string>('');
    const [loadingPBar4, setLoadingPBar4] = useState<boolean>(false);
    const [currentPBar4, setCurrentPBar4] = useState<number>(0);
    const [maxPBar4, setMaxPBar4] = useState<number>(0);
    const [textMaxPBar4, setTextMaxPBar4] = useState<string>('');


    // configuração da probe
    const [deviceAtual, setDeviceAtual] = useState<number>(-1);
    const [nomeDevice, setNomeDevice] = useState<string>('');
    const [endiannessDevice, setEndiannessDevice] = useState<number>(-1);
    const [slaveAtual, setSlaveAtual] = useState<number>(-1)
    const [statusSlave, setStatusSlave] = useState<boolean>(false);
    const [nomeSlave, setNomeSlave] = useState<string>('');
    const [enderecoSlave, setEnderecoSlave] = useState<string>('');
    const [tempoEnvioSlave, setTempoEnvioSlave] = useState<number>(-1);
    const [protocoloSlave, setProtocoloSlave] = useState<number>(-1);
    const [paridadeSlave, setParidadeSlave] = useState<number>(-1);
    const [baudRateSlave, setBaudRateSlave] = useState<number>(-1);
    const [conexaoSlave, setConexaoSlave] = useState<number>(-1);
    const [portSlave, setPortSlave] = useState<string>('');
    const [ipSlave, setIpSlave] = useState<string>('');
    const [variableAtual, setVariableAtual] = useState<number>(-1);
    const [meanAtual, setMeanAtual] = useState<number>(-1);

    // controle timeout loading    
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);
    let timeoutWait: number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];

    const [slavesOK, setSlavesOK] = useState<SlavesOK[]>([]);
    const [variablesOK, setVariablesOK] = useState<VariablesOK[]>([]);
    const [variablesMeanOK, setVariablesMeanOK] = useState<VariablesMeanOK[]>([]);

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // inicializa as variaveis do loading progress bar
    const ResetLoadingProgressBar = () => {
        setLoadingPBar(false);
        setTextoLoadingPBar('');
        setLoadingPBar1(false);
        setCurrentPBar1(1);
        setMaxPBar1(0);
        setTextMaxPBar1('');
        setLoadingPBar2(false);
        setCurrentPBar2(1);
        setMaxPBar2(0);
        setTextMaxPBar2('');
        setLoadingPBar3(false);
        setCurrentPBar3(1);
        setMaxPBar3(0);
        setTextMaxPBar3('');
        setLoadingPBar4(false);
        setCurrentPBar4(1);
        setMaxPBar4(0);
        setTextMaxPBar4('');
    }
    // navegar de volta
    const goBack = () => {

        navigation.goBack();
    };

    // solicitação para o MQTT
    const EscreveMQTT = (variable: string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch (error) { }
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable: string, auxiliar: string = '') => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch (error) { }
    }

    /**
     * remove a inscrição do topico
     * @param variable 
     * @param auxiliar 
     */
    const Unsubscribe = (variable: string, auxiliar: string = '') => {

        try {
            // pega resposta
            unsubscribeMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch (error) { }
    }


    // seta o status do módulo modbus [ativado/desativado]
    async function setStatusModbus() {

        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`${(modbusStatus) ? 'Desativando' : 'Ativando'} o módulo Modbus...`);

            if (modbusStatus) {
                // envio de solicitação de desativação do módulo modbus
                EscreveMQTT('modbus_master', 'module_end');
            }
            else {
                // envio de solicitação de ativação do módulo modbus
                EscreveMQTT('modbus_master', 'module_start');
            }

            // leitura da solicitação 
            LerMQTT('modbus_master');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // adiciona o dispositivo a estrutura
    function AdicionaDevice(probe_settings: ProbeSettings, device: string)
    {    
        // insere um device nas configurações da probe
        probe_settings.modbusDevices?.push(AddDevice(deviceCount, device, -1));

        // atualiza estrutura de configuração da probe        
        const probeAtualizada = { ...probe_settings };

        // atualiza informações de device da probe
        setProbe(probeAtualizada);

        // incrementa proximo device
        setDeviceCount(deviceCount + 1);
    }

    function InicializaSlave(device: modbusSettings) {
        setNomeDevice(device.nameDevice ?? '');

        setEndiannessDevice(device.endianessDevice ?? -1);       

        const medicao = SlavesSettingsInicial()[0]; 
        
        setSlaveAtual(device.slaves?.length ?? 0);
        setStatusSlave(medicao.statusSlave ?? false);
        setNomeSlave(medicao.nomeSlave ?? '');
        setEnderecoSlave(medicao.enderecoSlave ?? '');
        setTempoEnvioSlave(medicao.tempoEnvioSlave ?? 0);
        setProtocoloSlave(medicao.protocoloSlave ?? 0);
        setParidadeSlave(medicao.paridadeSlave ?? 0);
        setBaudRateSlave(medicao.baudeRateSlave ?? 0);
        setConexaoSlave(medicao.conexaoSlave ?? 0);
        setPortSlave(medicao.portSlave?.toString() ?? '');
        setIpSlave(medicao.ipSlave ?? '0.0.0.0');
        setVariables([]);

        GetVariaveisDevice(device.nameDevice ?? '');
    }

    /**
     * faz uma copia do ultimo slave
     * @param device 
     */
    function CopiaUltimoSlave(device: modbusSettings) {

        /* pega o indice do ultimo slave configurado */
        let ultimo_slave = device.slaves?.length ?? 0;

        /* adiciona uma copia do ultimo slave a conjunto de slaves */
        let novo_slave ={ ...device.slaves?.[ultimo_slave - 1],
                          nomeSlave: SetNameSlave(device.slaves?.[ultimo_slave - 1].nomeSlave ?? 'Slave', device.slaves?.[ultimo_slave - 1].enderecoSlave ?? '0') ,
                          id_slave: ultimo_slave,            
                          enderecoSlave: String(Number(device.slaves?.[ultimo_slave - 1].enderecoSlave) + 1),  
        }; 

        /* pega o indice */
        let indice_device: number = probe.modbusDevices?.findIndex(item => item.id_device === device.id_device) ?? -1;

        if(indice_device < 0)
            return;

        /* pega os dados da probe atualizada */
        const probeAtualizada = {...probe}

        /* insere o novo slave na ultima posição */
        probeAtualizada.modbusDevices?.[indice_device].slaves?.push(novo_slave);

        // atualiza a configuração da probe
        setProbe(probeAtualizada);

        // incrementa proximo slave
        setSlaveCount(slaveCount + 1);
    }


    async function EditarSlave (device: modbusSettings, indice_slave: number)
    {                
        setNomeDevice(device.nameDevice ?? '');
        setEndiannessDevice(device.endianessDevice ?? 0);

        setStatusSlave(device.slaves?.[indice_slave].statusSlave ?? false);
        setNomeSlave(device.slaves?.[indice_slave].nomeSlave ?? '');
        setEnderecoSlave(device.slaves?.[indice_slave].enderecoSlave ?? '');
        setTempoEnvioSlave(device.slaves?.[indice_slave].tempoEnvioSlave ?? 0);
        setProtocoloSlave(device.slaves?.[indice_slave].protocoloSlave ?? 0);
        setParidadeSlave(device.slaves?.[indice_slave].paridadeSlave ?? 0);
        setBaudRateSlave(device.slaves?.[indice_slave].baudeRateSlave ?? 0);
        setConexaoSlave(device.slaves?.[indice_slave].conexaoSlave ?? 0);
        setPortSlave(device.slaves?.[indice_slave].portSlave?.toString() ?? '');
        setIpSlave(device.slaves?.[indice_slave].ipSlave ?? '0.0.0.0');
        setVariablesAux([...device.slaves?.[indice_slave].variables ?? []]);

        // abre a tela da medição
        refRBSheetSlave.current?.open();
    }

    // exclui um determinado device
    function ExcluirDevice(probe_settings: ProbeSettings, indice_device: number) {
        // exclui o device selecionado
        probe_settings.modbusDevices?.splice(indice_device, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {
            ...probe_settings,
            modbusDevices: [...probe_settings.modbusDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbe(probeAtualizada);
    }

    async function GetVariaveisDevice(device: string) {
        // texto do loading
        setTextoLoading(`Variaveis ${device}`);

        // finaliza loading
        setLoading(true);

        // pega o json com as variaveis
        if (await getVariaveisJson(device)) {
            // loading vai durar 4 segundos
            setTimeout(function () {

                setLoading(false);

                // abre a tela da medição
                refRBSheetSlave.current?.open();

            }, 4000);
        }
    }

    // validar campo texto
    function validarCampoTexto(valor: string, mensagem_erro: string) {
        if (valor.length <= 0) {
            setTextoMessage(mensagem_erro);
            setShowMessageErro(true);
            return false;
        }

        return true;
    }

    // validar de item selecionado
    function validarItemSelecionado(valor: number, mensagemErro: string) {
        if (valor < 0) {
            setTextoMessage(mensagemErro);
            setShowMessageErro(true);
            return false;
        }
        return true;
    }

    // Validação se texto números
    function validarSeNumero(valor: string, mensagemErro: string) {
        if (!isNumber(valor)) {
            setTextoMessage(mensagemErro);
            setShowMessageErro(true);
            return false;
        }
        return true;
    }

    // salva as configurações do Slave (medição)
    async function SalvaSlave(probe_settings: ProbeSettings, indice_device: number, indice_slave: number) {

        if (!validarItemSelecionado(endiannessDevice, 'O tipo de endianness da medição não foi selecionado.') ||
            !validarCampoTexto(nomeSlave, 'O nome da medição não foi inserido.') ||
            !validarCampoTexto(enderecoSlave, 'O endereço da medição não foi inserido.') ||
            !validarSeNumero(enderecoSlave, 'O campo endereço da medição somente permite números.') ||
            !validarItemSelecionado(tempoEnvioSlave, 'O tempo de envio da medição não foi selecionado.') ||
            !validarItemSelecionado(protocoloSlave, 'O tipo de protocolo da medição não foi selecionado.')) {

            return;
        }

        // verifica o tipo de protocolo para analisar as informações
        if (protocoloSlave === ProtocoloProbe.RTU) {

            if (!validarItemSelecionado(baudRateSlave, 'O baud rate da medição não foi selecionado.') ||
                !validarItemSelecionado(paridadeSlave, 'O bit de paridade da medição não foi selecionado')) {
                return;
            }
        }
        else if (!validarItemSelecionado(conexaoSlave, 'O tipo de conexão não foi selecionado.') ||
            !validarCampoTexto(portSlave, 'O port da medição não foi inserido.') ||
            !validarSeNumero(portSlave, 'O campo port da slave somente permite números.') ||
            !validarCampoTexto(ipSlave, 'O ip da slave não foi inserido.')) {
            return;
        }

        // pega os dados da medição configuradao
        const slaveAtualizado = {
            ...probe_settings.modbusDevices?.[indice_device].slaves,
            id_slave: slaveCount,
            statusSlave: statusSlave,
            nomeSlave: nomeSlave,
            enderecoSlave: enderecoSlave,
            tempoEnvioSlave: tempoEnvioSlave,
            protocoloSlave: protocoloSlave,
            paridadeSlave: paridadeSlave,
            baudeRateSlave: baudRateSlave,
            conexaoSlave: conexaoSlave,
            portSlave: Number(portSlave),
            ipSlave: ipSlave,
            variables: [...variables],
        };

        // atualiza com as configurações do slave (medição)
        probe_settings.modbusDevices?.[indice_device].slaves?.splice(indice_slave, 1, slaveAtualizado);

        // pega os dados do device atualizado
        const deviceAtualizado = {
            ...probe_settings.modbusDevices?.[indice_device],
            nome: nomeDevice,
            endianessDevice: endiannessDevice,
            slaves: [...probe_settings.modbusDevices?.[indice_device].slaves ?? []]
        };

        // atualiza com as configurações do device
        probe_settings.modbusDevices?.splice(indice_device, 1, deviceAtualizado);

        // pega os dados da probe atualizada
        const probeAtualizada = {
            ...probe_settings,
            modbusDevices: [...probe_settings.modbusDevices ?? []]
        };

        // atualiza a configuração da probe
        setProbe(probeAtualizada);

        // incrementa proximo slave
        setSlaveCount(slaveCount + 1);

        // fecha tela de configuração da medição
        refRBSheetSlave.current?.close();
    }

    // exclui um determinado slave (medição) do device
    function ExcluirSlave(probe_: ProbeSettings, indiceDevice_: number, indiceSlave_: number) {
        // exclui o device selecionado
        probe_.modbusDevices?.[indiceDevice_].slaves?.splice(indiceSlave_, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {
            ...probe_,
            //devices: [...probe_.modbusDevices],
            modbusDevices: [...probe_.modbusDevices ?? []]
        };

        // atualiza informações de device da probe
        setProbe(probeAtualizada);
    }

    /**
     * reinicializa as configurações modbus
     * @param probe_settings 
     */
    function resetConfigModbus(probe_settings: ProbeSettings) {

        /* pega os dados da probe atualizada */
        const probeAtualizada = { ...probe_settings, modbusDevices: [] };

        /* atualiza informações de device da probe */
        setProbe(probeAtualizada);

        setDeviceAtual(0);
        setDeviceCount(0);
        setSlaveAtual(0);
        setSlaveCount(0);
        setVariableAtual(0);
    }

    // le as informações modbus
    const getModbusJson = async () => {

        // autenticação
        await apiZordonModbus().then(async (result) => {

            // se resposta OK
            if (result.status === 200) {

                // pega os dados do modbus
                setModbus(result.data);

                // pega as grandezas modbus
                setGrandezas(await getGrandezas(result.data));

                // finaliza loading
                setLoading(false);
            }
            else {

                // apresenta mensagem de erro
                setTextoMessage(`Não foi possível retornar as grandezas Modbus`);
                setShowMessageErro(true);

                // finaliza loading
                setLoading(false);
            }

        }).catch(error => {

            setTextoMessage(`Não foi possível ler as configurações Modbus.\nVerificar disponibilidade do servidor.`);
            setShowMessageErro(true);

            // finaliza loading
            setLoading(false);
        })
    }

    // le as informações das variaveis modbus
    const getVariaveisJson = async (device_name: string) => {

        // autenticação
        await apiZordonVariaveis(device_name).then(async (result) => {

            // se resposta OK
            if (result.status === 200) {

                // pega o indianess do dispositivo
                setEndiannessDevice(await getEndianness(result.data));

                // pega as variaveis do dispositivo                                                                   
                setVariables(await getVariaveis(result.data));
                setVariablesAux(await getVariaveis(result.data));
            }
            else {
                // apresenta mensagem de erro
                setTextoMessage(`Não foi possível ler as variáveis Modbus`);
                setShowMessageErro(true);

                // finaliza loading
                setLoading(false);
            }

        }).catch(error => {

            setTextoMessage(`Não foi possível ler as variáveis do Medidor.\nVerificar disponibilidade do servidor.`);
            setShowMessageErro(true);

            // finaliza loading
            setLoading(false);

            return false;
        })

        return true;
    }

    /* salvar as configurações modbus feitas - fase 1 | apaga configurações anteriores */
    function SalvarModbus_Reset(is_saving: boolean) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicializa loading progressbar
            ResetLoadingProgressBar();

            if (is_saving) {
                // inicia loading
                setLoadingPBar(true);
                setTextoLoadingPBar('\nApagando configurações...');
            }
            else {
                // inicia loading
                setLoading(true);
                setTextoLoading(`\nApagando configurações...`);
            }


            // envio de solicitação de reset da configuração modbus atual
            EscreveMQTT('modbus_reset');

            // leitura da solicitação 
            LerMQTT('modbus_reset');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }


    /* solicita os dados do slave */
    function DataModbusSlave(device: modbusSettings, slave_atual: number) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`\nSolicitando dados do slave \n${(device.slaves?.[slave_atual]?.nomeSlave ?? '')}`);

            // envio de solicitação de reset da configuração modbus atual
            EscreveMQTT('mdb_refresh_data', JSON.stringify({ value: device.slaves?.[slave_atual]?.enderecoSlave }));

            // leitura da solicitação 
            LerMQTT('mdb_refresh_data', getModbusDataSlave(device, slave_atual));

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fase 2 | reserva as configurações
    function SalvarModbus_AddBeginIni(probe_settings: ProbeSettings) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setTextoLoadingPBar('\nAdicionando configurações...');

            // inicializa
            setDeviceAtual(0);

            // total de devices configurados
            const num_devices = probe_settings.modbusDevices?.length ?? 0;
            setMaxPBar1(num_devices);

            // pega a lista de variaveis a serem configuradas
            const num_var = getTotalVariables({ ...probe_settings }, setVariablesOK);
            setVariableTotal(num_var);
            setMaxPBar2(num_var);

            // total de slaves configurados
            const num_slaves = getTotalSlaves({ ...probe_settings }, setSlavesOK);
            setSlaveTotal(num_slaves);
            setMaxPBar3(num_slaves);

            // total de medições com medias configuradas
            const num_mean = getTotalVariablesMean({ ...probe_settings }, setVariablesMeanOK);
            setMeanTotal(num_mean);
            setMaxPBar4(num_mean);

            // inicializa
            setDeviceAtual(0);
            setSlaveAtual(0);
            setVariableAtual(0);
            setMeanAtual(0);

            // solicita a necessidade de configuração para a probe
            EscreveMQTT('modbus_add_begin', JSON.stringify({ device_size: num_devices, mean_size: num_mean, slave_size: num_slaves, value: true }));

            // leitura da solicitação 
            LerMQTT('modbus_add_begin');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fase 3 | salva as configurações do device
    function SalvarModbus_AddDevice(probe_settings: ProbeSettings, device: number) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoadingPBar1(true);
            setCurrentPBar1(deviceAtual + 1);
            setTextoLoadingPBar('\nSalvando');
            setTextMaxPBar1(`Device: ${(probe_settings.modbusDevices?.[device]?.nameDevice ?? 'Device')}`);

            // monta a solicitação
            EscreveMQTT('modbus_add_device', getModbusAddDevice(probe_settings, device));

            // leitura da solicitação 
            LerMQTT('modbus_add_device');

            // proximo device
            setDeviceAtual(deviceAtual + 1);

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fase 5 | salva as configurações das variaveis do slave
    function SalvarModbus_AddVariable(probe_settings: ProbeSettings, device: number, slave: number, variable: number) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoadingPBar2(true);
            setCurrentPBar2(variableAtual + 1);
            setTextoLoadingPBar('\nSalvando');
            setTextMaxPBar1(`Device: Concluído`);
            setTextMaxPBar2(`Variável: ${(probe_settings.modbusDevices?.[device]?.slaves?.[slave].variables?.[variable].descricaoVariable ?? 'variable')}`);

            // monta a solicitação
            EscreveMQTT('modbus_add_variable', getModbusAddVariable(probe_settings, device, slave, variable));

            // leitura da solicitação 
            LerMQTT('modbus_add_variable');

            // proxima variavel
            setVariableAtual(variableAtual + 1);

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fase 4 | salva as configurações do slave
    function SalvarModbus_AddSlave(probe_settings: ProbeSettings, device: number, slave: number) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoadingPBar3(true);
            setCurrentPBar3(slaveAtual + 1);
            setTextoLoadingPBar('\nSalvando');
            setTextMaxPBar2(`Variável: Concluído`);
            setTextMaxPBar3(`Slave: ${(probe_settings.modbusDevices?.[device]?.slaves?.[slave]?.nomeSlave ?? 'slave')}`);

            // envia a solicitação
            EscreveMQTT('modbus_add_slave', getModbusAddSlave(probe_settings, device, slave));

            // leitura da solicitação 
            LerMQTT('modbus_add_slave');

            // proximo slave
            setSlaveAtual(slaveAtual + 1);

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fase 6 | salva as configurações das variaveis do slave que possuem média
    function SalvarModbus_AddVariableMean(probe_settings: ProbeSettings, device: number, slave: number, variable: number) {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoadingPBar4(true);
            setCurrentPBar4(meanAtual + 1);
            setTextoLoadingPBar('\nSalvando');
            setTextMaxPBar3(`Slave: Concluído`);
            setTextMaxPBar4(`Mean: ${probe_settings.modbusDevices?.[device]?.slaves?.[slave].variables?.[variable].nomeVariable}`);

            /* envia a solicitação */
            EscreveMQTT('modbus_add_mean', getModbusAddVariableMean(probe_settings, device, slave, variable));

            // pega o mean atual
            setMeanAtual(meanAtual + 1);

            // leitura da solicitação 
            LerMQTT('modbus_add_mean');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // salvar as configurações modbus feitas - fim
    function SalvarModbus_AddBeginFim() {
        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            // inicia loading
            setLoadingPBar(true);
            setTextMaxPBar4(`Mean: Concluído`);
            setTextoLoadingPBar('\nFinalizando as configurações...');

            EscreveMQTT('modbus_add_begin', JSON.stringify({ value: false }));

            // leitura da solicitação 
            LerMQTT('modbus_add_begin');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // leitura das configurações modbus
    const getConfiguracaoModbus = async () => {

        // se conectado com o servidor mqtt
        if (isMQTTConnected()) {

            setProbe({ ...route.params?.probeSettings });

            // envio de solicitação de da leituradas configurações do módulo modbus
            EscreveMQTT('sys_config', 'modbus');

            // leitura da solicitação 
            LerMQTT('sys_config');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function () {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {

        // se timeout não alarmado
        if (!timeoutMessage)
            return

        // verifica se o loading esta carregando
        if (!loading)
            return;

        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);

        // timeout não alarmado
        setTimeoutMessage(false);

    }, [timeoutMessage]);

    useEffect(() => {

        // texto do loading
        setTextoLoading('Informações Modbus...');

        // inicia loading
        setLoading(true);

        // loading vai durar 4 segundos
        setTimeout(async function () {

            await getModbusJson();

            // inicia leitura das configurações Modbus
            setIniciaLeitura(modbusStatus);

        }, 4000);

    }, []);

    useEffect(() => {

        // se deve iniciar leitura e modbus habilitado
        if ((iniciaLeitura) && (modbusStatus)) {

            // texto do loading
            setTextoLoading('Configurações Modbus...');

            // inicia loading
            setLoading(true);

            // loading vai durar 4 segundos
            setTimeout(function () {
                getConfiguracaoModbus();
                setIniciaLeitura(false);
            }, 4000);
        }

    }, [iniciaLeitura]);

    // executa sempre que a variavel 'modbusMasterMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusMasterMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // altera o status do modbus
                setModbusStatus(!modbusStatus);

                // pega os dados da probe atualizada
                const probeAtualizada = {
                    ...probe,
                    modbus: modbusStatus,
                };

                setProbe(probeAtualizada);

                // finaliza o loading
                setLoading(false);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);

    // executa sempre que a variavel 'modbusConfigMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusConfigMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // pega os dados da probe atualizada
            const probeAtualizada = {
                ...{ ...probe },
                modbusDevices: mqtt,
            };

            setProbe(probeAtualizada);

            // finaliza o loading
            setLoading(false);

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'modbusResetMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusResetMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // se esta salvando
                if (saving) {

                    // finaliza o loading
                    //setLoadingPBar(false);

                    // reserva a configuração modbus
                    SalvarModbus_AddBeginIni({ ...probe });
                }
                else {

                    // limpa configuração modbus
                    resetConfigModbus({ ...probe });

                    // finaliza o loading
                    setLoading(false);

                    // monta mensagem de sucesso
                    setTextoMessage(`Configuração Modbus foi limpa.`);
                    setShowMessageSucesso(true);
                }
            }
            else {

                // finaliza o loading
                setLoading(false);
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível limpar configuração Modbus`);
                setShowMessageErro(true);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [saving]);

    // executa sempre que a variavel 'modbusAddBeginIniMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusAddBeginIniMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // verifica se existem devices
                if ((probe.modbusDevices?.length ?? 0) > 0) {

                    // salva a configuração modbus do device
                    SalvarModbus_AddDevice({ ...probe }, deviceAtual);
                }
                else {

                    // finaliza o loading
                    setLoadingPBar(false);

                    // monta mensagem de erro
                    setTextoMessage(`Não foi possível configurar device [${deviceAtual}].`);
                    setShowMessageErro(true);
                }
            }
            else {

                // finaliza o loading
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível reservar a configuração Modbus.`);
                setShowMessageErro(true);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, deviceAtual]);

    // executa sempre que a variavel 'modbusAddDeviceMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusAddDeviceMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // verifica se existem devices
                if (deviceAtual < (probe.modbusDevices?.length ?? 0)) {

                    // salva a configuração modbus do device
                    SalvarModbus_AddDevice({ ...probe }, deviceAtual);
                }
                // se existem variaveis a serem configuradas
                else if (variableTotal > 0) {

                    // salva a configuração modbus da variavel do slave                    
                    SalvarModbus_AddVariable({ ...probe }, variablesOK[0].device ?? 0, 0, variablesOK[0].variable ?? 0);
                }
                else {

                    // finaliza o loading
                    setLoadingPBar(false);

                    // monta mensagem de erro
                    setTextoMessage(`Não foi possível configurar variavel [${variableAtual}].`);
                    setShowMessageErro(true);
                }
            }
            else {

                // finaliza o loading
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível salvar a configuração do device.`);
                setShowMessageErro(true);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, deviceAtual]);

    // executa sempre que a variavel 'modbusAddVariableMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusAddVariableMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // verifica se existem variaveis
                if (variableAtual < variableTotal) {

                    // salva a configuração modbus do variavel
                    SalvarModbus_AddVariable({ ...probe }, variablesOK[variableAtual]?.device ?? 0, 0, variablesOK[variableAtual]?.variable ?? 0);
                }
                else if (slaveTotal > 0) {

                    // salva a configuração modbus do slave
                    SalvarModbus_AddSlave({ ...probe }, slavesOK[0].device ?? 0, slavesOK[0].slave ?? 0);
                }
                else {

                    // finaliza o loading
                    setLoadingPBar(false);

                    // monta mensagem de erro
                    setTextoMessage(`Não foi possível configurar medição.`);
                    setShowMessageErro(true);
                }
            }
            else {

                // finaliza o loading
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível salvar a configuração da variável da medição.`);
                setShowMessageErro(true);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, variableAtual]);

    // executa sempre que a variavel 'modbudAddSlaveMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbudAddSlaveMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // verifica se existem slaves
                if (slaveAtual < slaveTotal) {

                    // salva a configuração modbus do slave
                    SalvarModbus_AddSlave({ ...probe }, slavesOK[slaveAtual]?.device ?? 0, slavesOK[slaveAtual]?.slave ?? 0);
                }
                else if (meanTotal > 0) {

                    // salva a configuração modbus do variavel que possui media
                    SalvarModbus_AddVariableMean({ ...probe }, variablesMeanOK[0]?.device ?? 0, variablesMeanOK[0]?.slave ?? 0, variablesMeanOK[0]?.variable ?? 0);
                }
                else {

                    // reserva a configuração modbus
                    SalvarModbus_AddBeginFim();
                }
            }
            else {

                // finaliza o loading
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível salvar a configuração da medição.`);
                setShowMessageErro(true);
            }
        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, slaveAtual]);

    // executa sempre que a variavel 'modbusAddMeanMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusAddMeanMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if (mqtt) {

                // verifica se ainda existem variaveis com media a serem verificadas            
                if (meanAtual < meanTotal) {

                    // salva a configuração modbus do variavel que possui media
                    SalvarModbus_AddVariableMean({ ...probe }, variablesMeanOK[meanAtual]?.device ?? 0, variablesMeanOK[meanAtual]?.slave ?? 0, variablesMeanOK[meanAtual]?.variable ?? 0);
                }
                else {

                    // reserva a configuração modbus
                    SalvarModbus_AddBeginFim();
                }
            }
            else {

                // finaliza o loading
                setLoadingPBar(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível salvar a configuração das variáveis com média.`);
                setShowMessageErro(true);
            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, meanAtual]);

    // executa sempre que a variavel 'modbusAddBeginFimMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusAddBeginFimMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // finaliza o loading
            setLoadingPBar(false);

            if (mqtt) {

                // monta mensagem de erro
                setTextoMessage(`Configuração Modbus foi salva.`);
                setShowMessageSucesso(true);
            }
            else {

                // monta mensagem de erro
                setTextoMessage(`Não foi possível salvar a configuração Modbus.`);
                setShowMessageSucesso(true);

            }

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    // executa sempre que a variavel 'modbusDataMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('modbusDataMQTT', mqtt => {

            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // finaliza o loading
            setLoading(false);

            // pega o payload
            setDataSlave([...mqtt]);

            // apresenta tela dos dados do slave modbus
            refRBSheetDataSlave.current?.open()

        },);
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);

    return (

        <>
            <Loading animating={loading} text={textoLoading} />

            <LoadingProgressBar height={screenHeight}
                animating={loadingPBar}
                text={textoLoadingPBar}
                showProgressBar1={loadingPBar1}
                currentProgress1={currentPBar1}
                maxProgress1={maxPBar1}
                textProgress1={textMaxPBar1}
                showProgressBar2={loadingPBar2}
                currentProgress2={currentPBar2}
                maxProgress2={maxPBar2}
                textProgress2={textMaxPBar2}
                showProgressBar3={loadingPBar3}
                currentProgress3={currentPBar3}
                maxProgress3={maxPBar3}
                textProgress3={textMaxPBar3}
                showProgressBar4={loadingPBar4}
                currentProgress4={currentPBar4}
                maxProgress4={maxPBar4}
                textProgress4={textMaxPBar4} />

            <MessageBoxErro
                visivel={showMessageErro}
                titulo="Atenção"
                descricao={textoMessage}
                textoBotao="OK"
                onFechar={() => setShowMessageErro(false)}
            />

            <MessageBoxSucesso
                visivel={showMessageSucesso}
                titulo="Sucesso"
                descricao={textoMessage}
                textoBotao="OK"
                onFechar={() => setShowMessageSucesso(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaDevice}
                titulo="Atenção"
                descricao={`Deseja excluir ${probe.modbusDevices?.[deviceAtual]?.nameDevice ?? ''} e todas os slaves que estiverem configurados?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaDevice(false);
                    ExcluirDevice({ ...probe }, deviceAtual);
                }}
                onCancel={() => setShowMessagePerguntaDevice(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSlave}
                titulo="Atenção"
                descricao={`Deseja realmente excluir o slave ${probe.modbusDevices?.[deviceAtual]?.slaves?.[slaveAtual]?.nomeSlave ?? ''}?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaSlave(false);
                    ExcluirSlave({ ...probe }, deviceAtual, slaveAtual);
                }}
                onCancel={() => setShowMessagePerguntaSlave(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaEdit}
                titulo="Atenção"
                descricao={`Deseja editar o slave ${probe.modbusDevices?.[deviceAtual]?.slaves?.[slaveAtual]?.nomeSlave ?? ''}?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={async () => {
                    setShowMessagePerguntaEdit(false);
                    EditarSlave({ ...probe.modbusDevices?.[deviceAtual] }, slaveAtual);
                }}
                onCancel={() => setShowMessagePerguntaEdit(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaData}
                titulo="Atenção"
                descricao={`Deseja solicitar os dados do slave ${probe.modbusDevices?.[deviceAtual]?.slaves?.[slaveAtual]?.nomeSlave ?? ''}?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={async () => {
                    setShowMessagePerguntaData(false);
                    DataModbusSlave({ ...probe.modbusDevices?.[deviceAtual] }, slaveAtual);
                }}
                onCancel={() => setShowMessagePerguntaData(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSaveMedicao}
                titulo="Atenção"
                descricao={`Salvar as alterações feitas na medição ${nomeSlave}?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaSaveMedicao(false);
                    SalvaSlave({ ...probe }, deviceAtual, slaveAtual);
                }}
                onCancel={() => setShowMessagePerguntaSaveMedicao(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaModbusStatus}
                titulo="Atenção"
                descricao={`${(modbusStatus) ? 'Desativar' : 'Ativar'} o módulo Modbus`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaModbusStatus(false);
                    setStatusModbus();
                }}
                onCancel={() => setShowMessagePerguntaModbusStatus(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSaveModbus}
                titulo="Atenção"
                descricao={`Deseja realmente salvar as configurações Modbus?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaSaveModbus(false);
                    setSaving(true);
                    SalvarModbus_Reset(true);
                }}
                onCancel={() => setShowMessagePerguntaSaveModbus(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaCleanModbus}
                titulo="Atenção"
                descricao={`Deseja realmente excluir as configurações Modbus?`}
                textoBotaoOK="Sim"
                textoBotaoCancelar='Não'
                onOK={() => {
                    setShowMessagePerguntaCleanModbus(false);
                    setSaving(false);
                    SalvarModbus_Reset(false);
                }}
                onCancel={() => setShowMessagePerguntaCleanModbus(false)}
            />

            <View style={styles.containerTela}>

                {/* header */}
                <View style={{ ...styles.containerHeader }}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID='button-back'>
                            <IconVoltar color={"#2E8C1F"} width={30} height={30} />
                        </TouchableOpacity>
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderProbeModbus}>MODBUS</Text>
                    </View>

                </View>

                {/* botões */}
                <View style={styles.containerBotoesProbeModbus}>

                    <TouchableOpacity style={{
                        height: 50, width: 70,
                        justifyContent: 'center', alignItems: 'center',
                        borderRadius: 8, borderColor: (modbusStatus) ? '#2E8C1F' : '#C0002B',
                        backgroundColor: (modbusStatus) ? '#2E8C1F' : '#C0002B'
                    }}
                        onPress={() => setShowMessagePerguntaModbusStatus(true)}
                        testID='button-status'>
                        <Text style={{ color: '#FFFFFF' }}>{(modbusStatus) ? `ON` : 'OFF'}</Text>
                    </TouchableOpacity>

                    <View style={{ height: '100%', width: '50%', justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row', gap: 30 }}>

                        <TouchableOpacity onPress={() => {
                            // se modbus habilitado
                            if (modbusStatus) {
                                setShowMessagePerguntaCleanModbus(true);
                            }
                            else {
                                setTextoMessage('Módulo Modbus está desativado.')
                                setShowMessageErro(true);
                            }
                        }}
                            testID="button-clean">
                            <IconBroom color={"#737373"} width={34} height={34} />
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            // se modbus habilitado
                            if (modbusStatus) {
                                setShowMessagePerguntaSaveModbus(true);
                            }
                            else {
                                setTextoMessage('Módulo Modbus está desativado.')
                                setShowMessageErro(true);
                            }
                        }}
                            testID='button-save'>
                            <IconSave color={"#737373"} width={34} height={34} />
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => {
                            // se modbus habilitado
                            if (modbusStatus) {
                                setIniciaLeitura(true);
                            }
                            else {
                                setTextoMessage('Módulo Modbus está desativado.')
                                setShowMessageErro(true);
                            }
                        }}
                            testID='button-refresh'>
                            <IconRefresh color={"#737373"} width={34} height={34} />
                        </TouchableOpacity>
                    </View>
                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoDevices}>
                    <Text style={styles.textoDevices}>Devices</Text>
                    {
                        (Boolean(modbus)) &&
                        <TouchableOpacity onPress={() => {
                            refRBSheetDispositivo.current?.open();
                            setSelecao(0);
                        }}
                            testID='button-plus'>
                            <IconPlus color={"#FFFFFF"} width={34} height={34} />
                        </TouchableOpacity>
                    }
                </View>

                {/* lista de equipamentos */}
                <View style={styles.containerListaEquipamentos}>

                    <FlatList style={{marginTop: 10}}
                            ItemSeparatorComponent={Space}
                            data = {probe.modbusDevices ?? []}
                            renderItem= { ({ item }) => 
                                            <CardProbe
                                                device={item.nameDevice}
                                                slaves={item.slaves}
                                                width={'100%'}
                                                onSlaveCurrent = {setSlaveAtual}
                                                onPressDelDevice= { () => {
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        setShowMessagePerguntaDevice(true);
                                                }}
                                                onPressAddSlave={ () => {
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        InicializaSlave({...item});
                                                }}
                                                onPressCopySlave={ () => {                                                        
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        CopiaUltimoSlave({...item});
                                                }}                                                
                                                onPressEditSlave={ () => {
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        setShowMessagePerguntaEdit(true);
                                                }}
                                                onPressDelSlave={ () => {
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        setShowMessagePerguntaSlave(true);
                                                } }
                                                onPressDataSlave={ () => {
                                                        setDeviceAtual(probe.modbusDevices?.findIndex(device => device.id_device === item.id_device) ?? 0);
                                                        setShowMessagePerguntaData(true);
                                                } }                                                
                                            />
                                        }
                    />

                </View>

            </View>

            {/* configuração das medições (slaves) da probe */}
            <RBSheet ref={refRBSheetSlave} height={screenHeight} >

                <View style={{ height: screenHeight}}>

                    <View style={{ height: 60, width: '100%', paddingHorizontal: 20, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>

                        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                            <CheckBox height={25} width={25} value={statusSlave} onValueChange={setStatusSlave} />
                            <Text style={styles.textoStatus}>Ativo</Text>
                        </View>

                        <TouchableOpacity onPress={() => { refRBSheetSlave.current?.close(); }} testID="button-close-slave">
                            <IconX color={"#737373"} width={34} height={34} />
                        </TouchableOpacity>
                    </View>

                    <View style={{ height: heightConfiguracoes(protocoloSlave), width: '100%', padding: 10, paddingHorizontal: 20, justifyContent: 'space-between' }}>

                        <View style={{ height: 50, width: '100%', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                            <TextEntry
                                value={nomeDevice}
                                height={'100%'}
                                width={'60%'}
                                editable={false}
                                fontSize={16}
                                maxLength={20}
                                textoLabel={`Dispositivo`}
                                backgroundColor={'#FFFFFF'}
                            />

                            <ComboBox
                                height={'100%'}
                                width={'35%'}
                                value={endiannessDevice}
                                onValueChange={setEndiannessDevice}
                                data={ListaEndianessProbe}
                                textoLabel={'Endianess'}
                                fontSize={16}
                                textoPlaceHolder={'Endianess'}
                                fontSizePlaceHolder={14}
                                textoCor={'#737373'}
                                borderRadius={8}
                                backgroundColor={'#FFFFFF'}
                            />
                        </View>

                        <View style={{ height: 0, width: '100%', justifyContent: 'center' }}>
                            <Divider />
                        </View>

                        <View style={{ height: 50, width: '100%', flexDirection: 'row', justifyContent: 'space-between' }}>
                            <TextEntry
                                value={nomeSlave}
                                height={'100%'}
                                width={'60%'}
                                editable={true}
                                placeHolder='Nome da Medição'
                                onValueChange={setNomeSlave}
                                fontSize={16}
                                maxLength={20}
                                type={'texto'}
                                textoLabel={'Nome da Medição'}
                                backgroundColor={'#FFFFFF'}
                            />
                            <TextEntry
                                value={enderecoSlave}
                                height={'100%'}
                                width={'35%'}
                                editable={true}
                                placeHolder='Endereço'
                                onValueChange={setEnderecoSlave}
                                fontSize={16}
                                type={'texto'}
                                textoLabel={'Endereço'}
                                backgroundColor={'#FFFFFF'}
                            />
                        </View>

                        <View style={{ height: 50, width: '100%', flexDirection: 'row', justifyContent: 'space-between' }}>
                            <ComboBox
                                height={'100%'}
                                width={'60%'}
                                value={tempoEnvioSlave}
                                onValueChange={setTempoEnvioSlave}
                                data={ListaTempoEnvioProbe}
                                textoLabel={'Tempo de Envio'}
                                fontSize={16}
                                textoPlaceHolder={'Tempo de Envio'}
                                fontSizePlaceHolder={14}
                                textoCor={'#737373'}
                                borderRadius={8}
                                backgroundColor={'#FFFFFF'}
                            />

                            <ComboBox
                                height={'100%'}
                                width={'35%'}
                                value={protocoloSlave}
                                onValueChange={setProtocoloSlave}
                                data={ListaProtocoloProbe}
                                textoLabel={'Protocolo'}
                                fontSize={16}
                                textoPlaceHolder={'Protocolo'}
                                fontSizePlaceHolder={14}
                                textoCor={'#737373'}
                                borderRadius={8}
                                backgroundColor={'#FFFFFF'}
                            />

                        </View>

                        {
                            (protocoloSlave === ProtocoloProbe.RTU) &&

                            <View style={{ height: 50, width: '100%', flexDirection: 'row', justifyContent: 'space-between' }}>

                                <ComboBox
                                    height={'100%'}
                                    width={'60%'}
                                    value={baudRateSlave}
                                    onValueChange={setBaudRateSlave}
                                    data={ListaBaudRateProbe}
                                    textoLabel={'Baud Rate'}
                                    fontSize={16}
                                    textoPlaceHolder={'Baud Rate'}
                                    fontSizePlaceHolder={14}
                                    textoCor={'#737373'}
                                    borderRadius={8}
                                    backgroundColor={'#FFFFFF'}
                                />

                                <ComboBox
                                    height={'100%'}
                                    width={'35%'}
                                    value={paridadeSlave}
                                    onValueChange={setParidadeSlave}
                                    data={ListaParidadeProbe}
                                    textoLabel={'Paridade'}
                                    fontSize={16}
                                    textoPlaceHolder={'Paridade'}
                                    fontSizePlaceHolder={14}
                                    textoCor={'#737373'}
                                    borderRadius={8}
                                    backgroundColor={'#FFFFFF'}
                                />
                            </View>
                        }

                        {
                            (protocoloSlave === ProtocoloProbe.TCP) &&

                            <View style={{ height: 120, width: '100%', gap: 20 }}>

                                <View style={{ height: 50, width: '100%', flexDirection: 'row', justifyContent: 'space-between' }}>

                                    <ComboBox
                                        height={'100%'}
                                        width={'60%'}
                                        value={(conexaoSlave === 2) ? 1 : conexaoSlave}
                                        onValueChange={setConexaoSlave}
                                        data={ListaTipoConexaoProbe}
                                        textoLabel={'Tipo de Conexão'}
                                        fontSize={16}
                                        textoPlaceHolder={'Tipo de Conexão'}
                                        fontSizePlaceHolder={14}
                                        textoCor={'#737373'}
                                        borderRadius={8}
                                        backgroundColor={'#FFFFFF'}
                                    />

                                    <TextEntry
                                        value={portSlave}
                                        height={'100%'}
                                        width={'35%'}
                                        editable={true}
                                        placeHolder='Port'
                                        onValueChange={setPortSlave}
                                        fontSize={16}
                                        type={'texto'}
                                        textoLabel={'Port'}
                                        backgroundColor={'#FFFFFF'}
                                    />

                                </View>

                                <View style={{ height: 50, width: '100%' }}>
                                    <TextEntry
                                        value={ipSlave}
                                        height={'100%'}
                                        width={'100%'}
                                        editable={true}
                                        placeHolder='IP'
                                        onValueChange={setIpSlave}
                                        fontSize={16}
                                        type={'ip'}
                                        textoLabel={'IP'}
                                        backgroundColor={'#FFFFFF'}
                                    />

                                </View>

                            </View>
                        }

                    </View>

                    {/* variables */}
                    <View style={{ width: '100%', height: heightVariables(protocoloSlave, screenHeight), gap: 10, padding: 20 }}>

                        <Text style={styles.textoStatus}>Variáveis</Text>
                    
                        <FlatList style={{marginTop: 0}}
                                  ItemSeparatorComponent={Space}              
                                  data = {[...variablesAux]}
                                  renderItem= { ({ item }) =>  <CardVariable
                                                                    data = {[...variables]}
                                                                    onUpdateData = {setVariables}
                                                                    descricao = {item.descricaoVariable}
                                                                    variavel = {item.nomeVariable}
                                                                    ultimoEnvio = {item.valorUltimaVariable}
                                                                    mediaEnvio = {item.valorMediaVariable}
                                                                    funcao = {item.funcaoVariable}
                                                                    endereco = {item.enderecoVariable}
                                                                    fator = {item.fatorVariable}
                                                                    formato = {item.formatoVariable}
                                                                />
                                }
                        />
                    </View>                

                    <View style={{ height: 80, width: '100%', paddingHorizontal: 20, flexDirection:'column', justifyContent:'flex-start' }}>
                        <TouchableOpacity style={styles.buttonSalvarMedicao}
                                onPress={() => { setShowMessagePerguntaSaveMedicao(true); }}
                                testID="button-save-slave">
                            <Text style={styles.textoButtonSalvarMedicao}>SALVAR</Text>
                        </TouchableOpacity>
                    </View>

                </View>

                
            </RBSheet>

            {/* seleção do tipo de dispositivo */}
            <RBSheet ref={refRBSheetDispositivo} height={screenHeight}>

                <View style={{ height: 60, width: '100%', paddingHorizontal: 20, flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center', backgroundColor: '#737373' }}>
                    <TouchableOpacity onPress={() => refRBSheetDispositivo.current?.close()} testID='button-close-device'>
                        <IconX color={"#FFFFFF"} width={34} height={34} />
                    </TouchableOpacity>
                </View>

                {
                    // se deve selecionar a grandeza
                    (selecao === 0) &&
                    <View style={{ height: '100%', width: '100%', paddingHorizontal: 20, paddingBottom: 80, justifyContent: 'flex-start', alignItems: 'flex-start', backgroundColor: '#737373', gap: 10 }}>
                        <Text style={{ ...styles.textoStatus, color: '#45D42E' }}>Selecione a grandeza</Text>

                        <FlatList style={{ width: '100%', marginTop: 20 }}
                            ItemSeparatorComponent={Divider}
                            data={grandezas}
                            renderItem={({ item }) =>
                                <TouchableOpacity style={{ height: 60, justifyContent: 'center', alignItems: 'center' }}
                                    onPress={async () => {
                                        setSelecao(1);
                                        setGrandeza(item);
                                        setFabricantes(await getFabricantes(modbus, item));
                                    }}
                                    testID="button-grandeza"
                                >
                                    <Text style={styles.textoItensSelecao}>{item}</Text>
                                </TouchableOpacity>
                            }
                        />
                    </View>
                }

                {
                    // se deve selecionar o fabricante
                    (selecao === 1) &&
                    <View style={{ height: '100%', width: '100%', paddingHorizontal: 20, paddingBottom: 80, justifyContent: 'flex-start', alignItems: 'flex-start', backgroundColor: '#737373', gap: 10 }}>
                        <Text style={{ ...styles.textoStatus, color: '#45D42E' }}>Selecione o fabricante</Text>

                        <FlatList style={{ width: '100%', marginTop: 20 }}
                            ItemSeparatorComponent={Divider}
                            data={fabricantes}
                            renderItem={({ item }) =>
                                <TouchableOpacity style={{ height: 60, justifyContent: 'center', alignItems: 'center' }}
                                    onPress={async () => {
                                        setSelecao(2);
                                        setFabricante(item);
                                        setMedidores(await getMedidores(modbus, grandeza, item));
                                    }}
                                    testID="button-fabricante"
                                >
                                    <Text style={styles.textoItensSelecao}>{item}</Text>
                                </TouchableOpacity>
                            }
                        />
                    </View>
                }

                {
                    // se deve selecionar o medidor
                    (selecao === 2) &&
                    <View style={{ height: '100%', width: '100%', paddingHorizontal: 20, paddingBottom: 80, justifyContent: 'flex-start', alignItems: 'flex-start', backgroundColor: '#737373', gap: 10 }}>
                        <Text style={{ ...styles.textoStatus, color: '#45D42E' }}>Selecione o medidor</Text>

                        <FlatList style={{ width: '100%', marginTop: 20 }}
                            ItemSeparatorComponent={Divider}
                            data={medidores}
                            renderItem={({ item }) =>
                                <TouchableOpacity style={{ height: 60, justifyContent: 'center', alignItems: 'center' }}
                                    onPress={async () => {
                                        AdicionaDevice({ ...probe }, await getDevice(modbus, grandeza, fabricante, item));
                                        refRBSheetDispositivo.current?.close();
                                    }}
                                    testID="button-medidor"
                                >
                                    <Text style={styles.textoItensSelecao}>{item}</Text>
                                </TouchableOpacity>
                            }
                        />
                    </View>
                }
            </RBSheet>

            {/* dados do slave modbus */}
            <RBSheet ref={refRBSheetDataSlave} height={screenHeight}>

                <View style={{ height: 60, width: '100%', paddingHorizontal: 20, flexDirection: 'row', justifyContent: 'flex-end', alignItems: 'center', backgroundColor: '#FFFFFF' }}>
                    <TouchableOpacity onPress={() => { 
                        Unsubscribe('mdb_refresh_data', getModbusDataSlave({ ...probe.modbusDevices?.[deviceAtual] }, slaveAtual));
                        refRBSheetDataSlave.current?.close()}} testID='button-close-data'>
                        <IconX color={"#737373"} width={34} height={34} />
                    </TouchableOpacity>
                </View>

                {/* area de conteudo dos dados do slave modbus*/}
                <DataSlave payload={dataSlave} slave={probe.modbusDevices?.[deviceAtual]?.slaves?.[slaveAtual]?.nomeSlave ?? ''} />

            </RBSheet>

        </>

    )
}

export default ProbeModbusX;
