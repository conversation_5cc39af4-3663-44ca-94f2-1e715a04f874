import { <PERSON>Container } from "@react-navigation/native";
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import React from "react";
import ProbeModbusX from ".";
import { MockModbusJson } from "../../../../__mocks__/ModbusJsonMock";

import * as ApiZordon from '../../../services/apiZordon';
const spyApiZordon = jest.spyOn(ApiZordon, 'default');

import { EventRegister } from "react-native-event-listeners";
import { MockModbusSettings, MockModbusSettingsSlaves, MockModbusSettingsSlaves1 } from "../../../../__mocks__/ModbusSettingsMock";
import { MockProbeSettings } from "../../../../__mocks__/ProbeSettingsMock";
import * as spyMQTT from "../../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn

jest.mock('sp-react-native-mqtt', () => ({
    connect: jest.fn(() => ({
        subscribe: jest.fn(),
        on: jest.fn(),
    })),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.useFakeTimers();

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                    }
    },
};

const mockRouteStatusON = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       modbus : true,
                       modbusDevices: MockModbusSettings,
                    }
    },
};

const mockRouteModbus = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       modbus : true,
                       modbusDevices: [MockModbusSettings],
                    }
    },
};

const mockRouteStatusSlaves = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       modbus : true,                       
                    }
    },
};

describe('ProbeModbus', () => {

    beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
    });
    

    it('renderiza corretamente com a rota fornecida e modulo off', () => {
    
        const {unmount} = render(   <NavigationContainer>
                                        <ProbeModbusX route={mockRoute} />
                                    </NavigationContainer> );
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });

        expect(screen).toBeTruthy();
        
        unmount();
    }); 

    it('renderiza corretamente com a rota fornecida e modulo on', () => {
    
        const {unmount} = render(   <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        unmount();
    });

    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {unmount, getByTestId, queryByTestId} = render(   <NavigationContainer>
                                                                    <ProbeModbusX route={mockRoute}/>
                                                                </NavigationContainer> );
        
        // Verifica se o botão existe no DOM e está visível
        const buttonBack = queryByTestId('button-back');
        expect(buttonBack).toBeTruthy(); // O botão deve estar presente

        act(() => {
            fireEvent.press(getByTestId('button-back'));
        });

        unmount();
    });
    
    it('renderiza corretamente ao clicar no botão de ativar modulo modbus e cancelar', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeModbusX route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {      
            fireEvent.press(getByTestId('button-status'));                                
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await act(async () => {      
            fireEvent.press(botaoNao);
        });
        
        unmount(); 
    });

    it('renderiza corretamente ao clicar no botão para desativar o modulo modbus e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeModbusX route={mockRouteStatusON} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {      
            fireEvent.press(getByTestId('button-status'));                
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });

        unmount();
    }); 

    it('renderiza corretamente com a rota fornecida para ler o modbus quando json como objeto', async () => {
            
        // cenario erro    
        spyApiZordon.mockResolvedValueOnce({data:MockModbusJson, status:500})

        // render erro
        const renderErro = render(  <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
        
        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        renderErro.unmount()

        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:MockModbusJson, status:200})
                
        const renderSucesso = render(  <NavigationContainer>
                                            <ProbeModbusX route={mockRouteStatusON} />
                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        renderSucesso.unmount()
    });
    
    it('renderiza corretamente com a rota fornecida para ler os modbus, mas houve erro', async () => {                

        // cenario erro            
        spyApiZordon.mockRejectedValueOnce(new Error('Erro na API'));

        // render erro
        const renderErro = render(  <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
        
        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        renderErro.unmount()
    });

    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar dispositivo e cancela', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        
        await act(async() => {
            fireEvent.press(getByTestId('button-close-device'));
        });        
        
        unmount()
    });

    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar dispositivo e seleciona grandeza', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId,getByText} = render(<NavigationContainer>
                                                            <ProbeModbusX route={mockRouteStatusON} />
                                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        

        // Encontre o botão com a grandeza
        const button = getByText('Energia');

        await act( async() => {            
            fireEvent.press(button);            
        });
        
        unmount()
    });
    
    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar dispositivo e seleciona fabricante', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId, getAllByTestId, getByText} = render(<NavigationContainer>
                                                                            <ProbeModbusX route={mockRouteModbus} />
                                                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        
        // Encontre o botão com a grandeza
        const buttonGrandeza = getByText('Energia');

        await act( async() => {            
            fireEvent.press(buttonGrandeza);            
        });
                        
        await act(async() => {
            expect('Selecione o fabricante').toBeTruthy();
        });
        
        // Usamos waitFor para esperar os botões do fabricante aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-fabricante');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a fabricante
        const buttonFabricantes = getAllByTestId('button-fabricante');

        await act( async() => {            
            fireEvent.press(buttonFabricantes[0]);
        });

        // Usamos waitFor para esperar os botões do medidor aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-medidor');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a medidor
        const buttonMedidor = getAllByTestId('button-medidor');

        await act( async() => {            
            fireEvent.press(buttonMedidor[0]);
        });

        unmount()
    }); 
    
    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar e depois deleta e cancela', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId, getAllByTestId, getByText} = render(<NavigationContainer>
                                                                            <ProbeModbusX route={mockRouteModbus} />
                                                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        
        // Encontre o botão com a grandeza
        const buttonGrandeza = getByText('Energia');

        await act( async() => {            
            fireEvent.press(buttonGrandeza);            
        });
                        
        await act(async() => {
            expect('Selecione o fabricante').toBeTruthy();
        });
        
        // Usamos waitFor para esperar os botões do fabricante aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-fabricante');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a fabricante
        const buttonFabricantes = getAllByTestId('button-fabricante');

        await act( async() => {            
            fireEvent.press(buttonFabricantes[0]);
        });

        // Usamos waitFor para esperar os botões do medidor aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-medidor');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a medidor
        const buttonMedidor = getAllByTestId('button-medidor');

        await act( async() => {            
            fireEvent.press(buttonMedidor[0]);
        });

        // Encontre os botões para apagar os devices
        const buttonDeleteDevice = getAllByTestId('delete-device-button');

        await act( async() => {            
            fireEvent.press(buttonDeleteDevice[0]);
        }); 
        
        await act( async() => {            
            fireEvent.press(getByText('Não'));
        });         
        
        unmount()
    });
    
    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar e depois deleta e confirma', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId, getAllByTestId, getByText} = render(<NavigationContainer>
                                                                            <ProbeModbusX route={mockRouteModbus} />
                                                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        
        // Encontre o botão com a grandeza
        const buttonGrandeza = getByText('Energia');

        await act( async() => {            
            fireEvent.press(buttonGrandeza);            
        });
                        
        await act(async() => {
            expect('Selecione o fabricante').toBeTruthy();
        });
        
        // Usamos waitFor para esperar os botões do fabricante aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-fabricante');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a fabricante
        const buttonFabricantes = getAllByTestId('button-fabricante');

        await act( async() => {            
            fireEvent.press(buttonFabricantes[0]);
        });

        // Usamos waitFor para esperar os botões do medidor aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-medidor');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a medidor
        const buttonMedidor = getAllByTestId('button-medidor');

        await act( async() => {            
            fireEvent.press(buttonMedidor[0]);
        });

        // Encontre os botões para apagar os devices
        const buttonDeleteDevice = getAllByTestId('delete-device-button');

        await act( async() => {            
            fireEvent.press(buttonDeleteDevice[0]);
        }); 
        
        await act( async() => {            
            fireEvent.press(getByText('Sim'));
        });         
        
        unmount()
    });    

    it('renderiza corretamente com a rota fornecida para ler o modbus e clica no botão de adicionar e depois adiciona slave #', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockModbusJson), status:200})
                
        const {unmount, getByTestId, getAllByTestId, getByText} = render(<NavigationContainer>
                                                                            <ProbeModbusX route={mockRouteModbus} />
                                                                        </NavigationContainer> );

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect('Configurações Modbus...').toBeTruthy();
        });

        act(() => {
            // Avance o tempo em 4 segundos
            jest.advanceTimersByTime(4000);
        });

        await act(async() => {
            expect(getByTestId('button-plus')).toBeTruthy();
        });

        await act(async() => {
            fireEvent.press(getByTestId('button-plus'));
        });
        
        // Encontre o botão com a grandeza
        const buttonGrandeza = getByText('Energia');

        await act( async() => {            
            fireEvent.press(buttonGrandeza);            
        });
                        
        await act(async() => {
            expect('Selecione o fabricante').toBeTruthy();
        });
        
        // Usamos waitFor para esperar os botões do fabricante aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-fabricante');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a fabricante
        const buttonFabricantes = getAllByTestId('button-fabricante');

        await act( async() => {            
            fireEvent.press(buttonFabricantes[0]);
        });

        // Usamos waitFor para esperar os botões do medidor aparecerem um por um
        await waitFor(() => {
            const button = getAllByTestId('button-medidor');
            expect(button.length).toBeGreaterThan(0); // Certifica-se de que pelo menos um botão está presente
        });

        // Encontre o botão com a medidor
        const buttonMedidor = getAllByTestId('button-medidor');

        await act( async() => {            
            fireEvent.press(buttonMedidor[0]);
        });

        // Encontre os botões para adicionar slaves
        const buttonAddSlave = getAllByTestId('add-slave-button');

        await act( async() => {            
            fireEvent.press(buttonAddSlave[0]);
        }); 
                
        unmount()
    });

    it('renderiza corretamente ao clicar no botão para ativar o modulo modbus e mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        }); 
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });

    it('renderiza corretamente ao clicar no botão para ativar o modulo modbus e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        }); 
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });    

    it('renderiza corretamente ao clicar no botão para limpar configuração modbus', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração modbus e modulo ON e clica no botão Não', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração modbus e modulo ON e clica no botão Sim, mas mqtt desconectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para limpar configuração modbus e modulo ON e clica no botão Sim e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });
    
    it('renderiza corretamente ao clicar no botão para ler a configuração modbus e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeModbusX route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });        
    });
    
    it('renderiza corretamente ao clicar no botão para ler a configuração modbus e modulo ON e MQTT desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeModbusX route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    }); 
    
   it('renderiza corretamente ao clicar no botão para ler a configuração modbus e modulo ON e MQTT conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeModbusX route={mockRouteStatusON} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    }); 
    
    it('renderiza corretamente ao clicar no botão de salvar a configuração modbus', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRoute}/>
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                                
            });
        });
    });

    it('renderiza corretamente ao clicar no botão de salvar a configuração modbus e cancelar', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    }); 
    
    it('renderiza corretamente ao clicar no botão de salvar a configuração modbus, modulo ON, clica no botão Sim e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));                                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });
    });    

    it('deve executar o fluxo esperado ao atualizar status do módulo modbus = true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusMasterMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusMasterMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao atualizar status do módulo modbus = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusMasterMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusMasterMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração modbus', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao limpar as configurações modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusResetMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao limpar as configurações modbus com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusResetMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao iniciar as configurações modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddBeginIniMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddBeginIniMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao limpar as configurações modbus com sucesso e salvar configuração', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusResetMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    

        act(() => {
            fireEvent.press(getByTestId('button-save'));
        });

        act(() => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusResetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao iniciar as configurações modbus com sucesso, mas não existe nenhum modbus', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddBeginIniMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddBeginIniMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar as configurações modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddDeviceMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddDeviceMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar as configurações modbus com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddDeviceMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddDeviceMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar as variaveis modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddVariableMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddVariableMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar as variaveis modbus com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddVariableMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddVariableMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar os slaves modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbudAddSlaveMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbudAddSlaveMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar os slaves modbus com sucesso', () => {
        
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbudAddSlaveMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbudAddSlaveMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar os means modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddMeanMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddMeanMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar os means modbus com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddMeanMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddMeanMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar os means modbus com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddBeginFimMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddBeginFimMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar os means modbus com falha', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusAddBeginFimMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeModbusX route={mockRouteStatusON} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusAddBeginFimMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar os dados do slave modbus', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusDataMQTT') {
            callback([]); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render( <NavigationContainer>
                                                    <ProbeModbusX route={mockRouteStatusON} />
                                                </NavigationContainer> );
    
        /* clica no botão de data */
        act(() => {
            fireEvent.press(getByTestId('button-close-data'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusDataMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    


    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão deletar e cancela', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('delete-slave-button-1');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* cancela operação */
        act(() => {
            fireEvent.press(getByText('Não'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão deletar e confirma', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('delete-slave-button-1');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão editar e cancelar', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('edit-slave-button-2');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* cancela operação */
        act(() => {
            fireEvent.press(getByText('Não'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão editar, confirma e fecha tela', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('edit-slave-button-2');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        });
        
        /* fecha tela do slave */
        act(() => {
            fireEvent.press(getByTestId('button-close-slave'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão editar, confirma, salva e cancela', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('edit-slave-button-2');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        });
        
        /* fecha tela do slave */
        act(() => {
            fireEvent.press(getByTestId('button-save-slave'));
        });        

        /* cancela operação */
        act(() => {
            fireEvent.press(getByText('Não'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão editar, confirma, salva e confirma', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves1()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de delete */
        const delete_button = getByTestId('edit-slave-button-2');
        
        /* clica no botão de delete */
        act(() => {
            fireEvent.press(delete_button);
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        });
        
        /* fecha tela do slave */
        act(() => {
            fireEvent.press(getByTestId('button-save-slave'));
        });        

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão data e cancelar', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de data */
        const data_button = getByTestId('data-slave-button-3');
        
        /* clica no botão de data */
        act(() => {
            fireEvent.press(data_button);
        });

        /* cancela operação */
        act(() => {
            fireEvent.press(getByText('Não'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão data, confirma mas mqtt desconectado', () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });

        /* pega o botão de data */
        const data_button = getByTestId('data-slave-button-3');
        
        /* clica no botão de data */
        act(() => {
            fireEvent.press(data_button);
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        }); 

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    
    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave, botão data, confirma mas mqtt conectado', () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();

        /* Procura elementos que começam com "show-toggle-buttons" */
        const toggle_buttons = getAllByTestId('show-toggle-buttons');

        // mostra os botões do slave
        act(() => {
            fireEvent.press(toggle_buttons[0]);
        });
        
        /* clica no botão de data */
        act(() => {
            fireEvent.press(getByTestId('data-slave-button-3'));
        });

        /* confirma operação */
        act(() => {
            fireEvent.press(getByText('Sim'));
        }); 
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração modbus e mostras os botões de recurso do slave e botão cópia', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusConfigMQTT') {
            callback(MockModbusSettingsSlaves()); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId,getByText, getAllByTestId} = render(<NavigationContainer>
                                                                           <ProbeModbusX route={mockRouteStatusSlaves} />
                                                                        </NavigationContainer> );
    
        expect('Slave 1').toBeTruthy();
        
        /* clica no botão de data */
        act(() => {
            fireEvent.press(getByTestId('copy-slave-button'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
});