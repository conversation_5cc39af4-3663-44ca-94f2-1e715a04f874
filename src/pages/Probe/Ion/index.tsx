import React, { useEffect, useRef, useState } from "react";
import { ActivityIndicator, FlatList, Text, TouchableOpacity, View } from "react-native";
import { EventRegister } from "react-native-event-listeners";
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import { screenHeight, styles } from './layout';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import { StackTypes } from '../../../routes/index';

// imagens vetoriais
import IconVoltar from '../../../assets/svg/icon_arrow-left.svg';
import IconBroom from '../../../assets/svg/icon_broom.svg';
import IconPlus from '../../../assets/svg/icon_plus.svg';
import IconRefresh from '../../../assets/svg/icon_refresh.svg';
import IconSave from '../../../assets/svg/icon_save-01.svg';
import IconSearch from '../../../assets/svg/icon_search.svg';
import IconX from '../../../assets/svg/icon_x.svg';


// componentes
import CardIon from "../../../componentes/CardIon";
import ComboBox from "../../../componentes/ComboBox";
import Divider from "../../../componentes/Divider";
import Loading from "../../../componentes/Loading";
import TextEntry from "../../../componentes/TextEntry";

// modal messages
import MessageBoxAtencao from "../../../modal/messagebox/Atencao";
import MessageBoxErro from "../../../modal/messagebox/Erro";
import MessageBoxPergunta from "../../../modal/messagebox/Pergunta";
import MessageBoxSucesso from "../../../modal/messagebox/Sucesso";

// funções mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT, unsubscribeMQTT } from "../../../services/mqtt";

// data
import { AddIon, ionSettings, ProbeSettings } from "../../../data";

// constantes
import {
    ListaBaudRateProbe,
    ListaComunicacaoSerial,
    ListaModoBuscaION,
    ListaParidadeProbe,
    TimeoutTipoConexao,
    TiposComunicacaoION
} from "../../../constantes";

// funções
import Space from "../../../componentes/Space";
import { isNumber } from "../../../funcoes";
import DataIon from "./DataIon";


const ProbeION = ({route}) => {

    const deepCopy = (obj: any) => { return JSON.parse(JSON.stringify(obj)); };

    const refRBSheetTipoIon = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetIon = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetSearchIon = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetDataIon = useRef<{ open: () => void, close: () => void }>(null);
    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
    
    // definições da probe
    const [probe, setProbe] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));
    const [probeCopy, setProbeCopy] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));

    // indica o status atual do ion
    const [ionStatus, setIonStatus] = useState<boolean>(route.params?.probeSettings.ion);
    
    // informações do ion    
    const [tipoIon, setTipoIon] = useState<string>('');
    const [tiposIon, setTiposIon] = useState<string[]>([...TiposComunicacaoION]);      
    const [ionCount, setIonCount] = useState<number>(0);
    const [id, setID] = useState<string>('');
    const [port, setPort] = useState<number>(-1);
    const [paridade, setParidade] = useState<number>(-1);
    const [baudRate, setBaudRate] = useState<number>(-1);
    const [modoBusca, setModoBusca] = useState<number>(-1);
    const [dataIon, setDataIon] = useState<string[]>([]);
    const [isCollect, setIsCollect] = useState<boolean>(false);


   // configuração da ion
    const [ionAtual, setIonAtual] = useState<number>(-1);
    const [saveIonAtual, setSaveIonAtual] = useState<number>(0);    
    const [excluirTodosIon, setExcluirTodosIon] = useState<boolean>(false);

    // inicia leitura da configuração ion
    const [iniciaLeitura, setIniciaLeitura] = useState<boolean>(true);

    // se esta salvando configuração
    const [saving, setSaving] = useState<boolean>(false);    
    const [searching, setSearching] = useState<boolean>(false);

    // se deve adicionar ion ou salvar
    const [adicionar, setAdicionar] = useState<boolean>(false);
    const [showAdicionar, setShowAdicionar] = useState<boolean>(true);

    // mensagens modal
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);
    const [showMessageAtencao, setShowMessageAtencao] = useState<boolean>(false);    
    const [showMessagePerguntaDel, setShowMessagePerguntaDel] = useState<boolean>(false);
    const [showMessagePerguntaEdit, setShowMessagePerguntaEdit] = useState<boolean>(false);
    const [showMessagePerguntaSave, setShowMessagePerguntaSave] = useState<boolean>(false);
    const [showMessagePerguntaIonStatus, setShowMessagePerguntaIonStatus] = useState<boolean>(false);
    const [showMessagePerguntaCleanIon, setShowMessagePerguntaCleanIon] = useState<boolean>(false);
    const [showMessagePerguntaSaveIon, setShowMessagePerguntaSaveIon] = useState<boolean>(false);
    const [showMessagePerguntaBusca, setShowMessagePerguntaBusca] = useState<boolean>(false);
    const [showMessagePerguntaData, setShowMessagePerguntaData] = useState<boolean>(false);
        
    const [textoMessage, setTextoMessage] = useState<string>('');
    
    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');
    const [textoLoadingBusca, setTextoLoadingBusca] = useState<string>('');

    // controle timeout loading    
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);    
    let timeoutWait: number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];

    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();
    };

    /**
     * publica no mqtt
     * @param variable 
     * @param auxiliar 
     */
    const Publish = (variable:string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error) {}
    }
    
    /**
     * inscreve o topico para leitura das informações
     * @param variable 
     * @param auxiliar 
     */
    const Subscribe = (variable:string, auxiliar: string = '') => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error) {}
    }

    /**
     * remove a inscrição do tópico 
     * @param variable 
     * @param auxiliar 
     */
    const Unsubscribe = (variable:string, auxiliar: string = '') => {

        try {
            // pega resposta
            unsubscribeMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error) {}
    }    

    // adiciona o dispositivo Ion a estrutura
    function AdicionarIon(probe_: ProbeSettings, id_: string, tipo_ion: string, port_: number, paridade_: number, baud_rate: number)
    {        
        // se foi inserido o id do Ion
        if(id_.length <= 0)  {
            setTextoMessage('O ID do ION não foi inserido.');
            setShowMessageErro(true);
            return;
        }

        // se foi digitado somente números
        if(!isNumber(id_)){
            setTextoMessage('O campo ID do ION somente permite números.');
            setShowMessageErro(true);
            return;
        }        

        // se foi selecionado o baud rate
        if(baud_rate < 0)  {
            setTextoMessage('O baud rate do ION não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        

        // se foi selecionado o bit de paridade
        if(paridade_ < 0)  {
            setTextoMessage('O bit de paridade do ION não foi selecionado');
            setShowMessageErro(true);
            return;
        }

        // insere um codi nas configurações da probe
        probe_.ionDevices?.push(AddIon(Number(id_), tipo_ion, port_, paridade_, baud_rate));
        
        // atualiza estrutura de configuração da probe        
        const probeAtualizada = {...probe_};        

        // atualiza informações de device da probe
        setProbe({...probeAtualizada});

        // remove o tipo de configurado para as próximas configurações
        removerTipoIon(tipo_ion);

        // fecha tela de configuração do ion
        refRBSheetIon.current?.close();        
    }

    // salva as configurações do ion
    async function SalvarIon(probe_: ProbeSettings, indice_ion: number, id_: string, tipo_ion: string, port_: number, paridade_: number, baud_rate: number) {        

        // se foi inserido o id do Ion
        if(id_.length <= 0)  {
            setTextoMessage('O ID do ION não foi inserido.');
            setShowMessageErro(true);
            return;
        }

        // se foi digitado somente números
        if(!isNumber(id_)){
            setTextoMessage('O campo ID do ION somente permite números.');
            setShowMessageErro(true);
            return;
        }        

        // se foi selecionado o baud rate
        if(baud_rate < 0)  {
            setTextoMessage('O baud rate do ION não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        

        // se foi selecionado o bit de paridade
        if(paridade_ < 0)  {
            setTextoMessage('O bit de paridade do ION não foi selecionado');
            setShowMessageErro(true);
            return;
        }                
        
        // pega os dados da ion configurado
        const ionAtualizado ={ ...{...probe_.ionDevices?.[indice_ion] ?? []},
                                id_ion: Number(id_),
                                ion: tipo_ion,
                                port: port_,    
                                paridade: paridade_,
                                baudRate: baud_rate,
        }; 

        // atualiza com as configurações do ion
        probe_.ionDevices?.splice(indice_ion, 1, ionAtualizado);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},
                                 ionDevices: [...probe_.ionDevices ?? []]
        };
        
        // atualiza a configuração da probe
        setProbe({...probeAtualizada});

        // incrementa proximo ion
        setIonCount(ionCount + 1);

        // fecha tela de configuração da medição
        refRBSheetIon.current?.close();
    }

    // se deve remover o tipo de ion a ser configurado
    const removerTipoIon = (tipo_ion: string) => {

        // verifica se diferente
        if(tipo_ion === 'RS485')
            return;

        // pega o indice do item a ser excluido da lista
        const indice = tiposIon.findIndex(item => item === tipo_ion);

        // exclui o item
        tiposIon.splice(indice, 1);
    }

    // se deve adicionar o tipo de ion a ser configurado
    const adicionarTipoIon = (tipo_codi: string) => {
        
        // adiciona um novo item
        tiposIon.push(tipo_codi);

        // ordena em ordem alfabetica
        setTiposIon(tiposIon.sort()); 

        // deve apresentar botão de adicionar configuração ion
        setShowAdicionar(true);
    }

    // leitura das configurações ion
    const getConfiguracaoIon = async () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // envio de solicitação de da leituradas configurações do módulo ion
            Publish('sys_config', 'ion');
            
            // leitura da solicitação 
            Subscribe('sys_config');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                 // indica que a mensagem demorou muito tempo para responder
                 setTimeoutMessage(true);               
            }, timeoutWait);
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }      
        
    // seta o status do módulo ion [ativado/desativado]
    async function setStatusIon() {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`${(ionStatus) ? 'Desativando' : 'Ativando'} o módulo ION...`);   

            if(ionStatus) {
                // envio de solicitação de desativação do módulo ion
                Publish('ion', 'module_end');
            }
            else {
                // envio de solicitação de ativação do módulo ion
                Publish('ion', 'module_start');
            }
    
            // leitura da solicitação 
            Subscribe('ion');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

           // pega o id do timeout atual
           setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    // exclui a configuração ion na probe
    function ExcluirIonProbe(probe_: ProbeSettings, ion_atual: number, is_saving: boolean)
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // se possui ion configurado
            if((probe_.ionDevices?.length ?? 0) > 0) {

                // inicia loading
                setLoading(true);
                setTextoLoading((is_saving) ? 'Inicializando configurações ION...' : `Excluindo configurações do ION ${probe_.ionDevices?.[ion_atual].ion}...`);   
                
                // envio de solicitação de reset da configuração ion
                Publish('ion_end', JSON.stringify({ slave_id: probe_.ionDevices?.[ion_atual].id_ion ?? 0, 
                                                        port: probe_.ionDevices?.[ion_atual].port ?? 0}));
 
                // leitura da solicitação 
                Subscribe('ion_end');

                // loading vai durar 60 segundos
                const timeout_id = setTimeout(function() {                
                    // indica que a mensagem demorou muito tempo para responder
                    setTimeoutMessage(true);               
                }, timeoutWait);

                // pega o id do timeout atual
                setTimeoutIDMessage(Number(timeout_id));                
            }
            else {
                setTextoMessage('Não existe nenhum ION configurado.');
                setShowMessageErro(true);
            }            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }    

    // exclui um determinado ion
    function ExcluirIon(probe_: ProbeSettings, ion_atual: number)
    {
        // retorna o tipo de ion a lista
        adicionarTipoIon(probe_.ionDevices?.[ion_atual]?.ion ?? 'RS485');

        // exclui o codi selecionado
        probe_.ionDevices?.splice(ion_atual, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},             
                                 ionDevices: [...probe_.ionDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbe({...probeAtualizada});
    }    

    // exclui um determinado ion
    function ExcluirIonCopy(probe_: ProbeSettings, ion_atual: number)
    {
        // exclui o codi selecionado
        probe_.ionDevices?.splice(ion_atual, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},
                                 ionDevices: [...probe_.ionDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbeCopy(deepCopy(probeAtualizada));
    }

    // salva as configurações ion na Probe
    function SalvarIonProbe(probe_: ProbeSettings, indice_: number)
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`Salvando configuração do ION ${probe_.ionDevices?.[indice_]?.ion ?? ''}...`);                   
            
            // envio de solicitação para adicionar a configuração ion
            Publish('ion_add', JSON.stringify({ slave_id: probe_.ionDevices?.[indice_]?.id_ion ?? 0,
                                                port: probe_.ionDevices?.[indice_]?.port ?? 1, 
                                                baud: Number(ListaBaudRateProbe.find(x => x.id === probe_.ionDevices?.[indice_]?.baudRate)?.descricao ?? '9600'),
                                                uart: ListaParidadeProbe.find(x => x.id === probe_.ionDevices?.[indice_]?.paridade)?.descricao ?? '8N1'}));
 
            // leitura da solicitação 
            Subscribe('ion_add');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    // edita um determinado ion configurado
    function EditarIon (ion_: ionSettings)
    {                
        setTipoIon(ion_.ion ?? '');

        setID(ion_.id_ion?.toString() ?? '');
        setPort(ion_.port ?? 0);
        setParidade(ion_.paridade ?? 0);
        setBaudRate(ion_.baudRate ?? 0);
        
        // abre a tela de configuração do ion
        refRBSheetIon.current?.open();        
    }

    // fazer a busca de ions configurados na rede
    function BuscarIon(port: number, modoBusca: number, is_searching: boolean) {

        // se foi selecionado o port
        if(port < 0)  {
            setTextoMessage('O tipo de comunicação não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi selecionado o modo de busca
        if(modoBusca < 0)  {
            setTextoMessage('O modo de busca não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia busca
            setSearching(true);
                
            // envio de solicitação de busca de ion's na rede
            if (is_searching)
                Publish('ion_auto_search', JSON.stringify({port: port, cancel: true}));
            else
                Publish('ion_auto_search', JSON.stringify({port: port, basic: true, extended: modoBusca, cancel: false}));
 
            // leitura da solicitação 
            Subscribe('ion_auto_search');
        }        
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    /**
     * solicita a coleta de dados para validação
     * @param port 
     * @param modoBusca 
     * @param is_searching 
     * @returns 
     */
    function GetColetaDados(probe_settings: ProbeSettings, indice: number) {

        // se conectado com o servidor mqtt
        if(!isMQTTConnected()) {

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);

            return;
        }
            
        /* se esta coletando dados */
        if(isCollect){

            /* remove a inscrição do topico para leitura dos dados solicitação */
            Unsubscribe('ion_data', JSON.stringify({ id: probe_settings.ionDevices?.[indice]?.id_ion ?? 0, 
                                                     port: probe_settings.ionDevices?.[indice]?.port ?? 0}));

            setIsCollect(false);
            setDataIon([]);
        }
        else {

            /* publica a solicitação no mqtt */
            Publish('ion_data');
 
            /* inscreve o topico para leitura dos dados solicitação */
            Subscribe('ion_data', JSON.stringify({ id: probe_settings.ionDevices?.[indice]?.id_ion ?? 0, 
                                                  port: probe_settings.ionDevices?.[indice]?.port ?? 0}));

            setIsCollect(true);
        }
    }    

    useEffect(() => {
        
        // se deve iniciar leitura e ion habilitado
        if( (iniciaLeitura) && (ionStatus) ) {

            // texto do loading
            setTextoLoading('Configurações ION...');

            // inicia loading
            setLoading(true);

            // loading vai durar 4 segundos
            setTimeout(function() {
                getConfiguracaoIon();
                setIniciaLeitura(false);
            }, 2000);
        }
        
    }, [ iniciaLeitura, ionStatus]);

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {
        
        // se timeout não alarmado
        if(!timeoutMessage)
            return
        
        // verifica se o loading esta carregando
        if(!loading)
            return;
        
        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);
    
        // timeout não alarmado
        setTimeoutMessage(false);
    
    }, [timeoutMessage]);    

    // executa sempre que a variavel 'ionStatusMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionStatusMQTT', mqtt => {                                    
        
            // timeout message não alarmado
            
            clearTimeout(timeoutIDMessage);

            if(mqtt) {
            
                // altera o status do ion                
                setIonStatus(prevIon => !prevIon);

                // pega os dados da probe atualizada
                const probeAtualizada = {...{...probe},             
                                         ion: ionStatus,
                };                

                setProbe({...probeAtualizada});

                // finaliza o loading
                setLoading(false);            
            }
            else {
                setLoading(false);
                setTextoMessage(`Erro ao ${(ionStatus) ? 'desativar' : 'ativar'} o módulo ION...`);
                setShowMessageErro(true);
            }
    
        }, );
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);


    // executa sempre que a variavel 'ionConfigMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionConfigMQTT', mqtt => {                                    
            
            // timeout message não alarmado            
            clearTimeout(timeoutIDMessage);

            // pega os dados da probe atualizada
            const probeAtualizada = {...{...probe},
                                     ionDevices : mqtt ,
            };                

            // atualiza estrutura
            setProbe({...probeAtualizada});

            // verifica se existe algum ion configurado
            if(mqtt.length > 0) {

                // percorre os codi's configurados
                for (const item of mqtt) {
                    // remove o codi da lista
                    removerTipoIon(item.codi);
                }
            }

            // finaliza o loading
            setLoading(false);            
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);
    
    // executa sempre que a variavel 'IonEndMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionEndMQTT', mqtt => {                                    

            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);

            /* se o ion não foi excluido */
            if(!mqtt) {       
                
                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao apagar configuração ${probe.codiDevices?.[0] ?? ''}`);
                setShowMessageErro(true);

                return;
            }            

            /* qual será o ion atual */
            const ion_atual = (excluirTodosIon) ? 0 : ionAtual;
        
            if(saving) {

                // exclui ion da configuração auxiliar
                ExcluirIonCopy({...probeCopy}, ion_atual);
            }
            else {

                // exclui ion da configuração               
                ExcluirIon({...probe}, ion_atual);
            }

            // pega o total de ion configurados
            let totalIon = (saving) ? (probeCopy.ionDevices?.length ?? 0) : (probe.ionDevices?.length ?? 0);

            // verifica se ainda existem configurações ion
            if( totalIon > 0 && excluirTodosIon === true) {

                ExcluirIonProbe((saving) ? {...probeCopy} : {...probe}, 0, saving);
            }
            else if(saving) {

                // salva a configuração da probe
                SalvarIonProbe({...probe}, 0)
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage( (excluirTodosIon) ? `Todas as configurações ION foram excluidas da Probe com sucesso.` : `Configuração ION excluida com sucesso.`);
                setShowMessageSucesso(true);                    
            }
                    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [saving, ionAtual, excluirTodosIon, timeoutMessage]); 
      

    // executa sempre que a variavel 'addIonMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionAddMQTT', mqtt => {                                    
             
            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);

            if(mqtt) {

                try {

                    // se existe mais uma configuração ion a ser salva
                    if( (probe.ionDevices?.length ?? 0) > (saveIonAtual + 1)) {

                        // salva a configuração
                        SalvarIonProbe({...probe}, saveIonAtual + 1);

                        // incrementa o ion atual
                        setSaveIonAtual(saveIonAtual + 1);
                    }
                    else {

                        // finaliza o loading
                        setLoading(false);
    
                        setTextoMessage(`Todas as configurações ION foram salvas com sucesso.`);
                        setShowMessageSucesso(true);                    
                    }

                }
                catch(error) {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage('Erro ao salvar a configuração ION');
                    setShowMessageErro(true);

                    return;
                }
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao salvar configuração ION`);
                setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage, probe, saveIonAtual, timeoutMessage]);    

   // executa sempre que a variavel 'ionAutoSearchMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionAutoSearchMQTT', mqtt => {                                    
             
            if(mqtt) {

                try {

                    // finaliza a busca
                    setSearching(false);

                    // monta mensagem
                    setTextoMessage('ION encontrado.');
                    setShowMessageSucesso(true);
                }
                catch(error) {

                    // finaliza a busca
                    setSearching(false);

                    // monta mensagem
                    setTextoMessage('Erro ao procurar ION.');
                    setShowMessageErro(true);
                }
            }
            else {

                // finaliza a busca
                setSearching(false);

                setTextoMessage('Procura pelo ION cancelada.');
                setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []); 
    
    // executa sempre que a variavel 'ionAutoSearchingMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionAutoSearchingMQTT', mqtt => {
            
            // texto do loading
            setTextoLoadingBusca(`${mqtt.replace(",", "\n")}`); 

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [textoLoadingBusca]);
        
   /**
    * executa sempre que a variavel 'ionDataMQTT' for ouvida
    */
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('ionDataMQTT', mqtt => {                                    
         
            /* recebe os dados do ion para apresentação */
            setDataIon([...mqtt]); 
    
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);



    // monitora se modo de busca
    useEffect(() => {
        
        // se estendido 
        if(modoBusca === 1) {
            setTextoMessage('O modo de busca Completo pode demorar muito tempo para ser finalizado.');
            setShowMessageAtencao(true);
        }
                    
    }, [modoBusca]);

    // monitora os tipos de Codi disponiveis
    useEffect(() => {            
            
        // verifica se deve mostrar ou esconder o botão de adicionar
        if(tiposIon.length > 0) {
            setShowAdicionar(true);
        }
        else {
            setShowAdicionar(false);
        }
    
    }, [tiposIon]);

    return(

        <>
            <Loading animating={loading} text={textoLoading} />        

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}
            />

            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}
            />

            <MessageBoxAtencao
                visivel={showMessageAtencao} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageAtencao(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaIonStatus} 
                titulo="Atenção" 
                descricao={`${(ionStatus) ? 'Desativar' : 'Ativar'} o módulo ION`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaIonStatus(false);
                    setStatusIon();
                }}
                onCancel={() => setShowMessagePerguntaIonStatus(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaDel} 
                titulo="Atenção" 
                descricao={`Deseja excluir o ION com comunicação ${probe.ionDevices?.[ionAtual]?.ion ?? ''} configurado?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaDel(false);                    
                    setExcluirTodosIon(false);
                    setSaving(false);
                    ExcluirIonProbe({...probe}, ionAtual, false);
                }}
                onCancel={() =>setShowMessagePerguntaDel(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaEdit} 
                titulo="Atenção" 
                descricao={`Deseja editar o ION com comunicação ${probe.ionDevices?.[ionAtual]?.ion ?? ''}?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ async () => { 
                    setShowMessagePerguntaEdit(false);
                    setAdicionar(false);
                    EditarIon({...probe.ionDevices?.[ionAtual]});
                }}
                onCancel={() => setShowMessagePerguntaEdit(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaCleanIon} 
                titulo="Atenção" 
                descricao={`Deseja realmente excluir as configurações ION da Probe?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => {
                    setShowMessagePerguntaCleanIon(false);
                    setExcluirTodosIon(true);
                    setSaving(false);
                    ExcluirIonProbe({...probe}, 0, false);
                }}
                onCancel={() => setShowMessagePerguntaCleanIon(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSave} 
                titulo="Atenção" 
                descricao={(adicionar) ? `Adicionar o ION com comunicação ${tipoIon} as configurações da Probe?` : `Salvar as alterações feitas no ION com comunicação ${tipoIon}?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaSave(false);
                    (adicionar) 
                        ? AdicionarIon({...probe}, id, tipoIon, port, paridade, baudRate) 
                        : SalvarIon({...probe}, ionAtual, id, tipoIon, port, paridade, baudRate);
                }}
                onCancel={() => setShowMessagePerguntaSave(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSaveIon} 
                titulo="Atenção" 
                descricao={`Deseja realmente salvar a configuração ION?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => {                    
                    setShowMessagePerguntaSaveIon(false);
                    setExcluirTodosIon(true);
                    setSaveIonAtual(0);
                    setSaving(true);
                    setProbeCopy(deepCopy(probe));
                    ExcluirIonProbe({...probe}, 0, true);                    
                }}
                onCancel={() => setShowMessagePerguntaSaveIon(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaBusca} 
                titulo="Atenção" 
                descricao={(searching) ? `Deseja realmente cancelar a busca de ION na rede?` : `Deseja realmente iniciar a busca de ION configurado na rede?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => {
                    setShowMessagePerguntaBusca(false);                    
                    BuscarIon(port, modoBusca, searching);                    
                }}
                onCancel={() => setShowMessagePerguntaBusca(false)}
            />
            
            <MessageBoxPergunta
                visivel={showMessagePerguntaData} 
                titulo="Atenção" 
                descricao={(isCollect) ? `Deseja realmente cancelar a coleta de dados?` : `Deseja realmente iniciar a coleta de dados?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => {
                    setShowMessagePerguntaData(false);                    
                    GetColetaDados({...probe}, ionAtual);                    
                }}
                onCancel={() => setShowMessagePerguntaData(false)}
            />

            <View style={styles.containerTela}>

                {/* header */}
                <View style={{...styles.containerHeader}}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID='button-back'>
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeader}>ION</Text>
                    </View>

                </View>

                {/* botões */}
                <View style={styles.containerBotoes}>

                    <TouchableOpacity style={{ height:50, width:70, 
                                               justifyContent:'center', alignItems:'center',
                                               borderRadius:8, borderColor: (ionStatus) ? '#2E8C1F' : '#C0002B',
                                               backgroundColor: (ionStatus) ? '#2E8C1F' : '#C0002B'
                                            }}
                                        onPress={ () => setShowMessagePerguntaIonStatus(true) }
                                        testID='button-status'>                                                
                        <Text style={{color:'#FFFFFF'}}>{(ionStatus) ? `ON` : 'OFF'}</Text>                        
                    </TouchableOpacity>

                    <View style={{height:'100%', width:'60%', justifyContent:'space-between', alignItems:'center', flexDirection:'row', gap: 20}}>
                        <TouchableOpacity onPress={ () => {
                                                            // se ion habilitado
                                                            if(ionStatus) {
                                                                setShowMessagePerguntaCleanIon(true)
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo ION está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                          testID='button-clean'>
                            <IconBroom color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={ () => {
                                                            // se ion habilitado
                                                            if(ionStatus) {
                                                                setShowMessagePerguntaSaveIon(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo ION está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                            testID="button-save">
                            <IconSave color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={ () => {
                                                            // se ion habilitado
                                                            if(ionStatus) {
                                                                setPort(-1);
                                                                setModoBusca(-1);                                                                
                                                                refRBSheetSearchIon.current?.open();
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo ION está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                        testID="button-search">
                            <IconSearch color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={() => { 
                                                            // se ion habilitado
                                                            if(ionStatus) {
                                                                setIniciaLeitura(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo ION está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                          testID='button-refresh'>
                            <IconRefresh color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                </View> 

                {/* texto cabeçalho */}
                <View style={styles.containerAddDevices}>
                    <Text style={styles.textoDevices}>Devices</Text>
                    {      
                        (showAdicionar) &&
                            <TouchableOpacity onPress={() => { 
                                                                // se ion habilitado
                                                                if(ionStatus) {
                                                                    setAdicionar(true);
                                                                    refRBSheetTipoIon.current?.open();
                                                                }
                                                                else {
                                                                    setTextoMessage('Módulo ION está desativado.')
                                                                    setShowMessageErro(true);
                                                                }
                                                            }}
                                            testID="button-plus">
                                <IconPlus color={"#FFFFFF"} width={34} height={34}/>
                            </TouchableOpacity>
                    }
                </View>

                {/* lista de ion */}
                <View style={styles.containerListaDevices}>
                    <FlatList style={{marginTop: 10}}
                            ItemSeparatorComponent={Space}
                            data = {probe.ionDevices ?? []}
                            renderItem= { ({ item }) => 
                                            <CardIon
                                                ion={item.ion} 
                                                id={item.id_ion}                                               
                                                width={'100%'}                                                
                                                onPressDelIon= { () => {
                                                        setIonAtual(probe.ionDevices?.findIndex(device => device.id_ion === item.id_ion) ?? 0);
                                                        setShowMessagePerguntaDel(true);
                                                }}
                                                onPressEditIon={ () => {
                                                        setIonAtual(probe.ionDevices?.findIndex(device => device.id_ion === item.id_ion) ?? 0);
                                                        setShowMessagePerguntaEdit(true);
                                                }}
                                                onPressDataIon={ () => {
                                                        setIonAtual(probe.ionDevices?.findIndex(device => device.id_ion === item.id_ion) ?? 0);
                                                        refRBSheetDataIon.current?.open();
                                            }}                                                
                                            />
                                        }
                    />
                </View>


            </View>

            {/* seleção do tipo de dispositivo */}
            <RBSheet ref={refRBSheetTipoIon} height={screenHeight}>
                                
                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#737373'}}>                                                            
                    <TouchableOpacity onPress={() => refRBSheetTipoIon.current?.close()} testID="button-close-tipo-ion">
                        <IconX color={"#FFFFFF"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                {
                    // se deve selecionar a grandeza                    
                    <View style={{height:'100%', width:'100%',paddingHorizontal: 20, paddingBottom:80, justifyContent:'flex-start', alignItems:'flex-start', backgroundColor:'#737373', gap: 10}}>                    
                        <Text style={styles.textoSelecao}>Selecione comunicação ION</Text>

                        <FlatList style={{width:'100%', marginTop: 20}}
                                  ItemSeparatorComponent={Divider}              
                                  data = {tiposIon}
                                  renderItem= { ({ item }) =>
                                    <TouchableOpacity style={{  height:60, justifyContent:'center', alignItems:'center'}}
                                                                onPress={ async () => { 
                                                                                        setTipoIon(item);
                                                                                        setID('');
                                                                                        setPort( Number(ListaComunicacaoSerial.find(x => x.descricao === item)?.id));
                                                                                        setParidade(-1);
                                                                                        setBaudRate(-1);                                                                                
                                                                                        refRBSheetTipoIon.current?.close();
                                                                                        refRBSheetIon.current?.open();
                                                                                      }} 
                                                                testID="button-tipo-ion">
                                                       <Text style={styles.textoItensSelecao}>{item}</Text>
                                    </TouchableOpacity>
                                }
                        />
                        
                    </View>
                }

            </RBSheet>            

            {/* configuração ion da probe */}
            <RBSheet ref={refRBSheetIon} height={screenHeight}>

                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                    <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>                        
                        <Text style={styles.textoConfiguracao}>{tipoIon}</Text>
                    </View>

                    <TouchableOpacity onPress={() => {refRBSheetIon.current?.close(); }} testID="button-ion-close">
                        <IconX color={"#737373"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                <View style={{height: screenHeight - 60 - ( screenHeight * 0.53), width:'100%', marginTop:10, paddingHorizontal: 20, gap: 20}}>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>
                        <TextEntry 
                            value={id}
                            height={'100%'}
                            width={'100%'}
                            editable={true}
                            placeHolder='Entre com o ID'
                            onValueChange={setID}
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'ID'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-id"                           
                        /> 
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>
                        <ComboBox
                            disable={true}
                            height={'100%'} 
                            width={'100%'}
                            value={port}
                            onValueChange={setPort}
                            data={ListaComunicacaoSerial}
                            textoLabel={'Comunicação'}
                            fontSize={16}
                            textoPlaceHolder={'Selecione a Comunicação'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                        />
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between'}}>                    
                        <ComboBox
                            disable={false}
                            height={'100%'} 
                            width={'100%'}
                            value={baudRate}
                            onValueChange={setBaudRate}
                            data={ListaBaudRateProbe}
                            textoLabel={'Baud Rate'}
                            fontSize={16}
                            textoPlaceHolder={'Selecione o baud rate'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-baudrate"
                        />
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between'}}>
                        <ComboBox
                            disable={false}
                            height={'100%'} 
                            width={'100%'}
                            value={paridade}
                            onValueChange={setParidade}
                            data={ListaParidadeProbe}
                            textoLabel={'Paridade'}
                            fontSize={16}
                            textoPlaceHolder={'Selecione a Paridade'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-paridade"
                        />
                    </View>   

                    <View style={{marginTop:30}}>
                        <TouchableOpacity style={styles.button} onPress={() => { setShowMessagePerguntaSave(true); }} testID="button-ion-save">
                            <Text style = {styles.textoButton}>{(adicionar) ? 'ADICIONAR' : 'SALVAR'}</Text>
                        </TouchableOpacity>
                    </View>

                </View>

            </RBSheet> 

            {/* busca ion na rede */}
            <RBSheet ref={refRBSheetSearchIon} height={screenHeight}>

                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                    <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>                        
                        <Text style={styles.textoConfiguracao}>Buscar por ION na rede</Text>
                    </View>

                    <TouchableOpacity onPress={() => {refRBSheetSearchIon.current?.close(); }} testID="button-close-search">
                        <IconX color={"#737373"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                <View style={{height: screenHeight - 60 - ( screenHeight * 0.53), width:'100%', marginTop:10, paddingHorizontal: 20, gap: 20}}>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>
                        <ComboBox
                            disable={searching}
                            height={'100%'} 
                            width={'100%'}
                            value={port}
                            onValueChange={setPort}
                            data={ListaComunicacaoSerial}
                            textoLabel={'Comunicação'}
                            fontSize={16}
                            textoPlaceHolder={'Selecione a Comunicação'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-port"
                        />
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between'}}>                    
                        <ComboBox
                            disable={searching}
                            height={'100%'} 
                            width={'100%'}
                            value={modoBusca}
                            onValueChange={setModoBusca}
                            data={ListaModoBuscaION}
                            textoLabel={'Tipo de busca'}
                            fontSize={16}
                            textoPlaceHolder={'Selecione o tipo de busca'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-tipo"
                        />
                    </View>

                    <View style={{marginTop:30}}>
                        <TouchableOpacity style={styles.button} onPress={() => { setShowMessagePerguntaBusca(true); }} testID="button-find">
                            <Text style = {styles.textoButton}>{(searching) ? 'CANCELAR BUSCAR' : 'BUSCAR ID'}</Text>
                        </TouchableOpacity>
                    </View>

                    {
                        (searching) &&
                            <View style={{marginTop:80, height:120, justifyContent:'space-between', flexDirection:'column'}}>
                                <ActivityIndicator size={'large'} color={'#45D42E'} animating={searching} style={{ transform: [{ scaleX: 2 }, { scaleY: 2 }] }} />
                                <Text style={styles.textoLoadingBusca}>{"Procurando por ION's configurados na rede."}</Text>                                
                            </View> 
                    }

                    {
                        (searching) &&
                            <View style={{marginTop:80, height:120, justifyContent:'space-between', flexDirection:'column'}}>
                                <Text style={styles.textoLoadingBuscaInfo}>{textoLoadingBusca}</Text>                                
                            </View> 
                    }                               
                </View>

            </RBSheet>

            {/* coleta de dados */}
            <RBSheet ref={refRBSheetDataIon} height={screenHeight} >

                <View style={{height:screenHeight, paddingHorizontal: 20, gap: 20}}>

                    <View style={styles.containerRBSheetDataHeader}>

                        <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>                        
                            <Text style={styles.textoConfiguracao}>Coleta de Dados</Text>
                        </View>

                        <TouchableOpacity onPress={() => { 
                                if(isCollect) {
                                    setShowMessageAtencao(true);
                                    setTextoMessage('É necessário encerrar a coleta de dados do ION.')
                                }
                                else {
                                    refRBSheetDataIon.current?.close();
                                }
                            }} 
                            testID="button-close-search">
                            <IconX color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.containerRBSheetDataInfo}>
                        <TextEntry                         
                            value={probe.ionDevices?.[ionAtual]?.ion ?? 'Desconhecido'}
                            height={'100%'}
                            width={'45%'}
                            editable={false}
                            placeHolder='Comunicação'
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'Comunicação'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-data-comunicacao"
                        />

                        <TextEntry                         
                            value={String(probe.ionDevices?.[ionAtual]?.id_ion)}
                            height={'100%'}
                            width={'45%'}
                            editable={false}
                            placeHolder='ID'                            
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'ID'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-data-comunicacao"
                        />                     
                    </View>

                    <View style={styles.containerRBSheetDataInfo}>
                    
                        <TouchableOpacity style={styles.button} onPress={() => { setShowMessagePerguntaData(true); }} testID="button-ion-data">
                            <Text style = {styles.textoButton}>{(isCollect) ? `PARAR COLETA` : `INICIAR COLETA`}</Text>
                        </TouchableOpacity>

                    </View>

                    <View style={styles.containerRBSheetData}>
                    
                        {
                            (isCollect) &&
                                /* area de conteudo dos dados do ion*/
                                <DataIon payload={dataIon} />
                        }

                    </View>

                </View>
            </RBSheet>            
                       

        </>

    )
}

export default ProbeION;