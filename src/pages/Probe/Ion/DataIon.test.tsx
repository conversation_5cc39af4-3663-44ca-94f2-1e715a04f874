import { NavigationContainer } from "@react-navigation/native";
import { render, screen } from "@testing-library/react-native";
import React from "react";
import DataIon from "./DataIon";

describe('Data ION', () => {
    it('renderiza corretamente quando payload vazio', () => {

        const { } = render( <NavigationContainer>
                                <DataIon payload={[]} />
                            </NavigationContainer>);

        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente quando payload esta preenchido', () => {

        const response = ['{\"time\":1745529300,\"ion_count_packets\":899,\"active\":10.16,\"reactive\":-6.76}'];

        const { } = render( <NavigationContainer>
                                <DataIon payload={response}/>
                            </NavigationContainer>);

        expect(screen).toBeTruthy();
    });

})