import React from "react";
import { ActivityIndicator, FlatList, Text, View } from "react-native";

// estilos para a page
import { DataTimeStamp, HoraTimeStamp } from "../../../funcoes";
import { styles } from './layout';

// Definindo as propriedades do componente Header
type DataIonProps = {
    payload: string[],
};

const DataIon: React.FC<DataIonProps> = ({payload}) => {
    
    const data: Item[] = payload.length ? payload.map(item => JSON.parse(item)) : [];

    // Definir a interface para os itens da FlatList
    interface Item {
        [key: string]: string; // Defina os tipos possíveis para os valores
    }

    const renderItem = ({ item }: { item: Item }) => (

        <View style={styles.containerTopic}>
            {
                Object.entries(item).map(([key, value]) => (
                    <Text key={key} style={styles.textoTopic}>
                        {key}: {(key === 'time') ? `${DataTimeStamp(Number(value))} ${HoraTimeStamp(Number(value), true)}` : value}
                    </Text>
                ))
            }
        </View>
    );

    return (

        <View style={styles.containerContent}>

            <FlatList <Item>
                data={data}
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
                ListHeaderComponent={
                    <View style={{height:50, flexDirection:'row', justifyContent:'flex-start', alignItems:'center', gap: 15}}>
                        <Text style={styles.textoTitleList}>{`Recebendo dados do ION`}</Text>
                        <ActivityIndicator size={'small'} color={'#45D42E'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID="activity-indicator-ion" />
                    </View>
                }
            />

        </View>
    );
};

export default DataIon;
