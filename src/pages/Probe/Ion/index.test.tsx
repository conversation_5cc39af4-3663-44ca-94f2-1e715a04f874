import { Navigation<PERSON>ontainer } from "@react-navigation/native";
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import React from "react";
import { EventRegister } from "react-native-event-listeners";
import ProbeION from ".";
import { MockIonSettings, MockIonSettingsBaudRateInvalido, MockIonSettingsParidadeInvalido } from "../../../../__mocks__/IonSettingsMock";
import { MockProbeSettings } from "../../../../__mocks__/ProbeSettingsMock";
import * as spyMQTT from "../../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn

jest.mock('sp-react-native-mqtt', () => ({
    connect: jest.fn(() => ({
        subscribe: jest.fn(),
        on: jest.fn(),
    })),
}));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.useFakeTimers();

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                    }
    },
};

const mockRouteStatusON1 = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       ion : true,
                       ionDevices: MockIonSettings,
                    }
    },
};

const mockRouteStatusON2 = {
    params: {
        probeSettings: {  ...MockProbeSettings,
                            ion : true,
                            ionDevices: [
                                {
                                    id_ion: 0,
                                    ion: 'RS232',
                                    port:0,
                                    paridade: 0,
                                    baudRate: 5,
                                },
                                {
                                    id_ion: 1,
                                    ion: 'RS485',
                                    port:1,
                                    paridade: 0,
                                    baudRate: 5,
                                }                        
                            ],                
                        }
    },
};

const mockRouteStatusON3 = {
    params: {
        probeSettings: {  ...MockProbeSettings,
                            ion : true,
                            ionDevices: [
                                {
                                    id_ion: 10,
                                    ion: 'RS232',
                                    port:0,
                                    paridade: 0,
                                    baudRate: -1,
                                },
                            ],                
                        }
    },
};

const mockRouteStatusON4 = {
    params: {
        probeSettings: {  ...MockProbeSettings,
                            ion : true,
                            ionDevices: [
                                {
                                    id_ion: 10,
                                    ion: 'RS232',
                                    port:0,
                                    paridade: -1,
                                    baudRate: 5,
                                },
                            ],                
                        }
    },
};

describe('ProbeION', () => {

    jest.clearAllMocks();

    it('renderiza corretamente com a rota fornecida e modulo off', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeION route={mockRoute} />
                            </NavigationContainer> );
        
        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });

        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente com a rota fornecida e modulo on', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeION route={mockRouteStatusON1} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {unmount, getByTestId, queryByTestId} = render(   <NavigationContainer>
                                                                    <ProbeION route={mockRoute}/>
                                                                </NavigationContainer> );
        
        // Verifica se o botão existe no DOM e está visível
        const buttonBack = queryByTestId('button-back');
        expect(buttonBack).toBeTruthy(); // O botão deve estar presente

        act(() => {
            fireEvent.press(getByTestId('button-back'));
        });

        unmount();
    });    

    it('renderiza corretamente ao clicar no botão de ativar modulo ion e cancelar', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para desativar o modulo ion e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {      
            fireEvent.press(getByTestId('button-status'));                
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });
    });
    
    it('renderiza corretamente ao clicar no botão para ativar o modulo ion e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });

    it('renderiza corretamente ao clicar no botão para ativar o modulo ion e mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRoute} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-status'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    });

    /*
    it('renderiza corretamente ao clicar no botão para limpar configuração ion e modulo ON e clica no botão Não', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    }); 
    */
   
    it('renderiza corretamente ao clicar no botão para limpar configuração ion e modulo ON e clica no botão Sim, mas mqtt desconectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });
    
   it('renderiza corretamente ao clicar no botão para limpar configuração ion e modulo ON e clica no botão Sim e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
    });
    
    it('renderiza corretamente ao clicar no botão para limpar configuração ion e modulo ON e clica no botão Sim, mas móduo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeION route={mockRoute} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-clean'));                
            });
        });
    });       

    it('renderiza corretamente ao clicar no botão para ler a configuração ion e modulo OFF', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para ler a configuração ion e modulo ON e MQTT desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });    

    it('renderiza corretamente ao clicar no botão para ler a configuração ion e modulo ON e MQTT conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));
            });
        });

        // Avance o tempo em 4 segundos
        act(() => {
            jest.advanceTimersByTime(4000);
        });        
    });

    it('renderiza corretamente ao clicar no botão para adicionar um ion e módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado e fecha escolha', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeION route={mockRouteStatusON1} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-close-tipo-ion'));
            });
        });        
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona e cancela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Não'));
            });
        });                        
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona, confirma mas não existe id', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona, confirma mas id invalido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={ mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, 'teste');        
        });        

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });

    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona, confirma mas baud rate invalido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={ mockRouteStatusON3} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, '10');        
        });         

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona, confirma mas paridade invalido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={ mockRouteStatusON4} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, '10');        
        });
                
        act(() => {
            fireEvent(getByTestId('combo-baudrate'), 'change', { nativeEvent: { value: { id: 0, descricao: '110' }}})
        });  

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });
    
    it('renderiza corretamente ao clicar no botão para adicionar um ion, módulo ativado, adiciona e confirma', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={ mockRouteStatusON4} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('RS485'));
            });
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, '10');        
        });
                
        act(() => {
            fireEvent(getByTestId('combo-baudrate'), 'change', { nativeEvent: { value: { id: 0, descricao: '110' }}})
        });  

        act(() => {
            fireEvent(getByTestId('combo-paridade'), 'change', { nativeEvent: { value: { id: 0, descricao: '8N1' }}})
        });
                
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-ion-save'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });                        
    });
    
    it('renderiza corretamente ao clicar no botão para salvar mas módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save'));
            });
        });
    });  
    
    it('renderiza corretamente ao clicar no botão para search mas módulo desativado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRoute} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });
    });    

    it('renderiza corretamente ao clicar no botão para search mas módulo ativado, mas fecha busca', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeION route={mockRouteStatusON1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-close-search'));
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para search mas módulo ativado, mas cancela search', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-find'));
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Não'));
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão para search com módulo ativado, e permiti a busca, confirma mas port invalido ', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-find'));
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });        
    });    

    it('renderiza corretamente ao clicar no botão para search com módulo ativado, e permiti a busca, confirma mas modo de busca invalido ', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });

        const dropdown = getByTestId('combo-port');
        act(() => {
            fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 0, descricao: 'RS485' }}})
        });     
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-find'));
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });        
    });
    
   it('renderiza corretamente ao clicar no botão para search com módulo ativado, e permiti a busca, confirma', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });
        
        act(() => {
            fireEvent(getByTestId('combo-port'), 'change', { nativeEvent: { value: { id: 0, descricao: 'RS485' }}})
        });     

        act(() => {
            fireEvent(getByTestId('combo-tipo'), 'change', { nativeEvent: { value: { id: 0, descricao: 'Básico' }}})
        });                     
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-find'));
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });        
    }); 
    
    it('renderiza corretamente ao clicar no botão para search com módulo ativado, e permiti a busca, confirma mas mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
        
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeION route={mockRouteStatusON1} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-search'));
            });
        });
        
        act(() => {
            fireEvent(getByTestId('combo-port'), 'change', { nativeEvent: { value: { id: 0, descricao: 'RS485' }}})
        });     

        act(() => {
            fireEvent(getByTestId('combo-tipo'), 'change', { nativeEvent: { value: { id: 0, descricao: 'Básico' }}})
        });                     
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-find'));
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByText('Sim'));
            });
        });        
    });
    
    it('deve executar o fluxo esperado ao ler o status do modulo do ion = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ionStatusMQTT') {
            callback((true)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                  </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });     

    it('deve executar o fluxo esperado ao ler o status do modulo do ion = false', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ionStatusMQTT') {
            callback((false)); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByText} = render(<NavigationContainer>
                                                <ProbeION route={mockRouteStatusON1} />
                                            </NavigationContainer> );
    
       await act(async () => {
            fireEvent.press(getByText('OK'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, mas veio vazio', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback(([])); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração do ion', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a exclusão do ion configurado e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                                <ProbeION route={mockRouteStatusON1} />
                                                           </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('delete-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a exclusão do ion configurado, confirma mas mqtt desconectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(  <NavigationContainer>
                                                                <ProbeION route={mockRouteStatusON1} />
                                                            </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('delete-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Chama o cleanup do listener
        unmount();

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a exclusão do ion configurado, confirma mas mqtt conectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(  <NavigationContainer>
                                                                <ProbeION route={mockRouteStatusON1} />
                                                            </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('delete-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a edição do ion configurado e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                                <ProbeION route={mockRouteStatusON1} />
                                                          </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a edição do ion configurado e fecha a tela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });
        
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-close'));
        });
        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });  

    it('deve executar o fluxo esperado ao ler a configuração do ion e solicitar a edição do ion configurado e confirma', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });
        
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });  
    
    it('deve executar o fluxo esperado ao ler ao excluir um ion configurado = false', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionEndMQTT') {
                callback((false)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionEndMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler ao excluir um ion configurado = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionEndMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionEndMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler ao excluir um ion configurado = true, mas começando no processo de salvar uma configuração e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionEndMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {      
            fireEvent.press(getByTestId('button-save'));
        });

        await act(async () => {      
            fireEvent.press(getByText('Não'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionEndMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
      
    it('deve executar o fluxo esperado ao ler ao excluir um ion configurado = true, mas começando no processo de salvar 1 configuração', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionEndMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeION route={mockRouteStatusON2} />
                                                          </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('button-save'));            
        });

        await act(async () => {      
            fireEvent.press(getByText('Sim'));
            jest.advanceTimersByTime(100);
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionEndMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

   it('deve executar o fluxo esperado ao ler ao excluir um ion configurado = true, mas começando no processo de salvar 2 configurações', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionEndMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeION route={mockRouteStatusON2} />
                                                          </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('button-save'));            
        });

        await act(async () => {      
            fireEvent.press(getByText('Sim'));
            jest.advanceTimersByTime(100);
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionEndMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar um ion configurado e mqtt = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionAddMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(  <NavigationContainer>
                                        <ProbeION route={mockRouteStatusON2} />
                                    </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionAddMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado ao adicionar um ion configurado e mqtt = false', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionAddMQTT') {
                callback((false)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(  <NavigationContainer>
                                        <ProbeION route={mockRouteStatusON1} />
                                    </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionAddMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao procurar um ion e mqtt = true', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionAutoSearchMQTT') {
                callback((true)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(  <NavigationContainer>
                                        <ProbeION route={mockRouteStatusON1} />
                                    </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionAutoSearchMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao procurar um ion e mqtt = false', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionAutoSearchMQTT') {
                callback((false)); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(  <NavigationContainer>
                                        <ProbeION route={mockRouteStatusON1} />
                                    </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionAutoSearchMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao procurar um ion com auto search', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionAutoSearchingMQTT') {
                callback(('Procurando ION')); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount} = render(  <NavigationContainer>
                                        <ProbeION route={mockRouteStatusON1} />
                                    </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionAutoSearchingMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar a edição do ion configurado e salvar', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });
        
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar a edição do ion configurado, salvar mas id não inserido', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, '');        
        });
        
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar a edição do ion configurado, salvar mas id invalido', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettings())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });

        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, 'teste');        
        });
        
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar a edição do ion configurado, salvar mas baud rate invalido', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettingsBaudRateInvalido())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeION route={mockRouteStatusON1} />
                                                          </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });
        
        const input = getByTestId('input-id');

        await act(async () => {
            fireEvent.changeText(input, '10');        
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar a edição do ion configurado, salvar mas paridade invalido', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('edit-button'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });    

        await act(async () => {
            fireEvent.press(getByTestId('button-ion-save'));    
        });
        
        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar os dados do ion configurado e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('data-button'));
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-close-search'));
        });
        
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar os dados do ion configurado e solicita e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('data-button'));
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });

        await act(async () => {
            fireEvent.press(getByText('Não'));
        });        
        
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar os dados do ion configurado e solicita e confirma e tenta fechar', async () => {

       // Usa jest.spyOn para mockar a função isMQTTConnected
       jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'ionConfigMQTT') {
                callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                return 'mockEventListenerId';
            }
        });

        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;

        const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                    <ProbeION route={mockRouteStatusON1} />
                                </NavigationContainer> );

        await act(async () => {
            fireEvent.press(getByTestId('data-button'));
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
        });        

        await act(async () => {
            fireEvent.press(getByTestId('button-close-search'));
        });        
        
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));

        // Chama o cleanup do listener
        unmount();

        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar os dados do ion configurado, solicita, confirma, cancela a coleta', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
 
         // Elenco (cast) o método para jest.Mock
         const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        
 
         mockedAddEventListener.mockImplementation((eventName, callback) => {
             if (eventName === 'ionConfigMQTT') {
                 callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                 return 'mockEventListenerId';
             }
         });
 
         const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
 
         const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                     <ProbeION route={mockRouteStatusON1} />
                                 </NavigationContainer> );
 
        await act(async () => {
            fireEvent.press(getByTestId('data-button'));
        });
 
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });
 
        await act(async () => {
            fireEvent.press(getByText('Sim'));
         });        
 
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
         });        

         // Aqui você pode verificar as chamadas ou comportamentos esperados
         expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));
 
         // Chama o cleanup do listener
         unmount();
 
         expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
     });
     
    it('deve executar o fluxo esperado ao ler a configuração do ion, solicitar os dados do ion configurado, solicita, confirma, cancela a coleta mas mqtt desconectado', async () => {
 
         // Elenco (cast) o método para jest.Mock
         const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        
 
         mockedAddEventListener.mockImplementation((eventName, callback) => {
             if (eventName === 'ionConfigMQTT') {
                 callback((MockIonSettingsParidadeInvalido())); // Simula o callback de reconexão
                 return 'mockEventListenerId';
             }
         });
 
         const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
 
         const { unmount, getByTestId, getByText} = render(<NavigationContainer>
                                     <ProbeION route={mockRouteStatusON1} />
                                 </NavigationContainer> );
 
        await act(async () => {
            fireEvent.press(getByTestId('data-button'));
        });
 
        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });
 
        await act(async () => {
            fireEvent.press(getByText('Sim'));
         });        
 
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        await act(async () => {
            fireEvent.press(getByTestId('button-ion-data'));
        });

        await act(async () => {
            fireEvent.press(getByText('Sim'));
         });        

         // Aqui você pode verificar as chamadas ou comportamentos esperados
         expect(mockedAddEventListener).toHaveBeenCalledWith('ionConfigMQTT', expect.any(Function));
 
         // Chama o cleanup do listener
         unmount();
 
         expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
     });     
});