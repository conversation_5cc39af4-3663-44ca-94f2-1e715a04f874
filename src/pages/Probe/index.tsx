import Clipboard from "@react-native-clipboard/clipboard";
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, ImageBackground, ScrollView, Switch, Text, TouchableOpacity, View } from 'react-native';
import { EventRegister } from 'react-native-event-listeners';
import RBSheet from 'react-native-raw-bottom-sheet';
import Share from 'react-native-share';
import ViewShot, { captureRef } from 'react-native-view-shot';



// estilos da tela
import { screenHeight, screenWidth, statusBarHeight, styles } from './layout';

// imagens vetoriais
import IconBack from '../../assets/svg/icon_arrow-left.svg';
import IconCamera from '../../assets/svg/icon_camera-01.svg';
import IconClock from '../../assets/svg/icon_clock.svg';
import IconCopy from '../../assets/svg/icon_copy.svg';
import IconAnalyser from '../../assets/svg/icon_data-analytic.svg';
import IconQrCode from '../../assets/svg/icon_qr-code.svg';
import IconRefresh from '../../assets/svg/icon_refresh.svg';
import IconProbe from '../../assets/svg/icon_server.svg';
import IconOrder from '../../assets/svg/icon_switch-vertical.svg';
import IconX from '../../assets/svg/icon_x.svg';

import LogoZordon from '../../assets/svg/logo_zordon-vertical.svg';

// navegação das telas
import { CommonActions, useNavigation } from '@react-navigation/native';
import { goToKhomp } from './navigation';

// rotas drawer de navegação
import { StackTypes } from '../../routes/index';

// componentes
import Divider from '../../componentes/Divider';
import SettingsProbe from '../../componentes/SettingsProbe';
import StatusProbe from '../../componentes/StatusProbe';

// MQTT
import { connectMQTT, disconnectMQTT, publishMQTT, subscribeMQTT } from '../../services/mqtt';

// funcoes
import { DataTimeStamp, FormataTimer, HoraTimeStamp } from '../../funcoes';
import { getModeloKhomp } from './functions';

// telas modal
import MessageBoxAtencao from '../../modal/messagebox/Atencao';
import MessageBoxErro from '../../modal/messagebox/Erro';
import MessageBoxPergunta from '../../modal/messagebox/Pergunta';

// data
import { ProbeSettings } from '../../data';

// storage
import { getStorageString, KEY } from '../../storages';

const Probe = ({route}: any) => {
  
  // referencia para o bottom sheet
  const refRBSheetDebug = useRef<{ open: () => void, close: () => void }>(null);
  const refRBSheetMqtt =useRef<{ open: () => void, close: () => void }>(null);
  const refShot = useRef<ViewShot>(null);  
  
  const [debugDesc, setDebugDesc] = useState<string>('');
  const [debugAsc, setDebugAsc] = useState<string>('');
  const [orderDesc, setOrderDesc] = useState<boolean>(false);

  // botao conectado / desconectado
  const [showPergunta02, setShowPergunta02] = useState<boolean>(false);
  const [showPergunta03, setShowPergunta03] = useState<boolean>(false);
  const [showMessageAtencao, setShowMessageAtencao] = useState<boolean>(false);
  const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
  
  // indica se conectou
  const [conectado, setConectado] = useState<boolean>(false);
  const [conectando, setConectando] = useState<boolean>(false);

  const [liberadoMenu, setLiberadoMenu] = useState<boolean>(false);

  // timer
  const [timer, setTimer] = useState<number>(0);
  const [timerWait, setTimerWait] = useState<number>(60);
  const [isRunning, setIsRunning] = useState<boolean>(false);

  // dados da probe
  const [firmware, setFirmware] = useState<string>(route.params?.probeSettings.firmware);
  const [dateTime, setDateTime] = useState<number>(route.params?.probeSettings.datetime); // 01/01/2000 00:00
  const [sinal, setSinal] = useState<number>(route.params?.probeSettings.sinal);
  const [bateria, setBateria] = useState<number>(route.params?.probeSettings.bateria);
  const [vBateria, setVBateria] = useState<number>(route.params?.probeSettings.vBateria);
  const [tipoConexao, setTipoConexao] = useState<number>(route.params?.probeSettings.tipoConexao);
  const [tipoOperadora, setTipoOperadora] = useState<number>(route.params?.probeSettings.tipoOperadora);
  const [tensaoProbe, setTensaoProbe] = useState<number>(route.params?.probeSettings.v_probe);
  const [firmwareBoot, setFirmwareBoot] = useState<number>(route.params?.probeSettings.firmwareBoot);
  const [resets, setResets] = useState<number>(route.params?.probeSettings.resets);
  const [excecoes, setExcecoes] = useState<number>(route.params?.probeSettings.excecoes);
  const [heap, setHeap] = useState<number>(route.params?.probeSettings.heap);
  const [totalMemory, setTotalMemory] = useState<number>(route.params?.probeSettings.totalMemory);
  const [usedMemory, setUsedMemory] = useState<number>(route.params?.probeSettings.usedMemory);


  const [sistema, setSistema] = useState<boolean>(route.params?.probeSettings.sistema);
  const [rede, setRede] = useState<boolean>(route.params?.probeSettings.rede);
  const [modbus, setModbus] = useState<boolean>(route.params?.probeSettings.modbus);
  const [codi, setCodi] = useState<boolean>(route.params?.probeSettings.codi);
  const [ion, setION] = useState<boolean>(route.params?.probeSettings.ion);
  const [pulso, setPulso] = useState<boolean>(route.params?.probeSettings.pulso);
  const [ota, setOTA] = useState<boolean>(route.params?.probeSettings.ota);

  const [ip, setIP] = useState<string>(route.params?.probeSettings.ip);

  const [isMqttHmg, setIsMqttHmg] = useState<boolean>(false);
  

  const heightHeader = 300;
  const heightHeaderTitle = 60;
  const heightHeaderProbe = 80;
  const heightStatusQRCode = 100;
  const MarginTopStatusQRCode = 25;
  const heightSettingsProbe = screenHeight - (heightHeader + (statusBarHeight ?? 30) + (heightStatusQRCode - MarginTopStatusQRCode)) ;

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  /**
   * limpa todas as informações da probe
   */
  const LimpaInfo = () => {

    setLiberadoMenu(false);

    setFirmware('Firmware');
    setDateTime(946692000);

    setSinal(9999);
    setBateria(9999);
    setTipoConexao(9999);
    setTipoOperadora(9999); 
    setTensaoProbe(9999);
    setVBateria(0);
    setFirmwareBoot(946692000);
    setResets(0);
    setExcecoes(0);
    setHeap(0);
    setTotalMemory(0);
    setUsedMemory(0);

    setSistema(false);
    setRede(false);
    setModbus(false);
    setCodi(false);
    setION(false);
    setPulso(false);
    setOTA(false);

    setIP('0.0.0.0');

    // indica que timer não esta contando
    setIsRunning(false);
    setTimer(0); 
    setTimerWait(60);   

  }

  /**
   * executa a conexão do mqtt
   */
  const ConexaoMQTT = () => {

    // verifica se cliente MQTT já esta conectado
    if( (!conectado) || (conectando) )
    {       
      // indica que esta conectando ao servidor broker 
      setConectando(true);

      // conecta o cliente MQTT
      connectMQTT(isMqttHmg);
    }
  }

  /**
   * executa a desconexão do mqtt
   */
  const DesconexaoMQTT = () => {

    // verifica se esta conectado
    if(conectado) {      
      // desconecta o cliente MQTT
      disconnectMQTT();
    }
  }  

  /**
   * solicitação para o MQTT
   * @param variable 
   * @param auxiliar 
   */
  const EscreveMQTT = (variable:string, auxiliar: string = '') => {

    // verifica se cliente MQTT já esta conectado
    if(conectado)
    {
      try {
        // envia a pergunta
        publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
      }
      catch(error){

      }
    }
  }

  /**
   * resposta da solicitação do MQTT
   * @param variable 
   */
  const LerMQTT = (variable:string) => {

    // verifica se cliente MQTT já esta conectado
    if(conectado)
    {
      try {
        // pega resposta
        subscribeMQTT(route.params?.probeSettings.id, variable);
      }
      catch(error) {

      }
    }
  }    

  /**
   * executa sempre que o listener for alterado
   */
  useEffect(() => {
    
    const eventListener: any = EventRegister.addEventListener('isConnectedMQTT', mqtt => {  
      // seta que esta conectado
      setConectado(mqtt);            
    }, );
    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'conectado' for alterada
   */
  useEffect(() => {

    // se conectou a uma probe
    if ( (conectado) && (route.params?.probeSettings.id.length < 16) ) {

      // envio da pergunta para a probe      
      EscreveMQTT('dbgack');

      // leitura da resposta da probe
      LerMQTT('dbgack');

      // indica que já se conectou
      setConectando(false);

      // indica que timer esta contando
      setIsRunning(true);
    }
    // se conectou a um equipamento khomp
    else if ( (conectado) && (route.params?.probeSettings.id.length === 16) ) {

      // seta o firmware como khomp
      setFirmware('Khomp');

      // indica que já se conectou
      setConectando(false);

      // indica que timer esta contando
      setIsRunning(true);
    }    
    else {            

      // limpa info
      LimpaInfo();

    }  

  }, [conectado]);

  /**
   * executa sempre que a variavel 'messageMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('messageMQTT', mqtt => {            

      try{
        // pega a string do histórico de mensagens
        getStorageString(KEY.historicoDebugDesc).then((value) => {           
            setDebugDesc(value ?? '')
        });

        // pega a string do histórico de mensagens
        getStorageString(KEY.historicoDebugAsc).then((value) => {           
            setDebugAsc(value ?? '')
        });        
      }
      catch(error){

      }

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'firmwareMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('firmwareMQTT', mqtt => {            
      setFirmware(mqtt);
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'signalMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('signalMQTT', mqtt => {          
      setSinal(mqtt);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};  
  }, []);

  /**
   * executa sempre que a variavel 'batteryMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('batteryMQTT', mqtt => {            
      setBateria(mqtt);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, []);  

  /**
   * executa sempre que a variavel 'vStatusProbeMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('vStatusProbeMQTT', mqtt => {            
      setTensaoProbe(mqtt);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, []);  

  /**
   * executa sempre que a variavel 'typeConnectionMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('typeConnectionMQTT', mqtt => {            
        setTipoConexao(mqtt);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, []);

  /**
   * executa sempre que a variavel 'operadoraMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('operadoraMQTT', mqtt => {                 
      
      // se acima de 90 é narrow band
      if(mqtt > 90) {
        setTipoOperadora(mqtt - 90);
      }        
      else {        
        setTipoOperadora(mqtt);
      }
      
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, []);

  /**
   * executa sempre que a variavel 'dateTimeMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('dateTimeMQTT', mqtt => {            
      setDateTime(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'sistemaMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('sistemaMQTT', mqtt => {            
      setSistema(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'redeMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('redeMQTT', mqtt => {            
      setRede(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'modbusMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('modbusMQTT', mqtt => {            
      setModbus(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'codiMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('codiMQTT', mqtt => {            
      setCodi(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);  
  
  /**
   * executa sempre que a variavel 'ionMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('ionMQTT', mqtt => {            
      setION(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);
  
  /**
   * executa sempre que a variavel 'pulsoMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('pulsoMQTT', mqtt => {            
      setPulso(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'otaMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('otaMQTT', mqtt => {            
      setOTA(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'ipMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('ipMQTT', mqtt => {            
      setIP(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'vBateriaMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('vBatteryMQTT', mqtt => {       
      setVBateria(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'resetsMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('resetsMQTT', mqtt => {            
      setResets(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'firmwareBootMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('firmwareBootMQTT', mqtt => {            
      setFirmwareBoot(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'exceptionsMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('exceptionsMQTT', mqtt => {            
      setExcecoes(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'heapMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('heapMQTT', mqtt => {            
      setHeap(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'totalMemoryMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('totalMemoryMQTT', mqtt => {            
      setTotalMemory(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);
  
  /**
   * executa sempre que a variavel 'usedMemoryMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('usedMemoryMQTT', mqtt => {            
      setUsedMemory(mqtt);      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);
  
  /**
   * executa sys_config após variaveis dbgack serem atualizadas
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('findDbgackMQTT', mqtt => {            
      // se conectou
      if (mqtt) {              

        // envio da pergunta para a probe
        EscreveMQTT('sys_config', 'all');

        // leitura da resposta da probe
        LerMQTT('sys_config');
      }     

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  },);

  /**
   * executa sys_config após variaveis dbgack serem atualizadas
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('findSysConfigMQTT', mqtt => {            
      setLiberadoMenu(mqtt);
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  },);  

  /**
   * executa sempre que a variavel 'modbusMasterMQTT' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('modbusMasterMQTT', mqtt => {  
      
      if(mqtt) {

        // altera o status do modbus
        setModbus(prevModbus => !prevModbus);
      }

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);  

  /**
   * executa sempre que a variavel 'codi' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('codiStatusMQTT', mqtt => {  
      
      if(mqtt) {

        // altera o status do codi
        setCodi(prevCodi => !prevCodi);
      }

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);  

  /**
   * executa sempre que a variavel 'ion' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('ionStatusMQTT', mqtt => {  
      
      if(mqtt) {

        // altera o status do ion
        setION(prevIon => !prevIon);
      }

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);  

  /**
   * executa sempre que a variavel 'pulse' for ouvida
   */
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('pulsoStatusMQTT', mqtt => {  
    
      if(mqtt) {

        // altera o status do contador de pulsos
        setPulso(prevPulso => !prevPulso);
      }

    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'otaRollbackMQTT' for ouvida
   */
  useEffect(() => {    
    const eventListener: any = EventRegister.addEventListener('otaRollbackMQTT', mqtt => {
        
      // altera a descrição do firmware da probe
      setFirmware(mqtt);
      
      }, );    
      return () => { EventRegister.removeEventListener(eventListener); };
  }, []);

  /**
   * executa sempre que a variavel 'otaUpdateMQTT' for ouvida
   */
  useEffect(() => {    
    const eventListener: any = EventRegister.addEventListener('otaUpdateMQTT', mqtt => {

      // altera a descrição firmware da probe
      getStorageString(KEY.probeFirmware).then((value) => {         
          setFirmware(value ?? 'Desconhecido');
      });      
      
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, []);  
  
  /**
   * somente monitora para atualização
   */
  useEffect(() => {
  }, [modbus, codi, ion, firmware]); 
  

  // 
  /**
   * executa o timer
   */
  useEffect(() => {

    // se não esta conectado
    if(!conectado)
      return;
  
    // se houve alguma resposta do servidor dentro de 1 minuto
    if((timer >= timerWait) && (firmware === 'Firmware')){
      setShowPergunta03(true);
      setTimerWait(timerWait + 60);
    }    

    let interval = null;
    if (isRunning) {
      interval = setInterval(() => {
        setTimer(prevTime => prevTime + 1);
      }, 1000);
    } else if (!isRunning && timer !== 0) {
      if(interval !== null)
        clearInterval(interval);
    }
    return () => { 
      if(interval !== null)
        clearInterval(interval); 
    }
  }, [isRunning, timer]);  
  
  /**
   * navegar para tela de probe ID
   */
  const goProbeID = () => {
        
    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeID',
        params: { id: route.params?.probeSettings.id },
      })
    );        
  };  

  /**
   * navega para pagina sistema da Probe
   * @param probe 
   */
  const goToProbeSistema = (probe: ProbeSettings) => {   
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,                              
                              firmware: firmware,
                              datetime : dateTime, 
                              sinal : sinal,
                              bateria : bateria,
                              vBateria : vBateria,
                              tipoConexao : tipoConexao,                              
                              tipoOperadora : tipoOperadora,
                              v_probe : tensaoProbe,
                              firmwareBoot : firmwareBoot,
                              excecoes : excecoes,
                              resets : resets,
                              heap : heap,
                              totalMemory: totalMemory, 
                              usedMemory: usedMemory,
                              sistema : sistema,
                              rede : rede,
                              modbus : modbus,
                              codi : codi,
                              ion : ion,
                              pulso : pulso,
                              ota : ota,                            
                            };

    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeSistema',
        params: { probeSettings: probeAtualizada },
      })
    );    

  };

  /**
   * navega para pagina de configuração de rede da Probe
   * @param probe 
   */
  const goToProbeRede = (probe: ProbeSettings) => { 

    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,                              
                              tipoConexao: tipoConexao,
                            };

    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeRede',
        params: { probeSettings: probeAtualizada },
      })
    );    
  };

  /**
   * navega para pagina de configuração modbus da Probe
   * @param probe 
   */
  const goToProbeModbus = (probe: ProbeSettings) => {          
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,
                              tipoConexao : tipoConexao,
                              modbus : modbus,                              
                              modbusDevices: [],
                            };

    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeModbus',
        params: { probeSettings: probeAtualizada },
      })
    );
          
  }; 
  
  /**
   * navega para pagina de configuração codi da Probe
   * @param probe 
   */
  const goToProbeCODI = (probe: ProbeSettings) => { 
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,
                              tipoConexao : tipoConexao,
                              codi : codi,
                              codiDevices: [],                              
                            };
                            
    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeCODI',
        params: { probeSettings: probeAtualizada },
      })
    );
  }; 

  /**
   * navega para pagina de configuração ion da Probe
   * @param probe 
   */
  const goToProbeION = (probe: ProbeSettings) => {     
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,
                              tipoConexao : tipoConexao,
                              ion : ion,
                              ionDevices: [],
                            };
                            
    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeION',
        params: { probeSettings: probeAtualizada },
      })
    );    
  };   

  /**
   * navega para pagina de configuração de contador de pulso da Probe
   * @param probe 
   */
  const goToProbePulso = (probe: ProbeSettings) => {     
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,
                              tipoConexao : tipoConexao,
                              pulso: pulso,
                              pulsoDevices: [],                              
                            };
                            
    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbePulso',
        params: { probeSettings: probeAtualizada },
      })
    );    
  };

  /**
   * navega para pagina de configuração OTA da Probe
   * @param probe 
   */
  const goToProbeOTA = (probe: ProbeSettings) => {    
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe,
                              tipoConexao : tipoConexao,
                              firmware: firmware,
                              ota: ota,
                            };

    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ProbeOTA',
        params: { probeSettings: probeAtualizada },
      })
    );       
  };
  
  /**
   * atualiza as informações da probe 
   */
  const AtualizaProbe = () => {          

    // limpa info
    LimpaInfo();

    // se conectou
    if(conectado) {      

      // envio da pergunta para a probe
      EscreveMQTT('dbgack');

      // leitura da resposta da probe
      LerMQTT('dbgack');

      // indica que já se conectou
      setConectando(false);

      // indica que timer esta contando
      setIsRunning(true);
    }
    else {            

      // precisa se reconectar
      ConexaoMQTT();
    }  

  };

  /**
   * copia as mensagens do Debug e permiti compartilhar
   */
  const copyToDebug = () => {
        Clipboard.setString((orderDesc) ? debugDesc : debugAsc);
  };

  /**
   * trata a captura e compatilhamento de telas 
   */
 const handleCaptureAndShare = async () => {

  try {

    /* verifica se undefined */
    if (refShot.current) {

      const uri = await captureRef(refShot.current, {
        format: "jpg",
        quality: 0.9,
      });
      
      await Share.open({ url: String(uri) });

    } else {      
      setShowMessageErro(true);
    }
  } catch (error) {    
    setShowMessageErro(true);
  }
};


  // altera o servidor mqtt entre PROD e HMG
  const toggleSwitchMqtt = () => setIsMqttHmg(previousState => !previousState);  


  // Encapsulando a lógica em variáveis ou funções
  const renderDisconnectedView = () => (
    <View style={{ height: '100%', width: '100%', justifyContent: 'center', alignItems: 'center', borderRadius: 10, flexDirection: 'row', backgroundColor: '#E5E5E5' }}>
      <View style={{ height: '100%', width: '30%', justifyContent: 'center', flexDirection: 'row' }}>
        <TouchableOpacity style={styles.botaoInfo} onPress={goProbeID} testID='button-id'>
          <IconQrCode height={60} width={60} color={'#727272'} />
        </TouchableOpacity>
      </View>
      <View style={{ height: '70%', width: '5%' }}>
        <Divider orientation='vertical' color='#FFFFFF' width={2} />
      </View>
      <View style={{ height: '70%', width: '60%', paddingHorizontal: 10, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: '#727272' }}>Clique no botão do QRCode para escanear ou digitar o ID da Probe.</Text>
      </View>
    </View>
  );

  const renderConnectedKhompView = () => (
    <View style={{ height: '100%', width: '100%', justifyContent: 'center', alignItems: 'center', borderRadius: 10, flexDirection: 'row', backgroundColor: '#E5E5E5' }}>
      <View style={{ height: '100%', width: '30%', justifyContent: 'center', flexDirection: 'row' }}>
      </View>
      <View style={{ height: '70%', width: '5%' }}>
        <Divider orientation='vertical' color='#FFFFFF' width={2} />
      </View>
      <View style={{ height: '70%', width: '60%', paddingHorizontal: 10, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: '#727272' }}>{getModeloKhomp(route.params?.probeSettings.id)}</Text>
      </View>
    </View>
  );

  // Usando uma variável auxiliar para decidir qual componente renderizar
  const renderProbeStatus = () => {
    if (!conectado) {
      return renderDisconnectedView();
    } else if (route.params?.probeSettings.id.length === 16) {
      return renderConnectedKhompView();
    } else {
      return (
        <StatusProbe
          conectado={conectado}
          tipoConexao={tipoConexao}
          ip={ip}
          tipoOperadora={tipoOperadora}
          sinal={sinal}
          nivelBateria={bateria}
          tensaoProbe={tensaoProbe}
        />
      );
    }
  };

  /**
   * renderiza a tela de Debug da comunicação mqtt lida
   * @param debugDesc 
   * @returns 
   */
  const renderDebug = (debugDesc: string) => {
    return debugDesc.split("\n").map((line) => (
      <Text
        key={line}
          style={{
          fontFamily: 'SourceSans3_400Regular',
          fontSize: 12,
          color: '#737373',
          fontWeight: line.includes("/") ? 'bold' : 'normal',
        }}
      >
        {line}
      </Text>
    ));
  };

  return (

    <>
      <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={'Erro ao capturar a imagem da tela.'} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}
      />

      <MessageBoxAtencao
                visivel={showMessageAtencao} 
                titulo="Atenção" 
                descricao={'Clique no botão do QRCode para escanear ou digitar o ID da Probe.'} 
                textoBotao="OK" 
                onFechar={() => setShowMessageAtencao(false)}
      />
      
      <MessageBoxPergunta
        visivel={showPergunta02} 
        titulo="Atenção" 
        descricao={'Deseja realmente se desconectar do servidor?'} 
        textoBotaoOK="Sim" 
        textoBotaoCancelar='Não'
        onOK={ () => {  setShowPergunta02(false); 
                        DesconexaoMQTT()
                    }}
        onCancel={() =>setShowPergunta02(false)}
      />       

      <MessageBoxPergunta
        visivel={showPergunta03} 
        titulo="Atenção" 
        descricao={'O servidor está demorando para responder. Deseja continuar aguardando?'} 
        textoBotaoOK="Sim" 
        textoBotaoCancelar='Não'
        onOK={ () =>setShowPergunta03(false) }
        onCancel={() => { setShowPergunta03(false); DesconexaoMQTT() }}
      />                  
      
      <View style = {styles.containerTela}>

        <ImageBackground        
          source={require("../../assets/png/os-resum-bg.png")}
          resizeMode='cover'
          style={{top:0, left: 0, right: 0, height:heightHeader, width:screenWidth}}
        >
            {/* titulo e botão voltar */}
            <View style={{height:heightHeaderTitle, flexDirection:'row', marginTop:40}}>

              {/* botão voltar */}
              <View style={styles.containerBotaoVoltar}>
                <TouchableOpacity onPress={ () => navigation.goBack()} testID='button-back'>
                  <IconBack color={"#FFFFFF"} width={30} height={30}/>
                </TouchableOpacity>                
              </View>

              {/* texto cabeçalho */}
              <View style={styles.containerTextoHeader}>
                <Text style={styles.textoHeader}>CONFIGURAÇÕES</Text>
              </View>

            </View>

            {/* icone e id da probe */}
            <View style={{height:heightHeaderProbe, paddingHorizontal:30, alignItems:'center', justifyContent:'center', flexDirection:'row'}}>

              <View style={{height:'100%', alignItems:'center', flexDirection:'row', gap: 10}}>
                <View style={{height:50, width:'15%', justifyContent:'center', alignItems:'flex-start'}}>                
                  <TouchableOpacity onPress={ () => { 
                                                      if(!conectado)
                                                        refRBSheetMqtt.current?.open()}
                                                    }
                                    testID='button-mqtt'>
                    <IconProbe height={40} width={40} color={'#FFFFFF'} />
                  </TouchableOpacity>                                     
                </View>

                <View style={{width:'55%', alignItems:'flex-start'}}>
                  <Text style={styles.textoHeaderProbe}>{route.params?.probeSettings.id}</Text>
                  <Text style={styles.textoHeaderFirmware}>{firmware}</Text>
                </View>
              </View>
            
                  <View style={{height:'100%', width:'30%', justifyContent: (conectado) ? 'space-between' : 'flex-end', flexDirection:'row'}}>
                    {
                      (conectado) &&
                      <TouchableOpacity style={styles.botaoAtualizar} onPress={() => AtualizaProbe()} testID='button-refresh'>
                        <IconRefresh height={30} width={30} color={'#FFFFFF'} />
                      </TouchableOpacity>
                    }
                    <TouchableOpacity style={styles.botaoInfo} onPress={ () => refRBSheetDebug.current?.open()} testID='button-analiser'>
                      <IconAnalyser height={40} width={40} color={'#FFFFFF'} />
                    </TouchableOpacity>
                  </View>

            </View>

            {/* icone e tempo conectado */}
            <View style={{height:heightHeaderProbe, width:'100%', alignItems:'center', justifyContent:'center', paddingHorizontal:30, flexDirection:'row'}}>

              <View style={{height:'100%', alignItems:'center', flexDirection:'row', gap: 10}}>
                <View style={{ height:50, width:'15%', justifyContent:'center', alignItems:'flex-start', }}>
                  <IconClock height={40} width={40} color={'#FFFFFF'} />
                </View>

                <View style={{width:'45%', alignItems:'flex-start'}}>
                  <Text style={styles.textoHeaderTituloData}>{DataTimeStamp(dateTime)}</Text>
                  <Text style={styles.textoHeaderTituloHora}>{HoraTimeStamp(dateTime, false)}</Text>
                </View>      
              </View>
            
              <View style={{height:'100%', width:'40%', justifyContent:'center'}}>
                <TouchableOpacity style={{...styles.botaoConectar, backgroundColor: (conectado) ? '#2E8C1F' : '#C0002B'  }}
                                  onPress={ () => {
                                                    if (conectado) 
                                                      setShowPergunta02(true)
                                                    else if (route.params?.probeSettings.id === 'Probe ID') 
                                                      setShowMessageAtencao(true)
                                                    else
                                                      ConexaoMQTT() 
                                                  }}
                                  testID='button-connect'>
                  <Text style={{...styles.textoConectar}}>{(conectado) ? 'Conectado' : 'Desconectado'}</Text>
                  <Text style={styles.textoHeaderTempo}>{FormataTimer(timer)}</Text>
                </TouchableOpacity>
              </View>

            </View>

        </ImageBackground>

        <View style={{marginTop:-MarginTopStatusQRCode, height:heightStatusQRCode, paddingHorizontal:25, justifyContent:'center', alignItems: 'center'}}>
        { 
          renderProbeStatus() 
        }
        </View>

        <View style={{height:heightSettingsProbe, width:screenWidth, justifyContent:'center', alignItems:'center'}}>
          {
            (conectado) // (true) //       
            ?
              <SettingsProbe
                height={heightSettingsProbe} 
                width={screenWidth}
                conectado = {liberadoMenu} // {true} //           
                sistemaConfigurado = {sistema}
                redeConfigurado = {rede}
                modbusConfigurado = {modbus}
                codiConfigurado = {codi}
                ionConfigurado = {ion}
                pulsoConfigurado = {pulso}
                otaConfigurado = {ota}
                khompConfigurado = {route.params?.probeSettings.id.length === 16}
                onPressSistema = {() => goToProbeSistema(route.params?.probeSettings)} 
                onPressRede = {() => goToProbeRede(route.params?.probeSettings)} 
                onPressModbus = {() => goToProbeModbus(route.params?.probeSettings)} 
                onPressCODI = {() => goToProbeCODI(route.params?.probeSettings)} 
                onPressION = {() => goToProbeION(route.params?.probeSettings)} 
                onPressPulso = {() => goToProbePulso(route.params?.probeSettings)} 
                onPressOTA = {() => goToProbeOTA(route.params?.probeSettings)} 
                onPressKhomp={() => goToKhomp(navigation, route.params?.probeSettings)}
              />
            :      
              (conectando)
              ?
                <View style={{height:'100%', width:'100%', justifyContent:'center', alignItems:'center', gap: 20}}>
                    <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} style={{ transform: [{ scaleX: 2 }, { scaleY: 2 }] }}/>
                    <Text style={styles.textoLoading}> Conectando-se ao servidor...</Text>
                </View>
              : 
                <View style={{height:'100%', width:'100%', opacity:0.5, marginTop:-60, justifyContent:'center', alignItems:'center'}}>
                  <LogoZordon />
                </View>
          }
        </View>

        <RBSheet ref={refRBSheetDebug} height={screenHeight * 0.8}>

            <ViewShot style={{flex:1, padding: 20, alignItems:'center', gap: 20, backgroundColor:'#FFFFFF'}} 
                      ref={refShot} options={{
                        fileName: 'file-name', // screenshot image name
                        format: 'jpg', // image extention
                        quality: 0.9 // image quality
                      }}>        
              
                <View style={{width:'100%', flexDirection:'row', justifyContent:'space-between'}}>
                  <Text style={styles.textoTitleDebug}>Debug</Text>
                  <View style={{width: 150, flexDirection:'row', justifyContent:'space-between'}}>
                    <TouchableOpacity testID='button-order' onPress={() => setOrderDesc(!orderDesc)}>
                      <IconOrder color={"#737373"} width={30} height={30}/>
                    </TouchableOpacity>                    
                    <TouchableOpacity testID='button-copy' onPress={copyToDebug}>
                      <IconCopy color={"#737373"} width={30} height={30}/>
                    </TouchableOpacity>
                    <TouchableOpacity testID='button-print' onPress={handleCaptureAndShare}>
                      <IconCamera color={"#737373"} width={30} height={30}/>
                    </TouchableOpacity>
                  </View>
                                        
                </View>                

                <ScrollView style={{flexGrow: 1, height:'90%', width:'100%', borderWidth:1, borderColor: '#D6D6D6', borderRadius: 5, padding:10 }}>
                  { renderDebug((orderDesc) ? debugDesc : debugAsc) }
                </ScrollView>                

            </ViewShot>
            
        </RBSheet>

        <RBSheet ref={refRBSheetMqtt} height={screenHeight * 0.25}>

            <View style={{flex:1, padding: 20, alignItems:'center', gap: 30}}>        
              
              <TouchableOpacity style={{alignSelf:'flex-end'}} onPress={() => { refRBSheetMqtt.current?.close(); }} testID='button-close-mqtt'>
                <IconX color={"#737373"} width={30} height={30}/>
              </TouchableOpacity>                

              <Text style={styles.textoSwitchMqtt}>Servidor MQTT</Text>
              <View style={{width:'50%', alignItems:'center', justifyContent:'space-between', flexDirection:'row'}}>

                <TouchableOpacity onPress={() => { setIsMqttHmg(false) }} testID='button-prod'>
                  <Text style={{...styles.textoSwitch, color: (!isMqttHmg) ? '#2E8C1F' : "#A3A3A3"}}>PROD</Text>
                </TouchableOpacity>
                <Switch                
                  trackColor={{true: '#A3A3A3', false: '#A3A3A3'}}
                  thumbColor={'#2E8C1F'}
                  ios_backgroundColor="#38B026"
                  onValueChange={toggleSwitchMqtt}
                  value={isMqttHmg}
                  style={{transform: [{ scaleX: 1.5 }, { scaleY: 1.5 }]}}
                  testID='switch-mqtt'
                />
                <TouchableOpacity onPress={() => { setIsMqttHmg(true) }} testID='button-hmg'>
                  <Text style={{...styles.textoSwitch, color: (isMqttHmg) ? '#2E8C1F' : "#A3A3A3"}}>HMG</Text>
                </TouchableOpacity>                                
              </View>
                      
            </View>
            
        </RBSheet>

      </View>

    </>

  );
};

export default Probe;