import { <PERSON><PERSON>ontainer } from "@react-navigation/native";
import { act, cleanup, fireEvent, render, screen } from "@testing-library/react-native";
import React from "react";
import { EventRegister } from "react-native-event-listeners";
import { MockFirmwareJson } from "../../../__mocks__/FirmwareJsonMocks";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";
import * as spyMQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import ProbeOTA from "./ProbeOTA";

import * as ApiZordon from '../../services/apiZordon';
const spyApiZordon = jest.spyOn(ApiZordon, 'default');

cleanup();
jest.mock('sp-react-native-mqtt', () => 'MQTT');

jest.useFakeTimers();

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       ota: true,
                       firmware : 'H3.0_Z2.0.31',
                     }
    },
};

describe('ProbeCODI', () => {

    beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
    });

    it('renderiza corretamente com a rota fornecida', () => {
    
        const {unmount} = render(  <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <ProbeOTA route={mockRoute} />
                                              </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-back'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de status', async () => {
    
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeOTA route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-status'));
        });

        act(() => {
            fireEvent.press(getByText('OK'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de rollback e cancela', async () => {
    
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeOTA route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-rollback'));
        });

        act(() => {
            fireEvent.press(getByText('Não'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de rollback, confirma e mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
                
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeOTA route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-rollback'));
        });

        act(() => {
            fireEvent.press(getByText('Sim'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de rollback, confirma e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);
                
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeOTA route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-rollback'));
        });

        act(() => {
            fireEvent.press(getByText('Sim'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de firmware Prod e fecha', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                            <ProbeOTA route={mockRoute} />
                                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });

        act(() => {
            fireEvent.press(getByTestId('button-close-prod'));
        });

        // Chama o cleanup do listener
        unmount();
    });

    it('renderiza corretamente com a rota fornecida ao clicar no botão de firmware hmg e fecha', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <ProbeOTA route={mockRoute} />
                                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        act(() => {
            fireEvent.press(getByTestId('button-open-hmg'));
        });

        act(() => {
            fireEvent.press(getByTestId('button-close-hmg'));
        });

        // Chama o cleanup do listener
        unmount();
    });  

    it('renderiza corretamente com a rota fornecida para ler os firmwares quando json como objeto', async () => {
            
        // cenario erro    
        spyApiZordon.mockResolvedValueOnce({data:MockFirmwareJson, status:500})

        // render erro
        const renderErro = render(  <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
        
        // Avance o tempo em 4 segundos
        jest.advanceTimersByTime(4000);

        renderErro.unmount()

        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:MockFirmwareJson, status:200})
                
        const renderSucesso = render(  <NavigationContainer>
                                            <ProbeOTA route={mockRoute} />
                                        </NavigationContainer> );

        // Avance o tempo em 4 segundos
        
        jest.advanceTimersByTime(4000);
        
        renderSucesso.unmount()
    });
    
    it('renderiza corretamente com a rota fornecida para ler os firmwares, mas houve erro', async () => {                

        // cenario erro            
        spyApiZordon.mockRejectedValueOnce(new Error('Erro na API'));


        // render erro
        const renderErro = render(  <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
        
        // Avance o tempo em 4 segundos
        jest.advanceTimersByTime(4000);

        renderErro.unmount()
    });


    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de produção', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, queryAllByText} = render(  <NavigationContainer>
                                                                    <ProbeOTA route={mockRoute} />
                                                                </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 1]);            
        });        

        unmount()
    });

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, queryAllByText} = render(  <NavigationContainer>
                                                                    <ProbeOTA route={mockRoute} />
                                                                </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-hmg'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 1]);            
        });        

        unmount()
    });    

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza e cancela', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-hmg'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 1]);            
        });        

        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Não'));
        });        

        unmount()
    });    
    
    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma, mas firmware é igual', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 1]);            
        });        

        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });    

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma e modo não escolhido', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 2]);            
        });        

        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma e firmware Timeout não inserido', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 2]);            
        });        


        const dropdown = getByTestId('combo-modo');
        act(() => {
                    fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
                  });     
        
        
        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma e firmware Timeout não é um numero', async () => {
            
        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 2]);            
        });        


        const dropdown = getByTestId('combo-modo');
        act(() => {
                    fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
                  });     
        
        const input = getByTestId('input-timeout');

        await act(async () => {
            fireEvent.changeText(input, 'teste');        
        });
        
        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });
    
    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma e mqtt desconectado', async () => {
            
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 2]);            
        });        


        const dropdown = getByTestId('combo-modo');
        act(() => {
                    fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
                  });     
        
        const input = getByTestId('input-timeout');

        await act(async () => {
            fireEvent.changeText(input, '60');        
        });
        
        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });    

    it('renderiza corretamente com a rota fornecida para ler os firmwares e seleciona um firmware de homologação, atualiza, confirma e mqtt conectado', async () => {
            
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // cenario sucesso
        spyApiZordon.mockResolvedValueOnce({data:JSON.stringify(MockFirmwareJson), status:200})
                
        const {unmount, getByTestId, getByText, queryAllByText} = render(   <NavigationContainer>
                                                                                <ProbeOTA route={mockRoute} />
                                                                            </NavigationContainer> );

        // Avance o tempo em 4 segundos        
        jest.advanceTimersByTime(4000);

        await act(async() => {
            fireEvent.press(getByTestId('button-open-prod'));
        });
        
        // Encontre todos os botões com o inicio H3.0
        const buttons = queryAllByText(/^H3\.0.*$/);

        
        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act( async() => {            
            fireEvent.press(buttons[buttons.length - 2]);            
        });        


        const dropdown = getByTestId('combo-modo');
        act(() => {
                    fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 0, descricao: 'Normal' }}})
                  });     
        
        const input = getByTestId('input-timeout');

        await act(async () => {
            fireEvent.changeText(input, '60');        
        });
        
        await act(async() => {
            fireEvent.press(getByTestId('button-update'));
        });
        
        await act(async() => {
            fireEvent.press(getByText('Sim'));
        });        

        unmount()
    });    

    it('deve executar o fluxo esperado ao acionar o rollback do firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaRollbackMQTT') {
            callback('firmware'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaRollbackMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao atualizar do firmware com sucesso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaUpdateMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaUpdateMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao atualizar do firmware com erro', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaUpdateMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaUpdateMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao estar atualizando o firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;        

        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'OTAupdatingMQTT') {
            callback('atualizando'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeOTA route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('OTAupdatingMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
});