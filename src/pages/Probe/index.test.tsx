import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import React from "react";
import { EventRegister } from "react-native-event-listeners";
import MQTT, { IMqttClient } from "sp-react-native-mqtt";
import Probe from ".";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";


jest.mock('sp-react-native-mqtt', () => 'MQTT');
jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');
jest.mock('react-native-view-shot', () => 'ViewShot');
jest.mock('react-native-share', () => 'Share');

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        navigate: jest.fn(), // Simulando o comportamento de navigate
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
      }),
    };
});

// Mock do MQTT
jest.mock('sp-react-native-mqtt', () => {
    return {
        MQTT: () => null,
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      emit: jest.fn(),
    },
}));  

jest.mock('react-native-reanimated', () => {
    const Reanimated = require('react-native-reanimated/mock');
    Reanimated.default.call = () => {}; // Correção para alguns problemas.
    return Reanimated;
});

jest.mock('@react-native-clipboard/clipboard', () => ({
  setString: jest.fn(),
  getString: jest.fn().mockResolvedValue('Texto copiado'),
})); 

jest.useFakeTimers();

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings, 
                    id:'Probe ID'
       }
    },
};

const mockRouteID = {
    params: {
      probeSettings: { ...MockProbeSettings,
                      id: 'ABCD12345678'
       }
    },
};

const mockRouteKhomp = {
  params: {
    probeSettings: { ...MockProbeSettings,
                    id: 'F803320500000000'
     }
  },
};

describe('Probe', () => {

    let mqttClientMock: IMqttClient;
            
    beforeEach(() => {

        // Substituímos manualmente o método createClient por um mock
        MQTT.createClient = jest.fn().mockImplementation(() => Promise.resolve(mqttClientMock));
        mqttClientMock = {
          on: jest.fn(),
          connect: jest.fn(),
          subscribe: jest.fn(),
          publish: jest.fn(),
          disconnect: jest.fn(),
          unsubscribe: jest.fn(),
          reconnect:jest.fn(),
          isConnected: jest.fn(),
        };
    });

    it('renderiza corretamente a tela inicial de configuração da Probe', () => {
    
        const {} = render(  <NavigationContainer>
                                <Probe route={mockRoute} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    /*
    it('renderiza corretamente ao clicar no botão user e depois logout', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                  <Probe route={mockRoute} />
                                                </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-logout'));                
        });
        
        unmount(); // Garante que o componente seja desmontado no final do teste
    });*/

    it('renderiza corretamente ao clicar no botão de inserir id', async () => {
    
        const {getByTestId} = render(   <NavigationContainer>
                                            <Probe route={mockRoute} />
                                        </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-id'));
        });
    });    
    
    it('renderiza corretamente ao clicar no botão para alterar servidor mqtt', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <Probe route={mockRoute} />
                                              </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-mqtt'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('renderiza corretamente ao clicar no botão para alterar servidor mqtt e fecha tela', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <Probe route={mockRoute} />
                                              </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-mqtt'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-close-mqtt'));
        });
        
        unmount(); // Garante que o componente seja desmontado no final do teste
    }); 

    it('renderiza corretamente ao clicar no botão para alterar servidor mqtt e alterna entre hmg e prod', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <Probe route={mockRoute} />
                                              </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-mqtt'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {
            fireEvent.press(getByTestId('button-hmg'));
            fireEvent.press(getByTestId('button-prod'));
        });
        
        unmount(); // Garante que o componente seja desmontado no final do teste
    });
    

    it('renderiza corretamente ao clicar no botão para alterar servidor mqtt e chaveia entre hmg e prod', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                                <Probe route={mockRoute} />
                                              </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-mqtt'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {
            fireEvent.press(getByTestId('switch-mqtt'));            
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('renderiza corretamente ao clicar no botão de conectar ao servidor mqtt', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <Probe route={mockRoute} />
                                    </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });                
    });
    
    it('renderiza corretamente ao clicar no botão de conectar ao servidor mqtt com um ID probe válido', async () => {
    
        const {getByTestId} = render(   <NavigationContainer>
                                            <Probe route={mockRouteID} />
                                        </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });
        
        act(() => {
          jest.advanceTimersByTime(60001); // Avança 60 segundos
        });
    });
    
    it('renderiza corretamente ao clicar no botão de conectar ao servidor mqtt com um Khomp válido', async () => {
    
      const {getByTestId} = render(   <NavigationContainer>
                                          <Probe route={mockRouteKhomp} />
                                      </NavigationContainer> );
      
      await act(async () => {         
          fireEvent.press(getByTestId('button-connect'));
      });
      
      act(() => {
        jest.advanceTimersByTime(60001); // Avança 60 segundos
      });
  });    

    it('deve executar o fluxo esperado solicitar o firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'firmwareMQTT') {
            callback('firmware'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('firmwareMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o signal gsm/wifi', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'signalMQTT') {
            callback(-50); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('signalMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado solicitar a autonomia da bateria', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'batteryMQTT') {
            callback(80); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('batteryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar se a Probe esta alimentada pela bateria ou não', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'vStatusProbeMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('vStatusProbeMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });   
    
    it('deve executar o fluxo esperado solicitar o tipo de conexão', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'typeConnectionMQTT') {
            callback(1); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('typeConnectionMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado solicitar o tipo de operadora', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'operadoraMQTT') {
            callback(1); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('operadoraMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o tipo de operadora é narrow band', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'operadoraMQTT') {
            callback(91); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('operadoraMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a data e hora', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'dateTimeMQTT') {
            callback(0); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('dateTimeMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo sistema', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'sistemaMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('sistemaMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo rede', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'redeMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('redeMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo modbus', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo codi', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo ion', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ionMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo contador e pulso', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o statu do módulo ota', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o ip da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ipMQTT') {
            callback('***********'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ipMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o tensão da bateria', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'vBatteryMQTT') {
            callback(100); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('vBatteryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o total de resets da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'resetsMQTT') {
            callback(10); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('resetsMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado solicitar a data de configuração do firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'firmwareBootMQTT') {
            callback(0); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('firmwareBootMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar as exceções da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'exceptionsMQTT') {
            callback(10); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('exceptionsMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o tamanho da memória heap', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'heapMQTT') {
            callback(1024); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('heapMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o tamanho da memória', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'totalMemoryMQTT') {
            callback(1024); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('totalMemoryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar o total de memória usada', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'usedMemoryMQTT') {
            callback(512); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('usedMemoryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar o inicio da chamada do sys_config = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'findDbgackMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('findDbgackMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar o inicio da chamada do sys_config = true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'findDbgackMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('findDbgackMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a liberação do menu de módulos', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'findSysConfigMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do modbus =  true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusMasterMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusMasterMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do modbus = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'modbusMasterMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('modbusMasterMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do codi =  true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiStatusMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do codi = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'codiStatusMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('codiStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do ion =  true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ionStatusMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do ion = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ionStatusMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ionStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado solicitar verifica o status atual do puslo =  true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoStatusMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado solicitar verifica o status atual do pulso = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'pulsoStatusMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('pulsoStatusMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica a versão de rollback do firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaRollbackMQTT') {
            callback('firmware'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaRollbackMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica a versão do firmware', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'otaUpdateMQTT') {
            callback('firmware'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('otaUpdateMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar verifica as mensagens mqtt', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'messageMQTT') {
            callback('mensagem'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('messageMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a conexão com o mqtt', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <Probe route={mockRouteID} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    

    it('deve executar o fluxo esperado solicitar a conexão com o mqttn e clica no botão conectar e não desconecta', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                              <Probe route={mockRouteID} />
                                                            </NavigationContainer> );

        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });

        await act(async () => {         
            fireEvent.press(getByText('Não'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a conexão com o mqttn e clica no botão conectar e desconecta', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                              <Probe route={mockRouteID} />
                                                            </NavigationContainer> );

        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });

        await act(async () => {         
            fireEvent.press(getByText('Sim'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a conexão com o mqttn e clica no botão refrescar', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId} = render( <NavigationContainer>
                                                  <Probe route={mockRouteID} />
                                                </NavigationContainer> );

        await act(async () => {         
            fireEvent.press(getByTestId('button-refresh'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado solicitar a conexão com o mqttn e clica no botão analisador de payloads', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId} = render( <NavigationContainer>
                                                  <Probe route={mockRouteID} />
                                                </NavigationContainer> );

        await act(async () => {         
            fireEvent.press(getByTestId('button-analiser'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-order'));
            fireEvent.press(getByTestId('button-copy'));
            fireEvent.press(getByTestId('button-print'));
        });

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de sistema', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-sistema'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de rede', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-rede'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });    


    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de modbus', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-modbus'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de codi', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-codi'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });    
       
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de ion', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-ion'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de contador de pulso', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-pulso'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });
    
    it('deve executar o fluxo esperado solicitando conexão com o mqtt e clica no módulo de contador de ota', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'isConnectedMQTT') {
                callback(true); // Simula o callback de reconexão
                return 'mockEventListenerId1';
            } else if (eventName === 'findSysConfigMQTT') {
                callback(true); // Simula o callback para o evento findSysConfigMQTT
                return 'mockEventListenerId2';
            }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId } = render(
            <NavigationContainer>
                <Probe route={mockRouteID} />
            </NavigationContainer>
        );
    
        await act(async () => {
            fireEvent.press(getByTestId('button-connect'));
        });
    
        await act(async () => {
            fireEvent.press(getByTestId('button-ota'));
        });
    
        // Verificar as chamadas para ambos os eventos
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
        expect(mockedAddEventListener).toHaveBeenCalledWith('findSysConfigMQTT', expect.any(Function));
    
        // Chama o cleanup dos listeners
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId1');
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId2');
    });  
    
    
    it('deve executar o fluxo esperado solicitar a conexão com o mqttn e clica no módulo khomp', async () => {
      // Elenco (cast) o método para jest.Mock
      const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
  
      mockedAddEventListener.mockImplementation((eventName, callback) => {
        if (eventName === 'isConnectedMQTT') {
          callback(true); // Simula o callback de reconexão
          return 'mockEventListenerId';
        }
      });
  
      const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
  
      const { unmount, getByTestId} = render( <NavigationContainer>
                                                <Probe route={mockRouteKhomp} />
                                              </NavigationContainer> );

      await act(async () => {
        fireEvent.press(getByTestId('button-connect'));
      });

      await act(async () => {
        fireEvent.press(getByTestId('button-khomp'));
      });

      // Aqui você pode verificar as chamadas ou comportamentos esperados
      expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
  
      // Chama o cleanup do listener
      unmount();
  
      expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
});