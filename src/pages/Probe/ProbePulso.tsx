import React, { useState, useRef, useEffect } from "react"
import { View, TouchableOpacity, Text, FlatList } from "react-native"
import { EventRegister } from "react-native-event-listeners";
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import {screenHeight, styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconSave from '../../assets/svg/icon_save-01.svg';
import IconRefresh from '../../assets/svg/icon_refresh.svg';
import IconPlus from '../../assets/svg/icon_plus.svg';
import IconX from '../../assets/svg/icon_x.svg';
import IconBroom from '../../assets/svg/icon_broom.svg';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import {StackTypes} from '../../routes/index'

// data
import { AddPulso, ProbeSettings, pulsoSettings } from "../../data";

// componentes
import Loading from "../../componentes/Loading";
import CardPulso from "../../componentes/CardPulso";
import Divider from "../../componentes/Divider";
import ComboBox from "../../componentes/ComboBox";
import TextEntry from "../../componentes/TextEntry";
import CheckBox from "../../componentes/CheckBox";

// modal messages
import MessageBoxErro from "../../modal/messagebox/Erro";
import MessageBoxSucesso from "../../modal/messagebox/Sucesso";
import MessageBoxAtencao from "../../modal/messagebox/Atencao";
import MessageBoxPergunta from "../../modal/messagebox/Pergunta";

// constantes
import {ListaContactPulso, 
        ListaContatoPulso, 
        ListaFlowPulso, 
        ListaMedicaoPulso, 
        ListaTempoEnvioProbe, 
        ListaTiposPulso, 
        TempoSegundosEnvioProbe, 
        TimeoutTipoConexao, 
        TipoMedicaoPulso, 
        TiposPulso } from "../../constantes";

// funções mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT } from "../../services/mqtt";
import { isNumber } from "../../funcoes";



const ProbePulso = ({route}) => {

    const deepCopy = (obj: any) => { return JSON.parse(JSON.stringify(obj)); };

    const refRBSheetTipoPulso = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetPulso = useRef<{ open: () => void, close: () => void }>(null);   
    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // definições da probe
    const [probe, setProbe] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));
    const [probeCopy, setProbeCopy] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));

    // indica o status atual do pulso
    const [pulsoStatus, setPulsoStatus] = useState<boolean>(route.params?.probeSettings.pulso);
    //const [pulsoStatus, setPulsoStatus] = useState<boolean>(true);

    // configuração do pulso
    const [pulsoAtual, setPulsoAtual] = useState<number>(-1);
    const [savePulsoAtual, setSavePulsoAtual] = useState<number>(0);    
    const [excluirTodosPulso, setExcluirTodosPulso] = useState<boolean>(false);

    // informações do contador de pulso
    const [pulsoConfigurado, setPulsoConfigurado] = useState<boolean>(false);
    const [tipoPulso, setTipoPulso] = useState<string>('');
    const [tiposPulso, setTiposPulso] = useState<string[]>([...TiposPulso]);  
    const [pulso, setPulso] = useState<string>('');
    const [pulsoCount, setPulsoCount] = useState<number>(0);    
    const [port, setPort] = useState<number>(-1);    
    const [tipoMedicao, setTipoMedicao] = useState<number>(-1);
    const [tipoContato, setTipoContato] = useState<number>(-1);
    const [repo, setRepo] = useState<boolean>(false);
    const [tempoRepo, setTempoRepo] = useState<string>('60');
    const [tempoEnvio, setTempoEnvio] = useState<number>(-1);
    const [fator, setFator] = useState<string>('1.0');
    const [reativo, setReativo] = useState<boolean>(false);

    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);
    const [showMessageAtencao, setShowMessageAtencao] = useState<boolean>(false);    
    const [showMessagePerguntaDel, setShowMessagePerguntaDel] = useState<boolean>(false);
    const [showMessagePerguntaEdit, setShowMessagePerguntaEdit] = useState<boolean>(false);
    const [showMessagePerguntaSave, setShowMessagePerguntaSave] = useState<boolean>(false);
    const [showMessagePerguntaPulsoStatus, setShowMessagePerguntaPulsoStatus] = useState<boolean>(false);
    const [showMessagePerguntaCleanPulso, setShowMessagePerguntaCleanPulso] = useState<boolean>(false);
    const [showMessagePerguntaSavePulso, setShowMessagePerguntaSavePulso] = useState<boolean>(false);
    const [textoMessage, setTextoMessage] = useState<string>('');

    // inicia leitura da configuração pulso
    const [iniciaLeitura, setIniciaLeitura] = useState<boolean>(true);

    // se deve adicionar contador de pulso ou salvar
    const [adicionar, setAdicionar] = useState<boolean>(false);
    const [showAdicionar, setShowAdicionar] = useState<boolean>(true);
    
    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');
    const [saving, setSaving] = useState<boolean>(false);

    // controle timeout loading
    let timeoutWait : number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);    

    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();
    };

    // solicitação para o MQTT
    const EscreveMQTT = (variable:string, auxiliar: string = '') => {

        try {
                        
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error){}
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable:string) => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable);
        }
        catch(error) {}
    }    

    // leitura das configurações do contador de pulsos
    const getConfiguracaoPulsos = async () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // envio de solicitação de da leituradas configurações do módulo pulse
            EscreveMQTT('sys_config', 'pulse');
            
            // leitura da solicitação 
            LerMQTT('sys_config');
 
            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                 // indica que a mensagem demorou muito tempo para responder
                 setTimeoutMessage(true);               
            }, timeoutWait);
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));            
         }
         else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
         }
    }

    // seta o status do módulo de contador de pulsos [ativado/desativado]
    async function setStatusPulso() {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`${(pulsoStatus) ? 'Desativando' : 'Ativando'} o módulo \nContador de Pulsos...`);   

            if(pulsoStatus) {
                // envio de solicitação de desativação do módulo contador de pulsos
                EscreveMQTT('pulse', 'module_end');
            }
            else {
                // envio de solicitação de ativação do módulo contador de pulsos
                EscreveMQTT('pulse', 'module_start');
            }
    
            // leitura da solicitação 
            LerMQTT('pulse');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

           // pega o id do timeout atual
           setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
         }        
    }

    // se deve remover o tipo de contador de pulso a ser configurado
    const removerTipoPulso = (tipo_pulso: string) => {

        // pega o indice do item a ser excluido da lista
        const indice = tiposPulso.findIndex(item => item === tipo_pulso);

        // exclui o item
        tiposPulso.splice(indice, 1);
    }

    // se deve adicionar o tipo de contador de pulso a ser configurado
    const adicionarTipoPulso = (tipo_pulso: string) => {
        
        // adiciona um novo item
        tiposPulso.push(tipo_pulso);

        // ordena em ordem alfabetica
        setTiposPulso(tiposPulso.sort()); 

        // deve apresentar botão de adicionar configuração contadores de pulso
        setShowAdicionar(true);
    }

    // adiciona o dispositivo contador de pulsos a estrutura
    function AdicionarPulso(probe_: ProbeSettings, 
                            tipo_pulso: string, 
                            port: number, 
                            tipo_medicao: number, 
                            tipo_contato: number,
                            repo: boolean, 
                            tempo_repo: string, 
                            tempo_envio: number, 
                            fator: string,
                            reativo: boolean)
    {
        // se foi selecionado o tipo de medicao
        if(tipo_medicao < 0)  {
            setTextoMessage('O tipo de medição não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi selecionado o tipo de contato
        if ((tipo_medicao === TipoMedicaoPulso.CONTATO) && (tipo_contato < 0) )  {
            setTextoMessage('O tipo de Contato não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi ativada as mensagens de reposição e não existe tempo de reposição
        if( (repo) && (tempo_repo.length <= 0))   {
            setTextoMessage('O tempo de reposição não foi inserido.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e tempo de resposição não é numero
        if( (repo) && (!isNumber(tempo_repo)))   {
            setTextoMessage('O campo Tempo de Reposição somente permite números.');
            setShowMessageErro(true);
            return;
        }

        // se foi selecionado o tempo de envio
        if(tempo_envio < 0)  {
            setTextoMessage('O tempo de envio não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e não existe tempo de reposição
        if(tempo_repo.length <= 0)   {
            setTextoMessage('O tempo de reposição não foi inserido.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e tempo de resposição não é numero
        if(!isNumber(tempo_repo))   {
            setTextoMessage('O campo Tempo de Reposição somente permite números.');
            setShowMessageErro(true);
            return;
        }

        // insere um contador de pulso nas configurações da probe
        probe_.pulsoDevices?.push(AddPulso(tipo_pulso, port, tipo_medicao, tipo_contato, reativo, repo, parseInt(tempo_repo), parseFloat(fator), tempo_envio,));
        
        // verifica se tipo do pulso = 'PULSO 1 e reativo ativo => insere um novo contador de pulso
        if ( (tipo_pulso === TiposPulso[0]) && (tipoMedicao === TipoMedicaoPulso.ENERGIA) ) {

            // insere um contador de pulso nas configurações da probe
            probe_.pulsoDevices?.push(AddPulso(TiposPulso[1], 2, tipo_medicao, tipo_contato, reativo, repo, parseInt(tempo_repo), parseFloat(fator), tempo_envio,));

            // remove o tipo de configurado 'PULSO 2' para as próximas configurações
            removerTipoPulso(TiposPulso[1]);
        }        

        // atualiza estrutura de configuração da probe        
        const probeAtualizada = {...probe_};        

        // atualiza informações de device da probe
        //setProbe({...probeAtualizada});        
        setProbe(deepCopy({...probeAtualizada}));

        // remove o tipo de configurado para as próximas configurações
        removerTipoPulso(tipo_pulso);

        // fecha tela de configuração do pulso
        refRBSheetPulso.current?.close();        
    }    

    // salva as configurações do contador de pulso
    async function SalvarPulso(probe_: ProbeSettings, 
                               indice_pulso: number, 
                               tipo_pulso: string, 
                               port: number, 
                               tipo_medicao: number,
                               tipo_contato: number, 
                               repo: boolean, 
                               tempo_repo: string, 
                               tempo_envio: number, 
                               fator: string,
                               reativo: boolean) {

        // se foi selecionado o tipo de medicao
        if(tipo_medicao < 0)  {
            setTextoMessage('O tipo de medição não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi selecionado o tipo de contato
        if ((tipo_medicao === TipoMedicaoPulso.CONTATO) && (tipo_contato < 0) )  {
            setTextoMessage('O tipo de Contato não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi ativada as mensagens de reposição e não existe tempo de reposição
        if( (repo) && (tempo_repo.length <= 0))   {
            setTextoMessage('O tempo de reposição não foi inserido.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e tempo de resposição não é numero
        if( (repo) && (!isNumber(tempo_repo)))   {
            setTextoMessage('O campo Tempo de Reposição somente permite números.');
            setShowMessageErro(true);
            return;
        }

        // se foi selecionado o tempo de envio
        if(tempo_envio < 0)  {
            setTextoMessage('O tempo de envio não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e não existe tempo de reposição
        if(tempo_repo.length <= 0)   {
            setTextoMessage('O tempo de reposição não foi inserido.');
            setShowMessageErro(true);
            return;
        }        

        // se foi ativada as mensagens de reposição e tempo de resposição não é numero
        if(!isNumber(tempo_repo))   {
            setTextoMessage('O campo Tempo de Reposição somente permite números.');
            setShowMessageErro(true);
            return;
        }                
        
        // pega os dados do contador de pulso configurado
        const pulsoAtualizado ={ ...{...probe_.pulsoDevices?.[indice_pulso] },
                                 pulso: tipo_pulso,
                                 port: port,
                                 type: tipo_medicao,
                                 contact: tipo_contato,
                                 reactive: reativo,
                                 repo: repo,
                                 send_time_repo: parseInt(tempo_repo),
                                 send_time: tempo_envio,
                                 factor: parseFloat(fator),
                                 offset_time: 0,                                 
        }; 

        // atualiza com as configurações do contador de pulso
        probe_.pulsoDevices?.splice(indice_pulso, 1, pulsoAtualizado);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},
                                 pulsoDevices: [...probe_.pulsoDevices ?? []]
        };
        
        // atualiza a configuração da probe
        //setProbe({...probeAtualizada});
        setProbe(deepCopy(probeAtualizada));
        

        // incrementa proximo slave
        setPulsoCount(pulsoCount + 1);

        // fecha tela de configuração da medição
        refRBSheetPulso.current?.close();
    }

    // exclui as configuração do contador de pulso na probe
    function ExcluirPulsoProbe(probe_: ProbeSettings, pulso_atual: number)
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            if( (probe_.pulsoDevices?.length ?? 0) > 0) {

                // inicia loading
                setLoading(true);
                setTextoLoading(`Excluindo configurações do ${probe_.pulsoDevices?.[pulso_atual].pulso}...`);   
                                
                // envio de solicitação da exclusão do contador de pulso
                EscreveMQTT('pulse_end', JSON.stringify({port: (probe_.pulsoDevices?.[pulso_atual].pulso === 'PULSO 1') ? 1 : 2 }));
 
                // leitura da solicitação 
                LerMQTT('pulse_end');

                // loading vai durar 60 segundos
                const timeout_id = setTimeout(function() {                
                    // indica que a mensagem demorou muito tempo para responder
                    setTimeoutMessage(true);               
                }, timeoutWait);

                // pega o id do timeout atual
                setTimeoutIDMessage(Number(timeout_id));                
            }
            else {
                setTextoMessage('Não existe nenhum Contador de Pulsos configurado.');
                setShowMessageErro(true);
            }            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    // edita um determinado contador de pulso configurado
    function EditarPulso (pulsoDevice: pulsoSettings)
    {                
        setTipoPulso(pulsoDevice.pulso ?? '');

        setPort(pulsoDevice.port ?? 0);
        setTipoMedicao(pulsoDevice.type ?? 0);
        setTipoContato(pulsoDevice.contact ?? 0);  
        setRepo(pulsoDevice.repo ?? false);
        setTempoRepo(pulsoDevice.send_time_repo?.toString() ?? '60')                                                                                                                                                                                    
        setTempoEnvio(pulsoDevice.send_time ?? 0);
        setFator(pulsoDevice.factor?.toString() ?? '1.0');
        setReativo(pulsoDevice.reactive ?? false);

        // abre a tela de configuração do contador de pulso
        refRBSheetPulso.current?.open();        
    }

    // salvar as configurações de contador de pulso feitas - fase 1 | apaga configurações anteriores
    function SalvarPulso_Reset()
    {
        // se conectado com o servidor mqtt
        if(!isMQTTConnected()) {

            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);

            return
        } 
            
        // se existem contador de pulso configurado
        if( (probe.pulsoDevices?.length ?? 0) > 0) {
                
            // inicia loading
            setLoading(true);
            setTextoLoading(`Apagando configurações...`);   

            // envio de solicitação de reset da configuração do contador de pulso atual
            EscreveMQTT('pulse_reset');
 
            // leitura da solicitação 
            LerMQTT('pulse_reset');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);  
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
            }
        else {
            setTextoMessage('Não existe nenhum Contador de Pulsos configurado.');
            setShowMessageErro(true);
        }            
    }    

    // limpa a configuração do contador de pulsos
    function resetConfigPulso() {

        // pega os dados da probe atualizada
        const probeAtualizada = {...probe, pulsoDevices:[]};
    
        // inicializa os tipos de pulso
        setTiposPulso([...TiposPulso]);

        // atualiza informações de device da probe
        setProbe(deepCopy(probeAtualizada));
    
        setTipoPulso('');
        setPort(-1);
        setTipoMedicao(-1);
        setTipoContato(-1);
        setRepo(false);
        setTempoRepo('60');
        setTempoEnvio(-1);
        setFator('1.0');
        setReativo(false);        
    }

    // salva as configurações do contador de Pulsos na Probe
    function SalvarPulsoProbe(probe_: ProbeSettings, indice_: number)
    {
        // se conectado com o servidor mqtt
        if(!isMQTTConnected()) {

            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);

            return
        }            
        
        // se existem contador de pulso configurado
        if( (probe.pulsoDevices?.length ?? 0) > 0) {

            // atualiza configuração copia
            setProbeCopy(deepCopy({...probe_}));

            // inicia loading
            setLoading(true);

            if ( (probe_.pulsoDevices?.[indice_].type === TipoMedicaoPulso.ENERGIA) && (probe_.pulsoDevices.length > 1) ) {
                setTextoLoading(`Salvando configuração do \nPULSE 1 e PULSE 2...`);   
            }
            else {
                setTextoLoading(`Salvando configuração do ${probe_.pulsoDevices?.[indice_].pulso}...`);   
            }
                               
            // envio de solicitação para adicionar a configuração contador de pulsos
            EscreveMQTT('pulse_add', JSON.stringify({  port: (probe_.pulsoDevices?.[indice_].pulso === 'PULSO 1') ? 1 : 2, 
                                                        type: ListaFlowPulso.find(x => x.id === probe_.pulsoDevices[indice_].type).descricao,
                                                        contact: (probe_.pulsoDevices?.[indice_].type === TipoMedicaoPulso.CONTATO) 
                                                                ? ListaContactPulso.find(x => x.id === probe_.pulsoDevices[indice_].contact).descricao 
                                                                : '##',
                                                        repo: probe_.pulsoDevices?.[indice_].repo,
                                                        send_time_repo: (probe_.pulsoDevices?.[indice_].repo) 
                                                                        ? (probe_.pulsoDevices[indice_].send_time_repo) 
                                                                        :  '##',
                                                        send_time: TempoSegundosEnvioProbe[probe_.pulsoDevices[indice_].send_time]
                                                    })); 
                                                    
            // leitura da solicitação 
            LerMQTT('pulse_add');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }            
    }    

    // exclui um determinado contador de pulso
    function ExcluirPulso(probe_: ProbeSettings, pulso_atual: number)
    {
        // retorna o tipo de contador de pulso a lista
        adicionarTipoPulso(probe_.pulsoDevices?.[pulso_atual].pulso ?? '');

        // exclui o contador de pulso selecionado
        probe_.pulsoDevices?.splice(pulso_atual, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},             
                                 pulsoDevices: [...probe_.pulsoDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbe(deepCopy(probeAtualizada));
    }

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {
        
        // se timeout não alarmado
        if(!timeoutMessage)
            return
        
        // verifica se o loading esta carregando
        if(!loading)
            return;
        
        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);
    
        // timeout não alarmado
        setTimeoutMessage(false);
    
    }, [timeoutMessage]);


    useEffect(() => {
        
        // se deve iniciar leitura e ion habilitado
        if( (iniciaLeitura) && (pulsoStatus) ) {

            // texto do loading
            setTextoLoading(`Configurações do \nContador de Pulsos...`);

            // inicia loading
            setLoading(true);

            // loading vai durar 4 segundos
            setTimeout(function() {
                getConfiguracaoPulsos();
                setIniciaLeitura(false);
            }, 2000);
        }
        
    }, [iniciaLeitura, pulsoStatus]);

    useEffect(() => {
        
        // reativo foi ativado
        if( ( tipoMedicao === TipoMedicaoPulso.ENERGIA ) && (adicionar) ) {            
            setTextoMessage('Será automaticamente configurada outra porta para a grandeza reativo.')
            setShowMessageAtencao(true);
        }
        
    }, [tipoMedicao]);

    // monitora os tipos de contador de pulso disponiveis
    useEffect(() => {            
            
        // verifica se deve mostrar ou esconder o botão de adicionar
        if(tiposPulso.length > 0) {
            setShowAdicionar(true);
        }
        else {
            setShowAdicionar(false);
        }
    
    }, [tiposPulso]);

    // executa sempre que a variavel 'statusIonMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('pulsoStatusMQTT', mqtt => {                                    
        
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if(mqtt) {
            
                // altera o status do contador de pulsos                
                setPulsoStatus(prevPulso => !prevPulso);

                // pega os dados da probe atualizada
                const probeAtualizada = {...{...probe},             
                                         pulso: pulsoStatus,
                }; 

                setProbe(deepCopy(probeAtualizada));

                // finaliza o loading
                setLoading(false);            
            }
            else {
                setLoading(false);
                setTextoMessage(`Erro ao ${(pulsoStatus) ? 'desativar' : 'ativar'} o módulo \n Contador de Pulsos...`);
                setShowMessageErro(true);
            }
    
        }, );
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);    

    // executa sempre que a variavel 'configPulsoMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('pulsoConfigMQTT', mqtt => {                                    
            
            // timeout message não alarmado            
            clearTimeout(timeoutIDMessage);

            // pega os dados da probe atualizada
            const probeAtualizada = {...{...probe},
                                     pulsoDevices : [...mqtt] ,
            };                

            // atualiza estrutura
            setProbe(deepCopy(probeAtualizada));
            setProbeCopy(deepCopy(probeAtualizada));

            // verifica se existe algum contador de pulso configurado
            if(mqtt.length > 0) {

                // percorre os contadores de pulso configurados
                for(const item of mqtt) {

                    // remove o contador de pulso da lista
                    removerTipoPulso(item.pulso);
                }
            }

            // finaliza o loading
            setLoading(false);            
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);
    
    // executa sempre que a variavel 'resetPulsoMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('pulsoResetMQTT', mqtt => {                                    
                  
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if(mqtt) {

                // se esta salvando
                if(saving) {
                    // salva a configuração do contador de pulsos
                    SalvarPulsoProbe({...probe}, 0);
                }
                else {

                    // limpa configuração modbus
                    resetConfigPulso();

                    // finaliza o loading
                    setLoading(false);

                    // monta mensagem de sucesso
                    setTextoMessage(`A configuração do Contador de Pulsos foi limpa.`);
                    setShowMessageSucesso(true);
                }
            }
            else {
            
                // finaliza o loading
                setLoading(false);

                // monta mensagem de erro
                setTextoMessage(`Não foi possível limpar configuração do Contador de Pulsos`);
                setShowMessageErro(true);                    
            }

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage, saving]);

    // executa sempre que a variavel 'pulsoAddMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('pulsoAddMQTT', mqtt => {                                    
             
            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);

            // sucesso
            if ( (mqtt == 0) || (mqtt == 1)) {

                try {
                    
                    // se existe mais uma configuração do contador de pulsos e o tipo de medição é diferente de energia
                    if(( (probe.pulsoDevices?.length ?? 0) > 1) && ((savePulsoAtual + 1) < 2) && (probe.pulsoDevices?.[savePulsoAtual + 1].type !== TipoMedicaoPulso.ENERGIA) ) {

                        // salva a configuração
                        SalvarPulsoProbe({...probe}, savePulsoAtual + 1);

                        // incrementa o ion atual
                        setSavePulsoAtual(savePulsoAtual + 1);
                    }
                    else {

                        // finaliza o loading
                        setLoading(false);
    
                        setTextoMessage(`Todas as configurações do Contador de Pulsos foram salvas com sucesso.`);
                        setShowMessageSucesso(true);                    
                    }

                }
                catch(error) {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage('Falha ao salvar a configuração do Contador de Pulsos.');
                    setShowMessageErro(true);

                    return;
                }
            }
            else {

                // finaliza o loading
                setLoading(false);
                setTextoMessage((mqtt === 2 ) ? `Porta nao suportada` : `Outro módulo já está utilizando a porta solicitada.`);
                setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);
    

    // executa sempre que a variavel 'pulsoEndMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('pulsoEndMQTT', mqtt => {                                    

            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);
            
            if(mqtt) {
                
                try {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage(`Configuração do Contador de Pulso excluida com sucesso.`);
                    setShowMessageSucesso(true);

                    // exclui o contador de pulso
                    ExcluirPulso({...probe}, pulsoAtual);

                    // teste
                    // atualiza configuração copia
                    setProbeCopy(deepCopy({...probe}));
                }
                catch(error) {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage('Erro ao excluir a configuração do Contador de Pulso');
                    setShowMessageErro(true);

                    return;
                }
            }
            else {       
                
                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage(`Erro ao apagar configuração ${probe.pulsoDevices?.[pulsoAtual].pulso}`);
                    setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);    


    // monta a descrição para o tipo de pergunta
    const getDescricaoTipoPulso = (deve_adicionar: boolean, tipo_medicao: number, tipo_pulso: string) => {

        if (!deve_adicionar)             
            return `Salvar as alterações feitas no ${tipo_pulso}?`

        if (tipo_medicao === TipoMedicaoPulso.ENERGIA)
            return `Adicionar o ${tipo_pulso} e o PULSO 2 as configurações da Probe?` 
        else 
            return `Adicionar o ${tipo_pulso} as configurações da Probe?` 
                    
    }

    return (
     
        <>
            <Loading animating={loading} text={textoLoading} />

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}
            />

            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}
            />

            <MessageBoxAtencao
                visivel={showMessageAtencao} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageAtencao(false)}
            />   

            <MessageBoxPergunta
                visivel={showMessagePerguntaPulsoStatus} 
                titulo="Atenção" 
                descricao={`${(pulsoStatus) ? 'Desativar' : 'Ativar'} o módulo de Contador de Pulsos` } 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaPulsoStatus(false);
                    setStatusPulso();
                }}
                onCancel={() => setShowMessagePerguntaPulsoStatus(false)}
            />                     

            <MessageBoxPergunta
                visivel={showMessagePerguntaDel} 
                titulo="Atenção" 
                descricao={`Deseja excluir ${(probe.pulsoDevices?.[pulsoAtual]?.pulso ?? 'Pulse')} configurado?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaDel(false);                    
                    setSaving(false);
                    
                    (typeof probeCopy.pulsoDevices?.[pulsoAtual] !== 'undefined')
                        ? ExcluirPulsoProbe(deepCopy({...probe}), pulsoAtual) 
                        : ExcluirPulso(deepCopy({...probe}), pulsoAtual); 
                }}
                onCancel={() =>setShowMessagePerguntaDel(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSave} 
                titulo="Atenção" 
                descricao={ getDescricaoTipoPulso(adicionar, tipoMedicao, tipoPulso ) } 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaSave(false);
                    (adicionar) 
                        ? AdicionarPulso({...probe}, tipoPulso, port, tipoMedicao, tipoContato, repo, tempoRepo, tempoEnvio, fator, reativo) 
                        : SalvarPulso({...probe}, pulsoAtual, tipoPulso, port, tipoMedicao, tipoContato, repo, tempoRepo, tempoEnvio, fator, reativo);
                }}
                onCancel={() => setShowMessagePerguntaSave(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaEdit} 
                titulo="Atenção" 
                descricao={`Deseja editar o Contador de Pulsos configurado no ${(probe.pulsoDevices?.[pulsoAtual]?.pulso ?? 'Pulso')}?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ async () => { 
                    setShowMessagePerguntaEdit(false);
                    setAdicionar(false);
                    EditarPulso({...probe.pulsoDevices?.[pulsoAtual]});
                }}
                onCancel={() => setShowMessagePerguntaEdit(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaCleanPulso} 
                titulo="Atenção" 
                descricao={`Deseja realmente excluir todas as configurações do Contador de Pulsos?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaCleanPulso(false);
                    setSaving(false);
                    SalvarPulso_Reset();
                }}
                onCancel={() => setShowMessagePerguntaCleanPulso(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSavePulso} 
                titulo="Atenção" 
                descricao={`Deseja realmente salvar as configurações do Contador de Pulsos?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaSavePulso(false);
                    setSaving(true);
                    SalvarPulso_Reset();
                }}
                onCancel={() => setShowMessagePerguntaSavePulso(false)}
            />

            <View style={styles.containerTela}>

                {/* header */}
                <View style={{...styles.containerHeaderProbePulso}}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID="button-back">
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderProbePulso}>CONTADOR DE PULSOS</Text>
                    </View>

                </View>

                {/* botões */}
                <View style={styles.containerBotoesProbeModbus}>

                    <TouchableOpacity style={{ height:50, width:70, 
                                               justifyContent:'center', alignItems:'center',
                                               borderRadius:8, borderColor: (pulsoStatus) ? '#2E8C1F' : '#C0002B',
                                               backgroundColor: (pulsoStatus) ? '#2E8C1F' : '#C0002B'
                                            }}
                                        onPress={ () => setShowMessagePerguntaPulsoStatus(true) }
                                        testID="button-status">                                                
                        <Text style={{color:'#FFFFFF'}}>{(pulsoStatus) ? `ON` : 'OFF'}</Text>                        
                    </TouchableOpacity>

                    <View style={{height:'100%', width:'50%', justifyContent:'space-between', alignItems:'center', flexDirection:'row', gap: 20}}>
                        <TouchableOpacity onPress={ () => {
                                                            // se pulso habilitado
                                                            if(pulsoStatus) {
                                                                setShowMessagePerguntaCleanPulso(true)
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo Contador de Pulsos está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                            testID="button-clean">
                            <IconBroom color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={ () => {
                                                            // se pulso habilitado
                                                            if(pulsoStatus) {
                                                                setShowMessagePerguntaSavePulso(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo Contador de Pulsos está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                            testID="button-save">
                            <IconSave color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={() => { 
                                                            // se pulso habilitado
                                                            if(pulsoStatus) {
                                                                setIniciaLeitura(true);                                                                
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo Contador de Pulsos está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                            testID="button-refresh">
                            <IconRefresh color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoEquipamentos}>
                    <Text style={styles.textoEquipamentos}>Devices</Text>
                    {      
                        (showAdicionar) &&
                            <TouchableOpacity onPress={() => { 
                                                                // se pulso habilitado
                                                                if(pulsoStatus) {
                                                                    setAdicionar(true);
                                                                    refRBSheetTipoPulso.current?.open();
                                                                }
                                                                else {
                                                                    setTextoMessage('Módulo Contador de Pulsos está desativado.')
                                                                    setShowMessageErro(true);
                                                                }
                                                            }}
                                                testID="button-plus">
                                <IconPlus color={"#FFFFFF"} width={34} height={34}/>
                            </TouchableOpacity>
                    }
                </View>

                {/* lista de contadores de pulso */}
                <View style={styles.containerListaPulso}>

                    <FlatList style={{marginTop: 10}}
                            ItemSeparatorComponent={() => <Text> </Text>}
                            data = {probe.pulsoDevices}
                            renderItem= { ({ item }) => 
                                            <CardPulso
                                                pulso={item.pulso}                                                
                                                width={'100%'}                                                
                                                onPressDelPulso= { () => {
                                                        setPulsoAtual(probe.pulsoDevices?.findIndex(device => device.pulso === item.pulso) ?? 0);
                                                        setShowMessagePerguntaDel(true);
                                                }}
                                                onPressEditPulso={ () => {
                                                        setPulsoAtual(probe.pulsoDevices?.findIndex(device => device.pulso === item.pulso) ?? 0);
                                                        setShowMessagePerguntaEdit(true);
                                                }}
                                            />
                                        }
                    />

                </View>

                {/* seleção do tipo de dispositivo */}
                <RBSheet ref={refRBSheetTipoPulso} height={screenHeight}>
                                
                    <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#737373'}}>                                                            
                        <TouchableOpacity onPress={() => refRBSheetTipoPulso.current?.close()} testID="button-close-tipo-pulso">
                            <IconX color={"#FFFFFF"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                    {
                        // se deve selecionar a grandeza                    
                        <View style={{height:'100%', width:'100%',paddingHorizontal: 20, paddingBottom:80, justifyContent:'flex-start', alignItems:'flex-start', backgroundColor:'#737373', gap: 10}}>                    
                            <Text style={{...styles.textoStatus, color:'#45D42E'}}>Selecione o Contador de Pulsos</Text>

                            <FlatList style={{width:'100%', marginTop: 20}}
                                      ItemSeparatorComponent={() => <Divider />}              
                                      data = {tiposPulso}
                                      renderItem= { ({ item }) =>
                                        <TouchableOpacity style={{  height:60, justifyContent:'center', alignItems:'center'}}
                                                                    onPress={ async () => { 
                                                                                            setTipoPulso(item);
                                                                                            setPort( Number(ListaTiposPulso.find(x => x.descricao === item).id));
                                                                                            setTipoMedicao(-1);
                                                                                            setTipoContato(-1);
                                                                                            setRepo(false);
                                                                                            setTempoRepo('60');
                                                                                            setTempoEnvio(-1);
                                                                                            setFator('1.0');
                                                                                            setReativo(false);
                                                                                            refRBSheetTipoPulso.current?.close();
                                                                                            refRBSheetPulso.current?.open();
                                                                                          }} 
                                                            >
                                                           <Text style={styles.textoItensSelecao}>{item}</Text>
                                        </TouchableOpacity>
                                    }
                            />                            
                        </View>
                    }

                </RBSheet>

                {/* configuração do contador de pulso da probe */}
                <RBSheet ref={refRBSheetPulso} height={screenHeight}>

                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                    <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>                        
                        <Text style={styles.textoStatus}>{tipoPulso}</Text>
                    </View>

                    <TouchableOpacity onPress={() => {refRBSheetPulso.current?.close(); }} testID="button-pulso-close">
                        <IconX color={"#737373"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                <View style={{height: screenHeight - 60 - ( screenHeight * 0.53), width:'100%', marginTop:10, paddingHorizontal: 20, gap: 20}}>

                    <View style={{ height: (tipoMedicao === TipoMedicaoPulso.CONTATO) ? 140 : 60, 
                                   width:'100%', 
                                   flexDirection:'column', 
                                   justifyContent:'space-between', 
                                   alignItems:'center', 
                                   gap: 20}}>

                        <ComboBox
                            disable={false}
                            height={60} 
                            width={'100%'}
                            value={tipoMedicao}
                            onValueChange={setTipoMedicao}
                            data={ListaMedicaoPulso}
                            textoLabel={'Tipo de Medição'}
                            fontSize={16}
                            textoPlaceHolder={'Tipo de Medição'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-medicao"
                        />                        
                        
                        {
                            
                            // se tipo de medição é de contato
                            (tipoMedicao === TipoMedicaoPulso.CONTATO) &&
                                <ComboBox
                                    disable={false}
                                    height={60} 
                                    width={'100%'}
                                    value={tipoContato}
                                    onValueChange={setTipoContato}
                                    data={ListaContatoPulso}
                                    textoLabel={'Tipo de Contato'}
                                    fontSize={16}
                                    textoPlaceHolder={'Tipo de Contato'}
                                    fontSizePlaceHolder={14}
                                    textoCor={'#737373'}
                                    borderRadius={8}
                                    backgroundColor={'#FFFFFF'}
                                    testID="combo-tipo"
                                />                                
                        }
                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height: (repo) ? 120 : 40, width:'100%', flexDirection:'column', justifyContent:'space-between', alignItems:'flex-start', gap: 20}}>

                        <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>
                            <CheckBox height={25} width={25} value={repo} onValueChange={setRepo} testID="check-repo"/>
                            <Text style={styles.textoStatus}>Mensagens de Reposição</Text>
                        </View>

                        {
                            // se reposição de mensagens ativada
                            (repo) &&
                                <TextEntry 
                                    value={tempoRepo}
                                    height={60}
                                    width={'100%'}
                                    editable={true}
                                    placeHolder='Tempo de Reposição (seg)'
                                    onValueChange={setTempoRepo}
                                    fontSize={16}
                                    type={'texto'}
                                    textoLabel={'Tempo de Reposição (seg)'}
                                    backgroundColor={'#FFFFFF'}
                                    testID="input-repo"
                                />
                        }
                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height: 60, width:'100%', flexDirection:'column', justifyContent:'space-between', alignItems:'flex-start', gap: 20}}>

                        <ComboBox
                            disable={false}
                            height={60} 
                            width={'100%'}
                            value={tempoEnvio}
                            onValueChange={setTempoEnvio}
                            data={ListaTempoEnvioProbe}
                            textoLabel={'Tempo de Envio'}
                            fontSize={16}
                            textoPlaceHolder={'Tempo de Envio'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-tempo"
                        />

                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height: 60, width:'100%', flexDirection:'column', justifyContent:'space-between', alignItems:'flex-start', gap: 20}}>

                        <TextEntry 
                            value={fator}
                            height={60}
                            width={'100%'}
                            editable={true}
                            placeHolder='Fator de Multiplicação'
                            onValueChange={setFator}
                            fontSize={16}
                            type={'texto'}
                            textoLabel={'Fator de Multiplicação'}
                            backgroundColor={'#FFFFFF'}
                            testID="input-fator"
                        />

                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height: 50, width:'100%', flexDirection:'column', justifyContent:'space-between', alignItems:'flex-start', gap: 20}}>

                        {
                            (tipoMedicao === TipoMedicaoPulso.ENERGIA) 
                            ?
                                <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>
                                    <CheckBox height={25} width={25} value={true} onValueChange={null}/>
                                    <Text style={styles.textoStatus}>{(tipoPulso === TiposPulso[0]) ? `Ativo` : 'Reativo'}</Text>
                                </View>                            
                            : null
                        }
                        
                    </View>

                    <View style={{marginTop:30}}>
                        <TouchableOpacity style={styles.buttonSalvarIon} onPress={() => { setShowMessagePerguntaSave(true); }} testID="button-pulso-save">
                            <Text style = {styles.textoButtonSalvarIon}>{(adicionar) ? 'ADICIONAR' : 'SALVAR'}</Text>
                        </TouchableOpacity>
                    </View>

                </View>

            </RBSheet>                

            </View>
        </>
    )

}

export default ProbePulso;