import React, { useEffect, useRef, useState } from "react";
import RBSheet from "react-native-raw-bottom-sheet";
import { View, Text, TouchableOpacity, FlatList } from "react-native"

// estilos para a page
import {screenHeight, styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconSave from '../../assets/svg/icon_save-01.svg';
import IconRefresh from '../../assets/svg/icon_refresh.svg';
import IconPlus from '../../assets/svg/icon_plus.svg';
import IconX from '../../assets/svg/icon_x.svg';
import IconBroom from '../../assets/svg/icon_broom.svg';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import {StackTypes} from '../../routes/index'

// componentes
import Divider from "../../componentes/Divider";
import CardCodi from "../../componentes/CardCodi";
import CheckBox from "../../componentes/CheckBox";
import ComboBox from "../../componentes/ComboBox";
import Loading from "../../componentes/Loading";

// constantes
import { ListaBaudRateProbe, ListaParidadeProbe, ListaProtocoloCodi, ListaProtocoloCodi232, ListaProtocoloCodi485, TimeoutTipoConexao, TiposCODI } from "../../constantes";

// dados
import { codiSettings, ProbeSettings } from "../../data";

// modal messages
import MessageBoxErro from "../../modal/messagebox/Erro";
import MessageBoxPergunta from "../../modal/messagebox/Pergunta";
import MessageBoxSucesso from "../../modal/messagebox/Sucesso";

// mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT } from "../../services/mqtt";
import { EventRegister } from "react-native-event-listeners";


const ProbeCODI = ({route}) => {

    const deepCopy = (obj: any) => { return JSON.parse(JSON.stringify(obj)); };

    const refRBSheetTipoCodi = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetCodi = useRef<{ open: () => void, close: () => void }>(null);

    // indica o status atual do modbus
    const [codiStatus, setCodiStatus] = useState<boolean>(route.params?.probeSettings.codi);

    // inicia leitura da configuração codi
    const [iniciaLeitura, setIniciaLeitura] = useState<boolean>(true);

    // se esta salvando configuração
    const [saving, setSaving] = useState<boolean>(false);

    // definições da probe
    const [probe, setProbe] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));
    const [probeCopy, setProbeCopy] = useState<ProbeSettings>(deepCopy(route.params?.probeSettings));
    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // informações do Codi
    const [tipoCodi, setTipoCodi] = useState<string>('');
    const [tiposCodi, setTiposCodi] = useState<string[]>([...TiposCODI]);
    const [codi, setCodi] = useState<string>('');
    const [codiCount, setCodiCount] = useState<number>(0);
    const [replicar, setReplicar] = useState<boolean>(false);
    const [invertido, setInvertido] = useState<boolean>(false);
    const [protocolo, setProtocolo] = useState<number>(-1);
    const [paridade, setParidade] = useState<number>(-1);
    const [baudRate, setBaudRate] = useState<number>(-1);
    const [repo, setRepo] = useState<boolean>(false);

    // configuração da codi
    const [codiAtual, setCodiAtual] = useState<number>(-1);
    const [saveCodiAtual, setSaveCodiAtual] = useState<number>(0);    
    const [excluirTodosCodi, setExcluirTodosCodi] = useState<boolean>(false);

    // mensagens modal
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);    
    const [showMessagePerguntaDel, setShowMessagePerguntaDel] = useState<boolean>(false);
    const [showMessagePerguntaEdit, setShowMessagePerguntaEdit] = useState<boolean>(false);
    const [showMessagePerguntaSave, setShowMessagePerguntaSave] = useState<boolean>(false);
    const [showMessagePerguntaCodiStatus, setShowMessagePerguntaCodiStatus] = useState<boolean>(false);
    const [showMessagePerguntaCleanCodi, setShowMessagePerguntaCleanCodi] = useState<boolean>(false);
    const [showMessagePerguntaSaveCodi, setShowMessagePerguntaSaveCodi] = useState<boolean>(false);
    const [textoMessage, setTextoMessage] = useState<string>('');

    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');

    // se deve adicionar codi ou salvar
    const [adicionar, setAdicionar] = useState<boolean>(false);
    const [showAdicionar, setShowAdicionar] = useState<boolean>(true);

    // controle timeout loading    
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);    
    let timeoutWait: number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];

    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();
    };

    // solicitação para o MQTT
    const EscreveMQTT = (variable:string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error){}
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable:string) => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable);
        }
        catch(error) {}
    } 

    // adiciona o dispositivo Codi a estrutura
    function AdicionarCodi(probe_: ProbeSettings)
    {
        // se foi selecionado o protocolo
        if(protocolo < 0)  {
            setTextoMessage('O tipo de protocolo CODI não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        

        // pega o codi selecionado
        setCodi(tipoCodi);

        // monta um novo codi para ser inserido
        let codiDevice : codiSettings = { id_codi: (probe_.codiDevices?.length ?? 0) + 1,
                                          codi:tipoCodi, 
                                          replicar:replicar,
                                          invertido:invertido,                              
                                          protocolo:protocolo, 
                                          paridade:paridade, 
                                          baudRate:baudRate, 
                                          repo:repo 
                                        }        

        // insere um codi nas configurações da probe
        probe_.codiDevices?.push(codiDevice);
        

        // atualiza estrutura de configuração da probe        
        const probeAtualizada = {...probe_};        

        // atualiza informações de codi da probe
        setProbe({...probeAtualizada});

        // remove o tipo de configurado para as próximas configurações
        removerTipoCodi(tipoCodi);

        // fecha tela de configuração do codi
        refRBSheetCodi.current?.close();
                 
    }

    // exclui um determinado codi
    function ExcluirCodi(probe_: ProbeSettings, codi_atual: number)
    {
        // retorna o tipo de codi a lista
        adicionarTipoCodi(probe_.codiDevices?.[codi_atual].codi ?? '');

        // exclui o codi selecionado
        probe_.codiDevices?.splice(codi_atual, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},             
                                 codiDevices: [...probe_.codiDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbe({...probeAtualizada});
    }    

    // exclui um determinado codi
    function ExcluirCodiCopy(probe_: ProbeSettings, codi_atual: number)
    {
        // exclui o codi selecionado
        probe_.codiDevices?.splice(codi_atual, 1);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},
                                 codiDevices: [...probe_.codiDevices ?? []],
        };

        // atualiza informações de device da probe
        setProbeCopy(deepCopy(probeAtualizada));
    }

    // edita um determinado codi configurado
    function EditarCodi (codi_: codiSettings)
    {                
        setTipoCodi(codi_.codi ?? '');

        setReplicar(codi_.replicar ?? false);   
        setInvertido(codi_.invertido ?? false);
        setProtocolo(codi_.protocolo ?? -1);
        setParidade(codi_.paridade ?? -1);
        setBaudRate(codi_.baudRate ?? -1);
        setRepo(codi_.repo ?? false);
        
        // abre a tela de configuração do Codi
        refRBSheetCodi.current?.open();        
    }

    // salva as configurações do codi
    async function SalvarCodi(probe_: ProbeSettings, indice_codi: number) {

        // se foi selecionado o protocolo
        if(protocolo < 0)  {
            setTextoMessage('O tipo de protocolo CODI não foi selecionado.');
            setShowMessageErro(true);
            return;
        }        
        
        // pega os dados do codi configurado
        const codiAtualizado ={ ...{...probe_.codiDevices?.[indice_codi]},
                                 id_codi: indice_codi,
                                 codi: tipoCodi,
                                 replicar: replicar,
                                 invertido: invertido,                                 
                                 protocolo: protocolo,
                                 paridade: paridade,
                                 baudRate: baudRate,
                                 repo: repo,
        }; 

        // atualiza com as configurações do codi
        probe_.codiDevices?.splice(indice_codi, 1, codiAtualizado);

        // pega os dados da probe atualizada
        const probeAtualizada = {...{...probe_},
                                 codiDevices: [...probe_.codiDevices ?? []]
        };
        
        // atualiza a configuração da probe
        setProbe({...probeAtualizada});

        // incrementa proximo codi
        setCodiCount(codiCount + 1);

        // fecha tela de configuração do codi
        refRBSheetCodi.current?.close();
    }

    // leitura das configurações codi
    const getConfiguracaoCodi = async () => {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // envio de solicitação de da leituradas configurações do módulo codi
            EscreveMQTT('sys_config', 'codi');
            
            // leitura da solicitação 
            LerMQTT('sys_config');
 
            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                 // indica que a mensagem demorou muito tempo para responder
                 setTimeoutMessage(true);               
            }, timeoutWait);
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }  
   
    // se deve remover o tipo de codi a ser configurado
    const removerTipoCodi = (tipo_codi: string) => {

        // pega o indice do item a ser excluido da lista
        const indice = tiposCodi.findIndex(item => item === tipo_codi);

        // exclui o item
        tiposCodi.splice(indice, 1);
    }

    // se deve adicionar o tipo de codi a ser configurado
    const adicionarTipoCodi = (tipo_codi: string) => {
        
        // adiciona um novo item
        tiposCodi.push(tipo_codi);

        // ordena em ordem alfabetica
        setTiposCodi(tiposCodi.sort()); 

        // deve apresentar botão de adicionar configuração codi
        setShowAdicionar(true);
    }

    // seta o status do módulo codi [ativado/desativado]
    async function setStatusCodi() {

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

           // inicia loading
           setLoading(true);
           setTextoLoading(`${(codiStatus) ? 'Desativando' : 'Ativando'} o módulo CODI...`);   

            if(codiStatus) {
                // envio de solicitação de desativação do módulo codi
                EscreveMQTT('codi', 'module_end');
            }
            else {
                // envio de solicitação de ativação do módulo codi
                EscreveMQTT('codi', 'module_start');
            }
    
            // leitura da solicitação 
            LerMQTT('codi');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);
            }, timeoutWait);

           // pega o id do timeout atual
           setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    // exclui as configuração codi na probe
    function ExcluirCodiProbe(probe_: ProbeSettings, codi_atual: number, is_saving: boolean)
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            if((probe_.codiDevices?.length || 0) > 0) {

                // inicia loading
                setLoading(true);
                setTextoLoading((is_saving) ? 'Inicializando configurações CODI...' : `Excluindo configurações do ${(probe_.codiDevices?.[codi_atual].codi) ?? 'CODI'}...`);   
                
                // envio de solicitação de reset da configuração codi
                EscreveMQTT('codi_end', JSON.stringify({port:(probe_.codiDevices?.[codi_atual].id_codi ?? 0)}));
 
                // leitura da solicitação 
                LerMQTT('codi_end');

                // loading vai durar 60 segundos
                const timeout_id = setTimeout(function() {                
                    // indica que a mensagem demorou muito tempo para responder
                    setTimeoutMessage(true);               
                }, timeoutWait);

                // pega o id do timeout atual
                setTimeoutIDMessage(Number(timeout_id));                
            }
            else {
                setTextoMessage('Não existe nenhum CODI configurado.');
                setShowMessageErro(true);
            }            
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }

    // limpa as configurações codi na Probe
    function SalvarCodiProbe(probe_: ProbeSettings, indice_: number)
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`Salvando configuração do ${(probe_.codiDevices?.[indice_]?.codi ?? 'CODI')}...`);   
                            
            const portItem = (probe_.codiDevices?.[indice_]?.id_codi ?? 0);
            const protocoloItem = ListaProtocoloCodi485.find(x => x.id === probe_.codiDevices?.[indice_]?.protocolo)?.descricao.toLocaleLowerCase();
            const replicateItem = (probe_.codiDevices?.[indice_]?.replicar ?? false);
            const reverseItem = (probe_.codiDevices?.[indice_]?.invertido ?? false);
            const baudItem = Number(ListaBaudRateProbe.find(x => x.id === probe_.codiDevices?.[indice_]?.baudRate)?.descricao);
            const confItem = ListaParidadeProbe.find(x => x.id === probe_.codiDevices?.[indice_]?.paridade)?.descricao;
            const repoItem = (probe_.codiDevices?.[indice_]?.repo ?? false);

            // envio de solicitação para adicionar a configuração codi
            EscreveMQTT('codi_add', JSON.stringify({ port: portItem, protocolo: protocoloItem, replicate: replicateItem, reverse: reverseItem, baud: baudItem, conf: confItem, repo: repoItem }));
 
            // leitura da solicitação 
            LerMQTT('codi_add');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);

            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }        
    }


    useEffect(() => {
        
        // se deve iniciar leitura e codi habilitado
        if ( (iniciaLeitura) && (codiStatus) ){

            // texto do loading
            setTextoLoading('Configurações CODI...');

            // inicia loading
            setLoading(true);

            // loading vai durar 4 segundos
            setTimeout(function() {
                getConfiguracaoCodi();
                setIniciaLeitura(false);
            }, 2000);
            
        }
        
    }, [iniciaLeitura, codiStatus]);    

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {
        
        // se timeout não alarmado
        if(!timeoutMessage)
            return
        
        // verifica se o loading esta carregando
        if(!loading)
            return;
        
        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);
    
        // timeout não alarmado
        setTimeoutMessage(false);
    
    }, [timeoutMessage]);
        
    // monitora os tipos de Codi disponiveis
    useEffect(() => {            
        
        // verifica se deve mostrar ou esconder o botão de adicionar
        if(tiposCodi.length > 0) {
            setShowAdicionar(true);
        }
        else {
            setShowAdicionar(false);
        }

    }, [tiposCodi]);

    // se houve alteração do tipo de protocolo
    useEffect(() => {            
        
        // preenche o baud rate e o bit de paridade em relação ao tipo de protocolo
        switch(protocolo) {
            case 0:
                setBaudRate(0);
                setParidade(0);
            break;
            case 1: 
                setBaudRate(1);
                setParidade(0);
            break;
            case 2:
                setBaudRate(0);
                setParidade(0);
            break;
            case 3:
                setBaudRate(5);
                setParidade(0);
            break;
            case 4:
                setBaudRate(5);
                setParidade(0);
            break;            
        }

    }, [protocolo, baudRate]);

    // executa sempre que a variavel 'codiStatusMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('codiStatusMQTT', mqtt => {                                    
        
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if(mqtt) {
            
                // altera o status do codi                
                setCodiStatus(prevCodi => !prevCodi);

                // pega os dados da probe atualizada
                const probeAtualizada = {...{...probe},             
                                         codi: codiStatus,
                };

                setProbe({...probeAtualizada});

                // finaliza o loading
                setLoading(false);            
            }
            else {
                setLoading(false);
                setTextoMessage(`Erro ao ${(codiStatus) ? 'desativar' : 'ativar'} o módulo CODI...`);
                setShowMessageErro(true);
            }            
    
        }, );
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]); 
    
    // executa sempre que a variavel 'codiConfigMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('codiConfigMQTT', mqtt => {                                    
            
            // timeout message não alarmado
            //setTimeoutMessage(false);
            clearTimeout(timeoutIDMessage);

            // pega os dados da probe atualizada
            const probeAtualizada = {...{...probe},
                                     codiDevices : mqtt,
            };                

            // atualiza estrutura
            setProbe({...probeAtualizada});

            // verifica se existe algum codi configurado
            if(mqtt.length > 0) {

                // percorre os codi's configurados
                for (const item of mqtt) {
                    // remove o codi da lista
                    removerTipoCodi(item.codi);
                }
            }

            // finaliza o loading
            setLoading(false);            
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);

    // executa sempre que a variavel 'codiEndMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('codiEndMQTT', mqtt => {                                    

            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);
            
            if(mqtt) {

                try {

                    if(saving) {

                        // exclui codi da configuração auxiliar
                        ExcluirCodiCopy({...probeCopy}, (excluirTodosCodi) ? 0 : codiAtual);
                    }
                    else {

                        // exclui codi da configuração               
                        ExcluirCodi({...probe}, (excluirTodosCodi) ? 0 : codiAtual);
                    }
                }
                catch(error) {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage('Erro ao excluir a configuração CODI');
                    setShowMessageErro(true);

                    return;
                }

                // pega o total de codi configurados
                let totalCodi = (saving) ? (probeCopy.codiDevices?.length ?? 0) : (probe.codiDevices?.length ?? 0);

                // verifica se ainda existem configurações codi
                if(totalCodi > 0 && excluirTodosCodi === true) {

                    ExcluirCodiProbe((saving) ? {...probeCopy} : {...probe}, 0, saving);
                }
                else if(saving) {

                    // salva a configuração da probe
                    SalvarCodiProbe({...probe}, 0)
                }
                else {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage( (excluirTodosCodi) ? `Todas as configurações CODI foram excluidas da Probe com sucesso.` : `Configuração CODI excluida com sucesso.`);
                    setShowMessageSucesso(true);                    
                }
            }
            else {       
                
                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage(`Erro ao apagar configuração ${probe.codiDevices?.[0] ?? ''}`);
                    setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [probe, saving, codiAtual, excluirTodosCodi, timeoutMessage]);
        

    // executa sempre que a variavel 'addCodiMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('codiAddMQTT', mqtt => {                                    
             
            // timeout não alarmado            
            clearTimeout(timeoutIDMessage);

            if(mqtt) {

                try {

                    // se existe mais uma configuração codi a ser salva
                    if( (probe.codiDevices?.length ?? 0) > (saveCodiAtual + 1)) {

                        // incrementa o codi atual
                        setSaveCodiAtual(saveCodiAtual + 1);

                        // salva a configuração
                        SalvarCodiProbe({...probe}, saveCodiAtual + 1);
                    }
                    else {

                        // finaliza o loading
                        setLoading(false);
    
                        setTextoMessage(`Todas as configurações CODI foram salvas com sucesso.`);
                        setShowMessageSucesso(true);                    
                    }

                }
                catch(error) {

                    // finaliza o loading
                    setLoading(false);

                    setTextoMessage('Erro ao salvar a configuração CODI');
                    setShowMessageErro(true);

                    return;
                }
            }
            else {

                // finaliza o loading
                setLoading(false);

                setTextoMessage(`Erro ao salvar configuração CODI`);
                setShowMessageErro(true);
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage, probe, saveCodiAtual, timeoutMessage]);    
    
    return(

        <>

            <Loading animating={loading} text={textoLoading} />

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}
            />

            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaCodiStatus} 
                titulo="Atenção" 
                descricao={`${(codiStatus) ? 'Desativar' : 'Ativar'} o módulo CODI`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaCodiStatus(false);
                    setStatusCodi();
                }}
                onCancel={() => setShowMessagePerguntaCodiStatus(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaDel} 
                titulo="Atenção" 
                descricao={`Deseja excluir ${(probe.codiDevices?.[codiAtual]?.codi ?? '')} configurado?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaDel(false);                    
                    setExcluirTodosCodi(false);
                    setSaving(false);
                    ExcluirCodiProbe({...probe}, codiAtual, false);
                }}
                onCancel={() =>setShowMessagePerguntaDel(false)}
            />


            <MessageBoxPergunta
                visivel={showMessagePerguntaEdit} 
                titulo="Atenção" 
                descricao={`Deseja editar o ${(probe.codiDevices?.[codiAtual]?.codi ?? '')}?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ async () => { 
                    setShowMessagePerguntaEdit(false);
                    setAdicionar(false);
                    EditarCodi({...probe.codiDevices?.[codiAtual]});
                }}
                onCancel={() => setShowMessagePerguntaEdit(false)}
            />


            <MessageBoxPergunta
                visivel={showMessagePerguntaSave} 
                titulo="Atenção" 
                descricao={(adicionar) ? `Adicionar o ${tipoCodi} as configurações da Probe?` : `Salvar as alterações feitas no ${tipoCodi}?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaSave(false);
                    (adicionar) 
                        ? AdicionarCodi({...probe}) 
                        : SalvarCodi({...probe}, codiAtual);
                }}
                onCancel={() => setShowMessagePerguntaSave(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaCleanCodi} 
                titulo="Atenção" 
                descricao={`Deseja realmente excluir as configurações CODI?`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaCleanCodi(false);
                    setExcluirTodosCodi(true);
                    setSaving(false);
                    ExcluirCodiProbe({...probe}, 0, false);
                }}
                onCancel={() => setShowMessagePerguntaCleanCodi(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaSaveCodi} 
                titulo="Atenção" 
                descricao={`Deseja realmente salvar as configurações CODI`} 
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaSaveCodi(false);
                    setExcluirTodosCodi(true);
                    setSaveCodiAtual(0);
                    setSaving(true);
                    setProbeCopy(deepCopy(probe));
                    ExcluirCodiProbe({...probe}, 0, true);
                }}
                onCancel={() => setShowMessagePerguntaSaveCodi(false)}
            />

            <View style={styles.containerTela}>

                {/* header */}
                <View style={{...styles.containerHeaderProbeCODI}}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>
                        <TouchableOpacity onPress={() => goBack()} testID="button-back">
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderProbeModbus}>CODI</Text>
                    </View>

                </View>

                {/* botões */}
                <View style={styles.containerBotoesProbeModbus}>

                    <TouchableOpacity style={{ height:50, width:70, 
                                               justifyContent:'center', alignItems:'center',
                                               borderRadius:8, borderColor: (codiStatus) ? '#2E8C1F' : '#C0002B',
                                               backgroundColor: (codiStatus) ? '#2E8C1F' : '#C0002B'
                                            }}
                                        onPress={ () => setShowMessagePerguntaCodiStatus(true) }
                                        testID="button-status">                                                
                        <Text style={{color:'#FFFFFF'}}>{(codiStatus) ? `ON` : 'OFF'}</Text>                        
                    </TouchableOpacity>

                    <View style={{height:'100%', width:'50%', justifyContent:'space-between', alignItems:'center', flexDirection:'row', gap: 30}}>
                        <TouchableOpacity onPress={ () => {
                                                            // se codi habilitado
                                                            if(codiStatus) {
                                                                setShowMessagePerguntaCleanCodi(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo CODI está desativado.')
                                                                setShowMessageErro(true);
                                                            }                                                            
                                                        }}
                                          testID="button-clean">
                            <IconBroom color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>

                        <TouchableOpacity onPress={ () => {
                                                            // se codi habilitado
                                                            if(codiStatus) {
                                                                setShowMessagePerguntaSaveCodi(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo CODI está desativado.')
                                                                setShowMessageErro(true);
                                                            }                                                           
                                                        }}
                                          testID="button-save">
                            <IconSave color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                        
                        <TouchableOpacity onPress={() => {
                                                            // se codi habilitado
                                                            if(codiStatus) {
                                                                setIniciaLeitura(true);
                                                            }
                                                            else {
                                                                setTextoMessage('Módulo CODI está desativado.')
                                                                setShowMessageErro(true);
                                                            }
                                                        }}
                                          testID="button-refresh">
                            <IconRefresh color={"#737373"} width={34} height={34}/>
                        </TouchableOpacity>
                    </View>

                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoEquipamentos}>
                    <Text style={styles.textoEquipamentos}>Devices</Text>
                    {      
                        (showAdicionar) &&
                            <TouchableOpacity onPress={() => { 
                                                                // se codi habilitado
                                                                if(codiStatus) {
                                                                    setAdicionar(true);
                                                                    refRBSheetTipoCodi.current?.open();    
                                                                }
                                                                else {
                                                                    setTextoMessage('Módulo CODI está desativado.')
                                                                    setShowMessageErro(true);
                                                                }
                                                            }}
                                              testID="button-add">
                                <IconPlus color={"#FFFFFF"} width={34} height={34}/>
                            </TouchableOpacity>
                    }
                </View>

                {/* lista de codi */}
                <View style={styles.containerListaCodi}>

                    <FlatList style={{marginTop: 10}}
                            ItemSeparatorComponent={() => <Text> </Text>}
                            data = {probe.codiDevices ?? []}
                            renderItem= { ({ item }) => 
                                            <CardCodi
                                                codi={item.codi}                                                
                                                width={'100%'}                                                
                                                onPressDelCodi= { () => {
                                                        setCodiAtual(probe.codiDevices?.findIndex(device => device.id_codi === item.id_codi) ?? 0);
                                                        setShowMessagePerguntaDel(true);
                                                }}
                                                onPressEditCodi={ () => {
                                                        setCodiAtual(probe.codiDevices?.findIndex(device => device.id_codi === item.id_codi) ?? 0);
                                                        setShowMessagePerguntaEdit(true);
                                                }}
                                            />
                                        }
                    />

                </View>                

            </View>

            {/* seleção do tipo de dispositivo */}
            <RBSheet ref={refRBSheetTipoCodi} height={screenHeight}>
                                
                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#737373'}}>                                                            
                    <TouchableOpacity onPress={() => refRBSheetTipoCodi.current?.close()} testID='button-close'>
                        <IconX color={"#FFFFFF"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                {
                    // se deve selecionar a grandeza                    
                    <View style={{height:'100%', width:'100%',paddingHorizontal: 20, paddingBottom:80, justifyContent:'flex-start', alignItems:'flex-start', backgroundColor:'#737373', gap: 10}}>                    
                        <Text style={{...styles.textoStatus, color:'#45D42E'}}>Selecione o CODI</Text>

                        <FlatList style={{width:'100%', marginTop: 20}}
                                  ItemSeparatorComponent={() => <Divider />}              
                                  data = {tiposCodi}
                                  renderItem= { ({ item }) =>
                                    <TouchableOpacity style={{  height:60, justifyContent:'center', alignItems:'center'}}
                                                      onPress={ async () => { 
                                                                                setTipoCodi(item);
                                                                                setReplicar(false);   
                                                                                setInvertido(false);
                                                                                setProtocolo(-1);
                                                                                setParidade(-1);
                                                                                setBaudRate(-1);
                                                                                setRepo(false);
                                                                                setCodiAtual(codiCount);
                                                                                refRBSheetTipoCodi.current?.close();
                                                                                refRBSheetCodi.current?.open();
                                                                              }} 
                                                        testID='button-list'>
                                                       <Text style={styles.textoItensSelecao}>{item}</Text>
                                    </TouchableOpacity>
                                }
                        />
                        
                    </View>
                }

            </RBSheet> 

            {/* configuração codi da probe */}
            <RBSheet ref={refRBSheetCodi} height={screenHeight}>

                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                    <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>                        
                        <Text style={styles.textoStatus}>{tipoCodi}</Text>
                    </View>

                    <TouchableOpacity onPress={() => {refRBSheetCodi.current?.close(); }} testID="button-close-add">
                        <IconX color={"#737373"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

                <View style={{height: screenHeight - 60 - ( screenHeight * 0.53), width:'100%', marginTop:10, paddingHorizontal: 20, gap: 20}}>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                        <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>
                            <CheckBox height={25} width={25} value={replicar} onValueChange={setReplicar}/>
                                <Text style={styles.textoStatus}>Replicar</Text>
                        </View>

                        <View style={{flexDirection:'row', alignItems:'center', gap: 10}}>
                            <CheckBox height={25} width={25} value={invertido} onValueChange={setInvertido}/>
                                <Text style={styles.textoStatus}>Invertido</Text>
                        </View>

                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center'}}>

                        <ComboBox
                            disable={false}
                            height={'100%'} 
                            width={'35%'}
                            value={protocolo}
                            onValueChange={setProtocolo}
                            data={(tipoCodi === 'CODI RS485') ? ListaProtocoloCodi485 : (tipoCodi === 'CODI RS232') ? ListaProtocoloCodi232 : ListaProtocoloCodi}
                            textoLabel={'Protocolo'}
                            fontSize={16}
                            textoPlaceHolder={'Protocolo'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-protocolo"
                        />

                        <ComboBox
                            disable={true}
                            height={'100%'} 
                            width={'35%'}
                            value={paridade}
                            onValueChange={setParidade}
                            data={ListaParidadeProbe}
                            textoLabel={'Paridade'}
                            fontSize={16}
                            textoPlaceHolder={'Paridade'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-paridade"
                        />

                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>

                    <View style={{height:50, width:'100%', flexDirection:'row', justifyContent:'space-between'}}>
                        <ComboBox
                            disable={true}
                            height={'100%'} 
                            width={'35%'}
                            value={baudRate}
                            onValueChange={setBaudRate}
                            data={ListaBaudRateProbe}
                            textoLabel={'Baud Rate'}
                            fontSize={16}
                            textoPlaceHolder={'Baud Rate'}
                            fontSizePlaceHolder={14}
                            textoCor={'#737373'}
                            borderRadius={8}
                            backgroundColor={'#FFFFFF'}
                            testID="combo-baudrate"
                        />                        

                    </View>

                    <View style={{height:0, width:'100%', justifyContent:'center'}}>
                        <Divider />
                    </View>
                    
                    <View style={{height: 50, flexDirection:'row', alignItems:'center', gap: 10}}>
                        <CheckBox height={25} width={25} value={repo} onValueChange={setRepo}/>
                        <Text style={styles.textoStatus}>{`Ativa a transmissão de dados\nparciais minuto a minuto`}</Text>
                    </View>

                    <View style={{marginTop:30}}>
                        <TouchableOpacity style={styles.buttonSalvarCodi} onPress={() => { setShowMessagePerguntaSave(true); }} testID="button-save-add">
                            <Text style = {styles.textoButtonSalvarCodi}>{(adicionar) ? 'ADICIONAR' : 'SALVAR'}</Text>
                        </TouchableOpacity>
                    </View>
                
                </View>

            </RBSheet>            

        </>

    )
}

export default ProbeCODI;