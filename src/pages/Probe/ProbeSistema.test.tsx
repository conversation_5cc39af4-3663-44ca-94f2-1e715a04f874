import * as MQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import { fireEvent, render, screen, act, waitFor } from "@testing-library/react-native";
import ProbeSistema from "./ProbeSistema";
import React from "react";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";
import { NavigationContainer } from "@react-navigation/native";
import { HeapFreeMax, HeapTotal } from "../../constantes";
import { EventRegister } from "react-native-event-listeners";

jest.mock('sp-react-native-mqtt', () => 'MQTT');

jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                       heap: HeapFreeMax,
                       totalMemory: 100,
                       usedMemory: 20,
                    }
    },
};

const mockRouteAux = {
    params: {
      probeSettings: { ...MockProbeSettings,
                        heap: HeapTotal,
                        totalMemory: 100,
                        usedMemory: 95,                        
                      }
    },
};

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('../../services/mqtt', () => ({
    // Mock da função isMQTTConnected para retornar true
    isMQTTConnected: jest.fn(),
}));

// Mock do módulo Clipboard
jest.mock('@react-native-clipboard/clipboard', () => ({
    setString: jest.fn(),
}));

jest.mock('react-native-reanimated', () => {
    const Reanimated = require('react-native-reanimated/mock');
    Reanimated.default.call = () => {}; // Correção para alguns problemas.
    return Reanimated;
});

describe('ProbeSistema', () => {
    it('renderiza corretamente com a rota fornecida', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeSistema route={mockRoute} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente com outros valores na rota', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeSistema route={mockRouteAux} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });      
      
    it('renderiza corretamente ao copiar o id da probe', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeSistema route={mockRouteAux} />
                                    </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-copy-id'));
        });
    });

    it('renderiza corretamente ao copiar o firmware da probe', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                <ProbeSistema route={mockRouteAux} />
                            </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-copy-firmware'));
        });
    });    

    it('deve renderizar ao clicar nos botões de ajuda', async () => {
        
        const {unmount, getByTestId} = render(   <NavigationContainer>
                                            <ProbeSistema route={mockRoute} />
                                        </NavigationContainer> );

        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonReset = getByTestId('button-reset-help');
        const buttonFormat = getByTestId('button-format-help');
        const buttonScan = getByTestId('button-scan-help');
        const buttonFormatMessage = getByTestId('button-format-messages-help');
        const buttonFiles = getByTestId('button-files-help');

        expect(buttonReset).toBeTruthy();
        expect(buttonFormat).toBeTruthy();
        expect(buttonScan).toBeTruthy();
        expect(buttonFormatMessage).toBeTruthy();
        expect(buttonFiles).toBeTruthy();        

        await act( async () => {              
            fireEvent.press(buttonReset);
            fireEvent.press(buttonFormat);
            fireEvent.press(buttonScan);   
            fireEvent.press(buttonFormatMessage);   
            fireEvent.press(buttonFiles); 
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('deve renderizar ao clicar nos botões de ajuda e clicar no botão OK do modal atenção', async () => {
        
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );

        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });        

        const buttonReset = getByTestId('button-reset-help');

        expect(buttonReset).toBeTruthy();

        await act( async () => {              
            fireEvent.press(buttonReset);
        });

        const buttonOK = getByText('OK');
        
        await act( async () => {              
            fireEvent.press(buttonOK);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste        
    });


    it('deve renderizar ao clicar nos botões do sistema', async () => {
        
       const {unmount, getByTestId} = render(<NavigationContainer>
                                                <ProbeSistema route={mockRoute} />
                                            </NavigationContainer> );
        act( () => {              
            fireEvent.press(getByTestId('button-sistema'));            
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonReset = getByTestId('button-reset');
        const buttonFormat = getByTestId('button-format');
        const buttonScan = getByTestId('button-scan');
        const buttonFormatMessage = getByTestId('button-format-messages');
        const buttonFiles = getByTestId('button-files');

        expect(buttonReset).toBeTruthy();
        expect(buttonFormat).toBeTruthy();
        expect(buttonScan).toBeTruthy();
        expect(buttonFormatMessage).toBeTruthy();
        expect(buttonFiles).toBeTruthy();        

        await act( async () => {              
            fireEvent.press(buttonReset);
            fireEvent.press(buttonFormat);
            fireEvent.press(buttonScan);   
            fireEvent.press(buttonFormatMessage);   
            fireEvent.press(buttonFiles); 
        });

        unmount(); // Garante que o componente seja desmontado no final do teste                
    }); 

    it('deve renderizar ao clicar nos botões do sistema e clicar no botão OK do modal sucesso', async () => {
        
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeSistema route={mockRoute} />
                                                </NavigationContainer> );
        act(() => {
            fireEvent.press(getByTestId('button-sistema'));
        });
 
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

         const buttonReset = getByTestId('button-reset');
 
         expect(buttonReset).toBeTruthy();

         await act( async () => {              
            fireEvent.press(buttonReset);
         }); 
         
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        const buttonOK = getByText('OK');
        
        await act( async () => {              
            fireEvent.press(buttonOK);
        });         

        unmount(); // Garante que o componente seja desmontado no final do teste                
     });    
 
    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {getByTestId} = render(   <NavigationContainer>
                                            <ProbeSistema route={mockRoute}/>
                                        </NavigationContainer> );
        
        await act( () => {         
            fireEvent.press(getByTestId('button-back'));
        });
    });

    it('deve executar o fluxo esperado ao reconectar MQTT', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'reconnectMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount} = render( <NavigationContainer>
                                                    <ProbeSistema route={mockRoute} />
                                               </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('reconnectMQTT', expect.any(Function));
                
        // Chama o cleanup do listener
        unmount();
        
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');        
    });

    it('deve executar o fluxo esperado ao senão reconectar MQTT', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'reconnectMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('reconnectMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });      

    it('deve executar o fluxo esperado ao formatar a memória da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao não formatar a memória da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });  
     
    it('deve executar o fluxo esperado ao escanear 1 módulo da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'moduleScanMQTT') {
            callback(1); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('moduleScanMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao escanear 10 módulo da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'moduleScanMQTT') {
            callback(10); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('moduleScanMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao escanear zero módulos da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'moduleScanMQTT') {
            callback(0); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('moduleScanMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao formatar as mensagens da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatMessageMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatMessageMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao não formatar as mensagens da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatMessageMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatMessageMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao listar os arquivos da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'listFilesMQTT') {
            callback('{\"sysfiles\": {\"files\": {\"1\": {\"name\":\"sys\",\"size\":\"     200\",\"time\":\"04/11/2024 16:59\",\"Final\": {\"free\":815,\"total\":816,\"files\":3,\"sizefiles\":216}}}}}'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('listFilesMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao não encontrar os arquivos da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'listFilesMQTT') {
            callback(''); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('listFilesMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 
    
    it('deve executar o fluxo esperado ao formatar um arquivo da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatFileMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatFileMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    

    it('deve executar o fluxo esperado ao não formatar um arquivo da Probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'formatFileMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('formatFileMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    }); 

    it('deve renderizar ao clicar nos botões do reset e confirmar reinicialização da Probe e o mqtt não esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
         
        await act(async () => {      
                fireEvent.press(getByTestId('button-sistema'));
        });
 
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonReset = getByTestId('button-reset');
        expect(buttonReset).toBeTruthy();

        await act(async () => {      
                fireEvent.press(buttonReset);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
                fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('deve renderizar ao clicar nos botão do reset e cancelar reinicialização da Probe e o mqtt não esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(false);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
         
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });        
 
        const buttonReset = getByTestId('button-reset');
        expect(buttonReset).toBeTruthy();
 
        await act(async () => {      
                fireEvent.press(buttonReset);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');

        await act(async () => {      
                fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste        
    });    

    it('deve renderizar ao clicar nos botão do reset e cancelar reinicialização da Probe e o mqtt esta conectado', async () => {
        
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
         
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });
 
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonReset = getByTestId('button-reset');
        expect(buttonReset).toBeTruthy();
 
        await act(async () => {      
            fireEvent.press(buttonReset);
        });
        
        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');

        await act(async () => {      
                fireEvent.press(botaoNao);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('deve renderizar ao clicar no botão do format e confirmar a formatação da Probee e o mqtt esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
         
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });
 
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonFormat = getByTestId('button-format');
        expect(buttonFormat).toBeTruthy();
 
        await act(async () => {      
            fireEvent.press(buttonFormat);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });
     
    it('deve renderizar ao clicar no botão do format e confirmar a formatação da Probee e o mqtt não esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(false);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
         
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });
 
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonFormat = getByTestId('button-format');
        expect(buttonFormat).toBeTruthy();
 
        await act(async () => {      
                fireEvent.press(buttonFormat);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });

    it('deve clicar nos botão do scan e confirmar a escaneamento dos módulos da Probe e o mqtt esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );        
 
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonScan = getByTestId('button-scan');
        expect(buttonScan).toBeTruthy();
 
        await act(async () => {      
            fireEvent.press(buttonScan);
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
                fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
     });

     it('deve clicar nos botão do scan e confirmar a escaneamento dos módulos da Probe e o mqtt não esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(false);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );        
 
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonScan = getByTestId('button-scan');
        expect(buttonScan).toBeTruthy();
 
        await act(async () => {      
            fireEvent.press(buttonScan);
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
     });

     it('deve clicar no botão do formatMessage e confirmar a formatação das mensagens da Probe e o mqtt esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));            
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });        

        const buttonFormat = getByTestId('button-format-messages');
        expect(buttonFormat).toBeTruthy();

        await act(async () => {      
            fireEvent.press(buttonFormat);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
     });

     it('deve clicar no botão do formatMessage e confirmar a formatação das mensagens da Probe e o mqtt não esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(false);
                
        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));            
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        const buttonFormat = getByTestId('button-format-messages');
        expect(buttonFormat).toBeTruthy();

        await act(async () => {      
            fireEvent.press(buttonFormat);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
     });     

     it('deve clicar no botão do listFiles e confirmar a listagem dos arquivos da Probe e o mqtt esta conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(true);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));            
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });        

        const buttonFiles = getByTestId('button-files');
        expect(buttonFiles).toBeTruthy();

        await act(async () => {      
                fireEvent.press(buttonFiles);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
     }); 
     
     it('deve clicar no botão do listFiles e confirmar a listagem dos arquivos da Probe e o mqtt nao esta desconectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(MQTT, 'isMQTTConnected').mockReturnValue(false);

        const {unmount, getByTestId, getByText} = render(<NavigationContainer>
                                                            <ProbeSistema route={mockRoute} />
                                                        </NavigationContainer> );
        
        await act(async () => {      
            fireEvent.press(getByTestId('button-sistema'));            
        });

        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });        

        const buttonFiles = getByTestId('button-files');
        expect(buttonFiles).toBeTruthy();

        await act(async () => {      
            fireEvent.press(buttonFiles);
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await act(async () => {      
            fireEvent.press(botaoSim);
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
    });     

    
    it('deve executar o fluxo esperado ao listar os arquivos da Probe e apresentar lista de arquivos e fechar lista', async () => {
        
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
        
        mockedAddEventListener.mockImplementation((eventName, callback) => {
            if (eventName === 'listFilesMQTT') {
            callback('{\"sysfiles\": {\"files\": {\"1\": {\"name\":\"sys\",\"size\":\"     200\",\"time\":\"04/11/2024 16:59\",\"Final\": {\"free\":815,\"total\":816,\"files\":3,\"sizefiles\":216}}}}}'); // Simula o callback de reconexão
            return 'mockEventListenerId';
            }
        });
                
        const {getByTestId } = render( <NavigationContainer>
                                            <ProbeSistema route={mockRoute} />
                                        </NavigationContainer> );
        
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('listFilesMQTT', expect.any(Function));
                
        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByTestId('button-close-files');        

        await act(async () => {      
            await fireEvent.press(botao);
        });
    });   

    it('deve executar o fluxo esperado ao reconectar MQTT e clica no botão OK do modal sucesso', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'reconnectMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
        
        const { getByText} = render( <NavigationContainer>
                                        <ProbeSistema route={mockRoute} />
                                     </NavigationContainer> );

        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('reconnectMQTT', expect.any(Function));
                        
        // Simula o clique no botão "OK" dentro do modal
        const botao = getByText('OK');        

        await act(async () => {      
            await fireEvent.press(botao);
        });
    });    


});