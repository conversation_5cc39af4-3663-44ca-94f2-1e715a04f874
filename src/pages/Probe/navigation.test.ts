// __tests__/goToKhomp.test.ts
import { CommonActions } from '@react-navigation/native';
import { ProbeSettings } from '../../data';
import { goToKhomp } from './navigation';
import { MockProbeSettings } from '../../../__mocks__/ProbeSettingsMock';

jest.mock('@react-navigation/native', () => ({
  CommonActions: {
    navigate: jest.fn(),
  },
}));

describe('goToKhomp', () => {
  it('deve navegar para a tela Khomp com os parâmetros corretos', () => {
    const mockDispatch = jest.fn();

    const mockNavigation = {
      dispatch: mockDispatch,
    };

    const mockProbe: ProbeSettings = {...MockProbeSettings() };

    const expectedParams = {
      name: 'Khomp',
      params: { probeSettings: mockProbe },
    };

    // Simula o retorno do CommonActions.navigate
    const navigateAction = { type: 'NAVIGATE', payload: expectedParams };
    (CommonActions.navigate as jest.Mock).mockReturnValue(navigateAction);

    // Chama a função
    goToKhomp(mockNavigation as any, mockProbe);

    // Verifica se CommonActions.navigate foi chamado corretamente
    expect(CommonActions.navigate).toHaveBeenCalledWith(expectedParams);

    // Verifica se navigation.dispatch foi chamado com o retorno de navigate
    expect(mockDispatch).toHaveBeenCalledWith(navigateAction);
  });
});
