import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import PassoID from './ProbeID';

// Mock de navegação
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    dispatch: jest.fn(),
  }),
}));

//jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');

describe('PassoID Component', () => {
  it('deve renderizar corretamente', () => {
    const { getByText, getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    expect(getByText('VINCULAR PROBE')).toBeTruthy();
    expect(getByTestId('button-back')).toBeTruthy();
    expect(getByTestId('button-flash')).toBeTruthy();
    expect(getByTestId('button-camera')).toBeTruthy();
    expect(getByTestId('button-input')).toBeTruthy();
  });

  it('deve alternar o flash da câmera ao clicar no botão', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const flashButton = getByTestId('button-flash');
    fireEvent.press(flashButton);
    fireEvent.press(flashButton);
  });

  it('deve alternar a câmera frontal ao clicar no botão', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const cameraButton = getByTestId('button-camera');
    fireEvent.press(cameraButton);
  });

  it('deve permitir digitar um código e clicar no botão continuar', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const inputButton = getByTestId('button-input');
    fireEvent.press(inputButton);

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
  });

  it('deve voltar ao clicar no botão de voltar', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-back');
    fireEvent.press(backButton);
  });

  it('deve voltar ao clicar no botão de codigo OK', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKL' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-code');
    fireEvent.press(backButton);
  });
  
  it('deve voltar ao clicar no botão de codigo com mais de 16 digitos', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKLMNOPQ' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);

    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, 'ABCDEFGHIJKLMNOPQ');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);

    const OhButton = getByText('OK');
    fireEvent.press(OhButton);    
  });

  it('deve voltar ao clicar no botão de codigo sem nenhum ID digitado', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKLMNO' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);


    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, '');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
    
    const OhButton = getByText('OK');
    fireEvent.press(OhButton);    
  });
  
  it('deve voltar ao clicar no botão de codigo com mais de 12 digitos', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKL' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);


    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, 'ABCDEFGHIJKL');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
    });  
  
});
