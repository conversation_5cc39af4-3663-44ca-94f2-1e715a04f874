import * as spyMQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import { act, fireEvent, render, screen, waitFor } from "@testing-library/react-native";
import ProbeRede from "./ProbeRede";
import { NavigationContainer } from "@react-navigation/native";
import { MockProbeSettings } from "../../../__mocks__/ProbeSettingsMock";
import React from "react";
import { EventRegister } from "react-native-event-listeners";
import { MockApnSettings } from "../../../__mocks__/ApnSettingsMock";

jest.mock('sp-react-native-mqtt', () => 'MQTT');

jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
}));

jest.useFakeTimers();

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

// Mock do módulo Clipboard
jest.mock('@react-native-clipboard/clipboard', () => ({
    setString: jest.fn(),
}));

const mockRoute = {
    params: {
      probeSettings: { ...MockProbeSettings,
                    }
    },
};

const mockRouteWifi = {
    params: {
      probeSettings: { ...MockProbeSettings,
                      tipoConexao: 0,
                      wifiSinal: -40,
                    }
    },
};

const mockRouteGsm = {
    params: {
      probeSettings: { ...MockProbeSettings,
                      tipoConexao: 1,
                      gsmSinal: 23,
                    }
    },
};

const mockRouteEth = {
    params: {
      probeSettings: { ...MockProbeSettings,
                      tipoConexao: 2,
                      ethernetDHCP: 1,                      
                    }
    },
};

const mockRouteEthFixo = {
    params: {
      probeSettings: { ...MockProbeSettings,
                      tipoConexao: 2,
                      ethernetDHCP: 0,                      
                    }
    },
};

describe('ProbeRede', () => {
    it('renderiza corretamente com a rota fornecida', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeRede route={mockRoute} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('deve renderizar ao clicar nos botão voltar', async () => {
        
        const {getByTestId} = render(   <NavigationContainer>
                                            <ProbeRede route={mockRoute}/>
                                        </NavigationContainer> );
        
        await act( () => {         
            fireEvent.press(getByTestId('button-back'));
        });
    });
    
    it('renderiza corretamente com a rota fornecida é wifi', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeRede route={mockRouteWifi} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente ao copiar as informações da rede wifi', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteWifi} />
                                    </NavigationContainer> );
        
        await act(async () => {      
            await fireEvent.press(getByTestId('button-copy-wifi'));
        });
    });    
    
    it('renderiza corretamente com a rota fornecida é gsm', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeRede route={mockRouteGsm} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });
    
    it('renderiza corretamente ao copiar as informações da rede gsm', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
        
        await act(async () => {      
            await fireEvent.press(getByTestId('button-copy-imei'));
            await fireEvent.press(getByTestId('button-copy-imsi'));
            await fireEvent.press(getByTestId('button-copy-iccid'));
            await fireEvent.press(getByTestId('button-copy-operadora'));
            await fireEvent.press(getByTestId('button-copy-sinal'));
            await fireEvent.press(getByTestId('button-copy-apn'));
            await fireEvent.press(getByTestId('button-copy-ip'));
        });
    });

    it('renderiza corretamente com a rota fornecida é eth', () => {
    
        const {} = render(  <NavigationContainer>
                                <ProbeRede route={mockRouteEth} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });
    






    /*
    it('renderiza corretamente a apresentação das informações de ip quando a rota fornecida é eth', () => {
           
       // Cria um spy para o React.useState
        const useStateSpy = jest.spyOn(React, 'useState');;

        const {} = render(  <NavigationContainer>
                                <ProbeRede route={mockRouteEth} />
                            </NavigationContainer> );
        
        const modoAtualInicial = useStateSpy.mock.calls[38];
        expect(modoAtualInicial).toBe(0);
    
        useStateSpy.mockRestore();        
        //expect(screen).toBeTruthy();
    });
    */







    it('renderiza corretamente ao copiar as informações da rede ethernet', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteEth} />
                                    </NavigationContainer> );
        
        await act(async () => {      
            await fireEvent.press(getByTestId('button-copy-eth'));
        });
    });    
    
    it('renderiza corretamente ao clicar no botão de voltar a opção de fabrica e salvar configuração wifi', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeRede route={mockRouteWifi} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-factory'));
                fireEvent.press(getByTestId('button-save-wifi'));
            });
        });            
    });
    
    it('renderiza corretamente ao clicar no botão de alterar configuração eth e refrescar tela', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ProbeRede route={mockRouteEth} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-eth'));
                fireEvent.press(getByTestId('button-refresh'));                
            });
        });            
    }); 
        
    it('deve executar o fluxo esperado ao salvar as configurações do eth', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'saveEthernetMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteEth} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('saveEthernetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });     

    it('deve executar o fluxo esperado ao não salvar as configurações do eth', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'saveEthernetMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteEth} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('saveEthernetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    
    it('deve executar o fluxo esperado ao salvar as configurações do wifi', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'saveWifiMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteWifi} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('saveWifiMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });     

    it('deve executar o fluxo esperado ao não salvar as configurações do wifi', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'saveWifiMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteWifi} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('saveWifiMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler as configurações do eth', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'ethernetMQTT') {
            callback(MockProbeSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteEth} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('ethernetMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler as configurações do gsm', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'gsmMQTT') {
            callback(MockProbeSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('gsmMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler as configurações do wifi', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'wifiMQTT') {
            callback(MockProbeSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteWifi} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('wifiMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler as configurações da apn', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnConfigMQTT') {
            callback(MockApnSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao ler as configurações da apn e deleta uma apn configurada e cancela', async () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnConfigMQTT') {
            callback(MockApnSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('delete-button'));                
            });
        }); 

        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
           
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    
    it('deve executar o fluxo esperado ao ler as configurações da apn e deleta uma apn configurada, mas mqtt desconectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnConfigMQTT') {
            callback(MockApnSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('delete-button'));                
            });
        }); 

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
           
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao ler as configurações da apn e deleta uma apn configurada e mqtt conectado', async () => {
        
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnConfigMQTT') {
            callback(MockApnSettings); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount, getByTestId, getByText } = render( <NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('delete-button'));                
            });
        }); 

        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });        
           
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnConfigMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    






    it('deve executar o fluxo esperado ao adicionar uma configuração da apn e for false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnAddMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar uma configuração da apn e for true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnAddMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnAddMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });

    it('deve executar o fluxo esperado ao adicionar uma configuração da apn e for false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnRemoveMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnRemoveMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao adicionar uma configuração da apn e for true', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'apnRemoveMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('apnRemoveMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
    
    it('renderiza corretamente ao clicar no botão de alterar configuração eth e confirmar e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteEth} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-eth'));                                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão de alterar configuração eth e confirmar e mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteEth} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-eth'));                                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });    
    
    it('renderiza corretamente ao clicar no botão de alterar configuração eth e cancelar', async () => {
    
        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteEth} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-eth'));                                
            });
        });

        // Simula o clique no botão "Não" dentro do modal
        const botaoNao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoNao);
            });
        });        
    }); 
    
    
    it('renderiza corretamente ao clicar no botão de alterar configuração wifi e confirmar e mqtt conectado', async () => {
            
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);
        
        // Cleanup
        clearTimeout(100);

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteWifi} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-save-wifi'));                                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });

    it('renderiza corretamente ao clicar no botão de voltar a opção de fabrica e salvar configuração wifi, mqtt conectado mas cancela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(  <NavigationContainer>
                                                    <ProbeRede route={mockRouteWifi} />
                                                  </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-factory'));                
            });
        }); 
        
        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });                  
    });    

    it('renderiza corretamente ao clicar no botão de voltar a opção de fabrica e salvar configuração wifi e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(  <NavigationContainer>
                                                    <ProbeRede route={mockRouteWifi} />
                                                  </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-factory'));                
            });
        }); 
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });                  
    });
    
    it('renderiza corretamente ao clicar no botão de voltar a opção de fabrica e salvar configuração wifi e mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);
    
        const {getByTestId, getByText} = render(  <NavigationContainer>
                                                    <ProbeRede route={mockRouteWifi} />
                                                  </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-factory'));                
            });
        }); 
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });         
    }); 
    
    it('renderiza corretamente ao clicar no botão de refresh e mqtt conectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh'));                
            });
        }); 
    });
    
    it('renderiza corretamente ao alterar configuração eth de dhcp para fixo e mqtt conectado', async () => {

        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteEthFixo} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();


        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-eth'));                                
            });
        });

        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });        
    });


    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN e fecha', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-close-apn'));                
            });
        });        
    });

    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN e refesca tela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-refresh-apn'));                
            });
        });        
    })
    
    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN e cancela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId} = render(<NavigationContainer>
                                        <ProbeRede route={mockRouteGsm} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-close-config-apn'));                
            });
        });        
    })
    
    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN, adiciona uma apn e cancela', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });
        
        // Simula o clique no botão "Não" dentro do modal
        const botao = getByText('Não');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });         
    })
    
    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN e adiciona uma apn', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText, getAllByText} = render(<NavigationContainer>
                                                                <ProbeRede route={mockRouteGsm} />
                                                              </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        });
        
        // Simula o clique no botão "OK" dentro do modal
        const botaoOK = getAllByText('OK');
        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoOK[0]);
                fireEvent.press(botaoOK[1]);
            });
        });        
        
    });    

    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN, adiciona uma apn e apn valida', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });

        const input_apn = getByTestId('input-apn');
  
        fireEvent.press(input_apn);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_apn, 'iot.teste.com.br');

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });
        
        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        }); 
    });
    
    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN, adiciona uma apn e user valido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });

        const input_apn = getByTestId('input-apn');
  
        fireEvent.press(input_apn);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_apn, 'iot.teste.com.br');

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });

        const input_user = getByTestId('input-user-apn');
  
        fireEvent.press(input_user);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_user, 'teste');

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });        
        
        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        }); 
    });
    
    it('renderiza corretamente com mqtt conectado e clica no botão de adicionar APN, adiciona uma apn e senha valido', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });

        const input_apn = getByTestId('input-apn');
  
        fireEvent.press(input_apn);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_apn, 'iot.teste.com.br');

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });

        const input_user = getByTestId('input-user-apn');
  
        fireEvent.press(input_user);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_user, 'teste');        

        const input_pass = getByTestId('input-pass-apn');
  
        fireEvent.press(input_pass);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_pass, 'teste');        

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });        
        
        // Simula o clique no botão "Sim" dentro do modal
        const botao = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botao);
            });
        }); 
    });
    
    it('renderiza corretamente ao clicar no botão de adicionar APN, adiciona uma apn, senha valida mas mqtt desconectado', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        // Fast-forward time
        jest.advanceTimersByTime(100);

        // Cleanup
        clearTimeout(100);        

        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <ProbeRede route={mockRouteGsm} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn'));                
            });
        });
        
        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-plus-apn'));                
            });
        });

        const input_apn = getByTestId('input-apn');
  
        fireEvent.press(input_apn);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_apn, 'iot.teste.com.br');

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });

        const input_user = getByTestId('input-user-apn');
  
        fireEvent.press(input_user);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_user, 'teste');        

        const input_pass = getByTestId('input-pass-apn');
  
        fireEvent.press(input_pass);
    
        // Simula a digitação do texto"
        fireEvent.changeText(input_pass, 'teste');        

        await waitFor(async () => {         
            await act(async () => {      
                fireEvent.press(getByTestId('button-apn-save'));                
            });
        });        
        
        // Simula o clique no botão "Sim" dentro do modal
        const botaoSim = getByText('Sim');        

        await waitFor(async () => {      
            await act(async () => {      
                fireEvent.press(botaoSim);
            });
        });
    });    
});
