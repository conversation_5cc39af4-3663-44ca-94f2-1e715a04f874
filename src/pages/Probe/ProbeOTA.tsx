import React, { useEffect, useRef, useState } from "react";
import { FlatList, Text, TouchableOpacity, View } from "react-native";
import { EventRegister } from "react-native-event-listeners";
import RBSheet from "react-native-raw-bottom-sheet";

// estilos para a page
import { screenHeight, styles } from './layout';

// navegação de paginas
import { useNavigation } from "@react-navigation/native";

// rotas drawer de navegação
import { StackTypes } from '../../routes/index';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconReverse from '../../assets/svg/icon_reverse-left.svg';
import IconX from '../../assets/svg/icon_x.svg';

// modal messages
import MessageBoxAtencao from "../../modal/messagebox/Atencao";
import MessageBoxErro from "../../modal/messagebox/Erro";
import MessageBox<PERSON>ergunta from "../../modal/messagebox/Pergunta";
import <PERSON><PERSON>oxSucesso from "../../modal/messagebox/Sucesso";

// componentes
import ComboBox from "../../componentes/ComboBox";
import Divider from "../../componentes/Divider";
import Loading from "../../componentes/Loading";
import TextEntry from "../../componentes/TextEntry";

// constantes
import { ListaFirmwareModo, TimeoutTipoConexao } from "../../constantes";

// serviços api
import apiZordonFirmware from "../../services/apiZordonFirmware";

// funções do sistema
import { isNumber, isObject } from "../../funcoes";

// data
import { ProbeSettings } from "../../data";

// mqtt
import { isMQTTConnected, publishMQTT, subscribeMQTT } from "../../services/mqtt";

// armazena dados no celular
import { KEY, setStorageString } from "../../storages";


const ProbeOTA = ({route}) => {

    const refRBSheetFirmwareProd = useRef<{ open: () => void, close: () => void }>(null);
    const refRBSheetFirmwareHmg = useRef<{ open: () => void, close: () => void }>(null);

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // definições da probe
    const [probe, setProbe] = useState<ProbeSettings>({...route.params?.probeSettings});

    // indica o status atual do ota
    const [otaStatus, setOTAStatus] = useState<boolean>(route.params?.probeSettings.ota);

    // informações da probe
    const [firmwareAtual, setFirmwareAtual] = useState<string>(route.params?.probeSettings.firmware);
    const [firmwareSelecionado, setFirmwareSelecionado] = useState<string>(route.params?.probeSettings.firmware);
    const [firmwareModo, setFirmwareModo] = useState<number>(-1);
    const [firmwareTimeout, setFirmwareTimeout] = useState<string>('');

    // informações de firmware    
    const [firmwaresProd, setFirmwaresProd] = useState<string[]>([]);
    const [firmwaresHmg, setFirmwaresHmg] = useState<string[]>([]);

    // mensagens modal
    const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
    const [showMessageSucesso, setShowMessageSucesso] = useState<boolean>(false);    
    const [showMessageAtencao, setShowMessageAtencao] = useState<boolean>(false);    
    const [showMessagePerguntaRollback, setShowMessagePerguntaRollback] = useState<boolean>(false);
    const [showMessagePerguntaAtualizar, setShowMessagePerguntaAtualizar] = useState<boolean>(false);    
    const [textoMessage, setTextoMessage] = useState<string>('');
    
    // loading
    const [loading, setLoading] = useState<boolean>(false);
    const [textoLoading, setTextoLoading] = useState<string>('');

    // controle timeout loading
    let timeoutWait: number = TimeoutTipoConexao[route.params?.probeSettings.tipoConexao];
    const [timeoutMessage, setTimeoutMessage] = useState<boolean>(false);
    const [timeoutIDMessage, setTimeoutIDMessage] = useState<number>(0);    

    // navegar de volta
    const goBack = () => {
           
        navigation.goBack();
    };

   // solicitação para o MQTT
   const EscreveMQTT = (variable:string, auxiliar: string = '') => {

        try {
            // envia a pergunta
            publishMQTT(route.params?.probeSettings.id, variable, auxiliar);
        }
        catch(error){}
    }

    // resposta da solicitação do MQTT
    const LerMQTT = (variable:string) => {

        try {
            // pega resposta
            subscribeMQTT(route.params?.probeSettings.id, variable);
        }
        catch(error) {}
    }

    // retorna os firmwares de produção
    async function getFirmwaresProd(data: string, firmware_atual: string) {

        let stringSemComentarios = '';

        // verifica se string é um objeto
        if(isObject(data)) {
            // remove os comentarios do json, para evitar valores indefinidos
            stringSemComentarios = JSON.stringify(data).replace(/\/\*[\s\S]*?\*\//g, '');
        }
        else {
            // remove os comentarios do json, para evitar valores indefinidos
            stringSemComentarios = data.toString().replace(/\/\*[\s\S]*?\*\//g, '');
        }
        
        // parsing 
        const json = JSON.parse(stringSemComentarios);

        // pega a versão do hardware
        let versao = firmware_atual.slice(0, 4);

        // se existe informações de firmware
        if (json.firmware) {
            return json.firmware[versao].prod;
        }
    }
    
    // retorna os firmwares de homologação
    async function getFirmwaresHmg(data: string, firmware_atual: string) {

        let stringSemComentarios = '';

        // verifica se string é um objeto
        if(isObject(data)) {
            // remove os comentarios do json, para evitar valores indefinidos
            stringSemComentarios = JSON.stringify(data).replace(/\/\*[\s\S]*?\*\//g, '');
        }
        else {
            // remove os comentarios do json, para evitar valores indefinidos
            stringSemComentarios = data.toString().replace(/\/\*[\s\S]*?\*\//g, '');
        }        
        
        // parsing 
        const json = JSON.parse(stringSemComentarios);

        // pega a versão do hardware
        let versao = firmware_atual.slice(0, 4);

        // se existe informações de firmware
        if (json.firmware) {
            return json.firmware[versao].hmg;
        }
    }

    // le as informações de firmwares disponíveis
    const getFirmwareJson = async () => {

        // autenticação
        await apiZordonFirmware().then(async (result) => {            

            // se resposta OK
            if (result.status === 200) {
  
                // pega os firmwares de produção
                setFirmwaresProd(await getFirmwaresProd(result.data, firmwareAtual));

                // pega os firmwares de homologação
                setFirmwaresHmg(await getFirmwaresHmg(result.data, firmwareAtual));

                // finaliza loading
                setLoading(false);                            
            }
            else{
          
                // apresenta mensagem de erro
                setTextoMessage(`Não foi possível retornar os firmwares disponíveis.`);
                setShowMessageErro(true);      
  
                // finaliza loading
                setLoading(false);
            }
  
        }).catch(error => {
        
            setTextoMessage(`Não foi possível ler os firmwares disponíveis para a Probe .\nVerificar disponibilidade do servidor.`);            
            setShowMessageErro(true);      
  
            // finaliza loading
            setLoading(false);
        })
    }

    const AtualizarFirmware = () => {

        // verifica se esta tentando atualizar a mesma versão
        if(firmwareAtual === firmwareSelecionado) {
            setTextoMessage('O Firmware selecionado é o mesmo que o atual.');
            setShowMessageErro(true);

            return;
        }

        // se foi selecionado o baud rate
        if(firmwareModo < 0)  {
            setTextoMessage('O modo de atualização do Firmware não foi selecionado.');
            setShowMessageErro(true);
            return;
        }

        // se foi digitado o timeout
        if(firmwareTimeout.length <= 0){
            setTextoMessage('O Timeout não foi inserido.');
            setShowMessageErro(true);
            return;
        }

        // se foi digitado somente números
        if(!isNumber(firmwareTimeout)){
            setTextoMessage('O campo Timeout somente permite números.');
            setShowMessageErro(true);
            return;
        }

        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`Atualizando Firmware...`);   

            // armazena as informações no app
            setStorageString(KEY.probeFirmware, firmwareSelecionado);
            
            // monta url
            const url = `https://zordon:<EMAIL>/${firmwareSelecionado}`;

            // envio de solicitação de atualização de firmware
            EscreveMQTT('ota',  JSON.stringify({url:url, timeout_s:Number(firmwareTimeout) * 60, modo: firmwareModo}));
 
            // leitura da solicitação 
            LerMQTT('ota');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);  
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // retorna a versão de firmware anterior da probe
    function RollbackFirmware()
    {
        // se conectado com o servidor mqtt
        if(isMQTTConnected()) {

            // inicia loading
            setLoading(true);
            setTextoLoading(`Carregando Firmware anterior...`);   

            // envio de solicitação de rollback (retorna versão anterior do firmware instalada)
            EscreveMQTT('system_rollback');
 
            // leitura da solicitação 
            LerMQTT('system_rollback');

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);  
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));
        }
        else {
            // inicia loading
            setLoading(false);

            setTextoMessage('Servidor MQTT desconectado.');
            setShowMessageErro(true);
        }
    }

    // executa sempre que a variavel timeout das mensagens alterada
    useEffect(() => {

        // se timeout não alarmado
        if(!timeoutMessage)
            return

        // verifica se o loading esta carregando
        if(!loading)
            return;

        setLoading(false);
        setTextoMessage('Probe demorou muito tempo para responder.');
        setShowMessageErro(true);

        // timeout não alarmado
        setTimeoutMessage(false);

    }, [timeoutMessage]);

    useEffect(() => {

        // texto do loading
        setTextoLoading('Firmwares disponíveis...');

        // inicia loading
        setLoading(true);

        // loading vai durar 4 segundos
        setTimeout(async function() {
            
            await getFirmwareJson(); 
            
        }, 4000);
        
    }, []);

    // executa sempre que a variavel 'otaRollbackMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('otaRollbackMQTT', mqtt => {
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);
                            
            // altera o firmware atual
            setFirmwareAtual(mqtt);

            // pega os dados da probe atualizada
            const probeAtualizada = {...probe,             
                                     firmware: mqtt,
            };                                

            // atualiza probe na configuração
            setProbe(probeAtualizada);

            // monta texto de sucesso
            setTextoMessage(`Firmware ${mqtt} reconfigurado.`);
            setShowMessageSucesso(true);

            // finaliza o loading
            setLoading(false);                        
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage]);
    
    // executa sempre que a variavel 'otaUpdateMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('otaUpdateMQTT', mqtt => {
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            if(mqtt) {
                
                // altera o firmware atual
                setFirmwareAtual(firmwareSelecionado);

                // pega os dados da probe atualizada
                const probeAtualizada = {...probe,             
                                         firmware: firmwareSelecionado,
                };                                

                // atualiza probe na configuração
                setProbe(probeAtualizada);

                // monta texto de sucesso
                setTextoMessage(`Probe atualizada para o Firmware ${firmwareSelecionado}.`);
                setShowMessageSucesso(true);

                // finaliza o loading
                setLoading(false);            
            }
        
        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, [timeoutIDMessage, firmwareSelecionado]);
        
    // executa sempre que a variavel 'OTAupdatingMQTT' for ouvida
    useEffect(() => {
        const eventListener: any = EventRegister.addEventListener('OTAupdatingMQTT', mqtt => {
            
            // timeout message não alarmado
            clearTimeout(timeoutIDMessage);

            // texto do loading
            setTextoLoading(`Atualizando Firmware...\n${mqtt}`);

            // loading vai durar 60 segundos
            const timeout_id = setTimeout(function() {                
                // indica que a mensagem demorou muito tempo para responder
                setTimeoutMessage(true);               
            }, timeoutWait);  
            
            // pega o id do timeout atual
            setTimeoutIDMessage(Number(timeout_id));            

        }, );    
        return () => { EventRegister.removeEventListener(eventListener); };
    }, []);
    

    return(

        <>
            <Loading animating={loading} text={textoLoading} />        

            <MessageBoxAtencao
                visivel={showMessageAtencao} 
                titulo="Atenção" 
                descricao={'Módulo sempre ativo.'} 
                textoBotao="OK" 
                onFechar={() => setShowMessageAtencao(false)}
            />

            <MessageBoxErro
                visivel={showMessageErro} 
                titulo="Atenção" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageErro(false)}
            />

            <MessageBoxSucesso
                visivel={showMessageSucesso} 
                titulo="Sucesso" 
                descricao={textoMessage} 
                textoBotao="OK" 
                onFechar={() => setShowMessageSucesso(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaRollback} 
                titulo="Atenção" 
                descricao={`A Probe retornará a versão anterior do firmware instalado. Deseja continuar?`}
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaRollback(false);
                    RollbackFirmware();
                }}
                onCancel={() =>setShowMessagePerguntaRollback(false)}
            />

            <MessageBoxPergunta
                visivel={showMessagePerguntaAtualizar} 
                titulo="Atenção" 
                descricao={`A Probe será atualizada para a versão ${firmwareSelecionado}. Deseja continuar?`}
                textoBotaoOK="Sim" 
                textoBotaoCancelar='Não'
                onOK={ () => { 
                    setShowMessagePerguntaAtualizar(false);
                    AtualizarFirmware();
                }}
                onCancel={() =>setShowMessagePerguntaAtualizar(false)}
            />

            {/* header */}
            <View style={{...styles.containerHeaderProbeOTA}}>

                {/* botão voltar */}
                <View style={styles.containerBotaoVoltar}>
                    <TouchableOpacity onPress={() => goBack()} testID="button-back">
                        <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                    </TouchableOpacity>    
                </View>

                {/* texto cabeçalho */}
                <View style={styles.containerTextoHeader}>
                    <Text style={styles.textoHeaderProbeOTA}>ATUALIZAÇÃO DE FIRMWARE</Text>
                </View>

            </View>

            {/* botões */}
            <View style={styles.containerBotoesProbeOTA}>

                <TouchableOpacity style={{ height:50, width:70, 
                                               justifyContent:'center', alignItems:'center',
                                               borderRadius:8, borderColor: (otaStatus) ? '#2E8C1F' : '#C0002B',
                                               backgroundColor: (otaStatus) ? '#2E8C1F' : '#C0002B'
                                        }}
                                    onPress={ () => setShowMessageAtencao(true) }
                                    testID="button-status">                                                
                    <Text style={{color:'#FFFFFF'}}>{`ON`}</Text>                        
                </TouchableOpacity>

                <View style={{height:'100%', width:'50%', justifyContent:'flex-end', alignItems:'center', flexDirection:'row', gap: 30}}>                        
                    <TouchableOpacity onPress={ () => setShowMessagePerguntaRollback(true) }
                                      testID="button-rollback">
                        <IconReverse color={"#737373"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>

            </View>

            {/* texto cabeçalho */}
            <View style={styles.containerTextoFirmwareAtual}>
                <Text style={styles.textoFirmwareAtualTitulo}>Firmware Atual</Text>
                <Text style={styles.textoFirmwareAtual}>{firmwareAtual}</Text>
            </View> 

            {/* configuração */}
            <View style={{backgroundColor:'#F2F2F2', height: screenHeight - 60 - ( screenHeight * 0.53), width:'100%', marginTop:20, paddingHorizontal: 20, gap: 20}}>

                <ComboBox
                    disable={false}
                    height={60} 
                    width={'100%'}
                    value={firmwareModo}
                    onValueChange={setFirmwareModo}
                    data={ListaFirmwareModo}
                    textoLabel={'Modo de Atualização'}
                    fontSize={18}
                    textoPlaceHolder={'Modo de Atualização'}
                    fontSizePlaceHolder={16}
                    textoCor={'#737373'}
                    borderRadius={8}
                    backgroundColor={'#F2F2F2'}
                    testID="combo-modo"
                />

                <View style={{height:0, width:'100%', justifyContent:'center'}}>
                    <Divider color={'#D6D6D6'} />
                </View>

                <TextEntry 
                    value={firmwareSelecionado}
                    height={60}
                    width={'100%'}
                    editable={false}
                    placeHolder='Firmware selecionado'
                    onValueChange={setFirmwareSelecionado}
                    fontSize={20}
                    type={'texto'}
                    textoLabel={'Firmware selecionado'}
                    backgroundColor={'#F2F2F2'}
                />

                <View style={{width:'100%', flexDirection:'row', justifyContent:'space-between', alignItems:'center', gap:10}}>
                    <TouchableOpacity style={styles.buttoHMG} 
                                      onPress={() => refRBSheetFirmwareHmg.current?.open()}
                                      testID="button-open-hmg">
                        <Text style = {styles.textoButtonHMG}>{`Firmwares de\n Homologação`}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.buttoPROD}
                                      onPress={() => refRBSheetFirmwareProd.current?.open()}
                                      testID="button-open-prod">
                        <Text style = {styles.textoButtonPROD}>{`Firmwares de\n Produção`}</Text>
                    </TouchableOpacity>                    
                </View>

                <View style={{height:0, width:'100%', justifyContent:'center'}}>
                    <Divider color={'#D6D6D6'}/>
                </View>                

                <TextEntry 
                    value={firmwareTimeout}
                    height={60}
                    width={'100%'}
                    editable={true}
                    placeHolder='Timeout (min)'
                    onValueChange={setFirmwareTimeout}
                    fontSize={20}
                    type={'texto'}
                    textoLabel={'Timeout (min)'}
                    backgroundColor={'#F2F2F2'}
                    testID="input-timeout"
                />

                <View style={{height:0, width:'100%', justifyContent:'center'}}>
                    <Divider color={'#D6D6D6'}/>
                </View>                

                <TouchableOpacity style={styles.buttonSalvarCodi} 
                                  onPress={() => setShowMessagePerguntaAtualizar(true)}
                                  testID="button-update">
                    <Text style = {styles.textoButtonSalvarCodi}>ATUALIZAR</Text>
                </TouchableOpacity>

            </View>

            {/* seleção do firmware de produção */}
            <RBSheet ref={refRBSheetFirmwareProd} height={screenHeight}>
                                
                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#737373'}}>                                                            
                    <TouchableOpacity onPress={() => refRBSheetFirmwareProd.current?.close()} testID="button-close-prod">
                        <IconX color={"#FFFFFF"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>
                
                {/* lista de firmwares de produção */}                    
                <View style={{height:'100%', width:'100%',paddingHorizontal: 20, paddingBottom:80, justifyContent:'flex-start', alignItems:'flex-start', backgroundColor:'#737373', gap: 10}}>                    
                    <Text style={{...styles.textoStatus, color:'#45D42E'}}>Selecione o Firmware de Produção</Text>

                    <FlatList style={{width:'100%', marginTop: 20}}
                        ItemSeparatorComponent={() => <Divider />}              
                        data = {firmwaresProd}
                        renderItem= { ({ item }) =>
                            <TouchableOpacity   style={{height:60, justifyContent:'center', alignItems:'center'}}
                                                onPress={ async () => {
                                                    setFirmwareSelecionado(item);
                                                    refRBSheetFirmwareProd.current?.close();
                                                }}
                                                testID="button-prod"
                            >
                                <Text style={styles.textoItensSelecao}>{item}</Text>
                            </TouchableOpacity>
                        }
                    />
                </View>
                
            </RBSheet>

            {/* seleção do firmware de homologação */}
            <RBSheet ref={refRBSheetFirmwareHmg} height={screenHeight}>
                                
                <View style={{height:60, width:'100%', paddingHorizontal: 20, flexDirection:'row', justifyContent:'flex-end', alignItems:'center', backgroundColor:'#737373'}}>                                                            
                    <TouchableOpacity onPress={() => refRBSheetFirmwareHmg.current?.close()} testID="button-close-hmg">
                        <IconX color={"#FFFFFF"} width={34} height={34}/>
                    </TouchableOpacity>
                </View>
                
                {/* lista de firmwares de produção */}                    
                <View style={{height:'100%', width:'100%',paddingHorizontal: 20, paddingBottom:80, justifyContent:'flex-start', alignItems:'flex-start', backgroundColor:'#737373', gap: 10}}>                    
                    <Text style={{...styles.textoStatus, color:'#45D42E'}}>Selecione o Firmware de Homologação</Text>

                    <FlatList style={{width:'100%', marginTop: 20}}
                        ItemSeparatorComponent={() => <Divider />}              
                        data = {firmwaresHmg}
                        renderItem= { ({ item }) =>
                            <TouchableOpacity style={{height:60, justifyContent:'center', alignItems:'center'}}
                                              onPress={ async () => {
                                                setFirmwareSelecionado(item);
                                                refRBSheetFirmwareHmg.current?.close();
                                            }}
                            >
                                <Text style={styles.textoItensSelecao}>{item}</Text>
                            </TouchableOpacity>
                        }
                    />
                </View>
                
            </RBSheet>            
        </>
        

    )
}

export default ProbeOTA;