import { CommonActions, useNavigation } from "@react-navigation/native";
import { ProbeSettings } from "../../data";
import { StackTypes } from "../../routes";


// navega para pagina de configuração do khomp
export const goToKhomp = (
        navigation: ReturnType<typeof useNavigation<StackTypes>>,
        probe: ProbeSettings ) => {    
    
    // estrutura inicial de configuração da probe
    const _probe: ProbeSettings = probe;
    
    // atualiza estrutura de configuração da probe
    const probeAtualizada = { ..._probe};

    // fecha a pagina de configuração da probe
    navigation.dispatch (
        CommonActions.navigate({
            name: 'Khomp',
           params: { probeSettings: probeAtualizada },
        })
    );       
};