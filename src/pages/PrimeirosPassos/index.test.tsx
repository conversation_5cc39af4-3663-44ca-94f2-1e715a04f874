
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';

import { NavigationContainer } from '@react-navigation/native';
import PrimeirosPassos from '.';
import { Text } from 'react-native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

const Stack = createNativeStackNavigator();

// Criando um mock para a navegação
const MockNavigator = () => (
  <NavigationContainer>
    <Stack.Navigator>
        <Stack.Screen name="PrimeirosPassos">
          {() => <PrimeirosPassos />}
        </Stack.Screen>
        <Stack.Screen name="MenuPrincipal">
          {() => <Text>Menu Principal</Text>}
        </Stack.Screen> 
    </Stack.Navigator>
  </NavigationContainer>
);

describe('PrimeirosPassos Screen', () => {
  it('deve renderizar inicialmente a tela com o titulo', () => {
    const { getByText } = render(
      <NavigationContainer>
        <PrimeirosPassos />
      </NavigationContainer>
    );

    expect(getByText('Primeiros Passos')).toBeTruthy();
  });

  it('deve renderizar o primeiro slide com texto', () => {
    const { getByText } = render(
      <NavigationContainer>
        <PrimeirosPassos />
      </NavigationContainer>
    );

    expect(getByText('Seja bem-vindo ao nosso app!')).toBeTruthy();
  });

  it('deve navegar para o MenuPrincipal ao clicar em pular', () => {
    const { getByText } = render(<MockNavigator />);

    fireEvent.press(getByText('PULAR'));
    expect(getByText('Menu Principal')).toBeTruthy();
  });

  it('deve navegar para "MenuPrincipal" após concluir o slider', () => {
    const { getByText } = render(<MockNavigator />);
    
    fireEvent.press(getByText('PRÓXIMO'));
    fireEvent.press(getByText('PRÓXIMO'));
    fireEvent.press(getByText('PRÓXIMO'));
    fireEvent.press(getByText('PRÓXIMO'));
    expect(getByText('Menu Principal')).toBeTruthy();
  });

  it('deve navegar direto para a tela de inicio da Jornada', () => {
    const { getByText } = render( <NavigationContainer>
                                    <Stack.Navigator>
                                      <Stack.Screen name="PrimeirosPassos">
                                        {() => <PrimeirosPassos iniciarJornada={true}/>}
                                      </Stack.Screen>
                                      <Stack.Screen name="Iniciar Jornada">
                                        {() => <Text>TELA INICIAR JORNADA</Text>}
                                      </Stack.Screen> 
                                    </Stack.Navigator>
                                  </NavigationContainer>);

    expect(getByText('TELA INICIAR JORNADA')).toBeTruthy();
  });  
});
