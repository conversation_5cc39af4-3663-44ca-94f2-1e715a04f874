import { useNavigation } from '@react-navigation/native';
import React from 'react';
import { Image, Text, View } from 'react-native';

// estilos da pagina
import { styles } from '../PrimeirosPassos/layout';

// component slider
import AppIntroSlider from 'react-native-app-intro-slider';

// rotas de navegação
import { StackTypes } from '../../routes/index';

const passos = [
    {
        key: '1',
        text: "Seja bem-vindo ao nosso app!",
        image: require('../../assets/png/passo-1.png')
    },
    {
        key: '2',
        text: "Lorem dolor sit amet consectetur adipisicing elit, sed do eiusmod tempor incididunt ut ero labore et dolore.",
        image: require('../../assets/png/passo-2.png')
    },
    {
        key: '3',
        text: "Lorem dolor sit amet consectetur adipisicing elit, sed do eiusmod tempor incididunt ut ero labore et dolore..",
        image: require('../../assets/png/passo-3.png')
    },
    {
        key: '4',
        text: "Lorem dolor sit amet consectetur adipisicing elit, sed do eiusmod tempor incididunt ut ero labore et dolore...",
        image: require('../../assets/png/passo-4.png')
    },    
];

interface PrimeirosPassosProps {
    iniciarJornada?: boolean;
}

const PrimeirosPassos: React.FC<PrimeirosPassosProps> = ({ iniciarJornada = false }) => 
{
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();    

    function renderPassos({item}:any){
        return(
            <View style={{flex:1}}>

                <Image
                    source={item.image}
                    style={styles.imagePassos}
                />
                <Text
                    style={styles.textoDescricao}>
                    {item.text}
                </Text>
            </View>
        )
    }

    if(iniciarJornada){
        return <Text>TELA INICIAR JORNADA</Text>
    }else{
        return(
            <View style={styles.containerTela}>

                <Text style={styles.textoTitulo}>
                    Primeiros Passos
                </Text>
                
                <AppIntroSlider
                    renderItem={renderPassos}
                    data={passos}
                    activeDotStyle={{
                        backgroundColor:'#FFFFFF',
                        width:15,
                        height:15,
                        borderRadius: 9999,
                        borderColor: '#38B026',
                        borderWidth:2,
                    }}
                    showSkipButton={true}
                    renderSkipButton={ () => <Text style={styles.textoPular}>PULAR</Text>}
                    onSkip={ () => navigation.navigate('MenuPrincipal')}
                    renderNextButton={ () => <Text style={styles.textoProximo}>PRÓXIMO</Text>}
                    renderDoneButton={ () => <Text style={styles.textoProximo}>PRÓXIMO</Text>}
                    onDone={ () => navigation.navigate('MenuPrincipal')}
                    testID='app-intro-slider'
                />            
            </View>
        );
    }   
}

export default PrimeirosPassos;