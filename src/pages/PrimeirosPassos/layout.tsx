import {StyleSheet} from "react-native";

export const styles = StyleSheet.create({

    containerTela:{
        flex:1,
        paddingTop:30,
    },

    imagePassos:{
        resizeMode: 'center',
        height:'70%',
        width: '90%',
        alignSelf: 'center',
    },

    textoTitulo:{
        paddingTop:20,
        paddingBottom:10,
        fontFamily:"Exo2_600SemiBold",
        fontSize: 25,        
        color:'#38B026',
        alignSelf:'center'
    },

    textoDescricao:{
        textAlign: 'center',
        color: '#525252',
        paddingHorizontal: 25,
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 17,        
    },

    textoPular:{
        fontSize:20,
        fontFamily:'SourceSans3_600SemiBold',
        color: '#38B026',
        alignSelf:'center'
    },

    textoProximo:{
        fontSize:20,
        fontFamily:'SourceSans3_600SemiBold',
        color: '#38B026',
        alignSelf:'center'
    },
});