import React from "react";
import * as spyMQTT from "../../services/mqtt"; // Importa todo o módulo para usar jest.spyOn
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import { NavigationContainer } from "@react-navigation/native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import PassoTesteConexao from "./passo_teste_conexao";
import MQTT, { IMqttClient } from 'sp-react-native-mqtt';
import { BrokerHmg, BrokerProd } from "../../constantes";
import { EventRegister } from "react-native-event-listeners";

jest.mock('sp-react-native-mqtt', () => ({
    connect: jest.fn(() => ({
        subscribe: jest.fn(),
        on: jest.fn(),
    })),
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

jest.mock('react-native-event-listeners', () => ({
    EventRegister: {
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      emit: jest.fn(),
    },
}));

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                tipo_conexao: 1, // gsm
      }
    },
};


describe('Passo Teste de conexão', () => {

    // Mock das dependências internas
    jest.mock('sp-react-native-mqtt');

    let mqttClientMock: IMqttClient;
            
    beforeEach(() => {

        // Substituímos manualmente o método createClient por um mock
        MQTT.createClient = jest.fn().mockImplementation(() => Promise.resolve(mqttClientMock));
        mqttClientMock = {
            on: jest.fn(),
            connect: jest.fn(),
            subscribe: jest.fn(),
            publish: jest.fn(),
            disconnect: jest.fn(),
            unsubscribe: jest.fn(),
            reconnect:jest.fn(),
            isConnected: jest.fn(),
        };
    });

    it('renderiza corretamente a tela e clica no botão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela, não conclui o teste com uma probe conectada no wifi e clica no botão continue', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute1} />
                                        </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
    });
    
    it('renderiza corretamente a tela, não conclui o teste com uma probe conectada no gsm e clica no botão continue', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute2} />
                                        </NavigationContainer> );
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
    });    

    
    it('renderiza corretamente a tela, probe conectada no wifi e clica no botão conectar e isMQTTConnected = true ', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute1} />
                                        </NavigationContainer> );
        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });
    });
    
    it('renderiza corretamente a tela, probe conectada no wifi e clica no botão conectar e isMQTTConnected = false ', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(false);

        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute1} />
                                        </NavigationContainer> );
        await act(async () => {         
            fireEvent.press(getByTestId('button-connect'));
        });
    });
    
    it('renderiza corretamente a tela, probe conectada no wifi e clica no botão continue e isMQTTConnected = true ', async () => {
    
        // Usa jest.spyOn para mockar a função isMQTTConnected
        jest.spyOn(spyMQTT, 'isMQTTConnected').mockReturnValue(true);

        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoTesteConexao route={mockRoute1} />
                                        </NavigationContainer> );
        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
    });    
   
    it('deve executar o fluxo esperado ao verificar se probe conectada = false', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(false); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    


    it('deve executar o fluxo esperado ao verificar se probe conectada = true', () => {

        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'isConnectedMQTT') {
            callback(true); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('isConnectedMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    


    it('deve executar o fluxo esperado ao solicitar o firmware da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'firmwareMQTT') {
            callback('versao firmware'); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('firmwareMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar a data e hora da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'dateTimeMQTT') {
            callback(0); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('dateTimeMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar o sinal do gsm ou wifi da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'signalMQTT') {
            callback(10); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('signalMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar o nivel da bateria da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'batteryMQTT') {
            callback(80); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('batteryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });
    
    it('deve executar o fluxo esperado ao solicitar o nivel de tensão da bateria da probe', () => {
        // Elenco (cast) o método para jest.Mock
        const mockedAddEventListener = EventRegister.addEventListener as jest.Mock;
    
        mockedAddEventListener.mockImplementation((eventName, callback) => {
          if (eventName === 'vBatteryMQTT') {
            callback(100); // Simula o callback de reconexão
            return 'mockEventListenerId';
          }
        });
    
        const mockedRemoveEventListener = EventRegister.removeEventListener as jest.Mock;
    
        const { unmount } = render( <NavigationContainer>
                                        <PassoTesteConexao route={mockRoute1} />
                                    </NavigationContainer> );
    
        // Aqui você pode verificar as chamadas ou comportamentos esperados
        expect(mockedAddEventListener).toHaveBeenCalledWith('vBatteryMQTT', expect.any(Function));
    
        // Chama o cleanup do listener
        unmount();
    
        expect(mockedRemoveEventListener).toHaveBeenCalledWith('mockEventListenerId');
    });    
});