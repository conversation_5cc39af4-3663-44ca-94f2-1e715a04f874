import {View, Text, TouchableOpacity, TextInput} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// modal messages
import MessageBoxErro from '../../modal/messagebox/Erro'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// dados das etapas de instalação
import {EtapasInstalacao, ListaMedidorFabricante, ListaMedidorModelo} from "../../data"

// componentes
import ComboBox from '../../componentes/ComboBox'
import { ScreenTipoOS } from '../../constantes';

const PassoMedidor = ({route}) => {
  
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // fabricante do medidor
    const [medidorFabricante, setMedidorFabricante] = useState(route.params?.etapas.medidor_fabricante);
    
    // modelo do medidor
    const [medidorModelo, setMedidorModelo] = useState(route.params?.etapas.medidor_modelo);

    // numero de serie do medidor
    const [medidorNS, setMedidorNS] = useState(route.params?.etapas.medidor_ns);
    
    const [textoMessageBoxErro, setTextoMessageBoxErro] = useState("");
    const [showMessageBoxErro, setShowMessageBoxErro] = useState(false);
  
    const ListaMedidorModeloFabricante = ListaMedidorModelo.filter(medidor => medidor.id_fabricante === medidorFabricante).map(medidor => ({
            id: medidor.id,
            descricao: medidor.descricao
        }));


    // verifica se as informações do medidor estão corretas
    const SeInfoMedidorOK = () => {

      // se foi digitado algum numero de serie para o medidor
      if (medidorNS == '') {
        setTextoMessageBoxErro("Digite o Número de Série do medidor");
        onMessageBoxErroAbrir();
    
        return false;
      }
      
      return true
    };

  
    // navega para pagina de ordem de servico
    const goToOrdemServico = (etapas: EtapasInstalacao) => {
  
      if(!SeInfoMedidorOK())
        return;
  
      const _etapas: EtapasInstalacao = etapas;
    
      // atualiza estrutura de etapas
      const etapasAtualizada = { ..._etapas, medidor_modelo: medidorModelo, medidor_fabricante: medidorFabricante, medidor_ns: medidorNS, step2: true };
    
      // navega a pagina progresso da instalação fechando as 2 paginas anteriores
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
          ],
        })
      );      
  
    };  
  
    // navegar de volta
    const goBack = (etapas: EtapasInstalacao) => {
           
      // navega a pagina progresso da instalação
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
              { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
          ],
        })
      );
    };

    const onMessageBoxErroAbrir = () => { setShowMessageBoxErro(true); };
    const onMessageBoxErroFechar = () => { setShowMessageBoxErro(false); };
  
    return (
      
      <>
  
        <MessageBoxErro 
          visivel={showMessageBoxErro} 
          titulo="Erro" 
          descricao={textoMessageBoxErro} 
          textoBotao="OK" 
          onFechar={onMessageBoxErroFechar}
        />
  
        <View style = {styles.containerTelaMedidor}>
  
          {/* header */}
          <View style={styles.containerHeaderMedidor}>
  
              {/* botão voltar */}
              <View style={styles.containerBotaoVoltar}>
                <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                  <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                </TouchableOpacity>    
              </View>
  
              {/* texto cabeçalho */}
              <View style={styles.containerTextoHeader}>
                <Text style={styles.textoHeaderPassoID}>INFORMAÇÕES DO MEDIDOR</Text>
              </View>
  
          </View>
  
          {/* informações do medidor da probe */}
          <View style = {styles.containerConteudoMedidor}>
  
            <View style={{gap:5}}>

              {/* fabricante do medidor */}
              <View style={{height:70}}>

                  <ComboBox
                    onValueChange={setMedidorFabricante}                  
                    data={ListaMedidorFabricante}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    focusColor={'#2E8C1F'}
                    borderColor={'#737373'}
                    borderRadius={5}
                    borderWidth={1.5}
                    textoPlaceHolder='Selecione o fabricante'
                    textoLabel='Fabricante'
                    textoCor={'#2E8C1F'}
                  />

              </View>

              {/* modelo do medidor */}
              <View style={{height:70}}>

                  <ComboBox
                    onValueChange={setMedidorModelo}
                    data={ListaMedidorModeloFabricante}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    focusColor={'#2E8C1F'}
                    borderColor={'#737373'}
                    borderRadius={5}
                    borderWidth={1.5}
                    textoPlaceHolder='Selecione o modelo'
                    textoLabel='Modelo'
                    textoCor={'#2E8C1F'}
                  />

              </View>

              <View style={styles.containerInputMedidorNS}>
            
                <TextInput
                    style={styles.textoInputMedidorNS}
                    placeholder="Nº de série" 
                    keyboardType='default'
                    value={medidorNS}
                    maxLength={12}                  
                    onChangeText={(texto) => setMedidorNS(texto)} 
                    testID='input-ns'/>
  
              </View>
  
            </View>
  
            <TouchableOpacity style={styles.buttonContinuarPassoID}
                              onPress={() => goToOrdemServico(route.params?.etapas)}
                              testID='button-continue'>
              <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
            </TouchableOpacity>
  
          </View>
  
        </View>
  
      </>
  
    );
  
  };
  
  export default PassoMedidor;