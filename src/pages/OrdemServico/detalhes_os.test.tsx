import React from "react";
import { fireEvent, render, screen, act } from "@testing-library/react-native";
import { NavigationContainer } from "@react-navigation/native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import DetalhesOS from "./detalhes_os";

jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
      }),
    };
});

// Mock do LeafletView
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => {
    return {
      LeafletView: () => null,  // Ou 'LeafletView' se preferir apenas renderizar o nome como string
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                latitude: 0,
                longitude: 0,                
                tipo_conexao: 1,
                teste_conexao: false,
                totalMaterial1: 2,
                totalMaterial2: 1,
                totalMaterial3: 1,
                totalMaterial4: 1,
                totalMaterial5: 1,
                totalMaterial6: 2,
                imagem_local1: 'exemplo.jpg',
                imagem_local2: 'exemplo.jpg',
                imagem_local3: 'exemplo.jpg',
                imagem_local4: 'exemplo.jpg',
                imagem_local5: 'exemplo.jpg',
                imagem_local6: 'exemplo.jpg',
                imagem_local7: 'exemplo.jpg',
                imagem_local8: 'exemplo.jpg',                                
                imagem_instalacao1: 'exemplo.jpg',
                imagem_instalacao2: 'exemplo.jpg',
                imagem_instalacao3: 'exemplo.jpg',
                imagem_instalacao4: 'exemplo.jpg',
                imagem_instalacao5: 'exemplo.jpg',
                imagem_instalacao6: 'exemplo.jpg',
      }
    },
};

const mockRoute3 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                numero_os: '00000000',
                descricao_problema : undefined,
                medidor_modelo: -1,
                medidor_fabricante: -1
      }
    },
};

describe('Detalhes OS', () => {
    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOS route={mockRoute1} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <DetalhesOS route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });
    
    it('renderiza corretamente a tela com tipo conexão gsm', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOS route={mockRoute2} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    

    it('renderiza corretamente a tela numero os invalida', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOS route={mockRoute3} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    
});