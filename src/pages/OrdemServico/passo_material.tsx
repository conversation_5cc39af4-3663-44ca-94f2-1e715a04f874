import {View, Text, TouchableOpacity, TextInput} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// dados
import {EtapasInstalacao} from "../../data"

// modal messages
import MessageBoxPergunta from '../../modal/messagebox/Pergunta'

// componentes
import Counter from '../../componentes/Counter'
import CheckBox from '../../componentes/CheckBox'
import { ScreenTipoOS } from '../../constantes';

const PassoMaterial = ({route}) => {
  
  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  const [material1, setMaterial1] = useState<number>(route.params?.etapas.totalMaterial1);
  const [material2, setMaterial2] = useState<number>(route.params?.etapas.totalMaterial2);
  const [material3, setMaterial3] = useState<number>(route.params?.etapas.totalMaterial3);
  const [material4, setMaterial4] = useState<number>(route.params?.etapas.totalMaterial4);
  const [material5, setMaterial5] = useState<number>(route.params?.etapas.totalMaterial5);
  const [material6, setMaterial6] = useState<number>(route.params?.etapas.totalMaterial6);
  const [materialExtra, setMaterialExtra] = useState<boolean>(route.params?.etapas.materialExtra);
  const [textoMaterialExtra, setTextoMaterialExtra] = useState<string>(route.params?.etapas.textoMaterialExtra);

  const [showMessageBoxPergunta, setShowMessageBoxPergunta] = useState(false);
  const [textoPergunta, setTextoPergunta] = useState('');
  const onMessageBoxPerguntaAbrir = () => { setShowMessageBoxPergunta(true); };

  // navega para pagina de inserção de conexão da probe
  const goToOrdemServico = (etapas: EtapasInstalacao) => {

    // pega o total de material utilizado
    let total_material = material1 + material2 + material3 + material4 + material5 + material6;

    // se algum material foi inserido
    if( (total_material > 0) || (materialExtra))
    {
      // estrutura de etapas atual
      const _etapas: EtapasInstalacao = etapas;

      // atualiza estrutura de etapas
      const etapasAtualizada = { ..._etapas, 
                                 totalMaterial1: material1,
                                 totalMaterial2: material2, 
                                 totalMaterial3: material3, 
                                 totalMaterial4: material4, 
                                 totalMaterial5: material5,
                                 totalMaterial6: material6,
                                 materialExtra: materialExtra, 
                                 textoMaterialExtra: textoMaterialExtra, 
                                 step5: true, 
      };    

      // navega para pagina
      navigation.navigate(ScreenTipoOS[route.params?.etapas.tipo_os], {etapas: etapasAtualizada})
    }
    else {
      setTextoPergunta("Tem certeza que deseja continuar sem contabilizar nenhum material utilizado?");
        
      onMessageBoxPerguntaAbrir();
    }

  };    

  const onResultOK = (etapas: EtapasInstalacao) => {

    setShowMessageBoxPergunta(false); 

    const _etapas: EtapasInstalacao = etapas;
    
    // atualiza estrutura de etapas
    const etapasAtualizada = { ..._etapas, numero_fotos_local: 0, step5: true };

    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes:[
         { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
        ],
      })
    );
        
  };

  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
           
    // navega a pagina progresso da instalação
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
       ],
      })
    );
  };  
  
  const onResultCancel = () => { 
    setShowMessageBoxPergunta(false); 
  };

  return (
    
    <>

      <MessageBoxPergunta
        visivel={showMessageBoxPergunta} 
        titulo="Atenção" 
        descricao={textoPergunta} 
        textoBotaoOK="Confirmar" 
        textoBotaoCancelar='Cancelar'
        onOK={() => onResultOK(route.params?.etapas)}
        onCancel={onResultCancel}
      />      
    
      <View style = {styles.containerTelaPassoMaterial}>

        {/* header */}
        <View style={styles.containerHeaderTelaPassoMaterial}>

            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
              </TouchableOpacity>    
            </View>

            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeaderPassoID}>MATERIAL UTILIZADO</Text>
            </View>

        </View>

        {/* digitar o código da probe */}
        <View style = {styles.containerConteudoTelaPassoMaterial}>

          <View style={{gap:10}} >
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                    
                     minimo={0} maximo={99}
                     enabledColor={'#A3A3A3'}
                     value={material1} texto='Antena' onValueChange={setMaterial1}/>
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                   
                     minimo={0} maximo={99} 
                     enabledColor={'#A3A3A3'}
                     value={material2} texto='Caixa Externa' onValueChange={setMaterial2}/>
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                    
                     minimo={0} maximo={99} 
                     enabledColor={'#A3A3A3'}
                     value={material3} texto='Duplicador' onValueChange={setMaterial3}/>
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                    
                     minimo={0} maximo={99} 
                     enabledColor={'#A3A3A3'} 
                     value={material4} texto='Conversor' onValueChange={setMaterial4}/>
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                     
                     minimo={0} maximo={99} 
                     enabledColor={'#A3A3A3'} 
                     value={material5} texto='Cabo Externo' onValueChange={setMaterial5}/>
            <Counter height={50} width={'100%'} heightIcon={22} widthIcon={22}                    
                     minimo={0} maximo={99} 
                     enabledColor={'#A3A3A3'}
                     value={material6} texto='Cabo Interno' onValueChange={setMaterial6}/>

            <View style={{paddingTop:20, justifyContent:'flex-start', alignItems:'center', flexDirection:'row', gap: 10}}>
              <CheckBox value={materialExtra} height={25} width={25} onValueChange={setMaterialExtra}/>
              <Text style={{fontSize:18, fontFamily:'SourceSans3_500Medium', color: (materialExtra) ? '#2E8C1F' : '#D6D6D6'}}>Materiais extras</Text>
            </View>
          
            {
              (materialExtra) &&
                <TextInput style={{height:100, borderWidth:1, borderColor: '#D6D6D6', borderRadius: 5, padding:10, fontFamily:'SourceSans3_400Regular', fontSize:16 }}
                          placeholder='Descrever os materiais'
                          multiline
                          textAlignVertical='top'
                          value={textoMaterialExtra}                
                          onChangeText={(texto) => setTextoMaterialExtra(texto)}
                          testID='input-material'
                >

                </TextInput>
            }
                
          </View>

          <TouchableOpacity style={styles.buttonContinuarPassoID}
                            onPress={() => goToOrdemServico(route.params?.etapas)} testID='button-continue'>
              <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
          </TouchableOpacity>  
        
        </View>

      </View>

    </>
  );

};

export default PassoMaterial;