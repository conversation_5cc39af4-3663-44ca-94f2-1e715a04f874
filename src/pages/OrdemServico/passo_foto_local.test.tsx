import React from "react";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import { NavigationContainer } from "@react-navigation/native";
import PassoFotoLocal from "./passo_foto_local";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                imagem_local1 :'imagem.png'
      }
    },
};

const mockRoute3 = {

    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                imagem_local1 :'imagem.png',
                imagem_local2 :'imagem.png',
                imagem_local3 :'imagem.png',
                imagem_local4 :'imagem.png'
      }
      
    },
};

const mockRoute4 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                imagem_local1 :'imagem.png',
                imagem_local2 :'imagem.png'
      }
    },
};


describe('Passo foto do local', () => {

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoFotoLocal route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI! mas não escolhe primeira imagem', async () => {
    
        const {getByText, getByTestId} = render(  <NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute1} />
                                                  </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });

    });

    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI! e escolhe a primeira imagem', async () => {
    
        const {getByText, getByTestId} = render(<NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute2} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });        
    });    

    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI!, escolhe primeira imagem mas não a segunda', async () => {
    
        const {getByText, getByTestId} = render(<NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute2} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));            
        });        

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
    });    

    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI!, escolhe primeira e segunda imagem mas não a terceira', async () => {
    
        const {getByText, getByTestId} = render(<NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute4} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));            
        });        

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });         
    });








    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI! escolha a imagem e botão proximo ate o fim', async () => {
    
        const {getByText, getByTestId} = render(<NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute3} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('CONTINUAR'));
        });  
        
        await act(async () => {         
            fireEvent.press(getByText('Confirmar'));
        });        
    });

    it('renderiza corretamente a tela, apresenta aviso e clica no botão ENTENDI! escolha a imagem e botão cancelar no fim', async () => {
    
        const {getByText, getByTestId} = render(<NavigationContainer>
                                                    <PassoFotoLocal route={mockRoute3} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENTENDI!'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('PRÓXIMO'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-view'));
        });

        await act(async () => {         
            fireEvent.press(getByText('CONTINUAR'));
        });  
        
        await act(async () => {         
            fireEvent.press(getByText('Cancelar'));
        });        
    });    
    
    
});