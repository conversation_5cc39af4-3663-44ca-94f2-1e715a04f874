import {View, Text, TouchableOpacity} from 'react-native';
import React, {useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// componentes
import CheckBox from '../../componentes/CheckBox'
import ComboBox from '../../componentes/ComboBox'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'
import IconWifi from '../../assets/svg/icon_wifi.svg'
import IconGSM from '../../assets/svg/icon_gsm.svg'
import IconEthernet from '../../assets/svg/icon_ethernet.svg'

// constantes
import { TipoConexao, OperadorasCelular, TipoStatusOS, ScreenTipoOS } from "../../constantes"

// modal messages
import MessageBoxErro from '../../modal/messagebox/Erro'

// rotas de drawer navegação
import {StackTypes} from '../MenuPrincipal/index'

// tipos de dados
import {EtapasInstalacao} from '../../data'
import TextEntry from '../../componentes/TextEntry';

const PassoConexao = ({route}) => {

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  // seta o tipo de conexão escolhida
  const [tipoConexao, setTipoConexao] = useState(3);

  // tipos de conexão
  const [checkGSM, setCheckGSM] = useState(false);
  const [checkEthernet, setCheckEthernet] = useState(false);
  const [checkWifi, setCheckWifi] = useState(false);

  // tipo de operadora GSM
  const [tipoOperadora, setTipoOperadora] = useState(-1);  

  // informações da conexão ethernet
  const [ip, setIP] = useState<string>('');
  const [gateway, setGateway] = useState<string>('');
  const [mascara, setMascara] = useState<string>('');

  // informações da conexão wifi
  const [ssid, setSSID] = useState<string>('');
  const [senha, setSenha] = useState<string>('');

  const [textoMessageBoxErro, setTextoMessageBoxErro] = useState("");
  const [showMessageBoxErro, setShowMessageBoxErro] = useState(false);

  // trata o tipo de escolha para o GSM
  const handleChangeGSM = () => {

    setTipoConexao(TipoConexao.GSM);

    setCheckGSM?.(true); 
    setCheckEthernet?.(false);
    setCheckWifi?.(false);

  };

  // trata o tipo de escolha para o Ethernet
  const handleChangeEthernet = () => {

    setTipoConexao(TipoConexao.ETHERNET);
    
    setCheckGSM?.(false);
    setCheckEthernet?.(true);
    setCheckWifi?.(false);

  };

  // trata o tipo de escolha para o Wi-fi
  const handleChangeWifi = () => {

    setTipoConexao(TipoConexao.WIFI);

    setCheckGSM?.(false);
    setCheckEthernet?.(false);
    setCheckWifi?.(true);

  };  

  // verifica se foi configurado o tipo de conexão
  const SeTipoConexaoOK = () => {

    // se foi selecionado algum tipo de conexão
    if ((!checkGSM) && (!checkEthernet) && (!checkWifi)) {

      setTextoMessageBoxErro("Selecione um tipo de conexão.");
      onMessageBoxErroAbrir();
  
      return false;
    }

    // se foi selecionado algum tipo de operadora
    if ((checkGSM) && (tipoOperadora === -1)) {

      setTextoMessageBoxErro("Selecione uma operadora.");
      onMessageBoxErroAbrir();
  
      return false;
    }    

    //navigation.navigate('OrdemServico');

    return true;    
  }

  // navega para pagina de inserção de conexão da probe
  const goToOrdemServico = (etapas: EtapasInstalacao) => {

    // se conexão esta OK
    if(!SeTipoConexaoOK())
      return;

    // estrutura de etapas atual
    const _etapas: EtapasInstalacao = etapas;

    // atualiza estrutura de etapas
    const etapasAtualizada = { ..._etapas, 
                               tipo_conexao: tipoConexao, 
                               operadora: tipoOperadora, 
                               status_os: TipoStatusOS.PENDENTE,
                               ip: ip,
                               mascara: mascara,
                               gateway: gateway,
                               ssid: ssid,
                               ssid_senha: senha,                               
                               step1: true };
        
    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
        ],
      })
    );
  };

  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
           
    // navega a pagina progresso da instalação
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
            { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
        ],
      })
    );
  };
  
  const onMessageBoxErroAbrir = () => { setShowMessageBoxErro(true); };
  const onMessageBoxErroFechar = () => { setShowMessageBoxErro(false); };

  return (

    <>

    <MessageBoxErro 
      visivel = {showMessageBoxErro} 
      titulo = "Erro" 
      descricao = {textoMessageBoxErro}
      textoBotao="OK" 
      onFechar={onMessageBoxErroFechar}>
    </MessageBoxErro>

    <View style = {styles.containerTelaTipoConexao}>

      {/* header */}
      <View style={styles.containerHeaderTipoConexao}>

        {/* botão voltar */}
        <View style={styles.containerBotaoVoltar}>
          <TouchableOpacity onPress={()=> goBack(route.params?.etapas)} testID='button-back'>
            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
          </TouchableOpacity>    
        </View>

        {/* texto cabeçalho */}
        <View style={styles.containerTextoHeader}>
          <Text style={styles.textoHeaderTipoConexao}>TIPO DE CONEXÃO</Text>
        </View>

      </View>

      {/* tipos de conexão */}
      <View style = {styles.containerConteudoTipoConexao}>

        <View style={{gap:20}}>

          <View style={{flexDirection:'row', justifyContent:'space-between'}}>

            <View style={{...styles.containerTipoConexao, borderColor:(checkGSM) ? '#2E8C1F' : '#FFFFFF' }}>
              <IconGSM color={'#2E8C1F'} height={60} width={60} />
              <View style={{justifyContent:'center', alignItems:'center', gap:5}}>
                <Text style={styles.textoTipoConexao}>GSM</Text>
                <CheckBox height={25} width={25} value={checkGSM} onValueChange={handleChangeGSM} testID='check-gsm'/>
              </View>
            </View>
  
            <View style={{...styles.containerTipoConexao, borderColor:(checkEthernet) ? '#2E8C1F' : '#FFFFFF' }}>
              <IconEthernet color={'#2E8C1F'} height={60} width={60} />
              <View style={{justifyContent:'center', alignItems:'center', gap:5}}>
                <Text style={styles.textoTipoConexao}>Ethernet</Text>
                <CheckBox height={25} width={25} value={checkEthernet} onValueChange={handleChangeEthernet} testID='check-ethernet'/>
              </View>
            </View>
  
            <View style={{...styles.containerTipoConexao, borderColor:(checkWifi) ? '#2E8C1F' : '#FFFFFF' }}>
              <IconWifi color={'#2E8C1F'} height={60} width={60} />
              <View style={{justifyContent:'center', alignItems:'center', gap:5}}>
                <Text style={styles.textoTipoConexao}>Wi-Fi</Text>
                <CheckBox height={25} width={25} value={checkWifi} onValueChange={handleChangeWifi} testID='check-wifi' />
              </View>
            </View>

          </View>

          {
            (checkGSM) &&
              <View style={{height:100}}>
                <ComboBox
                  onValueChange={setTipoOperadora}
                  data={OperadorasCelular}
                  height={60}
                  width={'100%'}
                  backgroundColor={'#F7F7F7'}
                  focusColor={'#2E8C1F'}
                  borderColor={'#737373'}
                  borderRadius={5}
                  borderWidth={1.5}
                  textoPlaceHolder='Selecione a operadora'
                  textoLabel='Operadora'
                  textoCor={'#2E8C1F'}/>
              </View>              
          }

          {
            (checkEthernet) &&
              <View style={{gap: 10}}>

                <TextEntry
                    value={ip}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    placeHolder='IP'
                    textoLabel= 'IP'
                    type='ip'
                    onValueChange= {setIP}
                />
                  
                <TextEntry
                    value={mascara}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    placeHolder='Máscara'
                    textoLabel= 'Máscara'
                    type='ip'
                    onValueChange= {setMascara}
                />                  

                <TextEntry
                    value={gateway}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    placeHolder='Gateway'
                    textoLabel= 'Gateway'
                    type='ip'
                    onValueChange= {setGateway}
                />

              </View>              
          }

          {
            (checkWifi) &&
              <View style={{gap: 10}}>

                <TextEntry
                    value={ssid}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    placeHolder='Rede Wi-Fi'
                    textoLabel= 'Rede Wi-Fi'
                    maxLength={30}
                    onValueChange= {setSSID}
                />

                <TextEntry
                    value={senha}
                    height={60}
                    width={'100%'}
                    backgroundColor={'#F7F7F7'}
                    placeHolder='Senha'
                    textoLabel= 'Senha'
                    maxLength={30}
                    onValueChange= {setSenha}
                />

              </View>
              
          }

        </View>

        <TouchableOpacity style={styles.buttonContinuarTipoConexao} onPress={() => goToOrdemServico(route.params?.etapas)} testID='button-os'>
          <Text style = {styles.textoButtonContinuarTipoConexao}>CONTINUAR</Text>
        </TouchableOpacity>

      </View>

    </View>

    </>

  );

};

export default PassoConexao;