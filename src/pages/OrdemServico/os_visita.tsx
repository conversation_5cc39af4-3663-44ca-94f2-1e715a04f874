import React, {useState} from 'react';
import {View, Text, ImageBackground, TouchableOpacity, Dimensions} from 'react-native';
import { useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// componente
import ProgressSteps from '../../componentes/ProgressSteps'

// constantes
import {DescricaoStatusOS, CoresStatusOS, TipoStatusOS } from "../../constantes"

// data
import {EtapasInstalacao} from "../../data"

// rotas drawer de navegação
import {StackTypes} from '../../pages/MenuPrincipal'

// modal messages
import MessageAvisoSucesso from '../../modal/messagebox/AvisoSucesso'

// localização
import { getCurrentPositionAsync, requestForegroundPermissionsAsync } from 'expo-location';


const OrdemServico = ({route}) => {

    const [showMessageAviso, setShowMessageAviso] = useState(false);

    const onMessageAvisoFechar = (etapas: EtapasInstalacao) => {

         setShowMessageAviso(false);

         goToResumoOSVisita(etapas);
    };

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();    

    // localização inicial
    const locationStart = { coords: {latitude: 0, longitude: 0, altitude: 0, accuracy: 0, altitudeAccuracy: 0, heading: 0, speed:0}, timestamp:0, mocked:false};
       
    // requisita a permissão de uso da localização do mobile
    async function requestLocationPermissions() {
    
        const {granted} = await requestForegroundPermissionsAsync();
    
        // se permitido
        if(granted) {

            try {                
                const currentPosition = await getCurrentPositionAsync();
                return currentPosition ?? locationStart;

            } catch (error) {
                // valores default
                return locationStart;
            }                        
        }
        else {
            // valores default
            return locationStart;
        }
    }

    // navega para pagina da fotos do local de instalação
    const goToPassoFotoLocal = async (etapas: EtapasInstalacao) => {
        
        let location = await requestLocationPermissions();
        
        // estrutura de etapas atual
        const _etapas: EtapasInstalacao = etapas;
      
        // atualiza a estrutura de etapas
        const etapasAtualizada = { ..._etapas, 
                                    latitude: location.coords.latitude,
                                    longitude: location.coords.longitude
                                };
      
        // navega para pagina
        navigation.navigate("PassoFotoLocal", {etapas: etapasAtualizada})

    };    

    // navega para pagina de teste de conexão da probe
    const goToPassoTesteConexao = (etapas: EtapasInstalacao) => {
        
        // navega para pagina        
        navigation.navigate("PassoTesteConexao", {etapas: etapas})
    };

    // navega para pagina de materiais utilizados
    const goToPassoMaterial = (etapas: EtapasInstalacao) => {
        
        // navega para pagina        
        navigation.navigate("PassoMaterial", {etapas: etapas})
    };

    // navega para pagina de resumo das etapas de instalação
    const goToResumoOSVisita = (etapas: EtapasInstalacao) => {

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas, status_os: TipoStatusOS.CONCLUIDO};

        navigation.navigate('ResumoOSVisita', {etapas: etapasAtualizada});                  
    };

    const heightHeader = 150;
    const heightInfoOrdem = 200;
    const heightFooter = 80;
    const heightSteps = Dimensions.get('window').height - heightHeader - heightInfoOrdem - heightFooter
    
    return (

        <>

            <MessageAvisoSucesso 
                visivel={showMessageAviso}
                titulo='SUCESSO!'
                textoBotao='OK'                 
                onFechar={() => onMessageAvisoFechar(route.params?.etapas)}
            />
        
            <View style = {styles.containerTela}>

                {/* cabeçalho */}
                <ImageBackground
                    source={require("../../assets/png/os-bar-bg.png")}
                    resizeMode='cover'
                    style={{top:0, left: 0, right: 0, height:heightHeader}}
                >            
                    <View style={{height:'30%'}}/>
                    <View style={{height:'20%', flexDirection:'row'}}>

                        {/* botão voltar */}
                        <View style={styles.containerBotaoVoltar}>
                            <TouchableOpacity onPress={()=>navigation.goBack()} testID='button-back'>
                                <IconVoltar color={"#FFFFFF"} width={30} height={30}/>
                            </TouchableOpacity>    
                        </View>

                        {/* texto cabeçalho */}
                        <View style={styles.containerTextoHeader}>
                            <Text style={styles.textoHeader}>ORDEM DE SERVIÇO</Text>
                        </View>
                    </View>

                    {/* Nome do Cliente */}
                    <View style={styles.containerNomeCliente}>
                        <Text style={styles.textoNomeCliente}>{route.params?.etapas.cliente}</Text>
                    </View>

                </ImageBackground>

                {/* informações da ordem de serviço*/}
                <View style={{height:heightInfoOrdem, paddingVertical:25, gap: 20}}>

                    <View style={{flexDirection:'row'}}>
                        <View style={{width:'70%', paddingStart: 30, gap: 20}}>
                            <Text style={styles.textoEtapasInstalacao}>ETAPAS DA VISITA</Text>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.textoOS}>OS: </Text>
                                <Text style={styles.textoNumeroOS}>{route.params?.etapas.numero_os}</Text>
                            </View>
                        </View>
                        <View style={{width:'30%', gap: 9}}>
                            <Text style={{
                                    ...styles.textoStatusOS, 
                                    backgroundColor:CoresStatusOS[(!route.params?.etapas.step3) ? 2 : (route.params?.etapas.step5) ? 0 : 1]
                                }}>
                                {DescricaoStatusOS[(!route.params?.etapas.step3) ? 2 : (route.params?.etapas.step5) ? 0 : 1]}
                            </Text>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.textoOS}>Tipo: </Text>
                                <Text style={styles.textoNumeroOS}>VISIT</Text>
                            </View>
                        </View>    
                    </View>

                    <View style={{width:'100%', paddingStart: 30, gap: 20}}>
                    <View style={{width:'100%', flexDirection:'column'}}>
                        <Text style={styles.textoOS}>Problema relatado: </Text>
                        <Text style={styles.textoNumeroOS}>{route.params?.etapas.descricao_problema}</Text>
                    </View> 

                </View>                

                </View>
                {/* etapas de instalação */}
                <View style={{height:heightSteps}}>
                    <ProgressSteps                                         

                       textStep1='Fotos do local'
                       onPressStep1={() => goToPassoFotoLocal(route.params?.etapas)} 
                       step1={route.params?.etapas.step3}
                       
                       textStep2='Testar conexão'
                       onPressStep2={() => goToPassoTesteConexao(route.params?.etapas)}
                       step2={route.params?.etapas.step4}
                       
                       textStep3='Material utilizado'
                       onPressStep3={() => goToPassoMaterial(route.params?.etapas)} 
                       step3={route.params?.etapas.step5}
                       
                       showStep4={false}
                       showStep5={false}
                       showStep6={false}
                       showStep7={false}
                    />
                </View>

                {
                    (route.params?.etapas.step5) &&
                    <View style={{justifyContent:'flex-end', paddingHorizontal:20, height:heightFooter}}>
                        <TouchableOpacity style={styles.buttonContinuarPassoID} onPress={() => setShowMessageAviso(true)}>
                            <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
                        </TouchableOpacity>                
                    </View>
                }

            </View>

        </>
    );
}

export default OrdemServico;
