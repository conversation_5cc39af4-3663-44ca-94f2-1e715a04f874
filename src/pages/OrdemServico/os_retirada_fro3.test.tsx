import React from 'react';
import { render, fireEvent, act, screen } from '@testing-library/react-native';
import OrdemServico from "./os_retirada_fro3";
import { useNavigation } from '@react-navigation/native';
import { MockEtapasInstalacaoFro1Hoje } from '../../../__mocks__/EtapasInstalacaoFro1HojeMock';

// Mock para useNavigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

const mockRoute1 = {
    params: {
      etapas: { ...MockEtapasInstalacaoFro1Hoje()}
    },
};

describe('OrdemServico - Troca Fro 3', () => {
    
    const mockNavigation = {
        navigate: jest.fn(),
        goBack: jest.fn(),
    };

    beforeEach(() => {
        (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    });

    it('deve renderizar o texto correto no cabeçalho', () => {
        const route = { params: { etapas: { cliente: 'Cliente Teste', numero_os: '12345' } } };

        const { getByText } = render( <OrdemServico route={route} /> );

        expect(getByText('ORDEM DE SERVIÇO')).toBeTruthy();
        expect(getByText('Cliente Teste')).toBeTruthy();
        expect(getByText('OS:')).toBeTruthy();
        expect(getByText('12345')).toBeTruthy();
    });

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {

        const {getByTestId} = render(<OrdemServico route={mockRoute1} />);
    
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('deve renderizar corretamente a tela se step1 = true e step6 = true', () => {
        const route = { params: { etapas: { step1: true, step6: true } } };

        const {} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();
    });

    it('deve renderizar corretamente a tela se step1 = true e step6 = false', () => {
        const route = { params: { etapas: { step1: true, step6: false } } };

        const {} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();
    });  
    
    it('deve exibir o modal de sucesso ao pressionar o botão "CONTINUAR"', () => {
        const route = { params: { etapas: { step7: true } } };

        const { getByText } = render(<OrdemServico route={route} />);

        const continuarButton = getByText('CONTINUAR');
        act(() => {
            fireEvent.press(continuarButton);
        });

        expect(getByText('SUCESSO!')).toBeTruthy();

        act(() => {
            fireEvent.press(getByText('OK'));
        });        
    });

    it('deve renderizar corretamente ao Material utilizado', () => {
        const route = { params: { etapas: { step1: true, step5:true } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Material utilizado');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });  
    
    it('deve renderizar corretamente ao Fotos após retirada', () => {
        const route = { params: { etapas: {step1: true, step2: true, step5: true, step7: true } } };
 
        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Fotos após retirada');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });
});
