import React, {useState} from 'react';
import {View, Text, ImageBackground, TouchableOpacity, Dimensions} from 'react-native';
import { useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// componente
import ProgressSteps from '../../componentes/ProgressSteps'

// constantes
import {DescricaoStatusOS, CoresStatusOS } from "../../constantes"

import {EtapasInstalacao} from "../../data"

// rotas drawer de navegação
import {StackTypes} from '../../pages/MenuPrincipal'

// modal messages
import MessageAvisoSucesso from '../../modal/messagebox/AvisoSucesso'


const OrdemServico = ({route}) => {

    const [showMessageAviso, setShowMessageAviso] = useState(false);

    const onMessageAvisoFechar = (etapas: EtapasInstalacao) => {

         setShowMessageAviso(false);

         goToResumoOSRetirada(etapas);
    };

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();    

   // navega para pagina de materiais utilizados
    const goToPassoMaterial = (etapas: EtapasInstalacao) => {
        
        // navega para pagina        
        navigation.navigate("PassoMaterial", {etapas: etapas})
    };

    // navega para pagina das fotos de instalação
    const goToPassoFotoRetirada = (etapas: EtapasInstalacao) => {
        
        // navega para pagina        
        navigation.navigate("PassoFotoRetirada", {etapas: etapas})
    };

    // navega para pagina de resumo das etapas de instalação
    const goToResumoOSRetirada = (etapas: EtapasInstalacao) => {

        navigation.navigate('ResumoOSRetiradaFro3', {etapas: etapas});                  
    };

    const heightHeader = 150;
    const heightInfoOrdem = 110;
    const heightFooter = 80;
    const heightSteps = Dimensions.get('window').height - heightHeader - heightInfoOrdem - heightFooter
    
    return (

        <>

            <MessageAvisoSucesso 
                visivel={showMessageAviso}
                titulo='SUCESSO!'
                textoBotao='OK'                 
                onFechar={() => onMessageAvisoFechar(route.params?.etapas)}
            />
        
            <View style = {styles.containerTela}>

                {/* cabeçalho */}
                <ImageBackground
                    source={require("../../assets/png/os-bar-bg.png")}
                    resizeMode='cover'
                    style={{top:0, left: 0, right: 0, height:heightHeader}}
                >            
                    <View style={{height:'30%'}}/>
                    <View style={{height:'20%', flexDirection:'row'}}>

                        {/* botão voltar */}
                        <View style={styles.containerBotaoVoltar}>
                            <TouchableOpacity onPress={()=>navigation.goBack()} testID='button-back'>
                                <IconVoltar color={"#FFFFFF"} width={30} height={30}/>
                            </TouchableOpacity>    
                        </View>

                        {/* texto cabeçalho */}
                        <View style={styles.containerTextoHeader}>
                            <Text style={styles.textoHeader}>ORDEM DE SERVIÇO</Text>
                        </View>
                    </View>

                    {/* Nome do Cliente */}
                    <View style={styles.containerNomeCliente}>
                        <Text style={styles.textoNomeCliente}>{route.params?.etapas.cliente}</Text>
                    </View>

                </ImageBackground>

                {/* informações da ordem de serviço*/}
                <View style={{height:heightInfoOrdem, paddingVertical:25, gap: 20}}>

                    <View style={{flexDirection:'row'}}>
                        <View style={{width:'70%', paddingStart: 30, gap: 20}}>
                            <Text style={styles.textoEtapasInstalacao}>ETAPAS DA RETIRADA</Text>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.textoOS}>OS: </Text>
                                <Text style={styles.textoNumeroOS}>{route.params?.etapas.numero_os}</Text>
                            </View>
                        </View>
                        <View style={{width:'30%', gap: 9}}>
                            <Text style={{...styles.textoStatusOS, backgroundColor:CoresStatusOS[route.params?.etapas.status_os]}}>
                                {DescricaoStatusOS[route.params?.etapas.status_os]}
                            </Text>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.textoOS}>Tipo: </Text>
                                <Text style={styles.textoNumeroOS}>FRO 1.3</Text>
                            </View>                            
                        </View>    
                    </View>

                </View>

                {/* etapas de instalação */}
                <View style={{height:heightSteps}}>
                    <ProgressSteps                                         
                        textStep1='Material utilizado'
                        onPressStep1={() => goToPassoMaterial(route.params?.etapas)} 
                        step1={route.params?.etapas.step5}
                        textStep2='Fotos após retirada'
                        onPressStep2={() => goToPassoFotoRetirada(route.params?.etapas)} 
                        step2={route.params?.etapas.step7}
                        showStep3={false}
                        showStep4={false}
                        showStep5={false}
                        showStep6={false} 
                        showStep7={false} 
                    />
                </View>

                {
                    (route.params?.etapas.step7) &&
                    <View style={{justifyContent:'flex-end', paddingHorizontal:20, height:heightFooter}}>
                        <TouchableOpacity style={styles.buttonContinuarPassoID} onPress={() => setShowMessageAviso(true)}>
                            <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
                        </TouchableOpacity>                
                    </View>
                }

            </View>

        </>
    );
}

export default OrdemServico;
