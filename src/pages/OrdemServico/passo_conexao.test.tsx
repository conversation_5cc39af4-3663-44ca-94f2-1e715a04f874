import React from "react";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import PassoConexao from "./passo_conexao";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                tipo_conexao: 1, 
                operadora: 4,
      }
    },
};


describe('Passo Conexão', () => {

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoConexao route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <PassoConexao route={mockRoute1} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela se tipo conexão gsm foi selecionada', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute1} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('check-gsm'));
        });        
    });
    
    it('renderiza corretamente a tela se tipo conexão ethernet foi selecionada', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute1} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('check-ethernet'));
        });        
    });
    
    it('renderiza corretamente a tela se tipo conexão wifi foi selecionada', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute1} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('check-wifi'));
        });        
    });
    
    it('renderiza corretamente a tela se clicar no botão continuar se não foi selecionado nenhuma conexão', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute1} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-os'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('OK'));
        });        
    });
    
    it('renderiza corretamente a tela se clicar no botão continuar se foi selecionado gsm mas nenhuma operadora', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute2} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('check-gsm'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-os'));
        });        
    });
    
    it('renderiza corretamente a tela se clicar no botão continuar se foi selecionado wifi', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoConexao route={mockRoute1} />
                                     </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('check-wifi'));
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-os'));
        });        
    });    
});