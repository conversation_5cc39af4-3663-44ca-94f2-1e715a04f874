import React from "react";
import { fireEvent, render, screen, act } from "@testing-library/react-native";
import DetalhesOSAgendada from "./detalhes_os_agendada";
import { NavigationContainer } from "@react-navigation/native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";

jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
      }),
    };
});

// Mock do LeafletView
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => {
    return {
      LeafletView: () => null,  // Ou 'LeafletView' se preferir apenas renderizar o nome como string
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                latitude: 0,
                longitude: 0,                
                tipo_conexao: 1,
      }
    },
};

const mockRoute3 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                numero_os: '00000000',
                descricao_problema : undefined,
                medidor_modelo: -1,
                medidor_fabricante: -1
      }
    },
};

describe('Detalhes OS agendada', () => {
    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOSAgendada route={mockRoute1} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <DetalhesOSAgendada route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });
    
    it('renderiza corretamente a tela com tipo conexão gsm', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOSAgendada route={mockRoute2} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    

    it('renderiza corretamente a tela numero os invalida', () => {
    
        const {} = render(  <NavigationContainer>
                                <DetalhesOSAgendada route={mockRoute3} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });    
});