import {View, Text, TouchableOpacity, TextInput, Image} from 'react-native';
import React, { useState, useRef } from 'react';

// navegação das telas
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {heightHeader, screenHeight, styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'
import IconFlash from '../../assets/svg/icon_solid-flash.svg'
import IconFlashOff from '../../assets/svg/icon_solid-flash-slash.svg'
import IconCameraRotate from '../../assets/svg/icon_solid-camera-rotate.svg'

// modal messages
import MessageBoxErro from '../../modal/messagebox/Erro'

// rotas drawer de navegação
import {StackTypes} from '../../pages/MenuPrincipal'

// dados de instalação
import {EtapasInstalacao} from "../../data"

// uso do qrcode scanner
import QRCodeScanner from 'react-native-qrcode-scanner';
import { RNCamera } from 'react-native-camera';
import { ScreenTipoOS} from '../../constantes';

const PassoID = ({route}) => {
  
  const qrCodeRef = useRef<View>(null);

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  // se esta usando qr code para obter o id da probe
  const [usaQrCode, setUsaQrCode] = useState<boolean>(true);

  // seta o id codigo da probe
  const [codigo, setCodigo] = useState<string>(route.params?.etapas?.codigo_probe ?? '');

  // habilita o flash da camera
  const [flash, setFlash] = useState<boolean>(false);
  const [cameraFrontal, setCameraFrontal] = useState<boolean>(false);

  const [textoMessageBoxErro, setTextoMessageBoxErro] = useState("");
  const [showMessageBoxErro, setShowMessageBoxErro] = useState(false);

  // verifica o codigo digitado
  const SeCodigoOK = () => {

    // se foi digitado algum e-mail ou e-mail incorreto
    if (codigo == "") {

      setTextoMessageBoxErro("Digite o código da Probe");
      onMessageBoxErroAbrir();
  
      return false;
    }
  
    // se foi digitado algum e-mail ou e-mail incorreto
    if (codigo.length > 14) {
  
      setTextoMessageBoxErro("O código da Probe possui até 14 caracteres alfanúmericos.");
      onMessageBoxErroAbrir();
  
      return false;
    }    

    return true
  };

  // navega para pagina de inserção de conexão da probe
  const goToPassoConexao = (etapas: EtapasInstalacao) => {

    if(!SeCodigoOK())
      return;

    // estrutura de etapas atual
    const _etapas: EtapasInstalacao = etapas;

    // atualiza a estrutura de etapas
    const etapasAtualizada = { ..._etapas, codigo_probe: codigo };

    // navega para pagina
    navigation.navigate("PassoConexao", {etapas: etapasAtualizada})

  }; 
  
  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
          
    const tipoOS = route.params?.etapas?.tipo_os ?? 0;

    // navega a pagina progresso da instalação
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
            { name: ScreenTipoOS[tipoOS], params: {etapas: etapas} },
        ],
      })
    );
  };

  const onMessageBoxErroAbrir = () => { setShowMessageBoxErro(true); };
  const onMessageBoxErroFechar = () => { setShowMessageBoxErro(false); };

  const onFlash = () => { setFlash(!flash); };
  const onCameraFrontal = () => { setCameraFrontal(!cameraFrontal); };
  
  return (
    
    <>

    <MessageBoxErro 
      visivel={showMessageBoxErro} 
      titulo="Erro ao vincular Probe" 
      descricao={textoMessageBoxErro} 
      textoBotao="OK" 
      onFechar={onMessageBoxErroFechar}
    />        

    <View style = {styles.containerTelaPassoID}>

      {/* header */}
      <View style={styles.containerHeaderTelaPassoID}>

          {/* botão voltar */}
          <View style={styles.containerBotaoVoltar}>
            <TouchableOpacity onPress={()=> goBack(route.params?.etapas)} testID='button-back'>
              <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
            </TouchableOpacity>    
          </View>

          {/* texto cabeçalho */}
          <View style={styles.containerTextoHeader}>
            <Text style={styles.textoHeaderPassoID}>VINCULAR PROBE</Text>
          </View>

      </View>

      {
        (usaQrCode) ?
            
            <QRCodeScanner
              ref={qrCodeRef}
              containerStyle={styles.containerConteudoQRCode}
              cameraStyle={styles.cameraQRCode}
              onRead={({data}) => {setCodigo(data)}}
              flashMode={(flash) ? RNCamera.Constants.FlashMode.on : RNCamera.Constants.FlashMode.off}
              cameraType= {(cameraFrontal) ? 'front' : 'back'}
              topContent={
                <View style={{marginTop: -80, justifyContent:'center', alignItems:'center'}}>
                  <Text style={styles.textoDescricaoQRCode} >Aponte a câmera do celular para o QR Code</Text>
                  <Text style={styles.textoDescricaoQRCode} >localizado na parte superior da Probe</Text>
                </View>
                
              }
              bottomContent={

                <View style={{height: screenHeight - heightHeader, justifyContent:'flex-start', gap: 30 }}>

                  <View style={{marginTop: -screenHeight * 0.3, height: 450, justifyContent:'space-between', alignItems:'center'}}>

                      <Image style={{}}
                             source={require('../../assets/png/scan-mask.png')}                    
                      />

                      <View style={{width:'80%', flexDirection:'row', justifyContent:'space-between'}} >
                        <TouchableOpacity onPress={() => onFlash()} testID='button-flash'>
                          {
                            (flash)                            
                            ?
                              <IconFlash color={"#FAFAFA"} width={40} height={40}/>
                            :
                              <IconFlashOff color={"#FAFAFA"} width={40} height={40}/>
                          }
                          
                        </TouchableOpacity>
                        <TouchableOpacity onPress={() => onCameraFrontal()} testID='button-camera'>
                          <IconCameraRotate color={"#FAFAFA"} width={40} height={40}/>
                        </TouchableOpacity>                  
                      </View>
                
                  </View>

                  <View style={{marginTop: 0, height: 130, justifyContent:'flex-end', gap: 10}}>

                    { 
                      (codigo) &&
                      <TouchableOpacity style={styles.buttonContinuarPassoID}
                                        onPress={() => goToPassoConexao(route.params?.etapas)}
                                        testID='button-connection'>
                        <Text style = {styles.textoButtonContinuarPassoID}>{codigo}</Text>
                      </TouchableOpacity>
                    }

                    <TouchableOpacity style={{...styles.buttonContinuarPassoID, backgroundColor:'#737373'}}
                                      onPress={ () => setUsaQrCode(false)}
                                      testID='button-input'>
                      <Text style = {styles.textoButtonContinuarPassoID}>DIGITAR CÓDIGO</Text>
                    </TouchableOpacity>          

                  </View>
                </View>

              }
            />          
        :
          // usa o teclado para entrar com o id da probe
          <View style = {styles.containerConteudoTelaPassoID}>

            <View style={{gap:5}}>
          
              <Text style={styles.textoDigiteCodigo}>Digite o código</Text>

              <View style={styles.containerInputCodigo}>
          
                <TextInput
                    style={styles.textoInputCodigo}
                    autoCapitalize = {"characters"}
                    placeholder="" 
                    keyboardType='ascii-capable'
                    value={codigo}
                    maxLength={12}                  
                    onChangeText={(texto) => setCodigo(texto.replace(/[^a-z0-9]/gi, ''))} 
                    testID='input-code'
                />

              </View>

            </View>

            <TouchableOpacity style={styles.buttonContinuarPassoID}
                              onPress={() => goToPassoConexao(route.params?.etapas)}
                              testID='button-continue'>
              <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
            </TouchableOpacity>

          </View>        
      }


    </View>

    </>

  );

};

export default PassoID;