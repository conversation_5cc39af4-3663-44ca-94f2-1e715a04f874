import React, {useState } from 'react';
import {View, ScrollView, Text, ImageBackground, TouchableOpacity, Image, SafeAreaView} from 'react-native';
import { CommonActions, useNavigation } from '@react-navigation/native';

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconPin from '../../assets/svg/icon_marker-pin-01.svg';
import IconCalendario from '../../assets/svg/icon_calendar.svg';
import IconUsuario from '../../assets/svg/icon_user-01.svg';
import IconGrid from '../../assets/svg/icon_grid-01.svg';

// estrutura de etapas de instalação
import {EtapasInstalacao, ListaOrdensServicoHoje, ListaMedidorFabricante, ListaMedidorModelo} from "../../data"

// rotas drawer de navegação
import {StackTypes} from '../../pages/MenuPrincipal/index'

// constantes
import { DescricaoOS, DescricaoStatusOS, CoresStatusOS, DescricaoConexao, DescricaoOperadora, ScreenTipoOS, TipoConexao } from "../../constantes"

// mapas
import Mapa from '../../componentes/Mapa';

const ResumoOSManutencao = ({route}) => {

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();
  
  const heightHeader = 300;
  const heightHeaderTitle = 60;
  const heightHeaderCliente = 80;
  const heightHeaderInfo = 100;

  const GetOrdemServico = () => {

    for (let i = 0; i < ListaOrdensServicoHoje.length; i++) {
      if(route.params?.etapas.numero_os == ListaOrdensServicoHoje[i].numero_os){
        return(i);
      }
    }
  }

  const [indiceOS, setIndiceOS] = useState(() => GetOrdemServico());

  // navega para pagina de inserção de observações do tecnico
  const goToObservacoes = (etapas: EtapasInstalacao) => {

    // navega para pagina
    navigation.navigate("PassoObservacoes", {etapas: etapas})
  };

  // navega para pagina de inserção da assinatura do cliente
  const goToAssinatura = (etapas: EtapasInstalacao) => {

    // navega para pagina
    navigation.navigate("PassoAssinatura", {etapas: etapas})
  };

  const goToOrdensServico = () => {

    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes:[
          { name: 'OrdensServico' },
        ],
      })
    );    
    
  };

  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
           
    // navega a pagina progresso da instalação
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
          { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
        ],
      })
     );
  };  

  return (

    <ScrollView style = {styles.containerTelaResumoOS}>

      {/* cabeçalho */}
      <ImageBackground
        source={require("../../assets/png/os-resum-bg.png")}
        resizeMode='cover'
        style={{top:0, left: 0, right: 0, height:heightHeader}}
      >            
        <View style={{height:'10%'}}/>

          <View style={{height:heightHeaderTitle, flexDirection:'row'}}>

            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                <IconVoltar color={"#FFFFFF"} width={30} height={30}/>
              </TouchableOpacity>    
            </View>

            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeader}>RESUMO DA OS</Text>
            </View>

          </View>

          {/* Logo e nome do cliente */}
          <View style={{height:heightHeaderCliente, marginStart: 30, justifyContent:'flex-start', alignItems:'center', flexDirection:'row', gap:15}}>
            <View style={{justifyContent:'center', alignItems:'center', width:'30%', height:50, borderRadius:5, borderWidth:2, borderColor:'#F5F5F5'}}>
              <Text style={{color:'#FFFFFF'}}>Logo</Text>
            </View>

            <View style={{width:'70%', flexDirection:'column'}}>
              <Text style={styles.textoResumoOSNomeFantasia}>{route.params?.etapas.cliente}</Text>
              <Text style={styles.textoResumoOSNomeCliente}>{`(${route.params?.etapas.cliente})`}</Text>
            </View>
          </View>

          {/* informações da ordem de serviço */}
          <View style={{gap:20}}>

            {/* tipo e status da ordem de serviço */}
            <View style={{flexDirection:'row', alignItems:'center'}}>

              <View style={{width:'70%', paddingStart: 30, gap: 20}}>              
                <View style={{flexDirection:'row'}}>
                  <Text style={styles.textoResumoOSNormal}>Tipo: </Text>
                  <Text style={{...styles.textoResumoOSNormal, fontWeight:'800'}}>{DescricaoOS[route.params?.etapas.tipo_os]}</Text>
                </View>                    
              </View>
              <View style={{width:'30%'}}>
                <Text style={{...styles.textoStatusOS, backgroundColor:CoresStatusOS[route.params?.etapas.status_os]}}>
                      {DescricaoStatusOS[route.params?.etapas.status_os]}
                </Text>
              </View>
            </View>

            {/* número de série e ordem de serviço */}
            <View style={{flexDirection:'row', alignItems:'center'}}>

              <View style={{width:'70%', paddingStart: 30, gap: 20}}>              
                <View style={{flexDirection:'row', alignItems:'flex-start'}}>
                  <Text style={styles.textoResumoOSDestaque}>Nº Série Medidor : </Text>
                  <Text style={styles.textoResumoOSNormal}>{route.params?.etapas.medidor_ns}</Text>
                </View>                    
              </View>
              <View style={{width:'30%', flexDirection:'row', alignItems:'center'}}>
                <Text style={styles.textoResumoOSDestaque}>OS: </Text>
                <Text style={styles.textoResumoOSNormal}>{route.params?.etapas.numero_os}</Text>
              </View>
            </View>

        </View>

      </ImageBackground>

      {/* mapa*/}
      <View style={{marginTop:-25, height:200, paddingHorizontal:25, justifyContent:'center', alignItems: 'center'}}>
      {          
          (route.params?.etapas.latitude && route.params?.etapas.longitude) 
          ?
            <SafeAreaView style={{height:'100%', width:'100%', borderRadius:10}}>
              <Mapa 
                latitude={route.params?.etapas.latitude} 
                longitude={route.params?.etapas.longitude} 
                zoom={25}
              />
            </SafeAreaView>
          : 
            <View style={{height:'100%', width:'100%', borderRadius:10, backgroundColor:'#E5E5E5'}}/>
      }
      </View>

        {/* endereço */}
        <View style={{flexDirection:'row', paddingTop: 10, paddingHorizontal:25}}>
        
          <View style={{width:'10%'}}>
              <IconPin color={"#2E8C1F"} height={20} width={20}/>
          </View>

          <View style={{width:'90%'}}>

            {/* endereco */}
            <View style={{flexDirection:'row', gap:3}}>
                <Text style={styles.textoInfoOSDestaque}>Endereço:</Text>
                <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.endereco ?? ''}</Text>
            </View>
    
            {/* bairro */}
            <View style={{flexDirection:'row', gap:3}}>
                <Text style={styles.textoInfoOSDestaque}>Bairro:</Text>
                <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.bairro ?? ''}</Text>
            </View>

            {/* cep */}
            <View style={{flexDirection:'row', gap:3}}>
                <Text style={styles.textoInfoOSDestaque}>CEP:</Text>
                <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.cep ?? ''}</Text>
            </View>            
          </View>            
        </View>        

        {/* horario agendado */}
        <View style={{flexDirection:'row', paddingTop: 10, paddingHorizontal:25}}>
                                    
          <View style={{width:'10%'}}>
            <IconCalendario color={"#2E8C1F"} height={20} width={20}/>
          </View>
          <View style={{width:'90%'}}>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Agendado: </Text>
              <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.agenda_data ?? ''}</Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Iniciado: </Text>
              <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.horario_ini}</Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Encerrado: </Text>
              <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.horario_fim}</Text>
            </View>
          </View>

        </View>        

        {/* informações do contato */}
        <View style={{flexDirection:'row', paddingTop: 10, paddingHorizontal:25}}>

          <View style={{width:'10%'}}>
            <IconUsuario color={"#2E8C1F"} height={20} width={20}/>
          </View>
          <View style={{width:'90%'}}>                                
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Contato: </Text>
              <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.contato ?? ''}</Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Telefone: </Text>
              <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.telefone ?? ''}</Text>
            </View>                                                
          </View>
          
      </View>              

      {/* pontos de medição */}
      <View style={{flexDirection:'row', paddingTop: 10, paddingHorizontal:25}}>

        <View style={{width:'10%'}}>
          <IconGrid color={"#2E8C1F"} height={20} width={20}/>
        </View>
        <View style={{width:'90%'}}>
          <View style={{flexDirection:'row', gap:3}}>
            <Text style={styles.textoInfoOSDestaque}>Pontos de Medição: </Text>
            <Text style={styles.textoInfoOSSimples}>{ListaOrdensServicoHoje[indiceOS]?.numero_pontos ?? 0}</Text>
          </View>                        
        </View>

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* Problema Relatado */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

          <Text style={styles.textoInfoOSTitulo}>Problema Relatado</Text>

          <View>
            <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.descricao_problema}</Text>
          </View>          

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />
      

      {/* informações da Probe */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

          <Text style={styles.textoInfoOSTitulo}>Probe </Text>

          <View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Código: </Text>
              <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.codigo_probe}</Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Conexão: </Text>
              <Text style={styles.textoInfoOSSimples}>{DescricaoConexao[route.params?.etapas.tipo_conexao]}</Text>
            </View>
  
            {
              (route.params?.etapas.tipo_conexao === TipoConexao.GSM) &&
                <View style={{flexDirection:'row', gap:3}}>
                  <Text style={styles.textoInfoOSDestaque}>Operadora: </Text>
                  <Text style={styles.textoInfoOSSimples}>{DescricaoOperadora[route.params?.etapas.operadora]}</Text>
                </View>
            }
            {
              (route.params?.etapas.tipo_conexao === TipoConexao.ETHERNET) &&
                <View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>IP: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.ip}</Text>
                  </View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Máscara: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.mascara}</Text>
                  </View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Gateway: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.gateway}</Text>
                  </View>
                </View>
            }
            {
              (route.params?.etapas.tipo_conexao === TipoConexao.WIFI) &&
                <View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Rede Wi-Fi: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.ssid}</Text>
                  </View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Senha: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.ssid_senha}</Text>
                  </View>
                </View>
            }

          </View>

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* informações do Medidor */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

        <Text style={styles.textoInfoOSTitulo}>Medidor </Text>

        <View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Modelo: </Text>
              <Text style={styles.textoInfoOSSimples}> 
                { ListaMedidorModelo.filter(medidor => medidor.id_fabricante === route.params?.etapas.medidor_fabricante)[route.params?.etapas.medidor_modelo]?.descricao ?? ''}
              </Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Fabricante: </Text>
              <Text style={styles.textoInfoOSSimples}>{ListaMedidorFabricante[route.params?.etapas.medidor_fabricante]?.descricao ?? ''}</Text>
            </View>
            <View style={{flexDirection:'row', gap:3}}>
              <Text style={styles.textoInfoOSDestaque}>Nº de série: </Text>
              <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.medidor_ns}</Text>
            </View>
        </View>

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* Fotos do local */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

        <Text style={styles.textoInfoOSTitulo}>Fotos do local </Text>

        <View style = {{flexDirection:'row', gap: 15}}>
          {
            (route.params?.etapas.imagem_local1) && <Image source={{ uri: route.params?.etapas.imagem_local1 }} resizeMode='contain' style={{height:80, width: 80}}/>              
          }
          {
            (route.params?.etapas.imagem_local2) && <Image source={{ uri: route.params?.etapas.imagem_local2 }} resizeMode='contain' style={{height:80, width: 80}}/>            
          }
          {
            (route.params?.etapas.imagem_local3) && <Image source={{ uri: route.params?.etapas.imagem_local3 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }
          {
            (route.params?.etapas.imagem_local4) && <Image source={{ uri: route.params?.etapas.imagem_local4 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }
          {
            (route.params?.etapas.imagem_local5) && <Image source={{ uri: route.params?.etapas.imagem_local5 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }
          {
            (route.params?.etapas.imagem_local6) && <Image source={{ uri: route.params?.etapas.imagem_local6 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }
          {
            (route.params?.etapas.imagem_local7) && <Image source={{ uri: route.params?.etapas.imagem_local7 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }
          {
            (route.params?.etapas.imagem_local8) && <Image source={{ uri: route.params?.etapas.imagem_local8 }} resizeMode='contain' style={{height:80, width: 80}}/>
          }          
        </View>

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* Teste de Conexão */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

          <Text style={styles.textoInfoOSTitulo}>Conexão </Text>

          <View style={{flexDirection:'row', gap:3}}>
            <Text style={styles.textoInfoOSDestaque}>Leitura aferida: </Text>
            <Text style={styles.textoInfoOSSimples}>{(route.params?.etapas.teste_conexao) ? 'Sucesso' : 'Falha'}</Text>
          </View>          

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* Materiais utilizados */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

          <Text style={styles.textoInfoOSTitulo}>Material utilizado </Text>

          <View>
            {
              (route.params?.etapas.totalMaterial1) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial1} x ${route.params?.etapas.textoMaterial1}`}</Text>               
            } 
            {              
              (route.params?.etapas.totalMaterial2) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial2} x ${route.params?.etapas.textoMaterial2}`}</Text>               
            } 
            {
              (route.params?.etapas.totalMaterial3) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial3} x ${route.params?.etapas.textoMaterial3}`}</Text>               
            } 
            {
              (route.params?.etapas.totalMaterial4) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial4} x ${route.params?.etapas.textoMaterial4}`}</Text>               
            } 
            {
              (route.params?.etapas.totalMaterial5) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial5} x ${route.params?.etapas.textoMaterial5}`}</Text>               
            }
            {
              (route.params?.etapas.totalMaterial6) &&
                <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.totalMaterial6} x ${route.params?.etapas.textoMaterial6}`}</Text>               
            }
            {
              (route.params?.etapas.materialExtra) &&
                <View style={{gap:3}}>
                  <Text style={styles.textoInfoOSDestaque}>Material Extra: </Text>
                  <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.textoMaterialExtra}</Text>
                </View>              
            }               
          </View>

      </View>

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* Fotos da instalação */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

          <Text style={styles.textoInfoOSTitulo}>Fotos do instalação </Text>

          <View style = {{flexDirection:'row', gap: 15}}>
            {
              (route.params?.etapas.imagem_instalacao1) && <Image source={{ uri: route.params?.etapas.imagem_instalacao1 }} resizeMode='contain' style={{height:80, width: 80}}/>              
            }
            {
              (route.params?.etapas.imagem_instalacao2) && <Image source={{ uri: route.params?.etapas.imagem_instalacao2 }} resizeMode='contain' style={{height:80, width: 80}}/>            
            }
            {
              (route.params?.etapas.imagem_instalacao3) && <Image source={{ uri: route.params?.etapas.imagem_instalacao3 }} resizeMode='contain' style={{height:80, width: 80}}/>
            }
            {
              (route.params?.etapas.imagem_instalacao4) && <Image source={{ uri: route.params?.etapas.imagem_instalacao4 }} resizeMode='contain' style={{height:80, width: 80}}/>
            }
            {
              (route.params?.etapas.imagem_instalacao5) && <Image source={{ uri: route.params?.etapas.imagem_instalacao5 }} resizeMode='contain' style={{height:80, width: 80}}/>
            }
            {
              (route.params?.etapas.imagem_instalacao6) && <Image source={{ uri: route.params?.etapas.imagem_instalacao6 }} resizeMode='contain' style={{height:80, width: 80}}/>
            }
          </View>          

      </View>
      
      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {/* observações finais do tecnico */}
      <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: (route.params?.etapas.observacoes) ? 20 : 5}}>

        <Text style={styles.textoInfoOSTitulo}>Observações do Técnico</Text>

            <Text style={styles.textoInfoOSSimples}>{`${route.params?.etapas.observacoes}`}</Text> 
          
            {
              // se resumo não foi validado permite alterar as observações finais
              (!route.params?.etapas.cliente_nome.length) &&
                <TouchableOpacity style = {{...styles.buttonObservacoes, backgroundColor: (route.params?.etapas.observacoes) ? '#A3A3A3' : '#2E8C1F'}}
                                  onPress={() => goToObservacoes(route.params?.etapas)}
                                  testID='button-observation'>
                  <Text style = {styles.textoAssinatura}>{(route.params?.etapas.observacoes) ? `EDITAR OBSERVAÇÕES` : `OBSERVAÇÕES FINAIS` } </Text>
                </TouchableOpacity>
            }
        
      </View>


      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />
      
      {
        // assinatura cliente
        (route.params?.etapas.observacoes) &&
          <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>

            <Text style={styles.textoInfoOSTitulo}>Assinatura do Cliente</Text>

            {
              // se tem assinatura apresenta a assinatura
              (route.params?.etapas.imagem_assinatura)
              ?                 
                <View style={{gap:5}}>
                  <Image source={{ uri: route.params?.etapas.imagem_assinatura }} resizeMode='contain' style={{height:100, width:'100%'}}/>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Nome: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.cliente_nome}</Text>
                  </View>
                  <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoInfoOSDestaque}>Telefone: </Text>
                    <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.cliente_telefone}</Text>
                  </View>
                </View>
              :
                // se não tem o nome do cliente permite assinatura
                (!route.params?.etapas.cliente_nome.length)
                ?
                  <TouchableOpacity style = {styles.buttonAssinatura}
                                    onPress={() => goToAssinatura(route.params?.etapas)}
                                    testID='button-signature'>
                    <Text style = {styles.textoAssinatura}>ASSINAR</Text>
                  </TouchableOpacity>
                :                  
                  <View style={{gap:5}}>
                    <Text style={styles.textoInfoOSDestaque}>Cliente não concordou com o trabalho realizado.</Text>
                    <View style={{flexDirection:'row', gap:3}}>
                      <Text style={styles.textoInfoOSDestaque}>Nome: </Text>
                      <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.cliente_nome}</Text>
                    </View>
                    <View style={{flexDirection:'row', gap:3}}>
                      <Text style={styles.textoInfoOSDestaque}>Telefone: </Text>
                      <Text style={styles.textoInfoOSSimples}>{route.params?.etapas.cliente_telefone}</Text>
                    </View>
                  </View>                  
            } 
            
          </View>
      }

      {/* divisor */}
      <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />

      {
        // se tem o nome do cliente já foi feito etapa de assinatura
        (route.params?.etapas.cliente_nome) &&
          // enviar OS
          <View style={{flexDirection:'column', paddingTop: 10, paddingHorizontal:25, gap: 20}}>
            <View>
              <TouchableOpacity style = {styles.buttonAssinatura} onPress={() => goToOrdensServico()} testID='button-send'>
                <Text style = {styles.textoAssinatura}>ENVIAR</Text>
              </TouchableOpacity>

              {/* divisor */}
              <View style={{...styles.linhaHorizontal, paddingTop: 10, paddingBottom: 10}} />            
            </View>
          </View>
      }

    </ScrollView>
  );
};

export default ResumoOSManutencao;