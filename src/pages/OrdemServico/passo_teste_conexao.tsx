import {View, Text, TouchableOpacity} from 'react-native';
import React, { useEffect, useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native';

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index';

import {EtapasInstalacao} from "../../data";

import {EventRegister} from 'react-native-event-listeners';

import { connectMQTT, disconnectMQTT, isMQTTConnected, subscribeMQTT } from '../../services/mqtt';
import StatusWiFi from '../../componentes/StatusWiFi';
import StatusGSM from '../../componentes/StatusGSM';
import { ScreenTipoOS, TipoConexao } from '../../constantes';
import StatusBattery from '../../componentes/StatusBattery';
import StatusVBattery from '../../componentes/StatusVBattery';
import StatusDateTime from '../../componentes/StatusDateTime';
import StatusFirmware from '../../componentes/StatusFirmware';

const PassoTesteConexao = ({route}) => {  
  
  // texto
  const [texto, setTexto] = useState<string>('INICIAR TESTE');
  
  // sinal da probe indicando a conexão
  const [firmware, setFirmware] = useState<string>('versão');
  const [sinal, setSinal] = useState<number>(0);
  const [bateria, setBateria] = useState<number>(0);
  const [tensaoBateria, setTensaoBateria] = useState<number>(0);
  const [dateTime, setDateTime] = useState<number>(0);  

  // loading de conexão
  const [loadFirmware, setLoadFirmware] = useState<boolean>(false);
  const [loadSinal, setLoadSinal] = useState<boolean>(false);
  const [loadBateria, setLoadBateria] = useState<boolean>(false);
  const [loadVBateria, setLoadVBateria] = useState<boolean>(false);
  const [loadDateTime, setLoadDateTime] = useState<boolean>(false);  

  // indica se conectou
  const [conectado, setConectado] = useState<boolean>(false);

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  // navega para pagina de inserção de conexão da probe
  const goToOrdemServico = (etapas: EtapasInstalacao) => {

    // estrutura de etapas atual
    const _etapas: EtapasInstalacao = etapas;

    // atualiza a estrutura de etapas
    const etapasAtualizada = { ..._etapas, teste_conexao: true, step4: true};

    // verifica se cliente MQTT já esta conectado
    if(isMQTTConnected())    
    {  
      // conecta o cliente MQTT
      disconnectMQTT();
    }
    
    // navega a pagina progresso da instalação fechando as paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes:[
         { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
        ],
      })
    );

  };

  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
           
    // navega a pagina progresso da instalação
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [
            { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
        ],
      })
    );
  };  

  // executa o teste de conexão
  const ConexaoMQTT = () => {

    //console.log('---------------------------------------------------');

    // verifica se cliente MQTT já esta conectado
    if(!isMQTTConnected())    
    {  
      // conecta o cliente MQTT
      //connectMQTT('4C752584CC2C', TipoConexaoMQTT.TESTE);
      connectMQTT();

      setLoadFirmware(true);
      setLoadSinal(true);
      setLoadBateria(true);
      setLoadVBateria(true);
      setLoadDateTime(true);               

    }
  }

  // executa o teste de conexão
  const LerMQTT = () => {

      subscribeMQTT('4C752584CC2C', 'dbgack');
  }    

  // executa sempre que a variavel 'conectado' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('isConnectedMQTT', mqtt => {      
      
      // seta que esta conectado
      setConectado(mqtt);
      
      if(conectado)
      {
        LerMQTT();

        // altera botão liberando novo teste
        setTexto('TESTAR NOVAMENTE'); 
      }
        
    }, );
    
    return () => {
      EventRegister.removeEventListener(eventListener);
    };
  }, [conectado]);

  // executa sempre que a variavel 'firmware' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('firmwareMQTT', mqtt => {            
      setFirmware(mqtt);
      setLoadFirmware(false);
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, [firmware]);

  // executa sempre que a variavel 'datetime' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('dateTimeMQTT', mqtt => {            
      setDateTime(mqtt);
      setLoadDateTime(false);
    }, );    
    return () => { EventRegister.removeEventListener(eventListener); };
  }, [dateTime]);

  // executa sempre que a variavel 'sinal' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('signalMQTT', mqtt => {          
      setSinal(mqtt);
      setLoadSinal(false);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};  
  }, [sinal]);  

  // executa sempre que a variavel 'bateria' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('batteryMQTT', mqtt => {            
      setBateria(mqtt);
      setLoadBateria(false);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, [bateria]);

  // executa sempre que a variavel 'v_bateria' for alterada
  useEffect(() => {
    const eventListener: any = EventRegister.addEventListener('vBatteryMQTT', mqtt => {            
      setTensaoBateria(mqtt);
      setLoadVBateria(false);
    }, );    
    return () => {EventRegister.removeEventListener(eventListener);};
  }, [tensaoBateria]);

  return (
    
    <View style = {styles.containerTelaTesteProbe}>

      {/* header */}
      <View style={styles.containerHeaderTelaTesteProbe}>

          {/* botão voltar */}
          <View style={styles.containerBotaoVoltar}>
            <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
              <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
            </TouchableOpacity>    
          </View>

          {/* texto cabeçalho */}
          <View style={styles.containerTextoHeader}>
            <Text style={styles.textoHeaderPassoID}>TESTE DE CONEXÃO</Text>
          </View>

      </View>

      {/* apresenta status da probe */}
      <View style = {styles.containerConteudoTelaTesteProbe}>

        {/* cards contendo o status da probe */}
        <View style = {{gap:20}}>

          <TouchableOpacity style={{...styles.buttonContinuarPassoID, backgroundColor:'#D6D6D6'}} 
                            onPress={() => ConexaoMQTT()}
                            testID='button-connect'>
            <Text style = {styles.textoButtonContinuarPassoID}>{texto}</Text>
          </TouchableOpacity>

          {/* linha 1 */}
          <View style = {{width:'100%', flexDirection:'row', gap: 10}}>

            {/* indica a tensão bateria */}
            <StatusFirmware
              value={firmware} 
              title={route.params?.etapas.codigo_probe}
              loading={loadFirmware}
              height={104}
              width={'100%'}
            />  

          </View>

          {/* linha 2 */}
          <View style = {{width:'100%', flexDirection:'row', justifyContent:'space-between', gap: 10}}>

            {/* indica a data e hora da Probe */}
            <StatusDateTime
              value={dateTime}
              loading={loadDateTime}
              height={104}
              width={'48%'}
            />

            {              
              // verifica se o tipo de conexão é wifi
              (route.params?.etapas.tipo_conexao === TipoConexao.WIFI) ?
                <StatusWiFi
                  value={sinal}
                  loading={loadSinal}
                  height={104}
                  width={'48%'}
                  signalLowColor={'#F7AABB'}
                  signalMediumColor={'#FFE6AA'}
                  signalHighColor={'#C2F0BA'}
                />
              :              
                <StatusGSM
                  value={sinal}
                  loading={loadSinal}
                  height={104}
                  width={'48%'}
                  signalLowColor={'#F7AABB'}
                  signalMediumColor={'#FFE6AA'}
                  signalHighColor={'#C2F0BA'}
                  title='Operadora'
                />
            } 
                    
          </View>

          {/* linha 3 */}
          <View style = {{width:'100%', flexDirection:'row', justifyContent:'space-between', gap: 10}}>

            {/* indica o nivel da bateria*/}
            <StatusBattery
              value={bateria}
              loading={loadBateria}
              height={104}
              width={'48%'}
              batteryLowColor={'#F7AABB'}
              batteryMidColor={'#FFE6AA'}
              batteryFullColor={'#C2F0BA'}
            />

            {/* indica a tensão bateria */}
            <StatusVBattery
              value={tensaoBateria}
              loading={loadVBateria}
              height={104}
              width={'48%'}
            />  

          </View>

        </View>  
          <TouchableOpacity style={styles.buttonContinuarPassoID} 
                            onPress={() => goToOrdemServico(route.params?.etapas)}
                            testID='button-continue'>
            <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
          </TouchableOpacity>                         
      </View>

    </View>

  );

};

export default PassoTesteConexao;