import { StyleSheet, Dimensions } from "react-native";

export const screenHeight = Dimensions.get('window').height;
export const screenWidth = Dimensions.get('window').width;

// 20% da largura da tela
const width20 = screenWidth * 0.2;

// 80% da largura da tela
const width80 = screenWidth * 0.8;

// area header
export const heightHeader = 80;

// altura padrão dos botões
export const heightButton = 60;

export const styles = StyleSheet.create({

    /********** TELA ORDEM DE SERVICO **********/

    containerTela: {
        flex:1,
        paddingBottom: 20, 
        backgroundColor:'#FFFFFF',        
    },

    containerBotaoVoltar:{
        width: width20, 
        justifyContent:'center',
        alignItems:'center'
    },

    containerTextoHeader:{
        width:'100%',
        marginStart:- width20,
        justifyContent:'center',
        alignItems:'center',        
    },

    containerNomeCliente:{
        height:'50%', 
        marginStart: 30, 
        justifyContent:'center', 
        alignItems:'flex-start'
    },
    
    textoHeader:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#FFFFFF', 
    },

    textoNomeCliente:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 17,        
        color:'#EAECF0', 
    },

    textoEtapasInstalacao:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 17,
        color:'#2E8C1F',         
        textAlignVertical:'center'
    },  

    textoOS:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 17,
        fontWeight:'800',
        color:'#737373',
    },

    textoNumeroOS:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 17,
        color:'#737373',
    },

    textoStatusOS:{
        height:35, 
        width:100, 
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 14,
        color:'#FFFFFF', 
        borderRadius:5, 
        textAlign:'center',
        textAlignVertical:'center'
    },

    /********** TELA PROBE ID **********/

    containerTelaPassoID: {
        flex:1,
        backgroundColor:'#F7F7F7'
    },

    containerHeaderTelaPassoID: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },    

    containerConteudoTelaPassoID:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingVertical: 20, 
        justifyContent:'space-between'
    },

    containerConteudoQRCode:{
        paddingTop: 100,
        width: screenWidth, 
        height: screenHeight - heightHeader,         
        justifyContent:'space-between'
    },    

    containerInputCodigo:{
        width:'100%', 
        height:60, 
        backgroundColor:'#FFFFFF', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'#525252',
        justifyContent:'center',
        alignItems:'center'
    },

    cameraQRCode:{
        height:'100%',
        width:'100%',
    },

    textoHeaderPassoID:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },

    textoDigiteCodigo:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        color:'#2E8C1F',
    }, 
    
    textoInputCodigo:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 26,
        fontWeight: '800',
        color:'#737373'
    },

    textoButtonContinuarPassoID:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',        
    },

    textoDescricaoQRCode:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,        
        color:'#737373', 
        textAlignVertical:'center',
    },

    buttonContinuarPassoID:{        
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },

    /********** TELA TIPO DE CONEXÃO **********/    

    containerTelaTipoConexao: {
        flex:1,
        backgroundColor:'#F7F7F7',
    },

    containerHeaderTipoConexao: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoTipoConexao:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingVertical: 20,
        justifyContent:'space-between'        
    },

    containerTipoConexao:{        
        width:'30%',
        height: 200, 
        backgroundColor:'#FFFFFF',
        borderWidth:2,
        borderRadius:8,
        alignItems:'center',
        justifyContent:'space-around',
        paddingVertical:10
    },
    
    textoHeaderTipoConexao:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F', 
    },

    textoButtonContinuarTipoConexao:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',
    },

    textoTipoConexao:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#737373',        
    },

    textoInfoEthernet:{
        fontFamily: 'SourceSans3_600SemiBold',        
        fontSize: 18,
        fontWeight: '500',
        color:'#737373',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 10,
        width:'100%',
        height:60, 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'#525252'        
    },    

    buttonContinuarTipoConexao:{        
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },

    /********** TELA MEDIDOR PROBE **********/    

    containerTelaMedidor: {
        flex:1,
        backgroundColor:'#F7F7F7',
    },

    containerHeaderMedidor: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoMedidor:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingVertical: 20,
        justifyContent:'space-between'        
    }, 

    containerInputMedidorNS:{
        paddingHorizontal: 10,
        width:'100%', 
        height:60, 
        backgroundColor:'#FFFFFF', 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'#525252',
        justifyContent:'center',
        alignItems:'flex-start'
    },

    textoInputMedidorNS:{
        fontFamily: 'SourceSans3_400Regular',        
        fontSize: 18,
        fontWeight: '500',
        color:'#737373',
        justifyContent: 'center',
        alignItems: 'center',
    },  
    
    /********** TELA FOTO LOCAL **********/    

    containerTelaFotoLocal: {
        flex:1,
        backgroundColor:'#FFFFFF',
    },    

    containerHeaderFotoLocal: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoFotoLocal:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingTop: 30,
        justifyContent:'space-between',
    },

    textoFotoTitulo:{
        textAlignVertical:'center',
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#A3A3A3',         
    },

    /********** TELA TESTE PROBE **********/ 

    containerTelaTesteProbe: {
        flex:1,
        backgroundColor:'#FFFFFF',
    },    

    containerHeaderTelaTesteProbe: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },    

    containerConteudoTelaTesteProbe:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingVertical: 20, 
        justifyContent:'space-between'
    },    

    /********** TELA MATERIAL UTILIZADO **********/ 

    containerTelaPassoMaterial: {
        flex:1,
        backgroundColor:'#FFFFFF'
    },

    containerHeaderTelaPassoMaterial: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoTelaPassoMaterial:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingTop: 40, 
        paddingBottom: 20, 
        justifyContent:'space-between'
    },    

    /********** TELA RESUMO OS **********/ 

    containerTelaResumoOS: {
        flex:1, 
        backgroundColor:'#FFFFFF',        
    },

    textoResumoOSNomeFantasia:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 20,        
        color:'#EAECF0', 
    },
    
    textoResumoOSNomeCliente:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 17,        
        color:'#EAECF0', 
    },

    textoResumoOSDestaque:{
        textAlignVertical:'center',
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 13,        
        color:'#FAFAFA',         
    },

    textoResumoOSNormal:{
        textAlignVertical:'center',
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 13,        
        color:'#FAFAFA',         
    },

    textoInfoOSDestaque:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 16,        
        color:'#525252',        
    },

    textoInfoOSSimples:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 16,        
        color:'#525252',        
    },

    textoInfoOSTitulo:{
        fontFamily: 'Exo2_700Bold',
        fontSize: 20,        
        color:'#2E8C1F',        
    },

    linhaHorizontal:{
        borderBottomColor: '#D6D6D6',
        borderBottomWidth: StyleSheet.hairlineWidth,        
    },

    /********** TELA ASSINATURA **********/ 

    containerTelaAssinatura: {
        flex:1,
        backgroundColor:'#F7F7F7'
    },

    containerHeaderTelaAssinatura: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoTelaAssinatura:{
        width: screenWidth * 0.9, 
        height: screenHeight - heightHeader, 
        //paddingHorizontal: screenWidth * 0.08, 
        paddingVertical: 20, 
        justifyContent:'space-between'
    },    

    buttonLimpar:{                
        height:heightButton, 
        borderWidth:2,
        borderColor:'#D6D6D6',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },

    buttonAssinatura:{        
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },
    
    buttonObservacoes:{        
        height:heightButton, 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },    

    textoAssinatura:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',        
    },

    textoDadosCliente:{
        fontFamily: 'SourceSans3_400Regular',        
        fontSize: 18,
        fontWeight: '500',
        color:'#737373',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 10,
        width:'100%',
        height:60, 
        borderRadius:8, 
        borderWidth:1, 
        borderColor:'#525252'        
    },    

    /********** TELA ASSINATURA **********/ 

    containerTelaObservacoes: {
        flex:1,
        backgroundColor:'#F7F7F7'
    },

    containerHeaderTelaObservacoes: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',
        paddingTop: 40
    },

    containerConteudoObservacoes:{
        width: screenWidth, 
        height: screenHeight - heightHeader, 
        paddingHorizontal: 30, 
        paddingVertical: 20,
        justifyContent:'space-between'        
    },  
    
    textoObservacoes:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 18,
        textAlign:'center',
        color:'#525252',        
    },    
})