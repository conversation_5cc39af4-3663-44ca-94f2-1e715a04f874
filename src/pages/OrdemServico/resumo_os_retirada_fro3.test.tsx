import React from "react";
import ResumoOSRetiradaFro3 from "./resumo_os_retirada_fro3";
import { NavigationContainer } from "@react-navigation/native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import { act, fireEvent, render, screen } from "@testing-library/react-native";

jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');

// Mock do LeafletView
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => {
    return {
      LeafletView: () => null,  // Ou 'LeafletView' se preferir apenas renderizar o nome como string
    };
});

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),                
                tipo_conexao: 1,
                teste_conexao: false,
                totalMaterial1: 2,
                totalMaterial2: 1,
                totalMaterial3: 1,
                totalMaterial4: 1,
                totalMaterial5: 1,
                totalMaterial6: 2,
                imagem_local1: 'exemplo.jpg',
                imagem_local2: 'exemplo.jpg',
                imagem_local3: 'exemplo.jpg',
                imagem_local4: 'exemplo.jpg',
                imagem_local5: 'exemplo.jpg',
                imagem_local6: 'exemplo.jpg',
                imagem_local7: 'exemplo.jpg',
                imagem_local8: 'exemplo.jpg',                                
                imagem_instalacao1: 'exemplo.jpg',
                imagem_instalacao2: 'exemplo.jpg',
                imagem_instalacao3: 'exemplo.jpg',
                imagem_instalacao4: 'exemplo.jpg',
                imagem_instalacao5: 'exemplo.jpg',
                imagem_instalacao6: 'exemplo.jpg',
                imagem_assinatura: '00 01 02'
      }
    },
};

const mockRoute3 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                latitude: 0,
                longitude: 0,        
                numero_os: '00000000',
                descricao_problema : undefined,
                medidor_modelo: -1,
                medidor_fabricante: -1,
                observacoes: undefined,

      }
    },
};

const mockRoute4 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),                
                tipo_conexao: 1,
                teste_conexao: false,
                totalMaterial1: 2,
                totalMaterial2: 1,
                totalMaterial3: 1,
                totalMaterial4: 1,
                totalMaterial5: 1,
                totalMaterial6: 2,
                imagem_local1: 'exemplo.jpg',
                imagem_local2: 'exemplo.jpg',
                imagem_local3: 'exemplo.jpg',
                imagem_local4: 'exemplo.jpg',
                imagem_local5: 'exemplo.jpg',
                imagem_local6: 'exemplo.jpg',
                imagem_local7: 'exemplo.jpg',
                imagem_local8: 'exemplo.jpg',                                
                imagem_instalacao1: 'exemplo.jpg',
                imagem_instalacao2: 'exemplo.jpg',
                imagem_instalacao3: 'exemplo.jpg',
                imagem_instalacao4: 'exemplo.jpg',
                imagem_instalacao5: 'exemplo.jpg',
                imagem_instalacao6: 'exemplo.jpg',
                imagem_assinatura: undefined,
                cliente_nome: '',

      }
    },
};

const mockRoute5 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),                
                tipo_conexao: 1,
                teste_conexao: false,
                totalMaterial1: 2,
                totalMaterial2: 1,
                totalMaterial3: 1,
                totalMaterial4: 1,
                totalMaterial5: 1,
                totalMaterial6: 2,
                imagem_local1: 'exemplo.jpg',
                imagem_local2: 'exemplo.jpg',
                imagem_local3: 'exemplo.jpg',
                imagem_local4: 'exemplo.jpg',
                imagem_local5: 'exemplo.jpg',
                imagem_local6: 'exemplo.jpg',
                imagem_local7: 'exemplo.jpg',
                imagem_local8: 'exemplo.jpg',                                
                imagem_retirada1: 'exemplo.jpg',
                imagem_retirada2: 'exemplo.jpg',
                imagem_retirada3: 'exemplo.jpg',
                imagem_retirada4: 'exemplo.jpg',
                imagem_assinatura: undefined,
                cliente_nome: 'ABC',

      }
    },
};

describe('Resumo OS - Retirada Fro 3', () => {
    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <ResumoOSRetiradaFro3 route={mockRoute1} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <ResumoOSRetiradaFro3 route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela numero os invalida', () => {
    
        const {} = render(  <NavigationContainer>
                                <ResumoOSRetiradaFro3 route={mockRoute3} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    }); 
    
    it('renderiza corretamente a tela com tipo conexão gsm', () => {
    
        const {} = render(  <NavigationContainer>
                                <ResumoOSRetiradaFro3 route={mockRoute2} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });
    
    it('renderiza corretamente a tela ao clicar no botão de inserir observações', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                <ResumoOSRetiradaFro3 route={mockRoute2} />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-observation'));
        });        
    });
    
    it('renderiza corretamente a tela ao clicar no botão de inserir assinatura', async () => {
    
        const {getByText} = render(   <NavigationContainer>
                                            <ResumoOSRetiradaFro3 route={mockRoute4} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ASSINAR'));
        });        
    });    

    it('renderiza corretamente a tela se cliente não concordar com a instalação', async () => {
    
        const {} = render(   <NavigationContainer>
                                            <ResumoOSRetiradaFro3 route={mockRoute5} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });
    
    it('renderiza corretamente a tela ao clicar no botão enviar', async () => {
    
        const {getByText} = render(   <NavigationContainer>
                                            <ResumoOSRetiradaFro3 route={mockRoute5} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByText('ENVIAR'));
        });        
    });    

});