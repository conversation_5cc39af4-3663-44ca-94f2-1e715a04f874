import {View, Text, TouchableOpacity, TextInput} from 'react-native';
import React, { useState, useCallback, useEffect } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native';

import {Canvas, Path, SkPath, Skia, TouchInfo, useCanvasRef, useTouchHandler} from "@shopify/react-native-skia";

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg';
import IconEraser from '../../assets/svg/icon_eraser.svg';

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index';

// estrutura de etapas de instalação
import {EtapasInstalacao} from "../../data";

import { Buffer } from 'buffer';

// constantes
import { ScreenResumoTipoOS} from '../../constantes';

// componentes
import CheckBox from '../../componentes/CheckBox';

// modal messages
import MessageBoxAtencao from '../../modal/messagebox/Atencao';

const PassoAssinatura = ({route}) => {

  // desenho da assinatura
  const [paths, setPaths] = useState<SkPath[]>([]);

  // se cliente ciente do trabalho realizado
  const [concorda, setConcorda] = useState<boolean>(true);

  // dados do clinte
  const [nome, setNome] = useState<string>('');
  const [telefone, setTelefone] = useState<string>('');

  // controle de apresentação modal message
  const [showMessageBoxAtencao, setShowMessageBoxAtencao] = useState(false);

  // tamanho das areas da pagina
  const areaButtonLimpar = 30;

  const ref = useCanvasRef();
  
  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  // imagem da assinatura
  const [assinatura, setAssinatura] = useState<string | null>(null);

  // navega para pagina de resumo da os
  const goToResumoOS = async (etapas: EtapasInstalacao) => {

    // salva a assinatura como imagem
    salvaImagemAssinatura();

    // estrutura de etapas atual
    const _etapas: EtapasInstalacao = etapas;

    // atualiza a estrutura de etapas
    const etapasAtualizada = { ..._etapas, 
                               imagem_assinatura: assinatura, 
                               cliente_nome: nome, 
                               cliente_telefone: telefone,
                               concorda:concorda};

    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes:[
          { name: ScreenResumoTipoOS[route.params?.etapas.tipo_os] , params: {etapas: etapasAtualizada} },
        ],
      })
    );
  };

  const onDrawingStart = useCallback((touchInfo: TouchInfo) => {
    setPaths((old) => {
      const { x, y } = touchInfo;
      const newPath = Skia.Path.Make();
      newPath.moveTo(x, y);
      return [...old, newPath];
    });
  }, []);

  const onDrawingActive = useCallback((touchInfo: TouchInfo) => {
    setPaths((currentPaths) => {
      const { x, y } = touchInfo;
      const currentPath = currentPaths[currentPaths.length - 1];
      const lastPoint = currentPath.getLastPt();
      const xMid = (lastPoint.x + x) / 2;
      const yMid = (lastPoint.y + y) / 2;

      currentPath.quadTo(lastPoint.x, lastPoint.y, xMid, yMid);
      return [...currentPaths.slice(0, currentPaths.length - 1), currentPath];
    });
  }, []);

  const touchHandler = useTouchHandler(
    {
      onActive: onDrawingActive,
      onStart: onDrawingStart,
    },
    [onDrawingActive, onDrawingStart]
  );

  // limpara assinatura
  const clearCanvas = () => {
    setPaths([]);
  };

  // salva a assinatura como imagem
  const salvaImagemAssinatura = () => {

    if(paths.length <= 0)
    {
      setShowMessageBoxAtencao(true);
      return;
    }

    // tira um print da assinatura
    const image = ref.current?.makeImageSnapshot();

    // convert em bytes
    const imageBytes = image?.encodeToBytes() ?? '';

    // converte em png
    const base64Image = `data:image/png;base64,${Buffer.from(imageBytes).toString('base64')}`;      

    // salva imagem
    setAssinatura(base64Image);
  };  

  // executa sempre que a variavel 'assinatura' for alterada
  useEffect(() => {

    // se assinatura foi escrita
    if(assinatura !== null)
      goToResumoOS(route.params?.etapas);

  }, [assinatura]);  

  return (

    <>

      <MessageBoxAtencao visivel={showMessageBoxAtencao} 
                         titulo="Atenção" 
                         descricao={'É necessário assinar o termo de acordo.'} 
                         textoBotao="OK" 
                         onFechar={() => setShowMessageBoxAtencao(false)}/>
    
      <View style = {{...styles.containerTelaAssinatura, alignItems: 'center'}}>

        {/* header */}
        <View style={styles.containerHeaderTelaAssinatura}>

            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goToResumoOS(route.params?.etapas)} testID='button-resumo'>
                <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
              </TouchableOpacity>
            </View>

            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeaderPassoID}>ASSINATURA DO CLIENTE</Text>
            </View>

        </View>

        {/* inserir assinatura */}
        <View style = {{...styles.containerConteudoTelaAssinatura, gap: 15}}>

          {/* area  da assinatura - removi o ref={ref} para não gerar mais o erro no sonar*/}
          <View style={{flex:1, borderWidth:1, borderRadius:8, borderStyle: 'dashed', borderColor:'#A3A3A3'}} testID='view-signature'>
              <Canvas style={{flex:1, width: '100%'}} onTouch={touchHandler} testID='canvas-signature'>
                  {paths.map((path, index) => (
                      <Path key={index} path={path} color={"black"} style={"stroke"} strokeWidth={2}/>
                  ))}
              </Canvas>        
          </View>

          {/* validação da assinatura */}
          <View style={{gap:25}}>
            <TouchableOpacity style={{alignItems:'flex-end'}} onPress={()=> clearCanvas()} testID='button-eraser'>
              <IconEraser color={"#A3A3A3"} width={areaButtonLimpar} height={areaButtonLimpar} />
            </TouchableOpacity> 
          
            <View style ={{gap: 10}}>
              <TextInput
                    style={{...styles.textoDadosCliente}}
                    placeholder="Nome" 
                    keyboardType='default'
                    value={nome}
                    maxLength={12}                  
                    onChangeText={(texto) => setNome(texto)} 
                    testID='input-nome'/>

              <TextInput
                    style={styles.textoDadosCliente}
                    placeholder="Telefone" 
                    keyboardType='default'
                    value={telefone}
                    maxLength={40}                  
                    onChangeText={(texto) => setTelefone(texto)}
                    testID='input-telefone'/>
            </View>

            <View style={{flexDirection:'row', gap: 10}}>
              <CheckBox height={20} width={20} value={concorda} onValueChange={setConcorda} testID='check-agree'/>
              <Text style={{textAlign:'justify'}}>
                  Estou ciente que o trabalho descrito foi realizado conforme solicitado e está em conformidade com as expectativas.
              </Text>
            </View>

            <TouchableOpacity style={{...styles.buttonAssinatura, backgroundColor: (concorda) ? `#2E8C1F` : `#A3A3A3` }}
                              onPress={() => salvaImagemAssinatura()}
                              testID='button-agree'>
              <Text style = {styles.textoAssinatura}>{ (concorda) ? `CONCORDO` : `NÃO CONCORDO` }</Text>
            </TouchableOpacity>          
          </View>
                    
        </View>

      </View>
    </>
  );

};

export default PassoAssinatura;