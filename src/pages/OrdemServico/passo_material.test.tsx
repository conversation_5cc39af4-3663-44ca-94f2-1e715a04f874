import React from "react";
import PassoMaterial from "./passo_material";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                totalMaterial1: 0,                
                totalMaterial2: 0,                
                totalMaterial3: 0,                
                totalMaterial4: 0,                
                totalMaterial5: 0,                
                totalMaterial6: 0,        
                materialExtra: false

      }
    },
};

describe('Passo Material', () => {

    it('renderiza corretamente a tela e clica no botão voltar', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoMaterial route={mockRoute1} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela e clica no botão continuar', async () => {
    
        const {getByTestId} = render(<NavigationContainer>
                                        <PassoMaterial route={mockRoute1} />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });         
    });
    
    it('renderiza corretamente a tela e clica no botão continuar, mas sem materiais cadastrados e confirma', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <PassoMaterial route={mockRoute2} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('Confirmar'));
        });        
    }); 
    
    it('renderiza corretamente a tela e clica no botão continuar, mas sem materiais cadastrados e cancela', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <PassoMaterial route={mockRoute2} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('Cancelar'));
        });        
    });
    
    it('renderiza corretamente a tela e clica no botão de inserir materiais', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoMaterial route={mockRoute1} />
                                        </NavigationContainer> );
        
        const input = getByTestId('input-material');
  
        fireEvent.press(input);

        // Simula a digitação do texto"
        fireEvent.changeText(input, '1 metro cabo');
    });    
});