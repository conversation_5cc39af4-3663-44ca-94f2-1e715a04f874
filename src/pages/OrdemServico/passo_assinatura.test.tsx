/*
import React from 'react';
import { NavigationContainer } from "@react-navigation/native";
import { render, fireEvent } from '@testing-library/react-native';
import { CommonActions } from '@react-navigation/native';
import PassoAssinatura from './passo_assinatura';
import { MockDetalhesConcluidoOSHoje } from '../../../__mocks__/DetalhesConcluidoOSHojeMock';

// Mock do React Navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(() => ({
    dispatch: jest.fn(),
  })),
  CommonActions: {
    reset: jest.fn(),
  },
}));

const mockTouchHandler = jest.fn();
// Mock do Skia (caso necessário)
jest.mock('@shopify/react-native-skia', () => ({
  Canvas: () => jest.fn( () => null),
  Path: () => jest.fn(() => null),
  useCanvasRef: jest.fn(() => ({ current: { makeImageSnapshot: jest.fn() } })),
  useTouchHandler: jest.fn(() => jest.fn()),
}));

describe('PassoAssinatura Component', () => {
 
  const mockRoute = {
      params: {
        etapas: { ...MockDetalhesConcluidoOSHoje()}
      },
  };

  it('deve renderizar corretamente o componente', () => {
    const { getByPlaceholderText, getByText } = render( <PassoAssinatura route={mockRoute} /> );

    expect(getByPlaceholderText('Nome')).toBeTruthy();
    expect(getByPlaceholderText('Telefone')).toBeTruthy();
    expect(getByText('CONCORDO')).toBeTruthy();
  });

  it('deve chamar a navegação ao clicar no botão voltar', () => {
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
    const buttonVoltar = getByTestId('button-resumo');

    fireEvent.press(buttonVoltar);

    expect(CommonActions.reset).toHaveBeenCalled();
  });

  it('deve exibir mensagem de erro ao tentar salvar a assinatura sem um desenho', () => {
    const { getByTestId, getByText } = render(<PassoAssinatura route={mockRoute} />);
    const buttonConcordo = getByTestId('button-agree');

    fireEvent.press(buttonConcordo);

    expect(getByText('É necessário assinar o termo de acordo.')).toBeTruthy();
  });

  it('deve limpar o canvas ao clicar no botão de limpar', () => {
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
    const buttonLimpar = getByTestId('button-eraser');

    fireEvent.press(buttonLimpar);

    // Verificar se os paths foram limpos (paths devem ser gerenciados de maneira controlada)
    expect(buttonLimpar).toBeTruthy(); // Simulação básica
  });

  it('deve clicar para inserir nome e digitar um texto', () => {    
    
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
    const input = getByTestId('input-nome');
  
    fireEvent.press(input);

    // Simula a digitação do texto "Olá, mundo!"
    fireEvent.changeText(input, 'Olá, mundo!');

  });

  it('deve clicar para inserir telefone e digitar um texto', () => {    
    
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
    const input = getByTestId('input-telefone');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, '(00) 00000-0000');

  });  
  it('deve clicar na opção de concordo ou não', () => {    
    
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
    const checkConcordo = getByTestId('check-agree');
  
    fireEvent.press(checkConcordo);
  });

  it('deve limpar o canvas ao clicar no botão de concordo', () => {    
    
    const { getByTestId } = render(<PassoAssinatura route={mockRoute} />);
  
    const canvas = getByTestId('canvas-signature');
        
    // Simula início do toque
    fireEvent(canvas, 'touchStart', {
      nativeEvent: { locationX: 50, locationY: 50 },
    });
  
    // Simula movimento de arraste
    fireEvent(canvas, 'touchMove', {
      nativeEvent: { locationX: 100, locationY: 100 },
    });
  
    // Simula término do toque
    fireEvent(canvas, 'touchEnd', {
      nativeEvent: { locationX: 100, locationY: 100 },
    });
  
    const buttonConcordo = getByTestId('button-agree');
    fireEvent.press(buttonConcordo);
  });  
});
*/
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import PassoAssinatura from './passo_assinatura';
import { MockDetalhesConcluidoOSHoje } from '../../../__mocks__/DetalhesConcluidoOSHojeMock';

jest.mock('@shopify/react-native-skia', () => ({
  Canvas: ({ children }: any) => <>{children}</>, 
  Path: () => null, 
  Skia: {
    Path: {
      Make: jest.fn(() => ({
        moveTo: jest.fn(),
        quadTo: jest.fn(),
        getLastPt: jest.fn(() => ({ x: 0, y: 0 })),
      })),
    },
  },
  useCanvasRef: () => ({
    current: {
      makeImageSnapshot: jest.fn(() => ({
        encodeToBytes: jest.fn(() => 'mockedImageBytes'),
      })),
    },
  }),
  useTouchHandler: () => jest.fn(),
}));

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      dispatch: jest.fn(),
      navigate: jest.fn(),
      goBack: jest.fn(),
    }),
    CommonActions: {
      reset: jest.fn(),
    },
  };
});

describe('PassoAssinatura Component', () => {
  const mockRoute = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
  };

  it('renderiza corretamente os elementos principais', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    expect(getByTestId('view-signature')).toBeTruthy();
    //expect(getByTestId('canvas-signature')).toBeTruthy();
    expect(getByTestId('input-nome')).toBeTruthy();
    expect(getByTestId('input-telefone')).toBeTruthy();
    expect(getByTestId('check-agree')).toBeTruthy();
    expect(getByTestId('button-eraser')).toBeTruthy();
    expect(getByTestId('button-agree')).toBeTruthy();
  });

  it('limpa a assinatura ao pressionar o botão de apagar', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    const eraseButton = getByTestId('button-eraser');
    fireEvent.press(eraseButton);

    // Aqui pode-se verificar o estado dos paths se houvesse acesso a ele
  });

  it('permite entrada de texto nos campos de nome e telefone', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    const inputNome = getByTestId('input-nome');
    const inputTelefone = getByTestId('input-telefone');

    fireEvent.changeText(inputNome, 'João Silva');
    fireEvent.changeText(inputTelefone, '11999999999');

    expect(inputNome.props.value).toBe('João Silva');
    expect(inputTelefone.props.value).toBe('11999999999');
  });

  it('alterna o estado do checkbox corretamente', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    const checkbox = getByTestId('check-agree');
  
    fireEvent.press(checkbox);    
  });

  it('deve exibir mensagem de erro ao tentar salvar a assinatura sem um desenho', () => {
    
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    const buttonConcordo = getByTestId('button-agree');

    fireEvent.press(buttonConcordo);

    expect(getByText('É necessário assinar o termo de acordo.')).toBeTruthy();
  });

  it('deve chamar a navegação ao clicar no botão voltar', () => {
    const { getByTestId} = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );
    const buttonVoltar = getByTestId('button-resumo');

    fireEvent.press(buttonVoltar);    
  });

  it('deve renderizar corretamente uma assinatura', () => {    
    
    const { getByTestId} = render(
      <NavigationContainer>
        <PassoAssinatura route={mockRoute} />
      </NavigationContainer>
    );

    const canvas = getByTestId('view-signature');
        
    // Simula início do toque
    fireEvent(canvas, 'touchStart', {
      nativeEvent: { locationX: 50, locationY: 50 },
    });
  
    // Simula movimento de arraste
    fireEvent(canvas, 'touchMove', {
      nativeEvent: { locationX: 100, locationY: 100 },
    });
  
    // Simula término do toque
    fireEvent(canvas, 'touchEnd', {
      nativeEvent: { locationX: 100, locationY: 100 },
    });
  
    const buttonConcordo = getByTestId('button-agree');
    fireEvent.press(buttonConcordo);
  });


});
