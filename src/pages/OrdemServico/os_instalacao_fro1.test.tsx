import React from 'react';
import { render, fireEvent, act, screen } from '@testing-library/react-native';
import OrdemServico from "./os_instalacao_fro1";
import { useNavigation } from '@react-navigation/native';
import { MockEtapasInstalacaoFro1Hoje } from '../../../__mocks__/EtapasInstalacaoFro1HojeMock';

// Mock para useNavigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: jest.fn(),
}));

let mockIsPermissionGranted = true;

jest.mock('expo-location', () => ({
    requestForegroundPermissionsAsync: jest.fn(() => Promise.resolve({granted: mockIsPermissionGranted})),
    getCurrentPositionAsync: jest.fn(() => Promise.resolve({
                                coords: {
                                            latitude: 10,
                                            longitude: 10,
                                        },
                                })),
}));

const mockRoute1 = {
    params: {
      etapas: { ...MockEtapasInstalacaoFro1Hoje()}
    },
};

describe('OrdemServico - Instalação Fro 1', () => {
    
    const mockNavigation = {
        navigate: jest.fn(),
        goBack: jest.fn(),
    };

    beforeEach(() => {
        (useNavigation as jest.Mock).mockReturnValue(mockNavigation);
    });

    it('deve renderizar o texto correto no cabeçalho', () => {
        const route = { params: { etapas: { cliente: 'Cliente Teste', numero_os: '12345' } } };

        const { getByText } = render( <OrdemServico route={route} /> );

        expect(getByText('ORDEM DE SERVIÇO')).toBeTruthy();
        expect(getByText('Cliente Teste')).toBeTruthy();
        expect(getByText('OS:')).toBeTruthy();
        expect(getByText('12345')).toBeTruthy();
    });

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {

        const {getByTestId} = render(<OrdemServico route={mockRoute1} />);
    
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('deve renderizar corretamente a tela se step1 = true e step6 = true', () => {
        const route = { params: { etapas: { step1: true, step6: true } } };

        const {} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();
    });

    it('deve renderizar corretamente a tela se step1 = true e step6 = false', () => {
        const route = { params: { etapas: { step1: true, step6: false } } };

        const {} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();
    });  
    
    it('deve exibir o modal de sucesso ao pressionar o botão "CONTINUAR"', () => {
        const route = { params: { etapas: { step6: true } } };

        const { getByText } = render(<OrdemServico route={route} />);

        const continuarButton = getByText('CONTINUAR');
        act(() => {
            fireEvent.press(continuarButton);
        });

        expect(getByText('SUCESSO!')).toBeTruthy();

        act(() => {
            fireEvent.press(getByText('OK'));
        });        
    });

    it('deve renderizar corretamente ao vincular probe', () => {
        const route = { params: { etapas: { step1: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Vincular Probe');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });    
  
    it('deve renderizar corretamente ao coletar informações do medidor', () => {
        const route = { params: { etapas: { step1: true, step2: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Informações do Medidor');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });
    
    it('deve renderizar corretamente ao coletar fotos do local', () => {
        const route = { params: { etapas: { step1: true, step2: true, step3: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Fotos do local');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });
    
    it('deve renderizar corretamente ao Testar conexão', () => {
        const route = { params: { etapas: { step1: true, step2: true, step3: true, step4: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Testar conexão');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    }); 

    it('deve renderizar corretamente ao Material utilizado', () => {
        const route = { params: { etapas: { step1: true, step2: true, step3: true, step4: true, step5: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Material utilizado');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });  
    
    it('deve renderizar corretamente ao Fotos da instalação', () => {
        const route = { params: { etapas: { step1: true, step2: true, step3: true, step4: true, step5: true, step6: false } } };

        const {getByText} = render(<OrdemServico route={route} />);

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Fotos da instalação');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });
    
    it('deve renderizar corretamente se não for permitido pegar a localização', () => {
        const route = { params: { etapas: { step1: false} } };

        mockIsPermissionGranted = false;

        const {getByText} = render(<OrdemServico route={route} />);     

        expect(screen).toBeTruthy();

        const continuarButton = getByText('Vincular Probe');
        act(() => {
            fireEvent.press(continuarButton);
        });        
    });
});
