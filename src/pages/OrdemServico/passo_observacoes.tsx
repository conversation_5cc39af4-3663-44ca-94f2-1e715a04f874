import {View, Text, TouchableOpacity, TextInput} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// modal messages
import MessageBoxErro from '../../modal/messagebox/Erro'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// dados das etapas de instalação
import {EtapasInstalacao} from "../../data"

// componentes
import { ScreenResumoTipoOS} from '../../constantes';

const PassoObservacoes = ({route}) => {
  
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    // fabricante do medidor
    const [observacoes, setObservacoes] = useState<string>(route.params?.etapas.observacoes);
    
    const [textoMessageBoxErro, setTextoMessageBoxErro] = useState<string>("");
    const [showMessageBoxErro, setShowMessageBoxErro] = useState<boolean>(false);

    // verifica se foi inserida alguma observação
    const SeExisteObservacoes = () => {

      // se foi digitado algum numero de serie para o medidor
      if (observacoes === '') {
        setTextoMessageBoxErro("É necessário inserir alguma observação.");
        onMessageBoxErroAbrir();
    
        return false;
      }
      
      return true
    };

  
    // navega para pagina de ordem de servico
    const goToOrdemServico = (etapas: EtapasInstalacao) => {
  
      if(!SeExisteObservacoes())
        return;
  
      const _etapas: EtapasInstalacao = etapas;
    
      // atualiza estrutura de etapas
      const etapasAtualizada = { ..._etapas, observacoes: observacoes};
    
      // navega a pagina progresso da instalação fechando as 2 paginas anteriores
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
            { name: ScreenResumoTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
          ],
        })
      );      
  
    };  
  
    // navegar de volta
    const goBack = (etapas: EtapasInstalacao) => {
           
      // navega a pagina progresso da instalação
      navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [
              { name: ScreenResumoTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
          ],
        })
      );
    };

    const onMessageBoxErroAbrir = () => { setShowMessageBoxErro(true); };
    const onMessageBoxErroFechar = () => { setShowMessageBoxErro(false); };
  
    return (
      
      <>
  
      <MessageBoxErro 
        visivel={showMessageBoxErro} 
        titulo="Erro" 
        descricao={textoMessageBoxErro} 
        textoBotao="OK" 
        onFechar={onMessageBoxErroFechar}
      />  
      
      <View style = {styles.containerTelaObservacoes}>
  
        {/* header */}
        <View style={styles.containerHeaderTelaObservacoes}>
  
            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
              </TouchableOpacity>    
            </View>
  
            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeaderPassoID}>OBSERVAÇÕES FINAIS</Text>
            </View>
  
        </View>
  
        {/* informações do medidor da probe */}
        <View style = {styles.containerConteudoObservacoes}>
  
          <View style={{gap:15}}>
            
            <Text style={styles.textoObservacoes}>Entre com as observações e problemas ocorridos durante a instalação.</Text>

            <TextInput style={{height:'80%', borderWidth:1, borderColor: '#D6D6D6', borderRadius: 5, padding:10, fontFamily:'SourceSans3_400Regular', fontSize:16 }}
                       placeholder='Máx. 500 caracteres.'
                       multiline
                       textAlignVertical='top'
                       value={observacoes}                
                       onChangeText={(texto) => setObservacoes(texto)}
                       testID='input-obs'
            >

            </TextInput>
    
          </View>
  
          <TouchableOpacity style={styles.buttonContinuarPassoID}
                            onPress={() => goToOrdemServico(route.params?.etapas)}
                            testID='button-continue'>
            <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
          </TouchableOpacity>
  
        </View>
  
      </View>
  
      </>
  
    );
  
  };
  
  export default PassoObservacoes;