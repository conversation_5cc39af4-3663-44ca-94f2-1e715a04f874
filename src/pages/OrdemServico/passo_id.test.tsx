/*
import React from "react";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";
import PassoID from "./passo_id";
import { NavigationContainer } from "@react-navigation/native";

jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');

jest.mock('../../assets/svg/icon_arrow-left.svg', () => 'IconVoltar');
jest.mock('../../assets/svg/icon_solid-flash.svg', () => 'IconFlash');
jest.mock('../../assets/svg/icon_solid-flash-slash.svg', () => 'IconFlashOff');
jest.mock('../../assets/svg/icon_solid-camera-rotate.svg', () => 'IconCameraRotate');

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

describe('Passo ID', () => {

    it('renderiza corretamente a tela e clica no boltão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoID route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });
   
});
*/

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import PassoID from './passo_id';
import { MockDetalhesConcluidoOSHoje } from '../../../__mocks__/DetalhesConcluidoOSHojeMock';

// Mock de navegação
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    dispatch: jest.fn(),
    navigate: jest.fn(),
  }),
}));

const mockRoute = {
  params: {
    etapas: { ...MockDetalhesConcluidoOSHoje()}
  },
};

describe('PassoID Component', () => {
  it('deve renderizar corretamente', () => {
    const {unmount, getByText, getByTestId } = render(<NavigationContainer>
                                                        <PassoID route={{ params: { id: '12345' } }} />
                                                      </NavigationContainer>
                                                    );

    expect(getByText('VINCULAR PROBE')).toBeTruthy();
    expect(getByTestId('button-back')).toBeTruthy();
    expect(getByTestId('button-flash')).toBeTruthy();
    expect(getByTestId('button-camera')).toBeTruthy();
    expect(getByTestId('button-input')).toBeTruthy();

    unmount(); // Garante que o componente seja desmontado no final do teste        
  });

  it('deve alternar o flash da câmera ao clicar no botão', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const flashButton = getByTestId('button-flash');
    fireEvent.press(flashButton);
    fireEvent.press(flashButton);
  });

  it('deve alternar a câmera frontal ao clicar no botão', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const cameraButton = getByTestId('button-camera');
    fireEvent.press(cameraButton);
  });

  it('deve permitir digitar um código e clicar no botão continuar', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const inputButton = getByTestId('button-input');
    fireEvent.press(inputButton);

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
  });

  it('deve voltar ao clicar no botão de voltar', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: '12345' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-back');
    fireEvent.press(backButton);
  });

  it('deve voltar ao clicar no botão de codigo OK', () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <PassoID route={mockRoute} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-connection');
    fireEvent.press(backButton);
  });
  
  it('deve voltar ao clicar no botão de codigo com mais de 14 digitos', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKLMNO' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);


    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, 'ABCDEFGHIJKLMNO');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);   
  });

  it('deve voltar ao clicar no botão de codigo sem codigo probe digitado', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKLMNO' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);


    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, '');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
    
    const OhButton = getByText('OK');
    fireEvent.press(OhButton);    
  });
  
  it('deve voltar ao clicar no botão de codigo com mais de 12 digitos', () => {
    const { getByTestId, getByText } = render(
      <NavigationContainer>
        <PassoID route={{ params: { id: 'ABCDEFGHIJKL' } }} />
      </NavigationContainer>
    );

    const backButton = getByTestId('button-input');
    fireEvent.press(backButton);


    const input = getByTestId('input-code');
  
    fireEvent.press(input);

    // Simula a digitação do texto"
    fireEvent.changeText(input, 'ABCDEFGHIJKL');

    const continueButton = getByTestId('button-continue');
    fireEvent.press(continueButton);
    });  
  
});
