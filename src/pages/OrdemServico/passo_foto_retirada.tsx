import {View, Text,TouchableOpacity} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// modal messages
import MessageAvisoFotoRetirada from '../../modal/messagebox/AvisoFotoRetirada'
import MessageBoxPergunta from '../../modal/messagebox/Pergunta'

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// componentes
import Foto from '../../componentes/Foto'

// dados das etapas de instalação
import {EtapasInstalacao} from "../../data"

// constantes
import {ScreenTipoOS, TipoStatusOS } from "../../constantes"

// funcoes
import {Hora} from "../../funcoes"

const PassoFotoRetirada = ({route}) => {
    
    const [imagemAtual, setImagemAtual] = useState<number>(1);

    const [imagem1, setImagem1] = useState<string>(route.params?.etapas.imagem_retirada1);
    const [imagem2, setImagem2] = useState<string>(route.params?.etapas.imagem_retirada2);

    const [texto1, setTexto1] = useState<string>(route.params?.etapas.imagem_texto_retirada1);
    const [texto2, setTexto2] = useState<string>(route.params?.etapas.imagem_texto_retirada2);

    // aviso de como tirar as fotos
    const [showMessageAviso, setShowMessageAviso] = useState(!route.params?.etapas.numero_imagens_retirada);
    const onMessageAvisoFechar = () => { setShowMessageAviso(false); };

    // mensagem de pergunta se deve ou não continuar sem adicionar fotos
    const [showMessageBoxPergunta, setShowMessageBoxPergunta] = useState(false);
    const [textoPergunta, setTextoPergunta] = useState('');
    const onMessageBoxPerguntaAbrir = () => { setShowMessageBoxPergunta(true); };

    // resultado da mensagem de pergunta
    const onResultCancel = () => { setShowMessageBoxPergunta(false); };

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    const heigthFoto = 300;
    const widthFoto = 300;
    const heigthIcon = 80;
    const widthIcon = 80;

    // indica o resultado da escolha do aviso
    const onResultOK = (etapas: EtapasInstalacao) => {

        setShowMessageBoxPergunta(false); 

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas, 
                                   horario_fim: Hora(new Date()), 
                                   numero_imagens_retirada: 0, 
                                   step7: true };

        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes:[
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
                ],
            })
        );        
    };

    // navegar de volta
    const goBack = (etapas: EtapasInstalacao) => {
           
        // navega a pagina progresso da instalação
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
                ],
            })
        );
    };  
    
   // aponta para a proxima imagem
   const proximaImagem = () => {

        if( (imagem1.length) && (imagemAtual === 1) )
            setImagemAtual(2);              
    };    

    // navega para pagina de ordem de serviço
    const goToOrdemServico = (etapas: EtapasInstalacao) => {

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas,
                                   horario_fim: Hora(new Date()),
                                   status_os: TipoStatusOS.CONCLUIDO,                                   
                                   imagem_retirada1: imagem1, 
                                   imagem_retirada2: imagem2, 
                                   imagem_texto_retirada1: texto1,
                                   imagem_texto_retirada2: texto2,
                                   step7: true };

        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes:[
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
                ],
            })
        );                
      
        setTextoPergunta("Tem certeza que deseja continuar sem registrar nenhuma foto da retirada?");
      
        onMessageBoxPerguntaAbrir();
    };

    // navega para pagina de visualização da imagem
    const goToPassoFoto = (etapas: EtapasInstalacao, imagem: string) => {
        
        // verifica se existe alguma imagem
        if(imagem.length > 0)
        {
            const _etapas: EtapasInstalacao = etapas;
        
            // atualiza estrutura de etapas
            const etapasAtualizada = { ..._etapas, 
                                       //numero_imagens_retirada: numero_imagens,
                                       imagem_retirada1: imagem1, 
                                       imagem_retirada2: imagem2, 
                                       imagem_texto_retirada1: texto1,
                                       imagem_texto_retirada2: texto2,
                                       step7: false };
    
            // navega a pagina progresso da instalação fechando as 2 paginas anteriores
            navigation.dispatch(
                CommonActions.reset({
                    index: 0,
                    routes: [
                        { name: 'PassoFoto', params: {etapas: etapasAtualizada, imagem: imagem, screen: 'PassoFotoRetirada'} },
                    ],
                })
            );
        }
    };

    return (

        <>
            <MessageAvisoFotoRetirada 
                visivel={showMessageAviso}                  
                onFechar={onMessageAvisoFechar}
            />                

            <MessageBoxPergunta
                visivel={showMessageBoxPergunta} 
                titulo="Atenção" 
                descricao={textoPergunta} 
                textoBotaoOK="Confirmar" 
                textoBotaoCancelar='Cancelar'
                onOK={() => onResultOK(route.params?.etapas)}
                onCancel={onResultCancel}
            />

            <View style = {styles.containerTelaFotoLocal}>

                {/* header */}
                <View style={styles.containerHeaderFotoLocal}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>

                        <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderPassoID}>FOTOS DA RETIRADA</Text>
                    </View>

                </View>

                {
                (!showMessageAviso) ?
                    
                    <View style={styles.containerConteudoFotoLocal}>


                        {
                            /* fotos do local antes da retirada */
                            (imagemAtual === 1) &&
                                <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                    <Text style={styles.textoFotoTitulo}>Tire uma foto antes da retirada</Text>
                                    <Foto imagem={imagem1}
                                          texto_imagem={texto1}
                                          height={heigthFoto} width={widthFoto} 
                                          heightIcon={heigthIcon} widthIcon={widthIcon}
                                          onValueChange={setImagem1}
                                          onValueText={setTexto1}
                                          onPressImage={() => goToPassoFoto(route.params?.etapas, imagem1)}
                                    />
                                </View>
                        }

                        {
                            /* fotos do local depois da retirada */
                            (imagemAtual === 2) &&
                                <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                    <Text style={styles.textoFotoTitulo}>Tire uma foto depois da retirada</Text>
                                    <Foto imagem={imagem2}
                                          texto_imagem={texto2}
                                          height={heigthFoto} width={widthFoto} 
                                          heightIcon={heigthIcon} widthIcon={widthIcon}
                                          onValueChange={setImagem2}
                                          onValueText={setTexto2}
                                          onPressImage={() => goToPassoFoto(route.params?.etapas, imagem2)}
                                    />
                                </View>
                        }
                        {
                            ( imagemAtual === 2 && imagem2.length)
                            ?
                                <TouchableOpacity style={styles.buttonContinuarPassoID}
                                                  onPress={() => goToOrdemServico(route.params?.etapas)}>
                                    <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
                                </TouchableOpacity>                                
                            :
                                <TouchableOpacity style={{...styles.buttonContinuarPassoID, backgroundColor:'#A3A3A3'}}
                                                  onPress={() => proximaImagem()}>
                                    <Text style = {styles.textoButtonContinuarPassoID}>PRÓXIMO</Text>
                                </TouchableOpacity> 
                            }

                    </View>
                    
                : null
                }

            </View>        
        </>

    );
};

export default PassoFotoRetirada;