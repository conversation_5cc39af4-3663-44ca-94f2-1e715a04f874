import React from "react";
import PassoMedidor from "./passo_medidor";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import { MockDetalhesConcluidoOSHoje } from "../../../__mocks__/DetalhesConcluidoOSHojeMock";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        goBack: jest.fn(), // Simulando o comportamento de goBack
        dispatch: jest.fn(), // Simulando o comportamento de dispatch
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

const mockRoute1 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje()}
    },
};

const mockRoute2 = {
    params: {
      etapas: { ...MockDetalhesConcluidoOSHoje(),
                medidor_ns: ''
      }
    },
};


describe('Passo Material', () => {

    it('renderiza corretamente a tela e clica no botão voltar', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoMedidor route={mockRoute1} />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-back'));
        });         
    });

    it('renderiza corretamente a tela, digita o numero de serie e clica no botão continue', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                            <PassoMedidor route={mockRoute1} />
                                        </NavigationContainer> );
        

        const input = getByTestId('input-ns');
  
        fireEvent.press(input);

        // Simula a digitação do texto"
        fireEvent.changeText(input, 'NS12345678');

        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });         
    });

    it('renderiza corretamente a tela, não digita o numero de serie e clica no botão continue', async () => {
    
        const {getByTestId, getByText} = render(<NavigationContainer>
                                                    <PassoMedidor route={mockRoute2} />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-continue'));
        });
        
        await act(async () => {         
            fireEvent.press(getByText('OK'));
        });        
    });

});