import {View, Text,TouchableOpacity} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// modal messages
import MessageAvisoFotoInstalacao from '../../modal/messagebox/AvisoFotoInstalacao'
import MessageBoxPergunta from '../../modal/messagebox/Pergunta'

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// componentes
import Foto from '../../componentes/Foto'

// dados das etapas de instalação
import {EtapasInstalacao} from "../../data"

// constantes
import {ScreenTipoOS, TipoStatusOS } from "../../constantes"

// funcoes
import {Hora} from "../../funcoes"

const PassoFotoInstalacao = ({route}) => {
    
    const [imagemAtual, setImagemAtual] = useState<number>(1);

    const [imagem1, setImagem1] = useState<string>(route.params?.etapas.imagem_instalacao1);
    const [imagem2, setImagem2] = useState<string>(route.params?.etapas.imagem_instalacao2);
    const [imagem3, setImagem3] = useState<string>(route.params?.etapas.imagem_instalacao3);

    const [texto1, setTexto1] = useState<string>(route.params?.etapas.imagem_texto_instalacao1);
    const [texto2, setTexto2] = useState<string>(route.params?.etapas.imagem_texto_instalacao2);
    const [texto3, setTexto3] = useState<string>(route.params?.etapas.imagem_texto_instalacao3);

    // aviso de como tirar as fotos
    const [showMessageAviso, setShowMessageAviso] = useState(!route.params?.etapas.numero_imagens_instalacao);
    const onMessageAvisoFechar = () => { setShowMessageAviso(false); };

    // mensagem de pergunta se deve ou não continuar sem adicionar fotos
    const [showMessageBoxPergunta, setShowMessageBoxPergunta] = useState(false);
    const [textoPergunta, setTextoPergunta] = useState('');
    const onMessageBoxPerguntaAbrir = () => { setShowMessageBoxPergunta(true); };

    // resultado da mensagem de pergunta
    const onResultCancel = () => { setShowMessageBoxPergunta(false); };

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    const heigthFoto = 300;
    const widthFoto = 300;
    const heigthIcon = 80;
    const widthIcon = 80;

    // indica o resultado da escolha do aviso
    const onResultOK = (etapas: EtapasInstalacao) => {

        setShowMessageBoxPergunta(false); 

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas, horario_fim: Hora(new Date()), numero_imagens_instalacao: 0, step6: true };

        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes:[
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
                ],
            })
        );        
    };

    // navegar de volta
    const goBack = (etapas: EtapasInstalacao) => {
           
        // navega a pagina progresso da instalação
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
                ],
            })
        );
    };     

    // navega para pagina de ordem de serviço
    const goToOrdemServico = (etapas: EtapasInstalacao) => {

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas,
                                   horario_fim: Hora(new Date()),
                                   status_os: TipoStatusOS.CONCLUIDO,
                                   imagem_instalacao1: imagem1, 
                                   imagem_instalacao2: imagem2, 
                                   imagem_instalacao3: imagem3, 
                                   imagem_texto_instalacao1: texto1,
                                   imagem_texto_instalacao2: texto2,
                                   imagem_texto_instalacao3: texto3,
                                   step6: true };

        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes:[
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
                ],
            })
        );        
  
        setTextoPergunta("Tem certeza que deseja continuar sem registrar nenhuma foto da instalação?");
        
        onMessageBoxPerguntaAbrir();
    };

    // navega para pagina de visualização da imagem
    const goToPassoFoto = (etapas: EtapasInstalacao, imagem: string) => {
        
        // verifica se existe alguma imagem
        if(imagem.length > 0)
        {
            const _etapas: EtapasInstalacao = etapas;
        
            // atualiza estrutura de etapas
            const etapasAtualizada = { ..._etapas, 
                                       imagem_instalacao1: imagem1, 
                                       imagem_instalacao2: imagem2, 
                                       imagem_instalacao3: imagem3, 
                                       imagem_texto_instalacao1: texto1,
                                       imagem_texto_instalacao2: texto2,
                                       imagem_texto_instalacao3: texto3,
                                       step6: false };
    
            // navega a pagina progresso da instalação fechando as 2 paginas anteriores
            navigation.dispatch(
                CommonActions.reset({
                    index: 0,
                    routes: [
                        { name: 'PassoFoto', params: {etapas: etapasAtualizada, imagem: imagem, screen: 'PassoFotoInstalacao'} },
                    ],
                })
            );
        }
    };

   // aponta para a proxima imagem
   const proximaImagem = () => {

        switch(imagemAtual) {
            case 1:            
                if(imagem1.length)
                    setImagemAtual(2);              
            break;
            case 2:
                if(imagem2.length)
                    setImagemAtual(3);
            break;
        }
    };

    return (

        <>
            <MessageAvisoFotoInstalacao 
                visivel={showMessageAviso}                 
                onFechar={onMessageAvisoFechar}
            />

            <MessageBoxPergunta
                visivel={showMessageBoxPergunta} 
                titulo="Atenção" 
                descricao={textoPergunta} 
                textoBotaoOK="Confirmar" 
                textoBotaoCancelar='Cancelar'
                onOK={() => onResultOK(route.params?.etapas)}
                onCancel={onResultCancel}
            />

            <View style = {styles.containerTelaFotoLocal}>

                {/* header */}
                <View style={styles.containerHeaderFotoLocal}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>

                        <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderPassoID}>FOTOS DA INSTALAÇÃO</Text>
                    </View>

                </View>

                {
                    (!showMessageAviso) 
                    ?
                    
                        <View style={styles.containerConteudoFotoLocal}>

                            {
                                /* fotos da Probe */
                                (imagemAtual === 1) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto da Probe</Text>
                                        <Foto imagem={imagem1}
                                            texto_imagem={texto1}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem1}
                                            onValueText={setTexto1}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem1)}
                                        />
                                    </View>
                            }

                            {
                                /* fotos da antena */
                                (imagemAtual === 2) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto da Antena</Text>
                                        <Foto imagem={imagem2}
                                            texto_imagem={texto2}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem2}
                                            onValueText={setTexto2}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem2)}
                                        />
                                    </View>
                            }

                            {
                                /* fotos da instalação */
                                (imagemAtual === 3) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto da instalação</Text>
                                        <Foto imagem={imagem3}
                                            texto_imagem={texto3}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem3}
                                            onValueText={setTexto3}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem3)}
                                        />
                                    </View>
                            }                            

                            {
                                ( imagemAtual === 3 && imagem3.length)
                                ?
                                    <TouchableOpacity style={styles.buttonContinuarPassoID}
                                                      onPress={() => goToOrdemServico(route.params?.etapas)}>
                                        <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
                                    </TouchableOpacity>                                
                                :
                                    <TouchableOpacity style={{...styles.buttonContinuarPassoID, backgroundColor:'#A3A3A3'}}
                                                      onPress={() => proximaImagem()}>
                                        <Text style = {styles.textoButtonContinuarPassoID}>PRÓXIMO</Text>
                                    </TouchableOpacity> 
                            }
                    </View>
                    
                : null
                }

            </View>        
        </>

    );
};

export default PassoFotoInstalacao;