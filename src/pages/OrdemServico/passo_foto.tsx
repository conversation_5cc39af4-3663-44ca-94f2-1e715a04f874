import {View, TouchableOpacity, ImageBackground} from 'react-native';
import React from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconX from '../../assets/svg/icon_x.svg'

// rotas drawer de navegação
import {StackTypes} from  '../MenuPrincipal/index';
import { EtapasInstalacao } from '../../data';


const PassoFoto = ({route}) => {

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();
  
  // navegar de volta
  const goBack = (etapas: EtapasInstalacao) => {
            
    // navega a pagina progresso da instalação fechando as 2 paginas anteriores
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
          routes: [
            { name: route.params?.screen, params: {etapas: etapas} },
          ],
        })
    );    
  };  

  return (

    <ImageBackground style = {styles.containerTelaFotoLocal} source={{ uri: route.params?.imagem }} >

      {/* header */}
      <View style={{...styles.containerHeaderFotoLocal}}>

        {/* botão voltar */}
        <View style={{width:'100%', paddingRight: 20, paddingTop: 20, alignItems:'flex-end', justifyContent:'flex-end'}}>

          <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
            <IconX color={"#FFFFFF"} width={50} height={50}/>
          </TouchableOpacity>

        </View>

      </View>    
    
    </ImageBackground> 


  );
};

export default PassoFoto;