import {View, Text,TouchableOpacity} from 'react-native';
import React, { useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// modal messages
import MessageAvisoFotoLocal from '../../modal/messagebox/AvisoFotoLocal'
import MessageBoxPergunta from '../../modal/messagebox/Pergunta'

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// componentes
import Foto from '../../componentes/Foto'

// dados das etapas de instalação
import {EtapasInstalacao} from "../../data"
import { ScreenTipoOS, TipoStatusOS } from '../../constantes';

const PassoFotoLocal = ({route}) => {

    const [imagemAtual, setImagemAtual] = useState<number>(1);

    const [imagem1, setImagem1] = useState<string>(route.params?.etapas.imagem_local1);
    const [imagem2, setImagem2] = useState<string>(route.params?.etapas.imagem_local2);
    const [imagem3, setImagem3] = useState<string>(route.params?.etapas.imagem_local3);
    const [imagem4, setImagem4] = useState<string>(route.params?.etapas.imagem_local4);

    const [texto1, setTexto1] = useState<string>(route.params?.etapas.imagem_texto_local1);
    const [texto2, setTexto2] = useState<string>(route.params?.etapas.imagem_texto_local2);
    const [texto3, setTexto3] = useState<string>(route.params?.etapas.imagem_texto_local3);
    const [texto4, setTexto4] = useState<string>(route.params?.etapas.imagem_texto_local4);

    // aviso de como tirar as fotos
    const [showMessageAviso, setShowMessageAviso] = useState(!route.params?.etapas.numero_imagens_local);
    const onMessageAvisoFechar = () => { setShowMessageAviso(false); };

    // mensagem de pergunta se deve ou não continuar sem adicionar fotos
    const [showMessageBoxPergunta, setShowMessageBoxPergunta] = useState(false);
    const [textoPergunta, setTextoPergunta] = useState('');
    const onMessageBoxPerguntaAbrir = () => { setShowMessageBoxPergunta(true); };

    // resultado da mensagem de pergunta
    const onResultCancel = () => { setShowMessageBoxPergunta(false); };

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    const heigthFoto = 300;
    const widthFoto = 300;
    const heigthIcon = 80;
    const widthIcon = 80;

    const onResultOK = (etapas: EtapasInstalacao) => {

        setShowMessageBoxPergunta(false); 

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas, status_os: TipoStatusOS.PENDENTE, numero_imagens_local: 0, step3: true };

        //navigation.navigate('OrdemServico', {etapas: etapasAtualizada});  
        
        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: 'OrdemServico', params: {etapas: etapasAtualizada} },
                ],
            })
        );
    };

    // navega para pagina de ordem de serviço
    const goToOrdemServico = (etapas: EtapasInstalacao) => {

        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas,                                    
                                    imagem_local1: imagem1, 
                                    imagem_local2: imagem2, 
                                    imagem_local3: imagem3, 
                                    imagem_local4: imagem4, 
                                    imagem_texto_local1: texto1,
                                    imagem_texto_local2: texto2,
                                    imagem_texto_local3: texto3,
                                    imagem_texto_local4: texto4,
                                    step3: true };
    
        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                        { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapasAtualizada} },
                ],
            })
        );
              
        setTextoPergunta("Tem certeza que deseja continuar sem registrar nenhuma foto do local?");
        
        onMessageBoxPerguntaAbrir();
    };

    // navega para pagina de visualização da imagem
    const goToPassoFoto = (etapas: EtapasInstalacao, imagem: string) => {
        
        const _etapas: EtapasInstalacao = etapas;
        
        // atualiza estrutura de etapas
        const etapasAtualizada = { ..._etapas, 
                                   imagem_local1: imagem1, 
                                   imagem_local2: imagem2, 
                                   imagem_local3: imagem3, 
                                   imagem_local4: imagem4, 
                                   imagem_texto_local1: texto1,
                                   imagem_texto_local2: texto2,
                                   imagem_texto_local3: texto3,
                                   imagem_texto_local4: texto4,
                                   step3: false };

        // navega a pagina progresso da instalação fechando as 2 paginas anteriores
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: 'PassoFoto', params: {etapas: etapasAtualizada, imagem: imagem, screen:'PassoFotoLocal'} },
                ],
            })
        );
        
    };

    // navegar de volta
    const goBack = (etapas: EtapasInstalacao) => {
           
        // navega a pagina progresso da instalação
        navigation.dispatch(
            CommonActions.reset({
                index: 0,
                routes: [
                    { name: ScreenTipoOS[route.params?.etapas.tipo_os], params: {etapas: etapas} },
                ],
            })
        );
    };

    // aponta para a proxima imagem
    const proximaImagem = () => {

        switch(imagemAtual) {
            case 1:            
                if(imagem1.length)
                    setImagemAtual(2);              
            break;
            case 2:
                if(imagem2.length)
                    setImagemAtual(3);
            break;
            case 3:
                if(imagem3.length)
                    setImagemAtual(4);
            break;
          }
    };

    return (

        <>
            <MessageAvisoFotoLocal 
                visivel={showMessageAviso}             
                onFechar={onMessageAvisoFechar}
            />                            

            <MessageBoxPergunta
                visivel={showMessageBoxPergunta} 
                titulo="Atenção" 
                descricao={textoPergunta} 
                textoBotaoOK="Confirmar" 
                textoBotaoCancelar='Cancelar'
                onOK={() => onResultOK(route.params?.etapas)}
                onCancel={onResultCancel}
            />

            <View style = {styles.containerTelaFotoLocal}>

                {/* header */}
                <View style={styles.containerHeaderFotoLocal}>

                    {/* botão voltar */}
                    <View style={styles.containerBotaoVoltar}>

                        <TouchableOpacity onPress={() => goBack(route.params?.etapas)} testID='button-back'>
                            <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
                        </TouchableOpacity>    
                    </View>

                    {/* texto cabeçalho */}
                    <View style={styles.containerTextoHeader}>
                        <Text style={styles.textoHeaderPassoID}>FOTOS DO LOCAL</Text>
                    </View>

                </View>

                {
                    (!showMessageAviso) 
                    ?                    
                        <View style={styles.containerConteudoFotoLocal}>

                            {
                                /* fotos da tomada */
                                (imagemAtual === 1) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto da Tomada</Text>
                                        <Foto imagem={imagem1}
                                            texto_imagem={texto1}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem1}
                                            onValueText={setTexto1}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem1)}
                                        />
                                    </View>
                            }

                            {
                                /* fotos da cabine */
                                (imagemAtual === 2) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto da Cabine</Text>
                                        <Foto imagem={imagem2}
                                            texto_imagem={texto2}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem2}
                                            onValueText={setTexto2}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem2)}
                                        />
                                    </View>
                            }
                            
                            {
                                /* fotos do medidor */
                                (imagemAtual === 3) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto do Medidor</Text>
                                        <Foto imagem={imagem3}
                                            texto_imagem={texto3}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem3}
                                            onValueText={setTexto3}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem3)}
                                        />
                                    </View>
                            }

                            {
                                /* fotos do Telhado */
                                (imagemAtual === 4) &&
                                    <View style={{justifyContent:'center', alignItems:'center', gap:20}}>
                                        <Text style={styles.textoFotoTitulo}>Tire uma foto do Telhado</Text>
                                        <Foto imagem={imagem4}
                                            texto_imagem={texto4}
                                            height={heigthFoto} width={widthFoto} 
                                            heightIcon={heigthIcon} widthIcon={widthIcon}
                                            onValueChange={setImagem4}
                                            onValueText={setTexto4}
                                            onPressImage={() => goToPassoFoto(route.params?.etapas, imagem4)}
                                        />
                                    </View>
                            }                            

                            {
                                ( imagemAtual === 4 && imagem4.length)
                                ?
                                    <TouchableOpacity style={styles.buttonContinuarPassoID}
                                                      onPress={() => goToOrdemServico(route.params?.etapas)}>
                                        <Text style = {styles.textoButtonContinuarPassoID}>CONTINUAR</Text>
                                    </TouchableOpacity>                                
                                :
                                    <TouchableOpacity style={{...styles.buttonContinuarPassoID, backgroundColor:'#A3A3A3'}}
                                                      onPress={() => proximaImagem()}>
                                        <Text style = {styles.textoButtonContinuarPassoID}>PRÓXIMO</Text>
                                    </TouchableOpacity> 
                            }

                        </View>
                    
                    : null
                }

            </View>        
        </>

    );
};

export default PassoFotoLocal;