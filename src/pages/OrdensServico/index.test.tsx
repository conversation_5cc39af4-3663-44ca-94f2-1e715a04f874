import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import React from "react";
import OrdensServico from ".";
import { MessageBoxProvider } from "../../contexts/MessageBoxContext";
import { Protheus } from "../../services/protheus";

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

// Mockando as dependências
jest.mock("../../storages", () => ({
  setStorageJson: jest.fn(),
  getStorageJson: jest.fn(),
  KEY: {
    ordensServicoHoje: "ordensServicoHoje",
    ordensServicoAmanha: "ordensServicoAmanha",
    ordensServicoFuturas: "ordensServicoFuturas",
    agendaProtheus: "agendaProtheus",
  },
}));

// Mock do Protheus (opcional para evitar requests reais)
jest.mock('../../services/protheus', () => ({
  Protheus: jest.fn().mockImplementation(() => ({
    autenticarProtheus: jest.fn().mockResolvedValue(201),
  })),
}));

jest.mock('react-native-calendar-events', () => ({
    checkPermissions: jest.fn(() => Promise.resolve('authorized')),
    requestPermissions: jest.fn(() => Promise.resolve('authorized')),
}));

jest.mock('../../../__mocks__/AgendaJsonMock', () => ({
  MockAgendaInstaladorJson: {
    Agendas: [
      {
        DtInicio: '2025-05-30',
        HrInicio: '08:00',
        DtFim: '2025-05-30',
        HrFim: '09:00',
        CodOcor: '001',
        DescOcor: 'Instalação',
        NomeSC: 'Cliente A',
        Endereco: 'Rua X',
        DescPro: 'Produto Y'
      }
    ]
  }
}));

describe('Ordens Serviço', () => {

    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
        jest.clearAllMocks();
    });

    it('renderiza corretamente a tela', () => {
    
        const {unmount} = render(   <MessageBoxProvider>
                                        <NavigationContainer>
                                            <OrdensServico />
                                        </NavigationContainer>
                                    </MessageBoxProvider>
                                );
        
        expect(screen).toBeTruthy();

        unmount(); // Garante que o componente seja desmontado no final do teste 
    });

    it('renderiza corretamente a tela selecionando o tabview Amanhã', async () => {
    
        const {unmount, getAllByText} = render( 
                                                <MessageBoxProvider>
                                                    <NavigationContainer>
                                                        <OrdensServico />
                                                    </NavigationContainer>
                                                </MessageBoxProvider>
                                            );
        
        const elements = getAllByText('Amanhã');

        // clica em todos os Amanhã encontrados
        await act(async () => {
            elements.forEach((element) => {
                fireEvent.press(element);
            });
        });

        unmount(); // Garante que o componente seja desmontado no final do teste 
    });    

    it('renderiza corretamente a tela selecionando o tabview Futuras', async () => {
    
        const {unmount, getAllByText} = render(
                                                <MessageBoxProvider>
                                                    <NavigationContainer>
                                                        <OrdensServico />
                                                    </NavigationContainer>
                                                </MessageBoxProvider>
                                            );
        
        const elements = getAllByText('Futuras');
        
        // clica em todos os Futuras encontrados
        await act(async () => {
            elements.forEach((element) => {
                fireEvent.press(element);
            });
        });

        unmount(); // Garante que o componente seja desmontado no final do teste         
    });

    it('chama showMessageBox se autenticação falhar', async () => {
        // Mocka Protheus retornando 403
        const mockAutenticar = jest.fn().mockResolvedValue(403);
        (Protheus as jest.Mock).mockImplementation(() => ({
            autenticarProtheus: mockAutenticar,
        }));

        // Espiona o showMessageBox
        const showMessageBoxMock = jest.fn();
        jest.spyOn(require('../../contexts/MessageBoxContext'), 'useMessageBox')
            .mockReturnValue({ showMessageBox: showMessageBoxMock });

        const { unmount } = render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        // Aguarda o tempo do setTimeout (ajustado para testes com fake timers)
        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // força a execução do .then da Promise
        });


        expect(mockAutenticar).toHaveBeenCalled();
        expect(showMessageBoxMock).toHaveBeenCalledWith(expect.objectContaining({
            title: 'Atenção',
            description: expect.stringContaining('Usuário não autorizado'),
            type: 'error'
        }));

        unmount();
    });

    it('chama showMessageBox ao lançar erro na autenticação (bloco catch)', async () => {
        // Mocka o Protheus para lançar erro
        const mockAutenticar = jest.fn().mockRejectedValue(new Error('Erro de rede'));
        (Protheus as jest.Mock).mockImplementation(() => ({
            autenticarProtheus: mockAutenticar,
        }));

        // Espiona showMessageBox
        const showMessageBoxMock = jest.fn();
        jest.spyOn(require('../../contexts/MessageBoxContext'), 'useMessageBox')
            .mockReturnValue({ showMessageBox: showMessageBoxMock });

        render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        // Avança o tempo do setTimeout E força execução da microtask com o erro
        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // Garante execução do catch
        });

        // Verifica que o showMessageBox foi chamado com a mensagem do catch
        expect(showMessageBoxMock).toHaveBeenCalledWith(expect.objectContaining({
            title: 'Atenção',
            description: expect.stringContaining('Falha na autenticação'),
            type: 'error',
        }));
    });

    it('autentica com sucesso e não chama showMessageBox', async () => {
        // Mocka Protheus retornando 201
        const mockAutenticar = jest.fn().mockResolvedValue(201);
        (Protheus as jest.Mock).mockImplementation(() => ({
            autenticarProtheus: mockAutenticar,
        }));

        const { unmount } = render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // força a execução do .then da Promise
        });

        // Verifica se a autenticação foi chamada
        expect(mockAutenticar).toHaveBeenCalled();

        unmount();
    });    

    it('exibe mensagem se getAgendaInstalador retornar erro', async () => {
        (Protheus as jest.Mock).mockImplementation(() => ({
            autenticarProtheus: jest.fn().mockResolvedValue(201),
            agendaInstaladoresProtheus: jest.fn().mockResolvedValue(500),
        }));

        const showMessageBoxMock = jest.fn();
        jest.spyOn(require('../../contexts/MessageBoxContext'), 'useMessageBox')
            .mockReturnValue({ showMessageBox: showMessageBoxMock });

        const { unmount} = render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // força a execução do .then da Promise
        });

        expect(showMessageBoxMock).toHaveBeenCalledWith(expect.objectContaining({
            description: expect.stringContaining('Não existem agendamentos'),
        }));
        
        unmount();
    });

    it('adiciona eventos ao calendário com dados da agenda', async () => {
        (Protheus as jest.Mock).mockImplementation(() => ({
            autenticarProtheus: jest.fn().mockResolvedValue(201),
            agendaInstaladoresProtheus: jest.fn().mockResolvedValue(200),
        }));

        const { unmount} = render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // força a execução do .then da Promise
        });

        expect(screen).toBeTruthy();

        unmount();
    });

it('exibe mensagem se usuário negar permissão do calendário', async () => {
  const mockAutenticar = jest.fn().mockResolvedValue(201);
  const mockAgenda = jest.fn().mockResolvedValue(200);

  (Protheus as jest.Mock).mockImplementation(() => ({
    autenticarProtheus: mockAutenticar,
    agendaInstaladoresProtheus: mockAgenda,
  }));

  const showMessageBoxMock = jest.fn();
  jest.spyOn(require('../../contexts/MessageBoxContext'), 'useMessageBox')
      .mockReturnValue({ showMessageBox: showMessageBoxMock });

        const { unmount} = render(
            <MessageBoxProvider>
                <NavigationContainer>
                    <OrdensServico />
                </NavigationContainer>
            </MessageBoxProvider>
        );

        await act(async () => {
            jest.advanceTimersByTime(1000);
            await Promise.resolve(); // força a execução do .then da Promise
        });

    unmount();
  //expect(showMessageBoxMock).toHaveBeenCalledWith(expect.objectContaining({
  //  description: expect.stringContaining('Usuário não permitiu acesso'),
  //}));
});
    
});