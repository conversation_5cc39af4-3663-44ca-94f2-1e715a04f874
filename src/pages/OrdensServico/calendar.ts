  import RNCalendarEvents from 'react-native-calendar-events';
  
  /**
   * adiciona agendamentos no calendario
   */
  export const addEventCalendar = async (title: string, date_start: string, date_end: string, location: string, notes: string) : Promise<boolean> => {

    try {
      await RNCalendarEvents.saveEvent(title, {
        calendarId: '2', // ID da conta Google
        startDate: date_start,
        endDate: date_end,
        location: location,
        notes: notes,
        timeZone: 'America/Sao_Paulo',
      });

      return true;

    } catch (error) {
      return false;
    }
  }

  /**
   * verifica a permissão para acessar o calendario do app
   * @returns 
   */
  export const calendarPermission = async () => {
      
    let permission = await RNCalendarEvents.checkPermissions();

    if (permission === 'undetermined') {
      permission = await RNCalendarEvents.requestPermissions();
    }

    if (permission === 'authorized') {
      return (true);
    } 

    return false;
  }
