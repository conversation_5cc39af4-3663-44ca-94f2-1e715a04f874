import {View, Text, TouchableOpacity, FlatList} from 'react-native';
import React, {useRef, useState} from 'react';
import RBSheet from 'react-native-raw-bottom-sheet';

// estilos para a page
import {styles} from '../OrdensServico/layout';

// constantes
import { DescricaoOS, TipoStatusOS, DescricaoStatusOS, CoresStatusOS } from "../../constantes"

// funções
import {AdicionaDiaDataAtual} from "../../funcoes"

// imagens svg
import IconFilter from '../../assets/svg/icon_filter-funnel-01.svg'
import IconPin from '../../assets/svg/icon_marker-pin-01.svg'
import IconCalendario from '../../assets/svg/icon_calendar.svg'
import IconUsuario from '../../assets/svg/icon_user-01.svg'
import IconGrid from '../../assets/svg/icon_grid-01.svg'
import IconCheck from '../../assets/svg/icon_check.svg'

// dados armazenados localmente no app
import { ListaOrdensServicoAmanha } from '../../data';

interface OrdemServicoCardProps {
    cliente: string; 
    numero_os: string;
    tipo_os: number;
    status_os: number;
    endereco: string;
    bairro: string;
    cep: string;
    agenda_data: string;
    agenda_hora: string;
    contato: string;
    telefone: string;
    numero_pontos: number;
}
const OrdemServicoCard = ({ cliente, 
                            numero_os,
                            tipo_os,
                            status_os,
                            endereco,
                            bairro,
                            cep,
                            agenda_data,
                            agenda_hora,
                            contato,
                            telefone,
                            numero_pontos }: OrdemServicoCardProps) => (                            

    <View style={{paddingBottom:20}}>

        <View style={{...styles.containerOS, height:340,}}>
        
            {/* nome do cliente */}
            <View>
                
                <Text style={styles.textoNomeCliente}>{cliente}</Text>
        
                {/* numero da OS */}
                <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoDestaque}>OS:</Text>
                    <Text style={styles.textoSimples}>{numero_os}</Text>
                </View>

            </View>
        
            {/* tipo de serviço */}
            <View style={{flexDirection:'row', alignItems:'center', justifyContent: 'space-between'}}>

                <View style={{flexDirection:'row', gap:3}}>
                    <Text style={styles.textoDestaque}>Tipo:</Text>
                    <Text style={styles.textoSimples}>{DescricaoOS[tipo_os]}</Text>
                </View>
                                    
                <Text style={{...styles.textoStatusOS, backgroundColor:CoresStatusOS[status_os]}}>{DescricaoStatusOS[status_os]}</Text>

            </View>
        
            <View style={styles.linhaHorizontal} />
            
            {/* endereço */}
            <View style={{flexDirection:'row'}}>
        
                <View style={{width:'10%'}}>
                    <IconPin color={"#2E8C1F"} height={20} width={20}/>
                </View>
        
                <View style={{width:'90%'}}>
        
                    {/* endereco */}
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Endereço:</Text>
                        <Text style={styles.textoSimples}>{endereco}</Text>
                    </View>
            
                    {/* bairro */}
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Bairro:</Text>
                        <Text style={styles.textoSimples}>{bairro}</Text>
                    </View>
        
                    {/* cep */}
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>CEP:</Text>
                        <Text style={styles.textoSimples}>{cep}</Text>
                    </View>
                    
                </View>
                    
            </View>
    
            {/* horario agendado */}
            <View style={{flexDirection:'row'}}>
                                    
                <View style={{width:'10%'}}>
                    <IconCalendario color={"#2E8C1F"} height={20} width={20}/>
                </View>
                <View style={{width:'90%'}}>
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Agendado: </Text>
                        <Text style={styles.textoSimples}>{agenda_hora}</Text>
                    </View>                        
                </View>

            </View>
        
            {/* informações do contato */}
            <View style={{flexDirection:'row'}}>

                <View style={{width:'10%'}}>
                    <IconUsuario color={"#2E8C1F"} height={20} width={20}/>
                </View>
                <View style={{width:'90%'}}>                                
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Contato: </Text>
                        <Text style={styles.textoSimples}>{contato}</Text>
                    </View>
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Telefone: </Text>
                        <Text style={styles.textoSimples}>{telefone}</Text>
                    </View>                                                
                </View>

            </View>
        
            {/* pontos de medição */}
            <View style={{flexDirection:'row'}}>

                <View style={{width:'10%'}}>
                    <IconGrid color={"#2E8C1F"} height={20} width={20}/>
                </View>
                <View style={{width:'90%'}}>
                    <View style={{flexDirection:'row', gap:3}}>
                        <Text style={styles.textoDestaque}>Pontos de Medição: </Text>
                        <Text style={styles.textoSimples}>{numero_pontos}</Text>
                    </View>                        
                </View>
            </View>
        
        </View>

    </View>                                
);

const CustomListOrdemServico = ({status}:any) => {
    
    // pega a lista armazenada com as ordens de serviço do dia seguinte getStorageJson(KEY.ordensServicoAmanha).then((value) => { setListaOrdensServicoAmanha(value)});

    return(

        <FlatList 
            data = {(status == TipoStatusOS.TODOS) ? ListaOrdensServicoAmanha: ListaOrdensServicoAmanha.filter(os => os.status_os == status)}
            renderItem={({ item }) => <OrdemServicoCard
                                        cliente = {item.cliente}
                                        numero_os = {item.numero_os}
                                        tipo_os = {item.tipo_os}
                                        status_os = {item.status_os}
                                        endereco = {item.endereco}
                                        bairro = {item.bairro}
                                        cep = {item.cep}
                                        agenda_data = {item.agenda_data}
                                        agenda_hora = {item.agenda_hora}
                                        contato = {item.contato}
                                        telefone = {item.telefone}
                                        numero_pontos = {item.numero_pontos}
                                    />
                        }
        />
    )
};

export default function Amanha () {

    // referencia para o bottom sheet
    const refRBSheet = useRef<{ open: () => void, close: () => void }>(null);

    // filtro para tipo de status da ordem de serviço
    const [filter, setFilter] = useState(3);    

    return(

        <View style={styles.containerTela}>

            <View style={{ paddingHorizontal:20, flexDirection:'row', justifyContent:'center', alignItems:'center'}}>

                {/* data do dia seguinte */}
                <View style={{width:'50%', alignItems:'flex-start'}}>
                    <Text style={styles.textoData}>{AdicionaDiaDataAtual(1)}</Text>            
                </View>

                {/* botão filtrar */}
                <View style={{width:'50%', alignItems:'flex-end'}}>
                    <TouchableOpacity style={{...styles.buttonFiltrar, justifyContent:'space-between', paddingHorizontal: 20, width: 150}}
                                      onPress={() => refRBSheet.current?.open()}
                                      testID='button-filter'>
                        <Text style={styles.textoFiltrar}>Filtrar</Text>
                        <IconFilter color={"#FFFFFF"} width={20} height={20}/>
                    </TouchableOpacity>
                </View>                

            </View>
            
            <View style={{height: '90%', padding: 20}}>

                <CustomListOrdemServico status={filter}/>

            </View>
        
            <RBSheet ref={refRBSheet} height={260}>

                <View style={{flex:1}}>
                    
                    <TouchableOpacity style={styles.buttonBottomSheet} onPress={() => {refRBSheet.current?.close(); setFilter(TipoStatusOS.TODOS)}} testID='button-todos'>
                        <Text style={styles.textoBottomSheet}>Todos</Text>
                        {
                            (filter == TipoStatusOS.TODOS) &&
                                <IconCheck color={'#2E8C1F'} height={30} width={30}/>                            
                        }
                    </TouchableOpacity>

                    <View style={{borderBottomColor: '#E5E5E5', borderBottomWidth: 1, width:'100%'}} />

                    <TouchableOpacity style={styles.buttonBottomSheet} onPress={() => {refRBSheet.current?.close(); setFilter(TipoStatusOS.CONCLUIDO)}} testID='button-concluido'>
                        <Text style={styles.textoBottomSheet}>Concluído</Text>
                        {
                            (filter == TipoStatusOS.CONCLUIDO) &&
                                <IconCheck color={'#2E8C1F'} height={30} width={30}/>                            
                        }                        
                    </TouchableOpacity>

                    <View style={{borderBottomColor: '#E5E5E5', borderBottomWidth: 1, width:'100%'}} />

                    <TouchableOpacity style={styles.buttonBottomSheet} onPress={() => {refRBSheet.current?.close(); setFilter(TipoStatusOS.PENDENTE)}} testID='button-pendente'>
                        <Text style={styles.textoBottomSheet}>Pendente</Text>
                        {
                            (filter == TipoStatusOS.PENDENTE) &&
                                <IconCheck color={'#2E8C1F'} height={30} width={30}/>
                            
                        }                        
                    </TouchableOpacity>

                    <View style={{borderBottomColor: '#E5E5E5', borderBottomWidth: 1, width:'100%'}} />

                    <TouchableOpacity style={styles.buttonBottomSheet} onPress={() => {refRBSheet.current?.close(); setFilter(TipoStatusOS.NAO_INICIADO)}} testID='button-nao-iniciado'>
                        <Text style={styles.textoBottomSheet}>Não iniciado</Text>
                        {
                            (filter == TipoStatusOS.NAO_INICIADO) &&
                                <IconCheck color={'#2E8C1F'} height={30} width={30}/>                            
                        }                        
                    </TouchableOpacity>

                </View>

            </RBSheet>

        </View>
    );
};