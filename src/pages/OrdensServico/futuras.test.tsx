import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import Futuras from "./futuras";


jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        navigate: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

describe('Ordens Serviço - futuras', () => {

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                <Futuras />
                            </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela ao clicar no botão filter', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Futuras />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });

        unmount(); // Garante que o componente seja desmontado no final do teste
            
    });
    
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Todos', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Futuras />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-todos'));
        });        
            
        unmount(); // Garante que o componente seja desmontado no final do teste        
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Concluido e verificar detalhes', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Futuras />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-concluido'));
        });        
       
        unmount(); // Garante que o componente seja desmontado no final do teste
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Pendente', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Futuras />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-pendente'));
        });        
              
        unmount(); // Garante que o componente seja desmontado no final do teste        
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Pendente', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Futuras />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        await act(async () => {         
            fireEvent.press(getByTestId('button-nao-iniciado'));
        });        
              
        unmount(); // Garante que o componente seja desmontado no final do teste
    });
});