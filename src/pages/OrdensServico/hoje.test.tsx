import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { act, fireEvent, render, screen } from "@testing-library/react-native";
import Hoje from "./hoje";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        navigate: jest.fn(), // Simulando o comportamento de goBack
      }),
    };
});

describe('Ordens Serviço - hoje', () => {

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                        <Hoje />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela ao clicar no botão filter', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                        <Hoje />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        }); 
        
        unmount(); // Garante que o componente seja desmontado no final do teste        
    });
    
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Todos', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Hoje />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });    

        await act(async () => {         
            fireEvent.press(getByTestId('button-todos'));
        });        
               
        unmount(); // Garante que o componente seja desmontado no final do teste          
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Concluido e verificar detalhes', async () => {
    
        const {unmount, getByTestId} = render(  <NavigationContainer>
                                                    <Hoje />
                                                </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-concluido'));
        });        
           
        unmount(); // Garante que o componente seja desmontado no final do teste 
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Pendente', async () => {
    
        const {unmount, getByTestId} = render(   <NavigationContainer>
                                            <Hoje />
                                        </NavigationContainer> );

        expect(screen).toBeTruthy();
                
        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-pendente'));
        });

        unmount(); // Garante que o componente seja desmontado no final do teste        
    });
    
    it('renderiza corretamente a tela ao clicar no botão filter e selecionar Pendente', async () => {
    
        const {unmount, getByTestId} = render(<NavigationContainer>
                                        <Hoje />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-filter'));
        });        
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        await act(async () => {         
            fireEvent.press(getByTestId('button-nao-iniciado'));
        });
        
        // Aguarde possíveis atualizações assíncronas no Animated ou estados posteriores
        await act(async () => {
            expect(screen).toBeTruthy();        
        });

        unmount(); // Garante que o componente seja desmontado no final do teste                
    });

    it('renderiza corretamente a tela ao clicar no botão de verificar detalhes de uma tarefa concluida.', async () => {
    
        const {getAllByText} = render(  <NavigationContainer>
                                            <Hoje />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();
        

        // Encontre todos os botões com o texto "VER DETALHES"
        const buttons = getAllByText('VER DETALHES');

        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act(async () => {
            buttons.forEach((button) => {
                fireEvent.press(button);
            });
        });        
           
    });
    
    it('renderiza corretamente a tela ao clicar no botão de verificar detalhes de uma tarefa não iniciada.', async () => {
    
        const {getAllByText} = render(   <NavigationContainer>
                                            <Hoje />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();
        

        // Encontre todos os botões com o texto "VER DETALHES"
        const buttons = getAllByText('DETALHES');

        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(1);

        await act(async () => {
            buttons.forEach((button) => {
                fireEvent.press(button);
            });
        });      
    });
    
    it('renderiza corretamente a tela ao clicar no botão de iniciar de uma tarefa não iniciada.', async () => {
    
        const {getAllByText} = render(   <NavigationContainer>
                                            <Hoje />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();
        

        // Encontre todos os botões com o texto "INICIAR"
        const buttons = getAllByText('INICIAR');

        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act(async () => {
            buttons.forEach((button) => {
                fireEvent.press(button);
            });
        });        
           
    });
    
    it('renderiza corretamente a tela ao clicar no botão de continuar de uma tarefa pendente.', async () => {
    
        const {getAllByText} = render(   <NavigationContainer>
                                            <Hoje />
                                        </NavigationContainer> );
        
        expect(screen).toBeTruthy();
        

        // Encontre todos os botões com o texto "CONTINUAR"
        const buttons = getAllByText('CONTINUAR');

        // Certifique-se de que existe pelo menos um botão
        expect(buttons.length).toBeGreaterThan(0);

        await act(async () => {
            buttons.forEach((button) => {
                fireEvent.press(button);
            });
        });        
           
    });    

});