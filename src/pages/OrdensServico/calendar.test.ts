import RNCalendarEvents from 'react-native-calendar-events';
import { addEventCalendar, calendarPermission } from './calendar';


jest.mock('react-native-calendar-events');

describe('Funções de calendário', () => {
  it('deve adicionar um evento ao calendário', async () => {
    RNCalendarEvents.saveEvent = jest.fn(() => Promise.resolve('event_id_123'));
    const result = await addEventCalendar('Reunião', '2025-06-01T10:00:00', '2025-06-01T11:00:00', 'Escritório', 'Importante');
    expect(result).toBe(true);
    expect(RNCalendarEvents.saveEvent).toHaveBeenCalledTimes(1);
  });

 it('deve retornar false caso ocorra erro ao salvar evento', async () => {
 jest.spyOn(RNCalendarEvents, 'saveEvent').mockImplementationOnce(() => Promise.reject(new Error('Erro ao salvar evento')));
  
  const result = await addEventCalendar('Reunião', '2025-06-01T10:00:00', '2025-06-01T11:00:00', 'Escritório', 'Importante');
  
  expect(result).toBe(false); // Garantindo que a função retorna `false`
  expect(RNCalendarEvents.saveEvent).toHaveBeenCalledTimes(2); // Certificando que `saveEvent` foi chamado
});


  it('deve verificar e solicitar permissão ao calendário', async () => {
    RNCalendarEvents.checkPermissions = jest.fn(() => Promise.resolve('undetermined'));
    RNCalendarEvents.requestPermissions = jest.fn(() => Promise.resolve('authorized'));
    
    const result = await calendarPermission();
    expect(result).toBe(true);
    expect(RNCalendarEvents.checkPermissions).toHaveBeenCalled();
    expect(RNCalendarEvents.requestPermissions).toHaveBeenCalled();
  });

  it('deve retornar verdadeiro se a permissão já for autorizada', async () => {
    RNCalendarEvents.checkPermissions = jest.fn(() => Promise.resolve('authorized'));    
    const result = await calendarPermission();
    expect(result).toBe(true);
  });

  it('deve retornar falso se a permissão não for concedida', async () => {
    RNCalendarEvents.checkPermissions = jest.fn(() => Promise.resolve('denied'));     
    const result = await calendarPermission();
    expect(result).toBe(false);
  });
});