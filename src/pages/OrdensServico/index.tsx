import React, { useEffect, useState } from 'react';
import { Text, useWindowDimensions, View } from 'react-native';
import { SceneMap, TabBar, TabView } from 'react-native-tab-view';

// estilos para a page
import { styles } from '../OrdensServico/layout';

// telas de ordem de servico
import Amanha from '../OrdensServico/amanha';
import Futuras from '../OrdensServico/futuras';
import Hoje from '../OrdensServico/hoje';

// importa as ordens de serviço
import { ListaOrdensServicoAmanha, ListaOrdensServicoFuturas, ListaOrdensServicoHoje } from "../../data";

// dados armazenados localmente no app
import { KEY, setStorageJson } from '../../storages';

// componente
import Loading from '../../componentes/Loading';
import { useMessageBox } from "../../contexts/MessageBoxContext";

// funções do protheus
import { Protheus } from '../../services/protheus';

// funções do calendario
import { MockAgendaInstaladorJson } from '../../../__mocks__/AgendaJsonMock';
import { converterData, Data, getDataUTC } from '../../funcoes';
import { addEventCalendar, calendarPermission } from './calendar';

const HojeScreen = () => (
  <Hoje />
);

const AmanhaScreen = () => (
  <Amanha />
);

const FuturasScreen = () => (
  <Futuras />
);

const renderScene = SceneMap({
  hoje: HojeScreen,
  amanha: AmanhaScreen,
  futuras: FuturasScreen,
});

const OrdensServico = () => {

  const protheus = new Protheus();

  let agenda_hoje = []
  let agenda_amanha = []
  let agenda_futuras = []

  /* caixa de mensagens do tipo modal */
  const { showMessageBox } = useMessageBox();

  // salva as ordens de serviço localmente
  setStorageJson(KEY.ordensServicoHoje, ListaOrdensServicoHoje);
  setStorageJson(KEY.ordensServicoAmanha, ListaOrdensServicoAmanha);
  setStorageJson(KEY.ordensServicoFuturas, ListaOrdensServicoFuturas);

  /* dimensões da tela */
  const layout = useWindowDimensions();

  /* indice das abas de ordens de serviço */  
  const [index, setIndex] = React.useState(0);

  /* se deve ou não carregar o loading */
  const [loading, setLoading] = useState<boolean>(false);

  const [totalHoje, setTotalHoje] = useState<number>(0);
  const [totalAmanha, setTotalAmanha] = useState<number>(0);
  const [totalFuturas, setTotalFuturas] = useState<number>(0);

  /* rotas contendo as abas de OS's */
  const [routes] = React.useState([
    { key: 'hoje', title: 'Hoje' },
    { key: 'amanha', title: 'Amanhã' },
    { key: 'futuras', title: 'Futuras' },
  ]);

  const renderBadge = ({ route }: any) => {
    
    switch(route.key)
    {
      case 'hoje':
        return (
          <View style={{...styles.badge, marginRight: 20}}>
            <Text style={styles.countBadge}>{totalHoje}</Text>
          </View>
        );                
      case 'amanha':
      return (
        <View style={{...styles.badge, marginRight: 10,}}>
          <Text style={styles.countBadge}>{totalAmanha}</Text>
        </View>
        );
      case 'futuras':
      return (
        <View style={{...styles.badge, marginRight: 8}}>
          <Text style={styles.countBadge}>{totalFuturas}</Text>
        </View>
      );
    }
    
  };

  /**
   * monta os badges do tab bar
   * @param props 
   * @returns 
   */
  const renderTabBar = (props:any) => (
    <TabBar
      {...props}
      renderBadge={renderBadge}
      indicatorStyle={{ backgroundColor: 'white' }}
      style={{ backgroundColor: 'gray', marginTop:10, borderTopWidth:0 }}    
    />
  );

  /**
   * aunteticação no servidor protheus
   * @returns 
   */
  const autenticaProtheus = async () : Promise<boolean> => {
      
    try {

      /* autenticação no servidor Protheus */
      const autenticado = await protheus.autenticarProtheus();

      if (autenticado === 201) {          
        return true;
      } else {

        /* caixa de mensagens do tipo modal */
        showMessageBox({ title: 'Atenção', description: `Erro[${autenticado}]: Protheus.\nUsuário não autorizado`, textButton: 'OK', type: 'error'});
        return false;
      }
    } catch (error) {        

      /* caixa de mensagens do tipo modal */
      showMessageBox({title: 'Atenção', description: `Erro: Protheus.\nFalha na autenticação.`, textButton: 'OK', type: 'error'});
      return false;
    }

  };

  /**
   * 
   * @returns 
   */
  const getAgendaInstalador = async () : Promise<boolean> => {

    try {
    
      /* solicita a agenda do instalador */
      const agenda = await protheus.agendaInstaladoresProtheus();

      if (agenda === 200) {                  
        return true;
      } 
      else {

        /* caixa de mensagens do tipo modal */
        showMessageBox({ title: 'Atenção', description: `Erro[${agenda}]: Não existem agendamentos para esse instalador.`, textButton: 'OK', type: 'error'});
        return false;
      }
    } catch (error) {        

      /* caixa de mensagens do tipo modal */
        showMessageBox({title: 'Atenção', description: `Erro: Protheus.\nFalha ao solicitar agenda do instalador`, textButton: 'OK', type: 'error'});
        return false;
    }
  }

  /**
   * seta os agendamentos no calendario
   * @returns 
   */
  const setCalendar = async () => {

    /* data de hoje */
    let data_hoje = new Date; 

    /* data de amanha */
    let data_amanha = new Date;
    data_amanha.setDate(data_hoje.getDate() + 1);
      
    /* inicializa */
    agenda_hoje = [];
    agenda_amanha = [];
    agenda_futuras = [];

    try {

      // pega os agendamentos const agendamentos = await getStorageJson(KEY.agendaProtheus); //const json = JSON.parse(value);
      const json = MockAgendaInstaladorJson;

      /* se existe agenda para o instalador */
      if(json.Agendas.length <= 0) {

        setStorageJson(KEY.ordensServicoHoje, []);
        setStorageJson(KEY.ordensServicoAmanha, []);
        setStorageJson(KEY.ordensServicoFuturas, []);

        return;
      }

      /* percorre os agendamentos encontrados */
      json.Agendas.forEach((agenda) => {
      
        switch(converterData(agenda.DtInicio)) {
          case Data(data_hoje,'-'):
            agenda_hoje.push(agenda);
          break;
          case Data(data_amanha,'-'):
            agenda_amanha.push(agenda);
          break;
          default:
            agenda_futuras.push(agenda);
          break;  
        };

        /* adiciona o evento ao calendario do celular */
        addEventCalendar(agenda.CodOcor + ' - ' + agenda.DescOcor, 
                         getDataUTC(agenda.DtInicio, agenda.HrInicio),
                         getDataUTC(agenda.DtFim, agenda.HrFim),
                         agenda.NomeSC + ' - ' + agenda.Endereco,
                         agenda.DescPro);
      });

      /* seta o total de itens agendados por periodo */
      setTotalHoje(agenda_hoje.length);
      setTotalAmanha(agenda_amanha.length);
      setTotalFuturas(agenda_futuras.length);


    } catch (error) {

        setStorageJson(KEY.ordensServicoHoje, []);
        setStorageJson(KEY.ordensServicoAmanha, []);
        setStorageJson(KEY.ordensServicoFuturas, []);
    }
    
  }

  /**
   * seta os dados da agenda do instalador na agenda do celular 
   * @returns
   */
  const setAgenda = async () => {
    
    /* pega os dados da agenda do instalador */
    if(!await getAgendaInstalador())
      return;

    /* verifica a permissão de acesso a agenda */
    if(!await calendarPermission()) {
       showMessageBox({ title: 'Atenção', description: `Usuário não permitiu acesso a agenda do celular.`, textButton: 'OK', type: 'error'});
       return;
    }
      
    /* seta os agendamentos no calendario */
    setCalendar();
    
  }

  useEffect(() => {
      
    /* inicia o loading */
    setLoading(true);
    
    setTimeout(async function() {

      /* autenticao no servidor Protheus */
      if(await autenticaProtheus()) {
        setAgenda();
      }
        
      /* encerra o loading */
      setLoading(false);
      
    }, 1000);
        
  }, []);

  return (
    
    <>
    
      <Loading animating={loading} text={"Verificando OS's disponíveis"} />


      <TabView style={{flex:1, marginTop: 0}}      
        renderTabBar={renderTabBar}
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        initialLayout={{ width: layout.width}}
        testID='tabview-container'   
      />      
    </>


    
  );
};

export default OrdensServico;
