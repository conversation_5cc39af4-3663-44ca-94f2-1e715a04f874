import { StyleSheet, Dimensions } from "react-native";

const screenHeight = Dimensions.get('window').height;
const screenWidth = Dimensions.get('window').width;

export const styles = StyleSheet.create({

    containerTela: {
        flex:1,
        alignItems: "center",
        justifyContent:'center',
        backgroundColor:'#D6D6D6',        
    },   
    
    containerOS:{
        padding: 20,
        height:425, 
        width: '100%', 
        borderWidth:1, 
        borderColor:'#2E8C1F', 
        borderRadius:10, 
        backgroundColor:'#FFFFFF',
        gap:8,
    },

    textoData:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,        
        color:'#2E8C1F'
    },

    textoFiltrar:{
        fontFamily: 'Exo2_400Regular',
        fontSize: 17,        
        color:'#FFFFFF',
    },

    textoNomeCliente:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 18,        
        color:'#2E8C1F',        
    },

    textoDestaque:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 16,        
        color:'#525252',        
    },

    textoSimples:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 16,        
        color:'#525252',        
    },

    textoVerDetalhes:{
        fontFamily: 'Exo2_400Regular',
        fontSize: 16,        
        color:'#525252',        
    },

    textoTracarRota:{
        fontFamily: 'Exo2_400Regular',
        fontSize: 16,        
        color:'#A3A3A3',        
    },

    textoContinuar:{
        fontFamily: 'Exo2_400Regular',
        fontSize: 16,        
    },

    textoIniciar:{
        fontFamily: 'Exo2_400Regular',
        fontSize: 16,        
        color:'#FFFFFF',
    },

    textoStatusOS:{
        height:35, 
        width:100, 
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 14,
        color:'#FFFFFF', 
        borderRadius:5, 
        textAlign:'center',
        textAlignVertical:'center'
    },

    textoBottomSheet:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 17,        
        color:'#2E8C1F',
    },

    buttonFiltrar:{         
        height:45, 
        flexDirection:'row', 
        justifyContent:'center', 
        alignItems:'center', 
        backgroundColor:'#2E8C1F', 
        borderRadius:5, 
        gap:10
    },

    buttonVerDetalhes:{
        height: 55, 
        borderWidth:1, 
        borderColor:'#A3A3A3', 
        borderRadius:8, 
        alignItems:'center', 
        justifyContent:'center',
    },

    buttonTracarRota:{
        height: 55,
        width: '47%',
        borderWidth:1, 
        borderColor:'#A3A3A3', 
        borderRadius:8, 
        alignItems:'center', 
        justifyContent:'center',
    },

    buttonContinuar:{
        height: 55, 
        width: '47%',
        backgroundColor: '#2E8C1F', 
        borderWidth:1, 
        borderColor:'#2E8C1F', 
        borderRadius:8, 
        alignItems:'center', 
        justifyContent:'center',
    },

    buttonIniciar:{
        height: 55, 
        width: '47%',
        backgroundColor: '#A3A3A3',
        borderWidth:1, 
        borderColor:'#A3A3A3', 
        borderRadius:8, 
        alignItems:'center', 
        justifyContent:'center',
    },

    buttonBottomSheet:{
        justifyContent:'space-between',
        height: 65,
        flexDirection:'row',
        alignItems:'center',
        paddingVertical: 10,
        marginStart: 30,
        marginEnd: 30
    },

    linhaHorizontal:{
        borderBottomColor: '#E5E5E5',
        borderBottomWidth: StyleSheet.hairlineWidth,        
    },

    badge: {
        marginTop: 12,
        backgroundColor: '#f44336',
        height: 24,
        width: 24,
        borderRadius: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },

    countBadge: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        marginTop: -2,
      },    

})