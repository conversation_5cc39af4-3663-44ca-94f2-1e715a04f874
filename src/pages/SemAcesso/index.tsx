import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {View, Text, TouchableOpacity} from 'react-native';

// icones
import IconUserX from '../../assets/svg/icon_user-x.svg'

// Style
import { styles } from './layout';

// rotas
import { StackTypes } from '../../routes';


export function SemAcesso() {

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
  
    return (
        <View style={styles.containerTela}>
            <IconUserX color={"#A3A3A3"} width={60} height={60} />
            <Text style={styles.texto}>{`Você não possui acesso a esse produto.\nPor favor, entre em contato com nosso suporte.`}</Text> 
            <TouchableOpacity style={styles.button}
                              onPress={ () => {navigation.navigate('Logout') }}
                              testID='button-logout'>
                                <Text style={styles.textoButton}>Tentar novamente</Text>
            </TouchableOpacity>
        </View>
    );
}
