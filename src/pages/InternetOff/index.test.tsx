import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { fireEvent, render, screen, act } from "@testing-library/react-native";
import { InternetOff } from ".";

jest.mock('@react-navigation/native', () => {
    return {
      ...jest.requireActual('@react-navigation/native'),
      useNavigation: () => ({
        navigate: jest.fn(), // Simulando o comportamento de navigate
      }),
    };
});

describe('Page InternetOff', () => {

    it('renderiza corretamente a tela', () => {
    
        const {} = render(  <NavigationContainer>
                                        <InternetOff />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();
    });

    it('renderiza corretamente a tela ao clicar no botão logout', async () => {
    
        const {getByTestId} = render(  <NavigationContainer>
                                        <InternetOff />
                                    </NavigationContainer> );
        
        expect(screen).toBeTruthy();

        await act(async () => {         
            fireEvent.press(getByTestId('button-splash'));
        });        
    });    
});