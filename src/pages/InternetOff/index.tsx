import React from 'react';
import {useNavigation} from '@react-navigation/native';
import {View, Text, TouchableOpacity} from 'react-native';

// logo Zordon
import IconAlert from '../../assets/svg/icon_alert-circle.svg'

// Style
import { styles } from './layout';

// rotas
import { StackTypes } from '../../routes';


export function InternetOff() {

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();
  
    return (
        <View style={styles.containerTela}>
            <IconAlert color={"#A3A3A3"} width={60} height={60} />
            <Text style={styles.texto}>{`Por favor verifique sua conexão com a internet e tente novamente.`}</Text> 
            <TouchableOpacity style={styles.button}
                              onPress={ () => {navigation.navigate('Splash') }}
                             testID='button-splash'>
                                <Text style={styles.textoButton}>Tentar novamente</Text>
            </TouchableOpacity>
        </View>
    );
}
