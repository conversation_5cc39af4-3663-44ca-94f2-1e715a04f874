import { StyleSheet } from "react-native";

export const styles = StyleSheet.create({

    containerTela: {
        flex:1,
        alignItems:'center',
        justifyContent: 'center',
        gap:70,
    },
    
    containerSubTela: {        
        alignItems:'center',
        justifyContent: 'center',
        gap:20,
    },

    containerTexto: {
        paddingHorizontal: 50,
        alignItems:'center',
        justifyContent: 'center',
        gap:20,
    },

    containerSlider: {   
        paddingHorizontal: 10,     
        alignItems:'center',
        justifyContent: 'center',
    },

    textoDescricao:{
        textAlign: 'center',
        color: '#35A55B',
        paddingHorizontal: 25,
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 17,
    },

    textoLembrete:{
        textAlign: 'center',
        color: '#525252',
        paddingHorizontal: 25,
        fontFamily: 'SourceSans3_400Regular_Italic',
        fontSize: 17,
    },

    imageIniciarJornada:{
        resizeMode: 'center',
        height:'70%',
        width: '100%',
        alignSelf: 'center',
    },

    saveIcon: {
        width: 24,
        height: 24,
        tintColor: '#00E096',
    },
    
    saveButton: {
       backgroundColor: '#00E096',
    },

    saveButtonUnderlay: {
        backgroundColor: '#51F0B0',
    },

    saveThumb: {
        width: 50,
        height: 60,
    },    

})