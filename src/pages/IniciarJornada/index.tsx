import React, { useState } from 'react';
import {View, Text, Image} from 'react-native';
import { useNavigation } from '@react-navigation/native'

// componente
import SlideButton from 'rn-slide-button';

// estilos da pagina
import {styles} from '../IniciarJornada/layout';

// rotas de drawer navegação
import {DrawerTypes} from '../MenuPrincipal/index'

const IniciarJornada = () => {

  // navegação entre telas
  const navigation = useNavigation<DrawerTypes>();
  const [toggle, setToggle] = useState(false);

  return (

    <View style={{ flex: 1 }}>
      
      <View style = {styles.containerTela}>

        <View style = {styles.containerSubTela}>

          <Image
            source={require('../../assets/png/iniciar-jornada.png')}
          />

          <View style = {styles.containerTexto}>

            <Text style={styles.textoDescricao}>
              INICIE SUA JORNADA DE TRABALHO E VEJA TODA LISTA DE TAREFAS
            </Text>
 
            <Text style={styles.textoLembrete}>
              Lembre-se de encerrar sua jornada no final do dia
            </Text>

          </View>

        </View>

        <SlideButton
          title="Deslise para iniciar"
          icon={<Image style={styles.saveIcon}source={require('../../assets/png/arrow-right.png')} />}
          containerStyle={styles.saveButton}
          underlayStyle={styles.saveButtonUnderlay}
          thumbStyle={styles.saveThumb}
          height={52}
          width={370}
          borderRadius={12}
          reverseSlideEnabled={true}
          padding={0}          
          onReachedToEnd={() => navigation.navigate('OrdensServico')}
          autoReset={true}
          testID="slide-button"
        />

      </View>

    </View>


  );
};

export default IniciarJornada;
