import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import IniciarJornada from '.';

// Mock de navegação
const mockNavigate = jest.fn();

// Mock de navegação
jest.mock('@react-navigation/native', () => {
  return {
    ...jest.requireActual('@react-navigation/native'),
    useNavigation: () => ({
      navigate: mockNavigate,      
    }),
  };
});


// Mock do SlideButton para evitar problemas com dependências nativas
jest.mock('rn-slide-button', () => {
  return {
    __esModule: true,
    default: (props) => (
      <button
        {...props}
        onClick={props.onReachedToEnd}
      >
        {props.title}
      </button>
    ),
  };
});

// Mock das imagens
jest.mock('../../assets/png/iniciar-jornada.png', () => 'mocked-image.png');
jest.mock('../../assets/png/arrow-right.png', () => 'mocked-arrow.png');

// Teste do componente
describe('IniciarJornada', () => {
  it('deve renderizar todos os elementos corretamente', () => {
    const { getByText, getByTestId } = render(
        <IniciarJornada />
    );

    // Verificar se o texto aparece na tela
    expect(getByText('INICIE SUA JORNADA DE TRABALHO E VEJA TODA LISTA DE TAREFAS')).toBeTruthy();
    expect(getByText('Lembre-se de encerrar sua jornada no final do dia')).toBeTruthy();

    // Verificar se o slide button está presente
    const slideButton = getByTestId('slide-button');
    expect(slideButton).toBeTruthy();
  });

  it('deve navegar para "OrdensServico" quando o slide button for deslizado até o final', () => {
    const { getByTestId } = render(
        <IniciarJornada />
    );

    // Mock da navegação
    const { navigate } = require('@react-navigation/native').useNavigation();

    // Obter o SlideButton pelo testID
    const slideButton = getByTestId('slide-button');

    // Simular o deslizar
    fireEvent(slideButton, 'onReachedToEnd');

    // Verificar se a navegação foi chamada
    expect(mockNavigate).toHaveBeenCalledWith('OrdensServico');
  });
});
