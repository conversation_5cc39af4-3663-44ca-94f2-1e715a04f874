import * as React from 'react';
import { ImageBackground, TouchableOpacity, View } from 'react-native';

import { createDrawerNavigator, DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator, NativeStackNavigationProp } from '@react-navigation/native-stack';


// imagens svg
import IconMenu from '../../assets/svg/icon_menu-03.svg';
import TextoZordon from '../../assets/svg/logo_texto_zordon.svg';

// page drawer customizada
import CustomDrawer from "../../menu";

// drawer pages
import IniciarJornada from '../IniciarJornada/index';
import Materiais from '../MateriaisCadastrados/index';
import Medidores from '../Medidores/index';
import OrdensServico from '../OrdensServico/index';

// stack pages
import DetalhesOS from '../OrdemServico/detalhes_os';
import DetalhesOSAgendada from '../OrdemServico/detalhes_os_agendada';
import OrdemServicoInstalacaoFro1 from '../OrdemServico/os_instalacao_fro1';
import OrdemServicoManutencao from '../OrdemServico/os_manutencao';
import OrdemServicoRetiradaFro3 from '../OrdemServico/os_retirada_fro3';
import OrdemServicoTrocaFro2 from '../OrdemServico/os_troca_fro2';
import OrdemServicoVisita from '../OrdemServico/os_visita';
import PassoAssinatura from '../OrdemServico/passo_assinatura';
import PassoConexao from '../OrdemServico/passo_conexao';
import PassoFoto from '../OrdemServico/passo_foto';
import PassoFotoInstalacao from '../OrdemServico/passo_foto_instalacao';
import PassoFotoLocal from '../OrdemServico/passo_foto_local';
import PassoFotoRetirada from '../OrdemServico/passo_foto_retirada';
import PassoID from '../OrdemServico/passo_id';
import PassoMaterial from '../OrdemServico/passo_material';
import PassoMedidor from '../OrdemServico/passo_medidor';
import PassoObservacoes from '../OrdemServico/passo_observacoes';
import PassoTesteConexao from '../OrdemServico/passo_teste_conexao';
import ResumoOSInstalacaoFro1 from '../OrdemServico/resumo_os_instalacao_fro1';
import ResumoOSManutencao from '../OrdemServico/resumo_os_manutencao';
import ResumoOSRetiradaFro3 from '../OrdemServico/resumo_os_retirada_fro3';
import ResumoOSTrocaFro2 from '../OrdemServico/resumo_os_troca_fro2';
import ResumoOSVisita from '../OrdemServico/resumo_os_visita';

// dados
import { EtapasInstalacao } from '../../data';

// rotas de navegação drawer navigation
type DrawerNavigation = {    
  OrdensServico: undefined;
  IniciarJornada: undefined;  
  Medidores: undefined;
  Materiais: undefined;
};

// rotas de navegação stack navigation
type StackNavigation = {  
  OrdemServicoInstalacaoFro1: {etapas: EtapasInstalacao};
  OrdemServicoTrocaFro2: {etapas: EtapasInstalacao};
  OrdemServicoManutencao: {etapas: EtapasInstalacao};
  OrdemServicoVisita: {etapas: EtapasInstalacao};  
  OrdemServicoRetiradaFro3: {etapas: EtapasInstalacao};
  PassoID: {etapas: EtapasInstalacao};
  PassoConexao: {etapas: EtapasInstalacao};
  PassoMedidor: {etapas: EtapasInstalacao};
  PassoFotoLocal: {etapas: EtapasInstalacao};
  PassoFoto: {etapas: EtapasInstalacao, imagem: string, screen: string};
  PassoTesteConexao: {etapas: EtapasInstalacao};
  PassoMaterial: {etapas: EtapasInstalacao};
  PassoFotoInstalacao: {etapas: EtapasInstalacao};
  PassoFotoRetirada: {etapas: EtapasInstalacao};
  ResumoOSInstalacaoFro1: {etapas: EtapasInstalacao};
  ResumoOSTrocaFro2: {etapas: EtapasInstalacao};
  ResumoOSRetiradaFro3: {etapas: EtapasInstalacao};
  ResumoOSManutencao: {etapas: EtapasInstalacao};
  ResumoOSVisita: {etapas: EtapasInstalacao};
  PassoAssinatura: {etapas: EtapasInstalacao};
  PassoObservacoes: {etapas: EtapasInstalacao};
  DetalhesOS: {etapas: EtapasInstalacao};
  DetalhesOSAgendada: {etapas: EtapasInstalacao};
};

// exporta as rotas de navegação
export type DrawerTypes = DrawerNavigationProp<DrawerNavigation>;

// exporta as rotas de navegação
export type StackTypes = NativeStackNavigationProp<StackNavigation>;


// cria um background customizado para o header drawer navigation
export function CustomHeaderBackground() {
  return (
    <ImageBackground
        source={require("../../assets/png/app-bar-bg.png")}
        style={{height: '106%', width: '100%', justifyContent: 'center', flex:1, position:'absolute'}}
        testID="custom-header-background"
    />
    );
}

// cria um titulo customizado para o header drawer navigation
export function CustomHeaderTitle() {
  return (
    <View style={{alignItems:'center'}} testID="custom-header-title">
      <TextoZordon width={110}/>
    </View>      
  );
}

// cria um menu buttom customizado para o header navigation
export function CustomHeaderMenuButton() {

  const navigation = useNavigation<DrawerTypes>();

  return (
    <TouchableOpacity
      style={{marginStart:20}}
      onPress={() => {navigation.openDrawer(); }}
      testID="custom-header-menu-button">
      <IconMenu color={"#FFFFFF"} width={25} height={25}/>
    </TouchableOpacity>
  );
}

export function DrawerContent(props:any) {
  return <CustomDrawer {...props} />;
}

export function HeaderBackground(props:any) {
  return <CustomHeaderBackground {...props} />;
}

export function HeaderTitle(props:any) {
  return <CustomHeaderTitle {...props} />;
}

export function HeaderLeft(props:any) {
  return <CustomHeaderMenuButton {...props} />;
}

export default function MenuPrincipal() {

  const Drawer = createDrawerNavigator();
  const Stack = createNativeStackNavigator();
  
  return (
    
      <Drawer.Navigator drawerContent={DrawerContent}
                        initialRouteName="IniciarJornada" 
                        screenOptions={{ drawerStyle: { width: '100%', zIndex:0},
                                         headerBackground: HeaderBackground,
                                         headerTitle: HeaderTitle, 
                                         headerTitleAlign:"center",
                                         headerLeft: HeaderLeft,
                                       }}>        
        <Drawer.Screen name="OrdensServico" component={OrdensServico} options={{ headerShown:true }} />
        <Drawer.Screen name="IniciarJornada" component={IniciarJornada} options={{ headerShown: true }}/>        
        <Drawer.Screen name="Medidores" component={Medidores} options={{ headerShown:false }}/>
        <Drawer.Screen name="Materiais" component={Materiais} options={{ headerShown:false }}/>
        
        <Stack.Screen  name="OrdemServicoInstalacaoFro1" component={OrdemServicoInstalacaoFro1} options={{headerShown: false}} />
        <Stack.Screen  name="OrdemServicoTrocaFro2" component={OrdemServicoTrocaFro2} options={{headerShown: false}} />
        <Stack.Screen  name="OrdemServicoManutencao" component={OrdemServicoManutencao} options={{headerShown: false}} />
        <Stack.Screen  name="OrdemServicoVisita" component={OrdemServicoVisita} options={{headerShown: false}} />
        <Stack.Screen  name="OrdemServicoRetiradaFro3" component={OrdemServicoRetiradaFro3} options={{headerShown: false}} />
        <Stack.Screen  name="PassoID" component={PassoID} options={{headerShown: false}} />
        <Stack.Screen  name="PassoConexao" component={PassoConexao} options={{headerShown: false}} />
        <Stack.Screen  name="PassoMedidor" component={PassoMedidor} options={{headerShown: false}} />
        <Stack.Screen  name="PassoTesteConexao" component={PassoTesteConexao} options={{headerShown: false}} />
        <Stack.Screen  name="PassoFotoLocal" component={PassoFotoLocal} options={{headerShown: false}} />
        <Stack.Screen  name="PassoFoto" component={PassoFoto} options={{headerShown: false}} />
        <Stack.Screen  name="PassoMaterial" component={PassoMaterial} options={{headerShown: false}} />        
        <Stack.Screen  name="PassoFotoInstalacao" component={PassoFotoInstalacao} options={{headerShown: false}} />
        <Stack.Screen  name="PassoFotoRetirada" component={PassoFotoRetirada} options={{headerShown: false}} />
        <Stack.Screen  name="ResumoOSInstalacaoFro1" component={ResumoOSInstalacaoFro1} options={{headerShown: false}} />
        <Stack.Screen  name="ResumoOSTrocaFro2" component={ResumoOSTrocaFro2} options={{headerShown: false}} />
        <Stack.Screen  name="ResumoOSManutencao" component={ResumoOSManutencao} options={{headerShown: false}} />
        <Stack.Screen  name="ResumoOSRetiradaFro3" component={ResumoOSRetiradaFro3} options={{headerShown: false}} />
        <Stack.Screen  name="ResumoOSVisita" component={ResumoOSVisita} options={{headerShown: false}} />
        <Stack.Screen  name="PassoObservacoes" component={PassoObservacoes} options={{headerShown: false}} />
        <Stack.Screen  name="PassoAssinatura" component={PassoAssinatura} options={{headerShown: false}} />
        <Stack.Screen  name="DetalhesOS" component={DetalhesOS} options={{headerShown: false}} />
        <Stack.Screen  name="DetalhesOSAgendada" component={DetalhesOSAgendada} options={{headerShown: false}} />

      </Drawer.Navigator> 

    
  );
}