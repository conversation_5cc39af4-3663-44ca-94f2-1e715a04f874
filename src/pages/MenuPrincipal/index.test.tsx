import { NavigationContainer } from '@react-navigation/native';
import { fireEvent, render, screen } from '@testing-library/react-native';
import React from 'react';
import MenuPrincipal, { CustomHeaderBackground, CustomHeaderMenuButton, CustomHeaderTitle, DrawerContent, HeaderBackground, HeaderLeft, HeaderTitle } from '.';

jest.mock('@shopify/react-native-skia', () => 'Canvas');
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');
jest.mock('sp-react-native-mqtt', () => 'MQTT');
jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');
jest.mock('expo-location', () => 'LocationObject');
jest.mock('rn-slide-button', () => 'SlideButton');

jest.mock('@react-navigation/drawer', () => {
  return {
    createDrawerNavigator: jest.fn().mockReturnValue({
      Navigator: ({ children }:any) => <>{children}</>,
      Screen: () => null,
    }),
  };
});

jest.mock('@react-navigation/native-stack', () => {
  return {
    createNativeStackNavigator: jest.fn().mockReturnValue({
      Navigator: ({ children }:any) => <>{children}</>,
      Screen: () => null,
    }),
  };
});

// Mock correto do useNavigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    openDrawer: jest.fn(),
  }),
  NavigationContainer: ({ children }: any) => children,
}));

// Mock do CustomDrawer
jest.mock('../../menu', () => jest.fn(() => <></>));

// mock all the screen componentes
const mockScreenComponent = () => <></>
jest.mock('../IniciarJornada/index', () => mockScreenComponent);
jest.mock('../MateriaisCadastrados/index', () => mockScreenComponent);
jest.mock('../Medidores/index', () => mockScreenComponent);
jest.mock('../OrdensServico/index', () => mockScreenComponent);
jest.mock('../OrdemServico/detalhes_os', () => mockScreenComponent);
jest.mock('../OrdemServico/detalhes_os_agendada', () => mockScreenComponent);
jest.mock('../OrdemServico/os_instalacao_fro1', () => mockScreenComponent);
jest.mock('../OrdemServico/os_manutencao', () => mockScreenComponent);
jest.mock('../OrdemServico/os_retirada_fro3', () => mockScreenComponent);
jest.mock('../OrdemServico/os_troca_fro2', () => mockScreenComponent);
jest.mock('../OrdemServico/os_visita', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_assinatura', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_conexao', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_foto', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_foto_instalacao', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_foto_local', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_foto_retirada', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_id', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_material', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_medidor', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_observacoes', () => mockScreenComponent);
jest.mock('../OrdemServico/passo_teste_conexao', () => mockScreenComponent);
jest.mock('../OrdemServico/resumo_os_instalacao_fro1', () => mockScreenComponent);
jest.mock('../OrdemServico/resumo_os_manutencao', () => mockScreenComponent);
jest.mock('../OrdemServico/resumo_os_retirada_fro3', () => mockScreenComponent);
jest.mock('../OrdemServico/resumo_os_troca_fro2', () => mockScreenComponent);
jest.mock('../OrdemServico/resumo_os_visita', () => mockScreenComponent);


describe('Componentes de Header', () => {
  test('Renderiza CustomHeaderBackground corretamente', () => {
    const { getByTestId } = render(<CustomHeaderBackground />);
    expect(getByTestId('custom-header-background')).toBeTruthy();
  });

  test('Renderiza CustomHeaderTitle corretamente', () => {
    const { getByTestId } = render(<CustomHeaderTitle />);
    expect(getByTestId('custom-header-title')).toBeTruthy();
  });  

  test('Renderiza CustomHeaderMenuButton e dispara a navegação', () => {
    const mockNavigate = jest.fn();
    jest.spyOn(require('@react-navigation/native'), 'useNavigation').mockReturnValue({
      openDrawer: mockNavigate,
    });

    const { getByTestId } = render(<CustomHeaderMenuButton />);
    fireEvent.press(getByTestId('custom-header-menu-button'));
    expect(mockNavigate).toHaveBeenCalled();
  });

  test('Renderiza DrawerContent corretamente', () => {
    const {} = render(<DrawerContent />);
    expect(screen).toBeTruthy();
  });
  
  test('Renderiza HeaderBackground corretamente', () => {
    const {} = render(<HeaderBackground />);
    expect(screen).toBeTruthy();
  });
  
  test('Renderiza HeaderTitle corretamente', () => {
    const {} = render(<HeaderTitle />);
    expect(screen).toBeTruthy();
  });
  
  test('Renderiza HeaderLeft corretamente', () => {
    const {} = render(<HeaderLeft />);
    expect(screen).toBeTruthy();
  });  

});

describe('MenuPrincipal', () => {
  test('Renderiza corretamente', async () => {

    const {} = render( <NavigationContainer>
                          <MenuPrincipal />
                        </NavigationContainer>
    );
    
    expect(screen).toBeTruthy();           
  });  
});
