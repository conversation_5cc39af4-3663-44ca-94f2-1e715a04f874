import {View, Text, TouchableOpacity, FlatList} from 'react-native';
import React, { useEffect, useState } from 'react';
import { CommonActions, useNavigation } from '@react-navigation/native'

// estilos da pagina
import {styles} from './layout';

// imagens vetoriais
import IconVoltar from '../../assets/svg/icon_arrow-left.svg'
import IconAlertCircle from '../../assets/svg/icon_alert-circle.svg'

// rotas drawer de navegação
import {StackTypes} from '../MenuPrincipal/index'

// componentes
import CardMedidor from '../../componentes/CardMedidor';
import Loading from '../../componentes/Loading';
import Filter from '../../componentes/Filter';

// dados armazenados no app
import { KEY, setStorageJson } from '../../storages';

// Definindo a interface para a tipagem
interface Medidor {
    IdMedidor: string;
    DescMed: string;
    VlrKE: string;
}

const MateriaisCadastrados = () => {
  
  // navegação entre telas
  const navigation = useNavigation<StackTypes>();
      
  // lista de materiais
  const[listaMateriais, setListaMateriais] = useState<Medidor[]>([]);

  // texto de pesquisa
  const[pesquisar, setPesquisar] = useState<string>('');

  // indica que o loading não esta visivel
  const [loading, setLoading] = useState<boolean>(false);

  // texto material não encontrado
  const[texto, setTexto] = useState<string>('');

  // atualiza lista de materiais cadastrados
  const AtualizarListaMateriais = async () => {

    // atualiza texto
    setTexto('');

  

    // inicia loading
    setLoading(true);

    const mockMateriais = [{IdMedidor: '0', DescMed:'Medidor 00', VlrKE: '1.0', }]

    // inicializa lista de materais
    setListaMateriais(mockMateriais);

    setStorageJson(KEY.materiaisProtheus, JSON.stringify(mockMateriais));

    // encerra o loading
    setLoading(false);
  }
  
  // executa a atualização da lista de materiais ao abrir a pagina
  useEffect( () => {

    if(listaMateriais.length <= 0) 
    {
        AtualizarListaMateriais()
    }

  }, []);

  // navegar de volta a tela de ordens de serviço
  const goBack = () => {
           
    // fecha a pagina atual retornando para pagina de ordens de serviço
    navigation.dispatch(
        CommonActions.reset({
          index: 0,
          routes:[
            { name: 'OrdensServico' },
          ],
        })
      );
  }; 

  return (
      
    <>
      <Loading animating={loading} text='Atualizando lista de Materiais...'/>

      <View style = {styles.containerTela}>
    
        {/* header */}
        <View style={styles.containerHeader}>
  
            {/* botão voltar */}
            <View style={styles.containerBotaoVoltar}>
              <TouchableOpacity onPress={() => goBack()} testID='back-button'>                
                <IconVoltar color={"#2E8C1F"} width={30} height={30}/>
              </TouchableOpacity>    
            </View>
  
            {/* texto cabeçalho */}
            <View style={styles.containerTextoHeader}>
              <Text style={styles.textoHeader}>MATERIAIS CADASTRADOS</Text>
            </View>
  
        </View>

        {/* content */}
        <View style = {styles.containerConteudo}>

          {
            (listaMateriais.length <= 0)
            ?
              <View style = {{width: '100%', height: '100%', justifyContent:'center', alignItems:'center', gap: 10}}>
                <View style={{height:50, width:50, justifyContent:'center', alignItems:'center', borderRadius:9999, backgroundColor:'#FFE6AA'}}>
                  <IconAlertCircle stroke={"#D49600"} width={24} height={24}/>
                </View>                
                <Text style ={styles.textoNenhumMaterial}>{texto}</Text>
              </View>
            :
              <View style = {{width: '100%', height: '100%', gap: 20}}>

                <Filter value={pesquisar} placeHolder='Pesquisar' onValueChange= {setPesquisar}/>
                    
                <FlatList 
                  ItemSeparatorComponent={() => <Text> </Text>}              
                  data = {(pesquisar === '') ? listaMateriais : listaMateriais.filter(medidor => medidor.DescMed.includes(pesquisar.toUpperCase()) || medidor.VlrKE.includes(pesquisar))}
                  renderItem={ ({ item }) => <CardMedidor
                                                value = {item.DescMed}
                                                valueKE = {item.VlrKE}
                                                valueID = {item.IdMedidor}
                                              />
                              }
                />

              </View>
          }

        </View>        

        {/* footer */}
        <View style={styles.containerFooter}>
  
          <TouchableOpacity style={styles.buttonAtualizar} onPress={() => AtualizarListaMateriais()} testID='update-button'>
            <Text style = {styles.textoButtonAtualizar}>ATUALIZAR</Text>
          </TouchableOpacity>                        

        </View>            

      </View>


    </>

    
  );
  
};
  
export default MateriaisCadastrados;