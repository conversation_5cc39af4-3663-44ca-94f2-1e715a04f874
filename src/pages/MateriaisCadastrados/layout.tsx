import { StyleSheet, Dimensions } from "react-native";

export const screenHeight = Dimensions.get('window').height;
export const screenWidth = Dimensions.get('window').width;

// 20% da largura da tela
const width20 = screenWidth * 0.2;

// 80% da largura da tela
const width80 = screenWidth * 0.8;

// area header
export const heightHeader = 80;

// area footer
export const heightFooter = 60;

// area do conteudo 
export const heightContent = screenHeight - ( heightHeader + heightFooter);

// altura padrão dos botões
export const heightButton = 60;

export const styles = StyleSheet.create({

    containerTela: {        
        flex:1,
        height: screenHeight,
        backgroundColor: '#FFFFFF'
    },

    containerHeader: {
        width:screenWidth,
        height: heightHeader,
        flexDirection:'row',        
        alignItems:'flex-end'
    },  
    
    containerBotaoVoltar:{
        width: width20, 
        justifyContent:'center',
        alignItems:'center'
    },

    containerTextoHeader:{
        width:'100%',
        marginStart:- width20,
        justifyContent:'center',
        alignItems:'center',        
    },

    containerConteudo:{
        width: screenWidth, 
        height: heightContent,          
        paddingHorizontal: 30, 
        paddingVertical: 20,
        gap: 20,
        justifyContent:'space-between',                
    },     

    containerFooter: {
        width:screenWidth,
        height: heightFooter,
        paddingHorizontal: 20,               
        justifyContent:'center',
        alignItems:'center',        
    },

    textoHeader:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        color:'#2E8C1F', 
    },

    textoButtonAtualizar:{
        fontFamily: 'Exo2_400Regular',
        fontSize:20,
        color:'#FFFFFF',        
    }, 
    
    textoNenhumMaterial:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize:20,
        color:'#D6D6D6',
    },

    buttonAtualizar:{        
        height:heightButton,
        width: '100%' , 
        backgroundColor:'#2E8C1F',
        justifyContent:'center',
        alignItems:'center',
        borderRadius:8        
    },    
});