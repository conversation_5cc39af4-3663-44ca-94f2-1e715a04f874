
import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import Filter from '../../componentes/Filter';
import Loading from '../../componentes/Loading';
import CardMedidor from '../../componentes/CardMedidor';
import MessageBoxAtencao from '../../modal/messagebox/Atencao';
import MessageBoxErro from '../../modal/messagebox/Erro';
import MateriaisCadastrados from '.';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearStorage, deleteStorage, getStorageBoolean, getStorageJson, getStorageString, KEY, setStorageBoolean, setStorageJson, setStorageString } from '../../storages';

jest.mock('@react-navigation/native', () => {
  const actualNav = jest.requireActual('@react-navigation/native');
  return {
    ...actualNav,
    useNavigation: () => ({
      navigate: jest.fn(),
      dispatch: jest.fn(),
      goback: jest.fn(),
    }),
  };
});


jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  clear: jest.fn(),
}));

describe('MateriaisCadastrados', () => {
    it('deve renderizar corretamente', () => {
        const { getByText } = render(<MateriaisCadastrados />);
        expect(getByText('MATERIAIS CADASTRADOS')).toBeTruthy();
    });

    it('deve exibir mensagem de erro quando atualiza materiais', async () => {
        const { getByTestId } = render(<MateriaisCadastrados />);
    
        // Simula a chamada para atualizar a lista de materiais
        fireEvent.press(getByTestId('update-button'));
    });

    it('deve exibir mensagem de erro quando atualiza materiais e volta a pagina', async () => {
      const { getByTestId } = render(<MateriaisCadastrados />);
  
      // Simula a chamada para atualizar a lista de materiais
      fireEvent.press(getByTestId('back-button'));
  });    

    it('deve renderizar o Filter corretamento com as propriedades default', () => {
        const { getByPlaceholderText } = render(<Filter />);
        expect(getByPlaceholderText('')).toBeTruthy();
    });

    it('deve renderizar o Filter corretamento quando existe um value passado pelo usuário', () => {
        const { getByPlaceholderText } = render(<Filter value ={'teste'}/>);
        expect(getByPlaceholderText('')).toBeTruthy();
    });

    it('deve chamar o onValueChange do Filter quando o text input muda', () => {
        const onValueChangeMock = jest.fn();
        const { getByPlaceholderText } = render(
            <Filter placeHolder="Search" onValueChange={onValueChangeMock} />
        );
        const input = getByPlaceholderText('Search');
    
        fireEvent.changeText(input, 'test');
        expect(onValueChangeMock).toHaveBeenCalledWith('test');        
    });

     it('deve renderizar corretamente os icones do Filter', () => {
        const { getByTestId, rerender } = render(<Filter icon="pesquisa" />);
        expect(getByTestId('icon-pesquisa')).toBeTruthy();

        rerender(<Filter icon="filtro" />);
        expect(getByTestId('icon-filtro')).toBeTruthy();
    });

    it('não deve renderizar o loading quando animating não é passado', () => { 
        const { queryByTestId } = render(
            <Loading />
        ); 
        
        const modal = queryByTestId('modal'); 
        expect(modal).toBeNull(); 
    });

    it('deve renderizar o Loading quando animating é true', () => { 
        const { getByTestId } = render(
            <Loading animating={true} text="Carregando..." />
        ); 
        const modal = getByTestId('modal'); 
        expect(modal.props.visible).toBe(true);
     }); 
     
     it('não deve renderizar o Loading quando animating é false', () => { 
        const { queryByTestId } = render(
            <Loading animating={false} text="Carregando..." />
        ); 
        
        const modal = queryByTestId('modal'); 
        expect(modal).toBeNull(); 
    }); 
    
    it('deve renderizar o texto do Loading corretamente', () => { 
        const { getByText } = render(
            <Loading animating={true} text="Carregando..." />
        ); 
        const text = getByText('Carregando...'); 
        expect(text).toBeTruthy(); 
    }); 
    
    it('deve aplicar o tamanho e a cor corretamente do ActivityIndicator do Loading', () => { 
        const { getByTestId } = render( 
            <Loading animating={true} size="large" color="#FF0000" /> 
        ); 
        
        const activityIndicator = getByTestId('activity-indicator'); 
        expect(activityIndicator.props.size).toBe('large'); 
        expect(activityIndicator.props.color).toBe('#FF0000'); 
    });

    it('deve renderizar o componente CardMedidor corretamente com os valores padrão', () => {
        const { getByText } = render(<CardMedidor />);
        const defaultTitle = getByText('Descrição');
        const defaultValue = getByText('Medidor');
        const defaultID = getByText('000');
        const defaultKe = getByText('Ke');
        expect(defaultTitle).toBeTruthy();
        expect(defaultValue).toBeTruthy();
        expect(defaultID).toBeTruthy();
        expect(defaultKe).toBeTruthy();
    });

    it('deve renderizar valores personalizados do CardMedidor', () => {
        const { getByText } = render(
            <CardMedidor 
                title="Meu Medidor"
                value="123456"
                valueID="789"
                valueKE="99"
                titleKE="Nova Constante" />
        );
        const customTitle = getByText('Meu Medidor');
        const customValue = getByText('123456');
        const customID = getByText('789');
        const customKe = getByText('99');
        const customTitleKe = getByText('Nova Constante');
        expect(customTitle).toBeTruthy();
        expect(customValue).toBeTruthy();
        expect(customID).toBeTruthy();
        expect(customKe).toBeTruthy();
        expect(customTitleKe).toBeTruthy();
    });    

    it('deve renderizar o modal Atenção corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxAtencao  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar o modal Atenção corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxAtencao visivel={true} />);
    
        expect(getByText('Erro')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar o modal Atenção onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxAtencao visivel={true} onFechar={onFecharMock} />);    
        fireEvent.press(getByText('OK'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar o modal Atenção corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxAtencao 
            visivel={true}
            titulo="Atenção"
            descricao="Esta é uma mensagem de atenção"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Atenção')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de atenção')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });

    it('deve renderizar o modal Erro corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxErro  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar o modal Erro corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxErro visivel={true} />);
    
        expect(getByText('Erro')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar do modal Erro quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxErro visivel={true} onFechar={onFecharMock} />);                    
        fireEvent.press(getByText('OK'));        
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar o modal Erro corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxErro 
            visivel={true}
            titulo="Atenção"
            descricao="Esta é uma mensagem de erro"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Atenção')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de erro')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
    
    describe('Storages', () => {    

        jest.mock('@react-native-async-storage/async-storage', () => ({      
            setItem: jest.fn(),
            getItem: jest.fn(),
            clear: jest.fn(),
        
        }));
          
        describe('setStorageJson', () => {  
          beforeEach(() => {
            jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
          });
                
          it('deve tratar o erro quando salva no AsyncStorage', async () => {
            const key = KEY.mockJest;
            const value = { test: 'value' };
            const error = new Error('Test error');
                  
            AsyncStorage.setItem = jest.fn(() => Promise.reject(error));              
            console.error = jest.fn();          
            await setStorageJson(key, value);          
            expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
          })        
        });
    
        describe('getStorageJson', () => {
          it('deve retornar um json quando não há erro', async () => {
            // Mockar o retorno do AsyncStorage
            AsyncStorage.getItem = jest.fn().mockResolvedValue(JSON.stringify({"test": "value"}));      
            const result = await getStorageJson('key');
            expect(result).toStrictEqual({"test": "value"});
          });
    
          it('deve retornar um null', async () => {
            // Mockar o retorno do AsyncStorage
            AsyncStorage.getItem = jest.fn().mockResolvedValue(null);    
            const result = await getStorageJson('key');
            expect(result).toBe(null);
          });      
    
          it('deve retornar uma string vazia quando há uma exceção', async () => {
            // Mockar um erro no AsyncStorage
            AsyncStorage.getItem = jest.fn().mockImplementation(() => {
              throw new Error('error');
            });      
            
            const result = await getStorageJson('key');
            expect(result).toBe('');
          });          
        });      
    
        describe('setStorageString', () => {
          beforeEach(() => {
            jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
          });
      
          it('deve salvar o dado como string no AsyncStorage', async () => {
            const key = KEY.mockJest;
            const value = 'value';
            await setStorageString(key, value);  
            expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, value);
          });     
          it('deve tratar o erro quando salva no AsyncStorage', async () => {
            const key = KEY.mockJest;
            const value = 'value';
            const error = new Error('Test error');              
            AsyncStorage.setItem = jest.fn(() => Promise.reject(error));              
            console.error = jest.fn();          
            await setStorageString(key, value);          
            expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
          })        
        });
    
        describe('getStorageString', () => {
          beforeEach(() => {
            jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
          });      
          it('deve retornar uma string quando não há erro', async () => {
            // Mockar o retorno do AsyncStorage
            AsyncStorage.getItem = jest.fn().mockResolvedValue('value');      
            const result = await getStorageString('key');
            expect(result).toBe('value');
          });        
          it('deve retornar uma string vazia quando há uma exceção', async () => {
            // Mockar um erro no AsyncStorage
            AsyncStorage.getItem = jest.fn().mockImplementation(() => {
              throw new Error('error');
            });      
            const result = await getStorageString('key');
            expect(result).toBe('');
          });    
        });
      
        describe('setStorageBoolean', () => {  
          it('deve salvar um valor booleano no AsyncStorage', async () => {
            const key = KEY.mockJest;
            const value = true;      
            await setStorageBoolean(key, value);            
            expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, JSON.stringify(value));
          });            
          it('deve tratar o erro quando salva no AsyncStorage', async () => {
            const key = 'testKey';
            const value = false;
            const error = new Error('Test error');                
            AsyncStorage.setItem = jest.fn(() => Promise.reject(error));                
            console.error = jest.fn();            
            await setStorageBoolean(key, value);            
            expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
          })        
        });
    
        describe('getStorageBoolean ', () => {
          beforeEach(() => {
            jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
          });    
          it('deve retornar uma string quando não há erro', async () => {
            // Mockar o retorno do AsyncStorage
            AsyncStorage.getItem = jest.fn().mockResolvedValue(true);      
            const result = await getStorageBoolean ('key');
            expect(result).toBe(true);
          });        
          it('deve retornar um null', async () => {
            // Mockar o retorno do AsyncStorage
            AsyncStorage.getItem = jest.fn().mockResolvedValue(null);      
            const result = await getStorageBoolean('key');
            expect(result).toBe(null);
          }); 
          it('deve retornar uma string vazia quando há uma exceção', async () => {
            // Mockar um erro no AsyncStorage
            AsyncStorage.getItem = jest.fn().mockImplementation(() => {
              throw new Error('error');
            });      
            const result = await getStorageBoolean ('key');
            expect(result).toBe(false);
          });    
        });
        
        describe('deleteStorage', () => {
          afterEach(() => {
            jest.clearAllMocks();
          });
                    
          it('deve registrar um erro se a remoção falhar', async () => {
            const consoleErrorSpy = jest.spyOn(console, 'error');
            const key = 'testKey';
            const error = new Error('Erro ao remover item');            
            const value = 'value';
            await setStorageString(key, value);  
            AsyncStorage.removeItem = jest.fn(() => Promise.reject(error));        
            await deleteStorage(key);
            expect(consoleErrorSpy).toHaveBeenCalledWith(`Erro ao remover ${key}: `, error);
          });
        });    
      
        describe('clearStorage', () => {          
          beforeEach(() => {
            // Limpa todos os mocks antes de cada teste
            jest.clearAllMocks(); 
          });        
            
          it('deve lidar com erros e chamar console.error', async () => {
            const error = new Error('Erro ao limpar os dados');          
            AsyncStorage.clear = jest.fn(() => Promise.reject(error));            
            console.error = jest.fn(); // Mock do console.error        
            await clearStorage();
            expect(console.error).toHaveBeenCalledWith('Erro ao limpar os dados', error);
          });
        });    
      });    

});
function performAction(arg0: () => void) {
    throw new Error('Function not implemented.');
}

