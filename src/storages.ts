import AsyncStorage from '@react-native-async-storage/async-storage';

// tipos de variaveis chave a serem armazenadas no app
export const KEY = Object.freeze({
  
  mockJest: 'mockJest',

  cognitoUser: 'cognitoUser',
  cognitoEmail: 'cognitoEmail',
  cognitoToken: 'cognitoToken',
  cognitoResponse: 'cognitoResponse',
  
  zordonEmail: 'zordonEmail',
  zordonSenha: 'zordonSenha',
  zordonToken: 'zordonToken',

  protheusToken: 'protheusToken',
  protheusRenovaToken: 'protheusRenovaToken',

  manterConectado: 'manterConectado',

  agendaProtheus: 'agendaProtheus',
  modelosMedidorProtheus: 'modelosMedidorProtheus',
  materiaisProtheus: 'materiaisProtheus',
  
  ordensServicoHoje: 'ordensServicoHoje',
  ordensServicoAmanha: 'ordensServicoAmanha',
  ordensServicoFuturas: 'ordensServicoFuturas',  
  idOrdemServicoHoje: 'idOrdemServicoHoje',
  idOrdemServicoAmanha: 'idOrdemServicoAmanha',
  idOrdemServicoFuturas: 'idOrdemServicoFuturas',
  etapasInstalacaoHoje: 'etapasInstalacaoHoje',

  historicoDebugDesc: 'historicoDebugDesc',
  historicoDebugAsc: 'historicoDebugAsc',

  probeVariaveis: 'probeVariaveis',
  probeFirmware: 'probeFirmware',

});

// salva um dado string como json no app
export const setStorageJson = async (key: string, value: any) => {

  try {
    const jsonValue = JSON.stringify(value);
    await AsyncStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error(`Erro ao salvar ${key}: `, error);
  }
};

// recupera um dado string como json do app
export const getStorageJson = async (key: string) => {  

    try {      
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;       
    } catch (error) {
      return '';
    }  
};

// salva um dado string no app
export const setStorageString = async (key: string, value: string) => {
  
  try {
    await AsyncStorage.setItem(key, value)
  } catch (error) {
    console.error(`Erro ao salvar ${key}: `, error);
  }
};

// recupera um dado string do app
export const getStorageString = async (key: string) => {      
    
    try {
       return await AsyncStorage.getItem(key);
    } catch (error) {
      return '';
    }  
};

// salva um dado boolean no app
export const setStorageBoolean = async (key: string, value: boolean) => {  

  try {
    await AsyncStorage.setItem(key,  value.toString());
  } catch (error) {
    console.error(`Erro ao salvar ${key}: `, error);
  }
};

// recupera um dado boolean armazenado localmente no app
export const getStorageBoolean = async (key: string) => {  

  try {
    const value = await AsyncStorage.getItem(key);
    
    return value !== null ? JSON.parse(value) : null;
  } catch (error) {    
    return false;
  }
};

// deleta um dado armazenado dentro do app
export const deleteStorage = async (key:string) => {

  try {
    await AsyncStorage.removeItem(key);
  } catch (error) {
    console.error(`Erro ao remover ${key}: `, error);
  }
}

export const clearStorage = async () => {
  try {
    await AsyncStorage.clear();
  } catch (error) {
    console.error('Erro ao limpar os dados', error);
  }
};