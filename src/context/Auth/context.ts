import React, {useContext} from 'react';

// @Types
import { User } from '../../@types/models';


interface AuthContextType {
  login: (request: any) => boolean;
  logout: () => Promise<void>;
  setIsLogged: React.Dispatch<React.SetStateAction<boolean>>;
  user?: User;
  isLogged: boolean;
}

const AuthContext = React.createContext<AuthContextType>({} as AuthContextType);

export default AuthContext;

export const useAuth = () => useContext(AuthContext);