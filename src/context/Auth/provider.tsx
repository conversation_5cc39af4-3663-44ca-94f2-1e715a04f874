import React, {useState} from 'react';
import {Platform} from 'react-native';

// Access Routes Globally
//import {navigate} from '../../../RootNavigation';

// Utils
//import {openLink} from '@utils/index';

// storage
import { deleteStorage, KEY, setStorageString } from '../../storages';

// Contexts
import AuthContext from './context';

// Types
import { User } from '../../@types/models';

// Services
import { CognitoAuthService } from '../../services/Cognito/Auth/service';
import { ZordonAuthService } from '../../services/Zordon/Auth/service';

// navegação
import { useNavigation } from '@react-navigation/native';
import { StackTypes } from '../../routes';


//import { KEY, setStorageString } from '../../storages';

interface AuthProviderProps {
    children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({children}) => {
    
    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    const [isLogged, setIsLogged] = useState<boolean>(false);  
    const [user, setUser] = useState<User>();

    // SignIn User Cognito
    function login(request: any) {
        
        const {url} = request;

        if (url.startsWith('http://localhost:8081/callback/login')) {

            const match = url.match(/code=([^&]*)/);
            const code = match ? match[1] : null;

            handleAuthorizationCodeExchange(code);

            return false;
        } else if (url.startsWith('mailto:')) {
              
            return false;
        }

        return true;
    }

    // Pegar Token
    async function handleAuthorizationCodeExchange(code: string) {
        try {
            // Realiza a troca do código pelo token
            const res = await CognitoAuthService.login(code);

            // Armazena Token
            setStorageString(KEY.cognitoToken, res.data.id_token);

        fetchUserData();
        } catch (error) {
        
            navigation.navigate('Logout');
        }
    }

    // Pegar dados do usuario logado
    async function fetchUserData() {
        try {
            const userResponse = await ZordonAuthService.login();

            setUser(userResponse.data);
            setStorageString(KEY.cognitoToken, userResponse.data);            
            setIsLogged(true);

        } catch (err) {
            navigation.navigate('Logout');
        }
    }

    // Função para deslogar usuario
    async function logout() {
        
        deleteStorage(KEY.cognitoToken);
        setIsLogged(false);
    }

    return (
        <AuthContext.Provider
            value={{
                login,
                logout,
                setIsLogged,
                user,
                isLogged,
            }}>
            {children}
        </AuthContext.Provider>
    );
};

export default AuthProvider;
