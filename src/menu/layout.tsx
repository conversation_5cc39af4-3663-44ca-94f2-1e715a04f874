import { Dimensions, StyleSheet } from "react-native";

const screenHeight = Dimensions.get('window').height;
const screenWidth = Dimensions.get('window').width;

export const styles = StyleSheet.create({
    
    containerTela:{
        flex:1,
    },

    containerFecharDrawer: {
        paddingTop:40, 
        paddingLeft:20,
        alignItems:'flex-start',
    }, 

    containerOs:{
        paddingTop:25, 
        justifyContent:'center', 
        flexDirection:'row', 
        gap:15,
    },

    containerDashboardOs:{
        width:105,
        height:70,
        borderWidth:1,
        borderRadius:5,
        alignItems:"center",
        justifyContent:"center",
        borderColor: "#E5E5E5",
        backgroundColor:"#E5E5E5",
        shadowColor:"#101828",
        shadowOpacity:5,
    },  
      
    containerTotalOs:{
        flexDirection:'row', 
        gap:5, 
        justifyContent:'center', 
        alignItems:'flex-end',
    },

    containerFooterLogo: {
        justifyContent:'flex-end',
        alignItems:'center',
        position: 'absolute',        
        top: screenHeight - 30, 
        width: screenWidth,
    },
    
    containerFooterVersion: {
        justifyContent:'flex-end',
        alignItems:'flex-end',
        position: 'absolute',        
        top: screenHeight, 
        width: screenWidth,
        paddingHorizontal:30,
    },    
    
    containerUsuario:{
        paddingTop:20, 
        paddingLeft:50, 
        alignItems: "center", 
        flexDirection:'row', 
        gap: 20,
    },

    imageBackgroundHeader: {
        height: screenHeight * 0.28,
        width: screenWidth,
        justifyContent: 'center',
        flex:1,
        position:'absolute',
        marginTop:0
    },

    imageBackgroundMain: {
        height: screenHeight,
        width: screenWidth,
        alignItems:'flex-end',
        justifyContent: 'center',
        marginTop:screenHeight * 0.1,        
      },
      
    textoNome:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:20,
        fontWeight: "600", 
        lineHeight: 32,   
        color: "#FFFFFF"
    },

    textoTipoCargo:{
        fontFamily: 'SourceSans3_400Regular',
        fontSize:16,
        fontWeight: "600", 
        lineHeight: 32,   
        color: "#CECFD2"
    },

    textoTotalOs:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:20,
        fontWeight: "400",         
        color: "#2E8C1F"
    },

    textoOs:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:12,
        fontWeight: "200", 
        color: "#94969C"
    },

    textoStatusOs:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:12,
        fontWeight: "400", 
        color: "#2E8C1F"
    },

    textoButtomMenu:{
        fontFamily: 'Exo2_600SemiBold',
        fontSize:20,
        color: "#D6D6D6"
    },

    textoVersao:{
        fontFamily: 'Exo2_400Regular',
        fontSize:14,
        fontWeight: "400",
        color: "#FFFFFF",
        textAlign:'right'
    },

    buttonMenu: {
        paddingStart: 30,
        width:'100%',
        height:70,
        alignItems: 'flex-start',
        justifyContent: 'center',
        backgroundColor: "transparente",        
    },

    buttonFecharDrawer: {
        backgroundColor: "transparente",        
    },      

    iconMenu:{
        color:"#D6D6D6",
        width:25,
        height:25,
    },

    iconUsuario:{
        color:"#FFFFFF",
        width:50,
        height:50,
    },

    linhaHorizontal:{
        borderBottomColor: 'white',
        borderBottomWidth: StyleSheet.hairlineWidth,
    },
      
});