import { DrawerActions, useNavigation } from '@react-navigation/native';
import React, { useEffect, useState } from 'react';
import { Image, ImageBackground, Text, TouchableOpacity, View } from "react-native";

// estilos da pagina
import { styles } from '../menu/layout';

// icones svg
import IconRight from '../assets/svg/icon_chevron-right.svg';
import IconLista from '../assets/svg/icon_dotpoints-01.svg';
import IconHome from '../assets/svg/icon_home-03.svg';
import IconLogout from '../assets/svg/icon_log-out-01.svg';
import IconMedidor from '../assets/svg/icon_server.svg';
import IconSetting from '../assets/svg/icon_settings-01.svg';
import IconTool from '../assets/svg/icon_tool.svg';
import IconUserCircle from '../assets/svg/icon_user-circle.svg';
import IconX from '../assets/svg/icon_x.svg';

// navegação
import { DrawerTypes } from '../pages/MenuPrincipal/index';
import { StackTypes } from '../routes/index';
import { goToProbe } from './navigation';

// storage
import { getStorageString, KEY } from '../storages';

import { version } from '../../package.json';


const CustomDrawer = () => {

    const navigationDrawer = useNavigation<DrawerTypes>();
    const navigationStack = useNavigation<StackTypes>();
    
    /* nome do usuario */
    const [nome, setNome] = useState('Desconhecido');

    /**
     * seta o nome do usuario atual
    */
    useEffect(() => {        
            
        async function fetchNome() {
            
            try {
                
                const cognito_user = await getStorageString(KEY.cognitoUser);
                
                if (cognito_user !== '' && cognito_user !== null) {
                    setNome(cognito_user);
                } else {
                    setNome('Desconhecido');
                }
            } 
            catch (error) {
                setNome('Error');
            }
        }

        fetchNome();

    }, []);    

    return(

        <View style={styles.containerTela} testID="custom-drawer">        

            <ImageBackground
                source={require("../assets/png/drawer-main-bg.png")}                    
                style={styles.imageBackgroundMain}
            >
                <View style={{marginTop:100}}>

                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina home */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => navigationDrawer.navigate('IniciarJornada')}>
                        <View style={{flexDirection:'row'}}>
                            <View style={{width:'90%', flexDirection:'row', alignItems:'center', gap: 15}}>
                                <IconHome style={styles.iconMenu}/>
                                <Text style={styles.textoButtomMenu}>Home</Text>                            
                            </View>
                            <View style={{width:'10%', justifyContent:'center'}}>
                                <IconRight style={styles.iconMenu}/>
                            </View>
                        </View>                                            
                    </TouchableOpacity>

                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina lista de ordens de serviço */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => navigationDrawer.navigate('OrdensServico')}>
                        <View style={{flexDirection:'row'}}>
                            <View style={{width:'90%', flexDirection:'row', alignItems:'center', gap: 15}}>
                                <IconLista style={styles.iconMenu}/>
                                <Text style={styles.textoButtomMenu}>Lista de OS</Text>
                            </View>
                            <View style={{width:'10%', justifyContent:'center'}}>
                                <IconRight style={styles.iconMenu}/>
                            </View>
                        </View>                        
                    </TouchableOpacity>
                    
                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina de configuração da probe */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => goToProbe(navigationStack, nome, 'Probe ID')}>
                        <View style={{flexDirection:'row'}}>
                            <View style={{width:'90%', flexDirection:'row', alignItems:'center', gap: 15}}>
                                <IconSetting style={styles.iconMenu}/>
                                <Text style={styles.textoButtomMenu}>Probe</Text>
                            </View>
                            <View style={{width:'10%', justifyContent:'center'}}>
                                <IconRight style={styles.iconMenu}/>
                            </View>
                        </View>                                            
                    </TouchableOpacity>
                    
                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina de modelos de medidores */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => navigationDrawer.navigate('Medidores')}>
                        <View style={{flexDirection:'row'}}>
                            <View style={{width:'90%', flexDirection:'row', alignItems:'center', gap: 15}}>
                                <IconMedidor style={styles.iconMenu}/>
                                <Text style={styles.textoButtomMenu}>Medidores</Text>
                            </View>
                            <View style={{width:'10%', justifyContent:'center'}}>
                                <IconRight style={styles.iconMenu}/>
                            </View>
                        </View>
                    </TouchableOpacity>
                    
                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina de materiais */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => navigationDrawer.navigate('Materiais')}>
                        <View style={{flexDirection:'row'}}>
                            <View style={{width:'90%', flexDirection:'row', alignItems:'center', gap: 15}}>
                                <IconTool style={styles.iconMenu}/>
                                <Text style={styles.textoButtomMenu}>Materiais</Text>
                            </View>
                            <View style={{width:'10%', justifyContent:'center'}}>
                                <IconRight style={styles.iconMenu}/>
                            </View>
                        </View>
                    </TouchableOpacity>
                    
                    <View style={styles.linhaHorizontal} />

                    {/* botao pagina sair do aplicativo */}
                    <TouchableOpacity style = {styles.buttonMenu} onPress={() => navigationStack.navigate('Logout')}>
                        <View style={{flexDirection:'row', justifyContent:'center', alignItems:'center', gap: 15}}>
                            <IconLogout style={styles.iconMenu}/>
                            <Text style={styles.textoButtomMenu}>Logout</Text>
                        </View>                        
                    </TouchableOpacity>

                    <View style={styles.linhaHorizontal} />

                </View>

            </ImageBackground>

            <ImageBackground
                source={require("../assets/png/drawer-header-bg.png")}
                style={styles.imageBackgroundHeader}
            >
                <View style={{flex:1}}>

                    <View style={styles.containerFecharDrawer}>
                        <TouchableOpacity style={styles.buttonFecharDrawer} onPress={() => navigationDrawer.dispatch(DrawerActions.closeDrawer())} testID='button-close-drawer'>
                            <IconX color={"#FFFFFF"} width={32} height={32}/>
                        </TouchableOpacity>
                        
                    </View>

                    <View style={{paddingTop:20, paddingLeft:50, alignItems: "center", flexDirection:'row', gap: 20}}>

                        <IconUserCircle color={"#FFFFFF"} width={50} height={50}/>
                        
                        <View style={{justifyContent:'center'}}>
                            <Text style={styles.textoNome}>{nome}</Text>
                            <Text style={styles.textoTipoCargo}>Zordon</Text>
                        </View>

                    </View>

                    <View style={styles.containerOs}>
                        
                        {/* total de ordens de serviço concluidas */}
                        <View style={styles.containerDashboardOs}>
                            <View>
                                <View style={styles.containerTotalOs}>
                                    <Text style={styles.textoTotalOs}>140</Text>
                                    <Text style={styles.textoOs}>OS</Text>
                                </View>
                                <Text style = {styles.textoStatusOs}>Concluídas</Text>
                            </View>
                        </View>

                        {/* total de ordens de serviço pendentes */}
                        <View style={styles.containerDashboardOs}>
                            <View>
                                <View style={styles.containerTotalOs}>
                                    <Text style={styles.textoTotalOs}>2</Text>
                                    <Text style={styles.textoOs}>OS</Text>
                                </View>
                                <Text style = {styles.textoStatusOs}>Pendentes</Text>
                            </View>
                        </View>

                        {/* total de ordens de serviço não iniciadas */}
                        <View style={styles.containerDashboardOs}>
                            <View>
                                <View style={styles.containerTotalOs}>
                                    <Text style={styles.textoTotalOs}>14</Text>
                                    <Text style={styles.textoOs}>OS</Text>
                                </View>
                                <Text style = {styles.textoStatusOs}>Não iniciadas</Text>
                            </View>
                        </View>
                    </View>
                </View>
                
            </ImageBackground>

            {/* rodape da pagina */}
            <View style={styles.containerFooterLogo}>
                <Image source={require('../assets/png/logo_comerc.png')} />
            </View>
            <View style={styles.containerFooterVersion}>
                <Text style={styles.textoVersao}>{`v.${version}`}</Text>
            </View>            

        </View>        

        
    );
};

export default CustomDrawer;
