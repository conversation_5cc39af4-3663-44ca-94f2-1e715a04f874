import { CommonActions, useNavigation } from "@react-navigation/native";

// rotas
import { StackTypes } from "../routes";

// data
import { ProbeSettings, ProbeSettingsInicial } from "../data";

/**
 *  navega para pagina principal de configuração da Probe
 * @param navigation 
 * @param user 
 * @param id_probe 
 */
export const goToProbe = (navigation: ReturnType<typeof useNavigation<StackTypes>>, user: string,  id_probe: string) => {

    /* estrutura inicial de configuração da probe */
    const _probe: ProbeSettings = ProbeSettingsInicial();

    /* atualiza estrutura de configuração da probe */
    const probeAtualizada = { ..._probe, user: user, id: id_probe };

    /* fecha a pagina de configuração da probe */
    navigation.dispatch(
        CommonActions.navigate({
            name: 'Probe',
            params: { probeSettings: probeAtualizada },
        })
    );
};