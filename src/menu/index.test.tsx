import { act, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import CustomDrawer from '.';
import { getStorageString } from '../storages';

// Mock da navegação
const mockNavigate = jest.fn();

jest.mock('@react-navigation/native', () => {
    return {        
        useNavigation: () => ({        
            navigate: mockNavigate, // Simulando o comportamento de navigate
            dispatch: mockNavigate, // Simulando o comportamento de dispatch
        }),
        DrawerActions: {
            closeDrawer: jest.fn(),
        },
        CommonActions: {
            navigate: jest.fn(), // Mock da função navigate
        },        
    };
});

// Mock do StatusBar
jest.mock('expo-status-bar', () => {
    return {
        StatusBar: () => null,
    };
});

// Mockando a função getStorageString
jest.mock('../storages', () => ({
    getStorageString: jest.fn(),
    KEY: { cognitoUser: 'mockUserKey' },
}));



describe('Menu Principal', () => {

    beforeEach(() => {
        jest.resetAllMocks(); // Reseta todos os mocks
        jest.clearAllMocks(); // Limpa chamadas anteriores
    });

    it('deve renderizar corretamente a tela do drawer', async () => {

        // Simulando um usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const {unmount, getByText} = render(<CustomDrawer /> );       
        
        // Verifica se o nome e o cargo estão sendo exibidos
        expect(getByText('Desconhecido')).toBeTruthy();
        expect(getByText('Zordon')).toBeTruthy();

        // Verifica se os botões da navegação estão presentes
        expect(getByText('Home')).toBeTruthy();
        expect(getByText('Lista de OS')).toBeTruthy();
        expect(getByText('Probe')).toBeTruthy();
        expect(getByText('Medidores')).toBeTruthy();
        expect(getByText('Materiais')).toBeTruthy();
        expect(getByText('Logout')).toBeTruthy();
        
        unmount();
    });

    it('deve navegar para a tela correta quando clicar em "Home"', async () => {

        // Simulando quando usuário não armazenado
        (getStorageString as jest.Mock).mockResolvedValue('');

        const {unmount, getByText } = render( <CustomDrawer /> );

        await act(async () => {      
            // Simula o clique no botão "Home"
            fireEvent.press(getByText('Home'));
        });           

        // Verifica se a navegação para a tela correta foi chamada
        expect(mockNavigate).toHaveBeenCalledWith('IniciarJornada');

        unmount();
    });

    it('deve navegar para a tela correta quando clicar em Lista de OS"', async () => {
              
        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const {unmount, getByText } = render( <CustomDrawer /> );
        
        await act( async () => {   
            // Simula o clique no botão "Lista de OS"
            fireEvent.press(getByText('Lista de OS'));
        });
    
        // Verifica se a navegação para a tela correta foi chamada
        expect(mockNavigate).toHaveBeenCalledWith('OrdensServico');

        unmount();
    });

    it('deve navegar para a tela correta quando clicar em Probe"', async () => {

        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const { unmount, getByText } = render( <CustomDrawer /> );
        
        await act(async () => {   
            // Simula o clique no botão "Probe"
            fireEvent.press(getByText('Probe'));
        });

        unmount();
    });

    it('deve navegar para a tela correta quando clicar em Medidores"', async () => {

        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');
    
        const {unmount, getByText } = render( <CustomDrawer /> );

        await act(async () => {           
            // Simula o clique no botão "Medidores"
            fireEvent.press(getByText('Medidores'));
        });

        // Verifica se a navegação para a tela correta foi chamada
        expect(mockNavigate).toHaveBeenCalledWith('Medidores');

        unmount();
    });

    it('deve navegar para a tela correta quando clicar em Materiais"', async () => {

        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const {unmount, getByText } = render( <CustomDrawer /> );

        await act(async () => {           
            // Simula o clique no botão "Materiais"
            fireEvent.press(getByText('Materiais'));
        });

        // Verifica se a navegação para a tela correta foi chamada
        expect(mockNavigate).toHaveBeenCalledWith('Materiais');

        unmount();
    });    

    it('deve navegar para a tela correta quando clicar em Sair"', async () => {

        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const { unmount, getByText } = render( <CustomDrawer /> );

        await act(async () => {           
            // Simula o clique no botão "Logout"
            fireEvent.press(getByText('Logout'));
        });

        // Verifica se a navegação para a tela correta foi chamada
        expect(mockNavigate).toHaveBeenCalledWith('Logout');

        unmount();
    });

    it('deve fechar o drawer ao pressionar o botão de fechar', async () => {
       
        // Simulando quando usuário armazenado
        (getStorageString as jest.Mock).mockResolvedValue('Usuário');

        const {unmount, getByTestId } = render(<CustomDrawer /> );

        await act(async () => {           
            // Simula o clique no botão de fechar o drawer
            fireEvent.press(getByTestId('button-close-drawer'));
        });

        // Verifica se a ação de fechar o drawer foi chamada
        expect(mockNavigate).toHaveBeenCalled();

        unmount();
    });
    
    it('deve renderizar corretamente a tela do drawer quando erro ao verificar nome do usuário', () => {

        // Simulando erro na chamada
        (getStorageString as jest.Mock).mockRejectedValue(new Error('Error'));

        const {unmount} = render( <CustomDrawer /> );

        unmount();
    });

});
