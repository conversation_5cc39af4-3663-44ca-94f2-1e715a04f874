import { CommonActions } from '@react-navigation/native';
import { goToProbe } from './navigation';


describe('goToProbe', () => {

    it('deve navegar para a tela de Probe com os parâmetros corretos', () => {

        const mockDispatch = jest.fn();

        const mockNavigation = {
            dispatch: mockDispatch,
        };

        // Dados de teste
        const user = 'Usuario';
        const id_probe = 'AB123456789C';

        // Chama a função
        goToProbe(mockNavigation as any, user, id_probe);

        // Verifica se `dispatch` foi chamado corretamente
        expect(mockNavigation.dispatch).toHaveBeenCalledWith(
            CommonActions.navigate({
                name: 'Probe',
                params: {
                    probeSettings: expect.objectContaining({
                        user: 'Usuario',
                        id: 'AB123456789C',
                    }),
                },
            })
        );
    });
});
