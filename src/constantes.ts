
// heap_init: At 3FFAE6E0 len 00001920 (6 KiB)
export const Heap00 = (6 * 1024);

// heap_init: At 3FFBCCE0 len 00023320 (140 KiB)
export const Heap01 = (140 * 1024);

// heap_init: At 3FFE0440 len 00003AE0 (14 KiB)
export const Heap02 = (14 * 1024); 

// heap_init: At 3FFE4350 len 0001BCB0 (111 KiB)
export const Heap03 = (111 * 1024);

// heap_init: At 40097A14 len 000085EC (33 KiB)
export const Heap04 = (33 * 1024);

// memoria heap total 311296
export const HeapTotal = Heap00 + Heap01 + Heap02 + Heap03 + Heap04;

// memoria heap livre maxima (10%)
export const HeapFreeMax = (HeapTotal * 0.1);

// autentição broker de Produção
export const BrokerProd = Object.freeze({
    URI: 'mqtt://broker.zordon.app:1883',    
    AUTH: true, 
    USER: 'apphw',
    PASS: '6vHkjARq',
    CLIENTE_ID: 'FabioAmaral',
});

// autentição broker de Homologação
export const BrokerHmg = Object.freeze({
    URI: 'mqtt://broker-hmg.zordon.app:1883',
    AUTH: true, 
    USER: 'apphw',
    PASS: '2xhNpWLP',
    CLIENTE_ID: 'FabioAmaral',
});

// tipos de ordens de serviço
export const TipoOS = Object.freeze({
    INSTALACAO_FRO1: 0, // FRO1.1 - Instalação de Fronteira    
    INSTALACAO_SET1: 1, // SET1.1 - Instalação de telemetria setorizada
    INSTALACAO_SEN1: 2, // SEN1.1 - Instalação de medidor setorizado
    TROCA_FRO2: 3,      // FRO2.1 - Substituição de telemetria de fronteira de terceiros (schneider, gestal, way2)
    MANUTENCAO: 4,      // MANUT - manutenção com troca de telemetria 
    RETIRADA_FRO3: 5,   // FRO1.3 - Desinstalação de telemetria de fronteira
    VISITA: 6           // VISIT - manutenção sem troca de telemetria
});

// descrição das telas de ordens de serviço
export const ScreenTipoOS = Object.freeze({
    [0]: 'OrdemServicoInstalacaoFro1',
    [1]: 'OrdemServicoInstalacaoSet1',
    [2]: 'OrdemServicoInstalacaoSen1',
    [3]: 'OrdemServicoTrocaFro2',
    [4]: 'OrdemServicoManutencao',  
    [5]: 'OrdemServicoRetiradaFro3',
    [6]: 'OrdemServicoVisita',
});

// descrição das telas de resumo das ordens de serviço
export const ScreenResumoTipoOS = Object.freeze({
    [0]: 'ResumoOSInstalacaoFro1',
    [1]: 'ResumoOSInstalacaoSet1',
    [2]: 'ResumoOSInstalacaoSen1',
    [3]: 'ResumoOSTrocaFro2',
    [4]: 'ResumoOSManutencao',  
    [5]: 'ResumoOSRetiradaFro3',
    [6]: 'ResumoOSVisita',
});

// descrição de ordens de serviço
export const DescricaoOS = Object.freeze({
    [0]: 'Instalação - FRO1.1',
    [1]: 'Instalação - SET1.1',
    [2]: 'Instalação - SEN1.1',
    [3]: 'Troca - FRO2.1',
    [4]: 'Manutenção',  
    [5]: 'Retirada - FRO3.1',
    [6]: 'Visita',
});

// sub descrição de ordens de serviço
export const SubDescricaoOS = Object.freeze({
    [0]: 'Instalação de Fronteira',
    [1]: 'Instalação de telemetria setorizada',
    [2]: 'Instalação de medidor setorizado',
    [3]: 'Substituição de telemetria de fronteira de terceiros',
    [4]: 'Manutenção com troca de telemetria',  
    [5]: 'Desinstalação de telemetria de fronteira',
    [6]: 'Manutenção sem troca de telemetria',
});

// tipos status de ordens de serviço
export const TipoStatusOS = Object.freeze({
    CONCLUIDO: 0,
    PENDENTE: 1,
    NAO_INICIADO: 2,
    TODOS: 3,
});

// descrição do status de ordens de serviço
export const DescricaoStatusOS = Object.freeze({
    [0]: 'Concluído',
    [1]: 'Pendente',
    [2]: 'Não Iniciado',
    [3]: 'Todos',
});

// Cores do status de ordens de serviço
export const CoresStatusOS = Object.freeze({
    [0]: '#2E8C1F',
    [1]: '#EF6820',
    [2]: '#EAAA08',
});

// tipos status de Pre OS
export const TipoStatusPreOS = Object.freeze({
    AGUARDANDO: 0,
    ABERTA: 1,
    AGENDADA: 2,
    ENCERRADA: 3,
    CANCELADA: 4,
    ATENDIDA: 5,
    TODOS: 6,
});

// descrição do status de Pre OS
export const DescricaoStatusPreOS = Object.freeze({
    [0]: 'Aguardando',
    [1]: 'Aberta',
    [2]: 'Agendada',
    [3]: 'Encerrada',
    [4]: 'Cancelada',
    [5]: 'Atendida',
    [6]: 'Todos',
    
});

// Cores do status de Pre OS
export const CoresStatusPreOS = Object.freeze({
    [0]: '#737373',
    [1]: '#EF6820',
    [2]: '#2970FF',
    [3]: '#7A5AF8',
    [4]: '#E60034',
    [5]: '#2E8C1F',
    [6]: '#EAAA08',
});


// tipos de conexão da probe
export const TipoConexao = Object.freeze({
    WIFI: 0,
    GSM: 1,
    ETHERNET: 2,
    NARROW_BAND: 3,
});

/**
 * timeout por  tipo de conexão
 */
export const TimeoutTipoConexao: Readonly<Record<number, number>> = Object.freeze({
    [0]: 120000,
    [1]: 300000,
    [2]: 60000,
    [3]: 300000,
});


// descrição do tipo de conexão
export const DescricaoConexao = Object.freeze({
    [0]: 'Wi-Fi',
    [1]: 'GSM',
    [2]: 'Ethernet',    
    [3]: 'Narrow Band',
});

// lista tipos de conexão
export const ListaTiposConexao = [
    { id: 0, descricao: 'Wi-Fi'},
    { id: 1, descricao: 'GSM' },
    { id: 2, descricao: 'Ethernet'},
    { id: 3, descricao: 'Narrow Band'},
];

// tipos de operadora
export const TipoOperadora = Object.freeze({
    DESCONHECIDO: 0,
    VIVO: 1,
    TIM: 2,
    CLARO: 3,
    OI: 4,
    NEXTEL: 5,    
    ALGAR: 6,
    SERCOMTEL: 7,
    MVNOs: 8,
});

// descrição do tipo de operadora
export const DescricaoOperadora = Object.freeze({
    [0]: 'Desconhecido',
    [1]: 'Vivo',
    [2]: 'Tim',
    [3]: 'Claro',
    [4]: 'Oi',
    [5]: 'Nextel',
    [6]: 'Algar',
    [7]: 'SerComTel',
    [8]: 'MVNOs'
});

// operadoras de telefonia celular
export const OperadorasCelular = [
    { id: 1, descricao: 'VIVO'},
    { id: 2, descricao: 'TIM' },
    { id: 3, descricao: 'CLARO'},
    { id: 4, descricao: 'OI'},
    { id: 5, descricao: 'NEXTEL'},
    { id: 6, descricao: 'ALGAR'},    
    { id: 7, descricao: 'SERCOMTEL'},    
    { id: 8, descricao: 'MVNOs'},    
];

// tipos de status dos instaladores
export const StatusInstalador = Object.freeze({
    BLOQUEADO: 1,
    ATIVO: 2,
});

// tipos de status Probe
export const StatusAtualProbe = Object.freeze({
    DESCONHECIDO: 0,
    DISPONIVEL: 1,
    EM_USO: 2,
    EM_REPARO: 3,
    SUCATA: 4,
    
});

// descrição de status da probe
export const DescricaoStatuProbe = Object.freeze({
    [0]: 'Desconhecido',
    [1]: 'Disponível',
    [2]: 'Em uso',
    [3]: 'Em reparo',
    [4]: 'Sucata',
});

// tipos de conexao mqtt
export const TipoConexaoMQTT = Object.freeze({
    TESTE: 0,
    CONFIGURACAO: 1,
});

// tipos de endianess Probe
export const EndianessProbe = Object.freeze({
    BACD: 0,
    CDAB: 1,
    DCBA: 2,
    ABCD: 3,    
});

// lista tipos de endianess
export const ListaEndianessProbe = [
    { id: 0, descricao: 'BACD'},
    { id: 1, descricao: 'CDAB' },
    { id: 2, descricao: 'DCBA'},
    { id: 3, descricao: 'ABCD'},
];

// tipos de tempo de envio
export const TempoEnvioProbe = Object.freeze({
    T_01: 0,
    T_05: 1,
    T_15: 2,
    T_60: 3,    
});

// lista tipos de tempo de envio
export const ListaTempoEnvioProbe = [
    { id: 0, descricao: '01 minuto'},
    { id: 1, descricao: '05 minutos' },
    { id: 2, descricao: '15 minutos'},
    { id: 3, descricao: '60 minutos'},
];

// descrição do tipo de formato
export const TempoSegundosEnvioProbe = Object.freeze({
    [0]: 60,
    [1]: 300,
    [2]: 900,
    [3]: 3600,
});

// tipos de protocolo
export const ProtocoloProbe = Object.freeze({
    RTU: 0,
    TCP: 1,
});

// lista tipos de protocolo
export const ListaProtocoloProbe = [
    { id: 0, descricao: 'RTU'},
    { id: 1, descricao: 'TCP' },
];

// lista tipos de conexao para o Slave Modbus
export const ListaTipoConexaoProbe = [
    { id: 0, descricao: 'Wi-Fi'},
    { id: 1, descricao: 'Ethernet' },
];

// tipos de bit de paridade
export const ParidadeProbe = Object.freeze({
    _8N1: 0,
    _8N2: 1,
    _8E1: 2,
    _8E2: 3,
    _8O1: 4,
    _8O2: 5,
});

// lista tipos de paridade
export const ListaParidadeProbe = [
    { id: 0, descricao: '8N1'},
    { id: 1, descricao: '8N2' },
    { id: 2, descricao: '8E1'},
    { id: 3, descricao: '8E2' },
    { id: 4, descricao: '8O1'},
    { id: 5, descricao: '8O2' },
];

// tipos de bit de baud rate
export const BaudRateProbe = Object.freeze({
    _110: 0,
    _600: 1,
    _1200: 2,
    _2400: 3,
    _4800: 4,    
    _9600: 5,
    _14400: 6,
    _19200: 7,
    _28800: 8,
    _38400: 9,
    _56000: 10,
    _115200: 11,
    
});

// lista tipos de baud rate
export const ListaBaudRateProbe = [
    { id: 0, descricao: '110'},
    { id: 1, descricao: '600'},
    { id: 2, descricao: '1200'},
    { id: 3, descricao: '2400'},
    { id: 4, descricao: '4800'},
    { id: 5, descricao: '9600'},
    { id: 6, descricao: '14400'},
    { id: 7, descricao: '19200' },
    { id: 8, descricao: '28800' },
    { id: 9, descricao: '38400' },
    { id: 10, descricao: '56000' },
    { id: 11, descricao: '115200' },
];

// tipos de formato
export const TipoFormato = Object.freeze({
    _INT16: 0,
    _UINT16: 1,
    _INT32: 2,
    _UINT32: 3,
    _INT64: 4,
    _UINT64: 5, 
    _FLOAT32: 6,
    _STRING: 7,
    _RAW: 8,
    _DOUBLE64: 9,
    _UINT32_T5: 10,
    _UINT32_T6: 11,
    _UINT32_T7: 12,
    _UINT32_TFLOAT: 13,
    _BCD: 14,
});

// descrição do tipo de formato
export const DescricaoFormato = Object.freeze({
    [0]: 'INT16',
    [1]: 'UINT16',
    [2]: 'INT32',
    [3]: 'UINT32',
    [4]: 'INT64',
    [5]: 'UINT64',
    [6]: 'FLOAT32',
    [7]: 'STRING',
    [8]: 'RAW',
    [9]: 'DOUBLE64',
    [10]: 'UINT32_T5',
    [11]: 'UINT32_T6',
    [12]: 'UINT32_T7',
    [13]: 'UINT32_TFLOAT',
    [14]: 'BCD',
});

// descrição do tipo de disponíveis
export const TiposCODI = Object.freeze([
    'CODI 1',
    'CODI 2',
    'CODI RS232',
    'CODI RS485',
]);

// lista tipos de protocolo codi
export const ListaProtocoloCodi = [
    { id: 0, descricao: 'Normal'},
    { id: 1, descricao: 'Misto'},
    { id: 2, descricao: 'Estendido'},
];

// lista tipos de protocolo codi rs232
export const ListaProtocoloCodi232 = [
    { id: 0, descricao: 'Normal'},
    { id: 1, descricao: 'Misto'},
    { id: 2, descricao: 'Estendido'},
    { id: 3, descricao: 'Bidirecional'},
];

// lista tipos de protocolo codi rs232
export const ListaProtocoloCodi485 = [
    { id: 0, descricao: 'Normal'},
    { id: 1, descricao: 'Misto'},
    { id: 2, descricao: 'Estendido'},
    { id: 3, descricao: 'Bidirecional'},
    { id: 4, descricao: 'Multiponto'},
];

// lista tipos de modos de atualização de firmware
export const ListaFirmwareModo = [
    { id: 0, descricao: 'Normal'},
    { id: 1, descricao: 'Estável'},
    { id: 2, descricao: 'Ultra estável'},
];

// tipos de modos de atualização de firmware
export const TipoFirmwareModo = Object.freeze({
    NORMAL: 0,
    ESTAVEL: 1,
    ULTRA_ESTAVEL: 2,
});

// descrição dos tipos comunicação serial
export const TiposComunicacaoION = Object.freeze([
    'RS485',
    'RS232',
]);

// lista dos tipos comunicação serial
export const ListaComunicacaoSerial = [
    { id: 0, descricao: 'RS485'},
    { id: 1, descricao: 'RS232'},
];

// lista dos modos de Busca
export const ListaModoBuscaION = [
    { id: 0, descricao: 'Básico'},
    { id: 1, descricao: 'Completo'},
];

// descrição do tipo de contador de pulso disponíveis
export const TiposPulso = Object.freeze([
    'PULSO 1',
    'PULSO 2',
]);

// lista dos tipos de contadores de pulso
export const ListaTiposPulso = [
    { id: 0, descricao: 'PULSO 1'},
    { id: 1, descricao: 'PULSO 2'},
];

// lista dos tipos de medição para o contador de pulso
export const ListaMedicaoPulso = [
    { id: 0, descricao: 'Energia'},
    { id: 1, descricao: 'Gás'},
    { id: 2, descricao: 'Água'},    
    { id: 3, descricao: 'Comgás'},
    { id: 4, descricao: 'Contato'},
    { id: 5, descricao: 'Genérico'},
    
];

// lista dos tipos de medição para o contador de pulso
export const ListaFlowPulso = [
    { id: 0, descricao: 'eflow'},
    { id: 1, descricao: 'gflow'},
    { id: 2, descricao: 'wflow'},    
    { id: 3, descricao: 'comgas'},
    { id: 4, descricao: 'contact'},
    { id: 5, descricao: 'pflow'},    
];

// tipos de medição para contador de pulso
export const TipoMedicaoPulso = Object.freeze({
    ENERGIA: 0,    
    GAS: 1,
    AGUA: 2,
    COMGAS: 3,
    CONTATO: 4, 
    GENERICO: 5,    
});

// lista dos tipos de borda para o contador de pulso
export const ListaContatoPulso = [
    { id: 0, descricao: 'Fechado'},
    { id: 1, descricao: 'Aberto'},    
];

// lista dos tipos de borda para o contador de pulso
export const ListaContactPulso = [
    { id: 0, descricao: 'nc'},
    { id: 1, descricao: 'no'},    
];

// lista tipos de modos de ethernet
export const ListaModosEthernet = [
    { id: 0, descricao: 'IP FIXO'},
    { id: 1, descricao: 'DHCP'},
];

export const INFO_IDLE = 0;
export const INFO_NOT_FINDED = 1;
export const INFO_FINDED = 2;

// informações vindas da probe solicitadas via MQTT
export const InfoProbeMQTT = Object.freeze({
    FIRMWARE: 0,              // firmware atual configurado probe
    DATAHORA: 1,              // data e hora atual da probe
    FIRMWARE_BOOT: 2,         // data de instalação do firmware da probe
    RESETS: 3,                // total de resets da probe
    EXCECOES: 4,              // total de exceções (bug) que ocasionaram resets da probe
    HEAP: 5,                  // total de memória heap livre
    NIVEL_BATERIA: 6,         // nivel de carga da bateria (%)
    TENSAO_BATERIA: 7,        // tensão atual da bateria (v)
    STATUS_TENSAO: 8,         // status de tensão da probe: alimentada pela tensão ou pela bateria
    SINAL: 9,                 // sinal do wi-fi ou gsm        
    
    TIPO_OPERADORA: 10,       // tipo de operadora atual da probe: vivo, tim, claro, oi, nextel, algar, sercomtel
    TIPO_CONEXAO: 11,         // tipo de conexão atual da probe: wifi, bc92, eth, sim8000    
    SISTEMA: 12,              // se esta liberado o menu de configuração do sistema da probe
    REDE: 13,                 // se esta liberado o menu de configuração da rede da probe
    MODBUS: 14,               // se esta liberado o menu de configuração do modbus da probe
    CODI: 15,                 // se esta liberado o menu de configuração do codi da probe
    ION: 16,                  // se esta liberado o menu de configuração do ion da probe
    PULSO: 17,                 // se esta liberado o menu de configuração do contador de pulsos da probe
    OTA: 18,                  // se esta liberado o menu de configuração ota da probe
    IP: 19,                   // ip atual da probe    
    MEMORIA_TOTAL: 20,        // total de memória flash disponivel
    MEMORIA_USADA: 21,        // total de memória flash utilizada
    
    RECONECTADO: 22,          // probe reconectada após solicitação de reset
    FORMATADO: 23,            // memoria da probe formatada após solicitação de format
    MODULO_SCAN: 24,          // escaneia os modulos para verificar a quantidade de tarefas executadas
    MSG_FORMATADA: 25,        // mensagens da probe formatadas após solicitação de format
    LISTA_ARQUIVOS: 26,       // lista os arquivos da probe
    FORMATA_ARQUIVO: 27,      // lista os arquivos da probe

    REDE_WIFI: 28,            // informações da rede ethernet
    REDE_GSM: 29,             // informações da rede ethernet
    REDE_ETHERNET: 30,        // informações da rede ethernet

    REDE_WIFI_SALVA: 31,     // salva as configurações dos parametros da rede wifi
    REDE_ETHERNET_SALVA: 32, // salva as configurações dos parametros da rede ethernet

    MODBUS_STATUS: 33,       // status do módulo modbus
    MODBUS_CONFIG: 34,       // configuração modbus
    MODBUS_RESET: 35,        // reset onfiguração modbus
    MODBUS_ADD_INI: 36,      // adiciona configuração modbus - inicio
    MODBUS_DEVICE: 37,       // adiciona configuração device modbus
    MODBUS_SLAVE: 38,        // adiciona configuração slave do device do modbus
    MODBUS_VARIABLE: 39,     // adiciona configuração das variaveis do slave do device modbus
    MODBUS_MEAN: 40,         // adiciona configuração das variaveis com media do slave do device modbus
    MODBUS_ADD_FIM: 41,      // adiciona configuração modbus - fim
    MODBUS_DATA: 42,         // solicita os dados do slave modbus

    CODI_STATUS: 43,         // status do módulo modbus
    CODI_CONFIG: 44,         // configuração codi
    CODI_END   : 45,         // exclui configuração codi
    CODI_ADD   : 46,         // adiciona configuração codi

    OTA_ROLLBACK: 47,        // retorna a versão anterior do firmware
    OTA_UPDATE  : 48,        // atualiza a vesão do firmware

    ION_STATUS: 49,          // status do módulo ion
    ION_CONFIG: 50,          // configuração ion
    ION_END   : 51,          // exclui configuração ion
    ION_ADD   : 52,          // adiciona configuração ion
    ION_SEARCH: 53,          // busca por ion na rede
    ION_DATA  : 54,          // retorna a coleta de dados

    PULSO_STATUS: 55,        // status do módulo contador de pulsos
    PULSO_CONFIG: 56,        // configuração do contador de pulsos
    PULSO_RESET : 57,        // reset configuração do contador de pulsos
    PULSO_END   : 58,        // exclui configuração do contador de pulsos
    PULSO_ADD   : 59,        // adiciona configuração do contador de pulsos

    KHOMP       : 60,        // informações khomp

    APN_CONFIG  : 61,        // apn's configuradas
    APN_ADD     : 62,        // adiciona apn
    APN_REMOVE  : 63,        // remove apn

    TOTAL: 64,               // numero total de informações 
});