import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearStorage, 
         deleteStorage, 
         getStorageBoolean, 
         getStorageJson, 
         getStorageString, 
         KEY, 
         setStorageBoolean, 
         setStorageJson, 
         setStorageString } from './storages';

const mockGetItem = jest.fn();

jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: mockGetItem,
  clear: jest.fn(),
}));

describe('Storages', () => {

    const mockGetItem = jest.fn();

    jest.mock('@react-native-async-storage/async-storage', () => ({
      setItem: jest.fn(),
      getItem: mockGetItem,
      clear: jest.fn(),
    }));
    

    describe('setStorageJson', () => {

      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });

      it('deve salvar o dado como json no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = { test: 'value' };

        await setStorageJson(key, value);

        expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, JSON.stringify(value));
      });
        
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = { test: 'value' };
        const error = new Error('Test error');
            
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));
            
        console.error = jest.fn();
        
        await setStorageJson(key, value);
        
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageJson', () => {

      it('deve retornar um json quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(JSON.stringify({"test": "value"}));
    
        const result = await getStorageJson('key');
        expect(result).toStrictEqual({"test": "value"});
      });

      it('deve retornar um null', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(null);
    
        const result = await getStorageJson('key');
        expect(result).toBe(null);
      });      

      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });
    
        const result = await getStorageJson('key');
        expect(result).toBe('');
      });          
    });      
    
  
    describe('setStorageString', () => {

      beforeEach(() => {
        jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });

      it('deve salvar o dado como string no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = 'value';

        await setStorageString(key, value);

        expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, value);
      }); 
        
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = 'value';
        const error = new Error('Test error');
            
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));
            
        console.error = jest.fn();
        
        await setStorageString(key, value);
        
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageString', () => {

      beforeEach(() => {
          jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });
    
      it('deve retornar uma string quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue('value');
    
        const result = await getStorageString('key');
        expect(result).toBe('value');
      });
      
      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });
    
        const result = await getStorageString('key');
        expect(result).toBe('');
      });    
    });

    describe('setStorageBoolean', () => {

      it('deve salvar um valor booleano no AsyncStorage', async () => {
        const key = KEY.mockJest;
        const value = true;          
        await setStorageBoolean(key, value);          
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(key, value.toString());
      });
          
      it('deve tratar o erro quando salva no AsyncStorage', async () => {
        const key = 'testKey';
        const value = false;
        const error = new Error('Test error');
              
        AsyncStorage.setItem = jest.fn(() => Promise.reject(error));
              
        console.error = jest.fn();
          
        await setStorageBoolean(key, value);
          
        expect(console.error).toHaveBeenCalledWith(`Erro ao salvar ${key}: `, error);
      })        
    });

    describe('getStorageBoolean ', () => {

      beforeEach(() => {
          jest.clearAllMocks(); // Limpa todos os mocks antes de cada teste
      });
    
      it('deve retornar uma string quando não há erro', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(true);
    
        const result = await getStorageBoolean ('key');
        expect(result).toBe(true);
      });
      
      it('deve retornar um null', async () => {
        // Mockar o retorno do AsyncStorage
        AsyncStorage.getItem = jest.fn().mockResolvedValue(null);
    
        const result = await getStorageBoolean('key');
        expect(result).toBe(null);
      }); 

      it('deve retornar uma string vazia quando há uma exceção', async () => {
        // Mockar um erro no AsyncStorage
        AsyncStorage.getItem = jest.fn().mockImplementation(() => {
          throw new Error('error');
        });
    
        const result = await getStorageBoolean ('key');
        expect(result).toBe(false);
      });    
    });

  
    describe('deleteStorage', () => {
      afterEach(() => {
        jest.clearAllMocks();
      });
              
      it('deve registrar um erro se a remoção falhar', async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error');
        const key = 'testKey';
        const error = new Error('Erro ao remover item');            
        const value = 'value';

        await setStorageString(key, value);

        AsyncStorage.removeItem = jest.fn(() => Promise.reject(error));
      
        await deleteStorage(key);

        expect(consoleErrorSpy).toHaveBeenCalledWith(`Erro ao remover ${key}: `, error);
      });
    });    

    describe('clearStorage', () => {
        
      beforeEach(() => {
        // Limpa todos os mocks antes de cada teste
        jest.clearAllMocks();
      });
      
      it('deve chamar AsyncStorage.clear uma vez', async () => {
        await clearStorage();
        expect(AsyncStorage.clear).toHaveBeenCalledTimes(1);
      });
      
      it('deve lidar com erros e chamar console.error', async () => {
        const error = new Error('Erro ao limpar os dados');
        
        AsyncStorage.clear = jest.fn(() => Promise.reject(error));
          
        console.error = jest.fn(); // Mock do console.error
      
        await clearStorage();
        expect(console.error).toHaveBeenCalledWith('Erro ao limpar os dados', error);
      });
    });    
  });