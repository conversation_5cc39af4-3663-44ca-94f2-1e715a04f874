import { CommonActions, useNavigation } from '@react-navigation/native';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import WebView, { WebViewNavigation } from 'react-native-webview';

// modal messages
import MessageBoxErro from '../modal/messagebox/Erro';

// storage
import { getStorageString, KEY, setStorageJson, setStorageString } from '../storages';

// componentes
import Loading from './Loading';

// data

// stack routes
import { StackTypes } from '../routes';

// pages
import { COGNITO_CLIENT_ID_PROD, COGNITO_CLIENT_SECRET_PROD } from '@env';
import { InternetOff } from '../pages/InternetOff';
import Logout from '../pages/Logout';

type WebViewSigninProps = {
  readonly url: string;
  readonly hasInternet: boolean;
};

export function WebViewSignin({ url, hasInternet }: WebViewSigninProps) {

  let access_token = '';

  // navegação entre telas
  const navigation = useNavigation<StackTypes>();

  const [showWebView, setShowWebView] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);
  const [textLoading, setTextLoading] = useState<string>('');

  const [showMessageErro, setShowMessageErro] = useState<boolean>(false);
  const [textoMessage, setTextoMessage] = useState<string>('Erro');

  /* navega para a pagina de primeiros passos */
  const goToPrimeirosPassos = () => {

    navigation.dispatch(
      CommonActions.navigate({
        name: 'PrimeirosPassos',
      })
    );
  };

  // navega para a pagina de sem acesso
  const goToSemAcesso = () => {

    navigation.dispatch(
      CommonActions.navigate({
        name: 'SemAcesso',
      })
    );
  };

  // navega para pagina de erro de autenticação
  const goToErroAuth = (status: string) => {

    // fecha a pagina de configuração da probe
    navigation.dispatch(
      CommonActions.navigate({
        name: 'ErroAuth',
        params: { erro: status },
      })
    );
  };

  // trata o estado de navegação de login do cognito
  const handleNavigationStateChange = async (event: WebViewNavigation) => {

    // se pagina ainda não carregada
    if (event.loading) {
      setLoading(true);
      return;
    }

    setLoading(false);

    // se existe url
    if (event.url.includes('callback/cognito?code=')) {

      setShowWebView(false);
      setLoading(true);
      setTextLoading("Autenticando...");
      
      const urlParams = new URLSearchParams(event.url.split('?')[1]);
      const code = urlParams.get('code');

      // Cancela o carregamento da URL imediatamente
      if (code) {

        // trata a troca do code pelo token
        handleAuthorizationCodeExchange(code);

      } else {

        setLoading(false);

        setTextoMessage('Código de autorização não encontrado na URL.');
        setShowMessageErro(true);

        // leva para o logout
        Logout();
      }
    }
  };

  // trata autorização do cognito depois de receber o code
  async function handleAuthorizationCodeExchange(code_comerc: string) {
    
    try {

      // Realiza a troca do código por tokens
      const res = await axios.post(
        'https://comerc-auth-api.comerc.com.br/api/authorization-code/exchange',
        {
          code: code_comerc,
          client_id: COGNITO_CLIENT_ID_PROD,
          client_secret: COGNITO_CLIENT_SECRET_PROD,
        },
        {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Content-Type': 'application/json'
          }
        }
      );

      // pega o token
      access_token = res.data.access_token;

      // Armazena o token do dispositivo e realiza o login do usuário
      setStorageString(KEY.cognitoToken, access_token);

      // pega as informações do usuário
      handleUserCredencials(access_token);

    } catch (error) {

      // finaliza loading
      setLoading(false);

      if (axios.isAxiosError(error)) {
        goToErroAuth(error.message);
      } else {
        // Manipulação de outros tipos de erro
        goToErroAuth("Erro desconhecido");
      }

      return;
    }

  }

  // pega as informaçõe do usuario
  async function handleUserCredencials(token_comerc: string) {

    try {

      // Realiza a troca do código por tokens
      const res = await axios.get('https://comerc-auth-api.comerc.com.br/api/users/me',
        {
          headers: {
            'Authorization': `Bearer ${token_comerc}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Content-Type': 'application/json'
          }
        }
      );

      /* se status OK */
      if (res.status === 200) {
        
        /* pega as informações de resposta do cognito */
        setStorageJson(KEY.cognitoResponse, JSON.stringify(res.data));

        /* pega a resposta do cognito */
        const response = JSON.parse(JSON.stringify(res.data))

        /* armazena o nome do usuario */
        setStorageString(KEY.cognitoUser, response.data.name);        
        
        /* armazena o email do usuario */
        setStorageString(KEY.cognitoEmail, response.data.email);

        

        /* encerra o loading */
        setLoading(false);

        /* verifica se usuário ativo */
        if (response.data.enabled && response.data.groups.includes("zordon")) {

          /* navega para página de primeiros passos */
          goToPrimeirosPassos();
        }
        else {

          /* limpa o token */
          setStorageString(KEY.cognitoToken, '');

          /* navega para a pagina de sem acesso */
          goToSemAcesso();
        }
      }
      else {

        /* limpa o token armazenado */
        setStorageString(KEY.cognitoToken, '');

        /* finaliza o loading */
        setLoading(false);

        /* navega para a pagina de autenticação */
        goToErroAuth(res.status.toString());
      }

    } catch (error) {
      
      /* limpa o token */
      setStorageString(KEY.cognitoToken, '');

      /* finaliza loading */
      setLoading(false);

      /* navega para a pagina de erro do cognito */
      setShowWebView(true);
    }
  }

  /**
   * verifica o token armazenado no mobile,
   * se existir token, é direcionado direto para autenticação
   * caso não exista, direciona para a tela de login
   * @returns 
   */
  const verificaTokenStorage = async () => {
    try {
      const token_comerc = await getStorageString(KEY.cognitoToken);

      if (!token_comerc) {
        setShowWebView(true);
        return;
      }

      setLoading(true);

      setTextLoading("Autenticando...");
      handleUserCredencials(token_comerc);
    } catch (error) {
      setShowWebView(true);
    }
  };


  /**
   * ao entrar verifica se existe token válido
   */
  useEffect(() => {

    /* se possui internet */
    if (!hasInternet) 
      return;

    /* verifica o token armazenado no mobile */
    verificaTokenStorage();

  }, []);


  /**
   * renderiza componente WebView
   */
  const renderWebViewSignin =
    showWebView
    ?
      <WebView
        testID="webview-signin"
        source={{ uri: url }}
        originWhitelist={['*']}
        onNavigationStateChange={handleNavigationStateChange}        
      />
    :
      <View style={{ flex: 1, backgroundColor: 'white' }} />
  ;

  return (

    <>

      <Loading animating={loading} text={textLoading} />

      <MessageBoxErro
        visivel={showMessageErro}
        titulo="Atenção"
        descricao={textoMessage}
        textoBotao="OK"
        onFechar={() => setShowMessageErro(false)}
      />

      <View style={{ flex: 1 }}>
        {
          (!hasInternet) ? <InternetOff /> : renderWebViewSignin
        }
      </View>

    </>

  );
};
