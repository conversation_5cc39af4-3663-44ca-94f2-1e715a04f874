import { render } from '@testing-library/react-native';
import React from 'react';
import LoadingProgressBar from './LoadingProgressBar';

describe('LoadingProgressBar', () => {

  it('não renderiza quando não passado nenhum parâmetro', () => {
    const { getByTestId } = render(
      <LoadingProgressBar />
    );
  });

  it('renderiza o modal quando o animating is true', () => {
    const { getByTestId } = render(
      <LoadingProgressBar animating={true} />
    );
    expect(getByTestId('modal')).toBeTruthy();
  });

  it('não renderiza o modal quando o animating é falso', () => {
    const { queryByTestId } = render(
      <LoadingProgressBar animating={false} />
    );
    expect(queryByTestId('modal')).toBeNull();
  });

  it('renderiza o activity indicator quando animating is true', () => {
    const { getByTestId } = render(
      <LoadingProgressBar animating={true} />
    );
    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('renderiza o texto corretamente', () => {
    const { getByText } = render(
      <LoadingProgressBar animating={true} text="Loading data..." />
    );
    expect(getByText('Loading data...')).toBeTruthy();
  });

  it('renderiza o primeiro progresso quando showProgressBar1 é true', () => {
    const { getByText } = render(
      <LoadingProgressBar animating={true} showProgressBar1={true} textProgress1="Progress 1" />
    );
    expect(getByText('Progress 1')).toBeTruthy();
  });

  it('renderiza o segundo progresso quando showProgressBar2 é true', () => {
    const { getByText } = render(
      <LoadingProgressBar animating={true} showProgressBar2={true} textProgress2="Progress 2" />
    );
    expect(getByText('Progress 2')).toBeTruthy();
  });

  it('renderiza o terceiro progresso quando showProgressBar3 é true', () => {
    const { getByText } = render(
      <LoadingProgressBar animating={true} showProgressBar3={true} textProgress3="Progress 3" />
    );
    expect(getByText('Progress 3')).toBeTruthy();
  });

  it('renderiza o quarto progresso quando showProgressBar4 é true', () => {
    const { getByText } = render(
      <LoadingProgressBar animating={true} showProgressBar4={true} textProgress4="Progress 4" />
    );
    expect(getByText('Progress 4')).toBeTruthy();
  });
});
