import React from 'react';
import { ColorValue, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones svg
import IconFile from '../assets/svg/icon_file-06.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

interface ListFilesProps {
    timer?: string;
    name?: string;
    size?: string;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    onDelete?: (value: any) => void;
}

const ListFiles: React.FC<ListFilesProps> = ({
    timer = '01/01/2000 00:00',
    name = 'file',
    size = '0 bytes',
    height = 120,
    width = '100%',
    backGroundColor = 'transparent',
    onDelete,
}) => {

    return (

        <View style={{
            height: height,
            width: width,
            backgroundColor: backGroundColor,
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
        }}>

            <View style={{
                height: 80, width: '80%', borderRadius: 12,
                borderWidth: 1, paddingHorizontal: 20, justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 10
            }}>

                <View style={{ height: 30, width: 30, borderRadius: 10, justifyContent: 'center', alignItems: 'center' }}>
                    <IconFile color={"#737373"} width={20} height={20} />
                </View>

                <View style={{ width: '80%' }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text style={styles.textoTitle}>
                            {`${timer}`}
                        </Text>
                        <Text style={styles.textoValueID}>
                            {`${size} bytes`}
                        </Text>
                    </View>
                    <Text style={styles.textoValue}>
                        {`${name}`}
                    </Text>
                </View>

            </View>

            <View style={{ height: 80, width: '20%', justifyContent: 'center', alignItems: 'flex-end' }}>
                <TouchableOpacity style={{ height: 56, width: 56, justifyContent: 'center', alignItems: 'center', borderRadius: 12, borderColor: '#C0002B', backgroundColor: '#C0002B' }}
                    onPress={onDelete}
                    testID="delete-button">
                    <IconTrash color={"#FFFFFF"} width={28} height={28} />
                </TouchableOpacity>
            </View>

        </View>
    )
}

export default ListFiles;

const styles = StyleSheet.create({

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        color: '#525252',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
    textoTitleKE: {
        fontFamily: 'SourceSans3_600SemiBold',
        color: '#2E8C1F',
        fontSize: 11,
    },
    textoValueKE: {
        fontFamily: 'Exo2_600SemiBold',
        color: '#2E8C1F',
        fontSize: 14,
    },

    textoValueID: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 11,
    },

    linhaHorizontal: {
        borderBottomColor: '#D6D6D6',
        borderBottomWidth: StyleSheet.hairlineWidth,
    },
});
