import React, { useState } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones vetoriais
import IconDown from '../assets/svg/icon_chevron-down.svg';
import IconRight from '../assets/svg/icon_chevron-right.svg';
import IconCopy from '../assets/svg/icon_copy.svg';
import IconData from '../assets/svg/icon_data.svg';
import IconEdit from '../assets/svg/icon_edit.svg';
import IconIndicator from '../assets/svg/icon_indicator.svg';
import IconPlus from '../assets/svg/icon_plus.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

// dados
import { SlavesSettings } from '../data';
import Space from './Space';

interface CardProbeProps {
    device?: string;
    slaves?: SlavesSettings[];
    height?: any;
    width?: any;
    onPressDelDevice?: (value: any) => void;
    onPressAddSlave?: (value: any) => void;
    onPressCopySlave?: (value: any) => void;
    onPressEditSlave?: (value: any) => void;
    onPressDelSlave?: (value: any) => void;
    onPressDataSlave?: (value: any) => void;
    onSlaveCurrent?: (value: number) => void;
}

const CardProbe: React.FC<CardProbeProps> = ({
        device = 'Probe',
        slaves = [], 
        height = 60,
        width = '100%',
        onPressDelDevice,
        onPressAddSlave,
        onPressCopySlave,
        onPressEditSlave,
        onPressDelSlave, 
        onPressDataSlave,       
        onSlaveCurrent,
    
   }) => {    

    // se deve mostrar os slaves
    const [showSlave, setShowSlave] = useState<boolean>(true);
    const [showButtonsSlave, setShowButtonsSlave] = useState<boolean>(false);

    // trata a exclusão do slave (medição) selecionada
    const handleDelSlave = (item: number) => {
        onSlaveCurrent?.(slaves.findIndex(slave => slave.id_slave === item));
        if (onPressDelSlave) {
            onPressDelSlave(item);
        }
    };

    // trata a edição do slave (medição) selecionada
    const handleEditSlave = (item: number) => {
        onSlaveCurrent?.(slaves.findIndex(slave => slave.id_slave === item));
        if (onPressEditSlave) {
            onPressEditSlave(item);
        }
    };

    // trata a solicitação de dados do slave (medição) selecionada
    const handleDataSlave = (item: number) => {
        onSlaveCurrent?.(slaves.findIndex(slave => slave.id_slave === item));
        if (onPressDataSlave) {
            onPressDataSlave(item);
        }
    };

    return (

        <View style={{ ...styles.container, width: width }}>            

            <View style={{flexDirection:'row', justifyContent:'space-between'}}>
                            
                <View style={styles.containerDevice}>
                    <TouchableOpacity style={{ height: 60, width: 25, justifyContent: 'center', alignItems: 'center' }}
                        onPress={() => setShowSlave(!showSlave)}
                        testID='show-toggle-slaves'>
                        {
                            (showSlave)
                                ? <IconDown color={"#FFFFFF"} width={20} height={20} />
                                : <IconRight color={"#FFFFFF"} width={20} height={20} />
                        }
                    </TouchableOpacity>
                    <Text style={styles.textoDevice}>{device}</Text>
                </View>
                <TouchableOpacity style={{height:60, width:60, borderRadius:10, justifyContent:'center', alignItems:'center', backgroundColor:'#C0002B'}}
                                  onPress={onPressDelDevice}
                                  testID='delete-device-button'>
                    <IconTrash color={"#FFFFFF"} width={25} height={25}/>
                </TouchableOpacity>                

            </View>
            
            
            {
                (showSlave) &&
                    <View>
                                                
                        <FlatList style={{marginTop: 15}}
                            ItemSeparatorComponent={Space}              
                            data = {slaves}
                            renderItem={ ({ item }) => 

                                <View style = {{height:(showButtonsSlave) ? 110 : 50, width: '100%', flexDirection:'row', gap:10}}>
                                                                        
                                    <IconIndicator color={"#FFFFFF"} width={20} height={20}/>
                               
                                    <View style={{...styles.containerSlave, height:(showButtonsSlave) ? 110 : 50}}>

                                        <View style={styles.containerSlaveInfo}>
                                            <View style={{height:'100%', width:'100%'}}>
                                                <Text style={styles.textoSlaveNome}>{item.nomeSlave}</Text>
                                                <Text style={styles.textoSlaveEnd}>{`End: ${item.enderecoSlave}`}</Text>
                                            </View>
                                            <TouchableOpacity   style={{height:'100%', width:25, justifyContent:'center', alignItems:'center'}} 
                                                                onPress={() => setShowButtonsSlave(!showButtonsSlave)}
                                                                testID='show-toggle-buttons'>
                                                                {
                                                                    (showButtonsSlave) 
                                                                        ? <IconDown color={"#2E8C1F"} width={20} height={20}/>
                                                                        : <IconRight color={"#2E8C1F"} width={20} height={20}/>
                                                                }
                                            </TouchableOpacity>
                                        </View>
                                        
                                        {
                                            (showButtonsSlave) &&

                                                <View style={{height:60, width:'100%', flexDirection:'row', alignContent:'flex-start', justifyContent:'flex-end', gap: 20, paddingHorizontal:10}}>
                                                    
                                                    <TouchableOpacity style={{...styles.botaoOption, backgroundColor:'#2E8C1F'}}
                                                                      onPress={() => item.id_slave !== undefined && handleDataSlave(item.id_slave)}
                                                                      testID={`data-slave-button-${item.id_slave}`}>
                                                        <IconData color={"#FFFFFF"} width={25} height={25}/>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity style={{...styles.botaoOption, backgroundColor:'#737373'}}
                                                                      onPress={() => item.id_slave !== undefined && handleEditSlave(item.id_slave)}
                                                                      testID={`edit-slave-button-${item.id_slave}`}>
                                                        <IconEdit color={"#FFFFFF"} width={25} height={25}/>
                                                    </TouchableOpacity>                                                        
                                                    <TouchableOpacity style={{...styles.botaoOption, backgroundColor:'#C0002B'}}
                                                                      onPress={() => item.id_slave !== undefined && handleDelSlave(item.id_slave)}
                                                                      testID={`delete-slave-button-${item.id_slave}`}>
                                                        <IconTrash color={"#FFFFFF"} width={25} height={25}/>
                                                    </TouchableOpacity>                                                    

                                                </View>
                                        }
                                        
                                    </View>                                                                    

                                </View>
                            }                        
                        />
                        <View style = {{height:50, width: '100%', marginTop: 15, flexDirection:'row', gap:10}}>
                            <IconIndicator color={"#FFFFFF"} width={20} height={20}/>
                            <TouchableOpacity style={{...styles.botaoOption, backgroundColor:'#C2F0BA'}}
                                              onPress={onPressAddSlave}
                                              testID='add-slave-button'>
                                <IconPlus color={"#2E8C1F"} width={25} height={25}/>
                            </TouchableOpacity>
                            
                            {
                                (slaves?.length > 0) &&
                                    <TouchableOpacity style={{...styles.botaoOption, backgroundColor:'#C2F0BA'}}
                                        onPress={onPressCopySlave}
                                        testID='copy-slave-button'>
                                        <IconCopy color={"#2E8C1F"} width={25} height={25}/>
                                    </TouchableOpacity>
                            }
                        </View>
                    </View>
                
            }
                    
        </View>
    )
}

export default CardProbe;

const styles = StyleSheet.create({

    container: {
        flex: 1
    },

    containerDevice: {
        height: 60,
        width: '80%',
        flexDirection: 'row',
        paddingHorizontal: 20,
        borderRadius: 10,
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#2E8C1F',
        gap: 10
    },

    containerSlave: {
        height: 50,
        width: '90%',
        borderRadius: 8,
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        backgroundColor: '#C2F0BA'
    },

    containerSlaveInfo: {
        height: 50,
        width: '92%',
        paddingHorizontal: 15,
        paddingVertical: 6,
        borderRadius: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        backgroundColor: '#C2F0BA'
    },

    botaoOption: {
        height: 50,
        width: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },

    textoDevice: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,
        color: '#FFFFFF',
    },

    textoSlaveNome: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 14,
        color: '#2E8C1F',
    },

    textoSlaveEnd: {
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 12,
        color: '#2E8C1F',
    },

});
