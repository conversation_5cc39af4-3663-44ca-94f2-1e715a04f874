import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones vetoriais
import IconEdit from '../assets/svg/icon_edit.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

interface CardPulsoProps {
    pulso?: string;
    height?: any;
    width?: any;
    onPressDelPulso?: (value: any) => void;
    onPressEditPulso?: (value: any) => void;
    onPulsoCurrent?: (value: number) => void;
}

const CardPulso: React.FC<CardPulsoProps> = ({
    pulso = '',
    height = 60,
    width = '100%',
    onPressDelPulso,
    onPressEditPulso,
    onPulsoCurrent,

}) => {

    return (

        <View style={{ ...styles.container, width: width }} testID="view-principal">

            {
                (pulso.length > 0)
                    ?
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 5 }}>

                        <View style={styles.containerPulso}>
                            <Text style={styles.textoPulso}>{pulso}</Text>
                        </View>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#C0002B' }}
                            onPress={onPressDelPulso}
                            testID="delete-button">
                            <IconTrash color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#737373' }}
                            onPress={onPressEditPulso}
                            testID="edit-button">
                            <IconEdit color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                    </View>
                    : null
            }

        </View>
    )
}

export default CardPulso;

const styles = StyleSheet.create({

    container: {
        flex: 1
    },

    containerPulso: {
        height: 60,
        width: '60%',
        flexDirection: 'row',
        paddingHorizontal: 20,
        borderRadius: 10,
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#2E8C1F',
        gap: 10
    },

    textoPulso: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,
        color: '#FFFFFF',
    },
});
