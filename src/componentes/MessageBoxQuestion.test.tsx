import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import MessageBoxQuestion from './MessageBoxQuestion';


describe('MessageBoxQuestion Component', () => {
  it('deve renderizar o título e a descrição corretamente', () => {
    const { getByText } = render(
      <MessageBoxQuestion title="Teste Título" description="Teste Descrição" onConfirm={jest.fn()} onCancel={jest.fn()} />
    );

    expect(getByText('Teste Título')).toBeTruthy();
    expect(getByText('Teste Descrição')).toBeTruthy();
  });

  it('deve chamar a função onConfirm quando o botão de confirmar é pressionado', () => {
    const onConfirmMock = jest.fn();
    const { getByText } = render(
      <MessageBoxQuestion textYes="Confirmar" onConfirm={onConfirmMock} onCancel={jest.fn()} />
    );

    fireEvent.press(getByText('Confirmar'));
    expect(onConfirmMock).toHaveBeenCalledTimes(1);
  });

  it('deve chamar a função onCancel quando o botão de cancelar é pressionado', () => {
    const onCancelMock = jest.fn();
    const { getByText } = render(
      <MessageBoxQuestion textNo="Cancelar" onConfirm={jest.fn()} onCancel={onCancelMock} />
    );

    fireEvent.press(getByText('Cancelar'));
    expect(onCancelMock).toHaveBeenCalledTimes(1);
  });
});