import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';
import IconClock from '../assets/svg/icon_clock.svg';
import { DataTimeStamp, HoraTimeStamp } from '../funcoes';

interface StatusDateTimeProps {
    value?: number;
    vBatteryColor?: ColorValue;
    iconVBatteryColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    loading?: boolean;
}

const StatusDateTime: React.FC<StatusDateTimeProps> = ({
    value = 0,
    vBatteryColor = '#F5F5F5',
    iconVBatteryColor = '#C0002B',
    height = 100,
    width = 150,
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    loading = false,
}) => {

    // pega a data
    const [data, setData] = useState('01/01/2000');

    // pega a data    
    const [hora, setHora] = useState('00:00');

    useEffect(() => {

        if (value === 0) {
            setData('01/01/2000');
            setHora('00:00');
        }
        else {
            setData(DataTimeStamp(value));
            setHora(HoraTimeStamp(value, false));
        }

    }, [value]);

    return (

        <View style={{
            height: 104,
            width: width,
            paddingHorizontal: 20,
            paddingVertical: 24,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        }} testID='status-datetime-container'>

            <View style={{ height: 56, width: '100%', justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                {
                    (loading)
                        ?
                        <View style={{ height: 48, width: 48, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} testID='activity-indicator' />
                        </View>
                        :
                        <View style={{ height: 48, width: 48, borderRadius: 9999, backgroundColor: vBatteryColor, justifyContent: 'center', alignItems: 'center' }}>
                            <IconClock color={"#737373"} width={20} height={20} />
                        </View>
                }

                <View >
                    <Text style={styles.textoTitle} testID='text_data'>
                        {data}
                    </Text>
                    <Text style={styles.textoValue} testID='text_hora'>
                        {`${hora}`}
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default StatusDateTime;

const styles = StyleSheet.create({

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
});
