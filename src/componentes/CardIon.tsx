import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones vetoriais
import IconData from '../assets/svg/icon_data.svg';
import IconEdit from '../assets/svg/icon_edit.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

interface CardIonProps {
    ion?: string;
    id?: number;
    height?: any;
    width?: any;
    onPressDelIon?: (value: any) => void;
    onPressEditIon?: (value: any) => void;
    onPressDataIon?: (value: any) => void;
    onIonCurrent?: (value: number) => void;
}

const CardIon: React.FC<CardIonProps> = ({
    ion = '',
    id = 0,
    height = 60,
    width = '100%',
    onPressDelIon,
    onPressEditIon,
    onPressDataIon,
    onIonCurrent,
}) => {

    return (

        <View style={{ ...styles.container, width: width }} testID="view-principal">

            {
                // se existe ion
                (ion.length > 0)
                    ?
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 5 }}>

                        <View style={styles.containerIon}>
                            <Text style={styles.textoIon}>{ion}</Text>
                            <Text style={styles.textoId}>{`ID: ${id}`}</Text>
                        </View>

                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#C0002B' }}
                            onPress={onPressDelIon}
                            testID="delete-button">
                            <IconTrash color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#737373' }}
                            onPress={onPressEditIon}
                            testID="edit-button">
                            <IconEdit color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#2E8C1F' }}
                            onPress={onPressDataIon}
                            testID="data-button">
                            <IconData color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>                        
                    </View>
                    : null
            }

        </View>
    )
}

export default CardIon;

const styles = StyleSheet.create({

    container: {
        flex: 1
    },

    containerIon: {
        height: 60,
        width: '45%',
        flexDirection: 'row',
        paddingHorizontal: 20,
        borderRadius: 10,
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#2E8C1F',
        gap: 10
    },

    textoIon: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,
        color: '#FFFFFF',
    },

    textoId: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        color: '#FFFFFF',
    },
});
