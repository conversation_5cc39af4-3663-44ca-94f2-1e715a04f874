import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { mockSlavesSettings } from '../../__mocks__/SlavesSettingsMock';
import CardProbe from './CardProbe';

describe('CardProbe Component', () => {
  const mockHandlers = {
    onPressDelDevice: jest.fn(),
    onPressAddSlave: jest.fn(),
    onPressCopySlave: jest.fn(),
    onPressEditSlave: jest.fn(),
    onPressDelSlave: jest.fn(),
    onPressDataSlave: jest.fn(),
    onSlaveCurrent: jest.fn(),
  };

  it('deve alternar o estado do toggle para apresentação dos slaves', () => {
    const { getByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );
    const toggleButton = getByTestId('show-toggle-slaves');
    fireEvent.press(toggleButton);
    expect(mockHandlers.onSlaveCurrent).not.toHaveBeenCalled();
  });

  it('deve chamar onPressDelDevice quando botão delete do device é pressionado', () => {
    const { getByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );
    const deleteButton = getByTestId('delete-device-button');
    fireEvent.press(deleteButton);
    expect(mockHandlers.onPressDelDevice).toHaveBeenCalled();
  });

  it('deve chamar onPressEditSlave quando botão edit do slave é pressionado', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const editSlaveButton = getByTestId('edit-slave-button-1');
    fireEvent.press(editSlaveButton);
    expect(mockHandlers.onPressEditSlave).toHaveBeenCalledWith(1);
  });

  it('deve chamar onPressDelSlave quando botão delete do slave é pressionado', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const deleteSlaveButton = getByTestId('delete-slave-button-1');
    fireEvent.press(deleteSlaveButton);
    expect(mockHandlers.onPressDelSlave).toHaveBeenCalledWith(1);
  });

  it('deve chamar onPressDataSlave quando botão data do slave é pressionado', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const dataSlaveButton = getByTestId('data-slave-button-1');
    fireEvent.press(dataSlaveButton);
    expect(mockHandlers.onPressDelSlave).toHaveBeenCalledWith(1);
  });

  test('não deve gerar erro quando onPressDelSlave não estiver definido', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} onPressDelSlave={undefined} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const deleteSlaveButton = getByTestId('delete-slave-button-1');
    expect(() => fireEvent.press(deleteSlaveButton)).not.toThrow();

  });

  test('não deve gerar erro quando onPressEditSlave não estiver definido', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} onPressEditSlave={undefined} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const editSlaveButton = getByTestId('edit-slave-button-1');
    expect(() => fireEvent.press(editSlaveButton)).not.toThrow();

  });

  test('não deve gerar erro quando onPressDataSlave não estiver definido', () => {
    const { getByTestId, getAllByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} onPressEditSlave={undefined} />
    );

    const toggleButtons = getAllByTestId('show-toggle-buttons');
    fireEvent.press(toggleButtons[0]);

    const editSlaveButton = getByTestId('data-slave-button-1');
    expect(() => fireEvent.press(editSlaveButton)).not.toThrow();

  });

  it('deve chamar onPressAddSlave quando botão add do slave é pressionado', () => {
    const { getByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );
    const addSlaveButton = getByTestId('add-slave-button');
    fireEvent.press(addSlaveButton);
    expect(mockHandlers.onPressAddSlave).toHaveBeenCalled();
  });

  it('deve chamar onPressCopySlave quando botão add do slave é pressionado', () => {
    const { getByTestId } = render(
      <CardProbe slaves={mockSlavesSettings} {...mockHandlers} />
    );
    const copylaveButton = getByTestId('copy-slave-button');
    fireEvent.press(copylaveButton);
    expect(mockHandlers.onPressCopySlave).toHaveBeenCalled();
  });  

  it('deve renderizar corretamente quando a propriedade slaves[] não é passada', () => {
    const { getByText, queryAllByTestId } = render(
      <CardProbe {...mockHandlers} />
    );

    // Verifica se nenhum elemento com testID edit-slave-button ou delete-slave-button está presente
    expect(queryAllByTestId(/^edit-slave-button-/).length).toBe(0);
    expect(queryAllByTestId(/^delete-slave-button-/).length).toBe(0);

    // Verifica se o texto do dispositivo está renderizado corretamente
    const deviceText = getByText('Probe');
    expect(deviceText).toBeTruthy();
  });
});