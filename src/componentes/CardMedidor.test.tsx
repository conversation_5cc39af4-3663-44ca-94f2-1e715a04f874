import { render } from '@testing-library/react-native';
import React from 'react';
import CardMedidor from './CardMedidor';

describe('CardMedidor Component', () => {
    it('deve renderizar o componente corretamente com os valores padrão', () => {
        const { getByText } = render(<CardMedidor />);
        const defaultTitle = getByText('Descrição');
        const defaultValue = getByText('Medidor');
        const defaultID = getByText('000');
        const defaultKe = getByText('Ke');
        expect(defaultTitle).toBeTruthy();
        expect(defaultValue).toBeTruthy();
        expect(defaultID).toBeTruthy();
        expect(defaultKe).toBeTruthy();
    });

    it('deve renderizar valores personalizados', () => {
        const { getByText } = render(
            <CardMedidor
                title="Meu Medidor"
                value="123456"
                valueID="789"
                valueKE="99"
                titleKE="Nova Constante" />
        );
        const customTitle = getByText('Meu Medidor');
        const customValue = getByText('123456');
        const customID = getByText('789');
        const customKe = getByText('99');
        const customTitleKe = getByText('Nova Constante');
        expect(customTitle).toBeTruthy();
        expect(customValue).toBeTruthy();
        expect(customID).toBeTruthy();
        expect(customKe).toBeTruthy();
        expect(customTitleKe).toBeTruthy();
    });
});
