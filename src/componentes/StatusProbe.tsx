import React from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';

// arquivos .svg
import IconBatteryEmpty from '../assets/svg/icon_solid-battery-empty.svg';
import IconBatteryFull from '../assets/svg/icon_solid-battery-full.svg';
import IconBatteryLow from '../assets/svg/icon_solid-battery-low.svg';
import IconBatteryMedium from '../assets/svg/icon_solid-battery-medium.svg';
import IconEthernet from '../assets/svg/icon_solid-ethernet.svg';
import IconGSMFull from '../assets/svg/icon_solid-gsm-full.svg';
import IconGSMLow from '../assets/svg/icon_solid-gsm-low.svg';
import IconGSMMedium from '../assets/svg/icon_solid-gsm-medium.svg';
import IconWiFiFull from '../assets/svg/icon_solid-wifi-full.svg';
import IconWiFiLow from '../assets/svg/icon_solid-wifi-low.svg';
import IconWiFiMedium from '../assets/svg/icon_solid-wifi-medium.svg';
import IconPlug from '../assets/svg/icon_solid_plug.svg';
import IconGSM from '../assets/svg/icon_solid_simcard.svg';
import LogoNBIoT from '../assets/svg/logo_nb_iot.svg';
import LogoWiFi from '../assets/svg/logo_wifi.svg';

// componenentes
import Divider from '../componentes/Divider';

// constantes
import { DescricaoOperadora, TipoConexao } from '../constantes';

// funções
import { getPercentualSinal } from '../funcoes';

interface StatusProbeProps {
    conectado?: boolean;
    tipoConexao?: number;
    tipoOperadora?: number;
    ip?: string;
    sinal?: number;
    nivelBateria?: number;
    tensaoProbe?: number;
    height?: any;
    width?: any;
    colorLoading?: ColorValue;
}

// apresenta o sinal do Wifi
const SinalWiFi = (esta_conectado: boolean, sinal_atual: number, icon_size: number, color_low: ColorValue, color_medium: ColorValue, color_full: ColorValue) => {

    if (!esta_conectado) {
        return null;
    }

    // sinal fraco
    if (sinal_atual <= -71) {
        return (
            <>
                <IconWiFiLow height={icon_size} width={icon_size} color={color_low} testID='icon-wifi-low' />
                <Text style={{ ...styles.textoStatus, color: color_low }}>{`${getPercentualSinal(sinal_atual, -90, 0).toFixed(0)}%`}</Text>
            </>
        )
    }

    // sinal bom
    if (sinal_atual > -70 && sinal_atual <= -51) {
        return (
            <>
                <IconWiFiMedium height={icon_size} width={icon_size} color={color_medium} testID='icon-wifi-medium' />
                <Text style={{ ...styles.textoStatus, color: color_medium }}>{`${getPercentualSinal(sinal_atual, -90, 0).toFixed(0)}%`}</Text>
            </>
        )
    }

    // sinal forte
    return (
        <>
            <IconWiFiFull height={icon_size} width={icon_size} color={color_full} testID='icon-wifi-full' />
            <Text style={{ ...styles.textoStatus, color: color_full }}>{`${getPercentualSinal(sinal_atual, -90, 0).toFixed(0)}%`}</Text>
        </>
    )
}

// apresenta o sinal do Wifi
const SinalGSM = (esta_conectado: boolean, sinal: number, icon_size: number, color_low: ColorValue, color_medium: ColorValue, color_full: ColorValue) => {

    if (!esta_conectado) {
        return null;
    }

    const percentualSinal = `${getPercentualSinal(sinal, 0, 31).toFixed(0)}%`;

    if (sinal <= 10) {
        return (
            <>
                <IconGSMLow height={icon_size} width={icon_size} color={color_low} testID='icon-gsm-low' />
                <Text style={{ ...styles.textoStatus, color: color_low }}> {percentualSinal} </Text>
            </>
        );
    }

    if (sinal > 10 && sinal <= 15) {
        return (
            <>
                <IconGSMMedium height={icon_size} width={icon_size} color={color_medium} testID='icon-gsm-medium' />
                <Text style={{ ...styles.textoStatus, color: color_medium }}>{percentualSinal}</Text>
            </>
        );
    }

    return (
        <>
            <IconGSMFull height={icon_size} width={icon_size} color={color_full} testID='icon-gsm-full' />
            <Text style={{ ...styles.textoStatus, color: color_full }}> {percentualSinal} </Text>
        </>
    );
}

const StatusProbe: React.FC<StatusProbeProps> = ({
    conectado = false,
    tipoConexao = 0,
    tipoOperadora = 0,
    ip = '***************',
    sinal = 0,
    nivelBateria = 0,
    tensaoProbe = 0,
    height = 100,
    width = 150,
    colorLoading = '#FFFFFF',

}) => {

    const iconSize = 50;
    const ColorLow = '#C0002B'; //'#F7AABB';
    const ColorMedium = '#D49600'; //'#FFE6AA';
    const ColorFull = '#38B026'; //'#C2F0BA';    

    return (

        <View style={{ height: '100%', width: '100%', justifyContent: 'center', alignItems: 'center', borderRadius: 10, flexDirection: 'row', backgroundColor: '#E5E5E5' }}>

            {/* tipo de conexão */}
            <View style={{ height: '70%', width: '30%', justifyContent: 'space-between', alignItems: 'center' }}>

                {
                    ((tipoConexao === 9999) && (conectado)) &&
                    <View style={{ height: '100%', width: '100%', justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator size={'large'} color={colorLoading} animating={true} testID='activity-indicator-01' />
                    </View>
                }

                {
                    // se tipo de conexão wifi
                    (tipoConexao === TipoConexao.WIFI) &&
                    <View style={{ height: '100%', width: '100%', justifyContent: 'space-between', alignItems: 'center' }}>
                        <LogoWiFi height={iconSize} width={iconSize} />
                        <Text style={{ ...styles.textoStatus, color: '#000000' }}>Wi-Fi</Text>
                    </View>
                }

                {
                    // se tipo de conexão com sim card
                    (tipoConexao === TipoConexao.GSM) &&
                    <View style={{ height: '100%', width: '100%', justifyContent: 'space-between', alignItems: 'center' }}>
                        <IconGSM height={iconSize} width={iconSize} />
                        <Text style={{ ...styles.textoStatus, color: '#000000' }}>{DescricaoOperadora[tipoOperadora as keyof typeof DescricaoOperadora]}</Text>
                    </View>
                }

                {
                    // se tipo de conexão ethernet
                    (tipoConexao === TipoConexao.ETHERNET) &&
                    <View style={{ height: '100%', width: '100%', justifyContent: 'space-between', alignItems: 'center' }}>
                        <IconEthernet height={iconSize} width={iconSize} />
                        <Text style={{ ...styles.textoStatus, color: '#000000', fontSize: 10 }}>{ip}</Text>
                    </View>
                }

                {
                    // se tipo de conexão narrow band
                    (tipoConexao === TipoConexao.NARROW_BAND) &&
                    <View style={{ height: '100%', width: '100%', justifyContent: 'space-between', alignItems: 'center' }}>
                        <LogoNBIoT height={iconSize} width={iconSize} testID='logo-nb-iot' />
                        <Text style={{ ...styles.textoStatus, color: '#000000' }}>{DescricaoOperadora[tipoOperadora as keyof typeof DescricaoOperadora]}</Text>
                    </View>
                }

            </View>

            <View style={{ height: '70%', width: '5%' }}>
                <Divider orientation='vertical' color='#FFFFFF' width={2} />
            </View>

            <View style={{ height: '70%', width: '65%', paddingHorizontal: 10, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>

                {
                    (((tipoConexao === 9999)) && (conectado)) &&
                    <View style={{ height: '100%', width: iconSize, justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator size={'large'} color={colorLoading} animating={true} />
                    </View>
                }

                {
                    // status wi-fi / gsm 
                    ((tipoConexao === TipoConexao.WIFI) || (tipoConexao === TipoConexao.GSM) || (tipoConexao === TipoConexao.NARROW_BAND)) &&

                    <View style={{ height: '100%', width: iconSize, justifyContent: 'space-between', alignItems: 'center' }}>
                        {
                            // se tipo de conexão wifi
                            (tipoConexao === TipoConexao.WIFI)
                                ?
                                SinalWiFi(conectado, sinal, iconSize, ColorLow, ColorMedium, ColorFull)
                                // se tipo conexão gsm ou narrow band
                                :
                                SinalGSM(conectado, sinal, iconSize, ColorLow, ColorMedium, ColorFull)
                        }
                    </View>
                }

                {/* status bateria */}
                <View style={{ height: '100%', width: iconSize, justifyContent: 'space-between', alignItems: 'center' }}>

                    {
                        ((nivelBateria === 9999) && (conectado)) &&
                        <View style={{ height: '100%', width: iconSize, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={colorLoading} animating={true} testID='activity-indicator-02' />
                        </View>
                    }

                    {
                        // nivel da bateria zerado
                        ((nivelBateria <= 0) && (conectado))
                            ?
                            <>
                                <IconBatteryEmpty width={iconSize} height={iconSize} color={ColorLow} />
                                <Text style={{ ...styles.textoStatus, color: ColorLow }}>{`${nivelBateria} %`}</Text>
                            </>
                            :
                            // se nivel da bateria ruim
                            ((nivelBateria <= 15) && (conectado))
                                ?
                                <>
                                    <IconBatteryLow width={iconSize} height={iconSize} color={ColorLow} />
                                    <Text style={{ ...styles.textoStatus, color: ColorLow }}>{`${nivelBateria} %`}</Text>
                                </>
                                :
                                // se nivel da bateria bom
                                ((nivelBateria > 15 && nivelBateria <= 70) && (conectado))
                                    ?
                                    <>
                                        <IconBatteryMedium width={iconSize} height={iconSize} color={ColorMedium} />
                                        <Text style={{ ...styles.textoStatus, color: ColorMedium }}>{`${nivelBateria} %`}</Text>
                                    </>
                                    :
                                    ((nivelBateria > 70 && nivelBateria <= 100) && (conectado)) &&
                                    <>
                                        <IconBatteryFull width={iconSize} height={iconSize} color={ColorFull} />
                                        <Text style={{ ...styles.textoStatus, color: ColorFull }}>{`${nivelBateria} %`}</Text>
                                    </>
                    }

                </View>

                {/* status tensão */}
                <View style={{ height: '100%', width: iconSize, justifyContent: 'space-between', alignItems: 'center' }}>

                    {
                        // se probe conectada
                        ((tensaoProbe === 9999) && (conectado))
                            ?
                            <View style={{ height: '100%', width: iconSize, justifyContent: 'center', alignItems: 'center' }}>
                                <ActivityIndicator size={'large'} color={colorLoading} animating={true} testID='activity-indicator-03' />
                            </View>
                            :
                            (conectado) &&
                            <>
                                <IconPlug height={iconSize} width={iconSize} color={(tensaoProbe === 1) ? ColorFull : ColorLow} />
                                <Text style={{ ...styles.textoStatus, color: (tensaoProbe === 1) ? ColorFull : ColorLow }}>{(tensaoProbe === 1) ? 'ON' : 'OFF'}</Text>
                            </>
                    }
                </View>

            </View>
        </View>
    )
}

export default StatusProbe;

const styles = StyleSheet.create({
    textoStatus: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 13,
        color: '#FFFFFF',
    },
});
