import { render, screen } from '@testing-library/react-native';
import React from 'react';
import { ColorValue } from 'react-native';
import {
  AdicionaDiaDataAtual,
  Data,
  DataCompleta,
  DataTimeStamp,
  FormataTimer,
  formatJson,
  getIndexBaudRate,
  getIndexParidade,
  getIndexTempoEnvio,
  getPercentualSinal,
  Hora,
  HoraTimeStamp,
  isNumber,
  isObject,
  MascaraIP,
  padTo2Digits,
  ValidarIP
} from '../funcoes';
import Divider from './Divider';
import StatusProbe from './StatusProbe';

describe('StatusProbe Component', () => {

  it('deve renderizar com as propriedades default', () => {
    render(<StatusProbe />);

    // Verificar se o componente está renderizando o texto "Wi-Fi" como esperado (conectado ao Wi-Fi)
    expect(screen.getByText('Wi-Fi')).toBeTruthy();
  });

  it('deve renderizar o ActivityIndicator quando o tipoConexao é 9999 conectado é true', () => {
    render(<StatusProbe tipoConexao={9999} conectado={true} />);

    // Verificar se o ActivityIndicator é renderizado
    expect(screen.getByTestId('activity-indicator-01')).toBeTruthy();
  });

  it('deve renderizar corretamente o sinal forte para a conexão Wi-Fi', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={0} conectado={true} sinal={-40} />);
    // Verificar se o sinal Wi-Fi forte é renderizado
    expect(getByTestId('icon-wifi-full')).toBeTruthy();
  });

  it('deve renderizar corretamente o sinal médio para a conexão Wi-Fi', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={0} conectado={true} sinal={-65} />);
    // Verificar se o sinal Wi-Fi medio é renderizado
    expect(getByTestId('icon-wifi-medium')).toBeTruthy();
  });

  it('deve renderizar corretamente o sinal fraco para a conexão Wi-Fi', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={0} conectado={true} sinal={-81} />);

    // Verificar se o sinal Wi-Fi fraco é renderizado
    expect(getByTestId('icon-wifi-low')).toBeTruthy();
  });

  it('deve renderizar corretamente quando é GSM mas não esta conectado', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={1} conectado={false} sinal={8} />);

    // Verificar se o sinal GSM está renderizado corretamente
    expect(screen).toBeNull;
  });

  it('deve renderizar corretamente o sinal fraco para a conexão GSM', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={1} conectado={true} sinal={8} />);

    // Verificar se o sinal GSM está renderizado corretamente
    expect(getByTestId('icon-gsm-low')).toBeTruthy();
  });

  it('deve renderizar corretamente o sinal medio para a conexão GSM', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={1} conectado={true} sinal={12} />);

    // Verificar se o sinal GSM está renderizado corretamente
    expect(getByTestId('icon-gsm-medium')).toBeTruthy();
  });

  it('deve renderizar corretamente o sinal forte para a conexão GSM', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={1} conectado={true} sinal={18} />);

    // Verificar se o sinal GSM está renderizado corretamente
    expect(getByTestId('icon-gsm-full')).toBeTruthy();
  });

  it('deve renderizar corretamente para a conexão Ethernet', () => {
    render(<StatusProbe tipoConexao={2} conectado={true} ip={'***************'} />);

    // Verificar o ip 
    expect(screen.getByText('***************')).toBeTruthy();
  });


  it('deve renderizar corretamente o sinal forte para a conexão Narrow Band', () => {
    const { getByTestId } = render(<StatusProbe tipoConexao={3} conectado={true} sinal={14} />);

    // Verificar se o sinal GSM está renderizado corretamente
    expect(getByTestId('logo-nb-iot')).toBeTruthy();
  });


  it('deve renderizar o ActivityIndicator quando o nivelBateria é 9999 conectado é true', () => {
    render(<StatusProbe nivelBateria={9999} conectado={true} />);

    // Verificar se o ActivityIndicator é renderizado
    expect(screen.getByTestId('activity-indicator-02')).toBeTruthy();
  });

  it('deve renderizar o status da bateria baseado no nível baixo da bateria ', () => {
    render(<StatusProbe conectado={true} nivelBateria={10} />);

    // Verificar se a bateria está com o status de 10%
    expect(screen.getByText('10 %')).toBeTruthy();
  });

  it('deve renderizar o status da bateria baseado no nível médio da bateria ', () => {
    render(<StatusProbe conectado={true} nivelBateria={30} />);

    // Verificar se a bateria está com o status de 30%
    expect(screen.getByText('30 %')).toBeTruthy();
  });

  it('deve renderizar o status da bateria baseado no nível alto da bateria ', () => {
    render(<StatusProbe conectado={true} nivelBateria={80} />);

    // Verificar se a bateria está com o status de 80%
    expect(screen.getByText('80 %')).toBeTruthy();
  });

  it('deve renderizar corretamente o texto baseado na tensaoProbe', () => {
    render(<StatusProbe conectado={true} tensaoProbe={1} />);

    // Verificar se o ícone de "ON" é exibido
    expect(screen.getByText('ON')).toBeTruthy();
  });

  it('deve renderizar corretamente loading quando tensão da Probe = 9999 e conectado', () => {
    render(<StatusProbe conectado={true} tensaoProbe={9999} />);

    // Verificar se o ActivityIndicator é renderizado
    expect(screen.getByTestId('activity-indicator-03')).toBeTruthy();
  });

  it('deverá apresentar de forma diferente a cor do loading baseado na propriedade.', () => {
    const customColor: ColorValue = '#FF5733';  // Cor personalizada para o carregamento
    render(<StatusProbe colorLoading={customColor} tipoConexao={9999} conectado={true} />);

    // Verificar se o ActivityIndicator usa a cor personalizada
    const activityIndicator = screen.getByTestId('activity-indicator-01');
    expect(activityIndicator.props.color).toBe(customColor);
  });

  it('deve renderizar corretamente o Divider usando a propriedade default', () => {
    const { getByTestId } = render(<Divider />);
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style.length).toBeGreaterThan(0); // Verifique se o estilo está aplicado
  });

  it('deve renderizar corretamente o Divider usando uma propriedade customizada', () => {
    const customStyle = { margin: 10 };
    const { getByTestId } = render(
      <Divider width={2} orientation="vertical" color="#000000" dividerStyle={customStyle} />
    );
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style).toContainEqual(expect.objectContaining({ width: 2 }));
    expect(divider.props.style).toContainEqual(expect.objectContaining({ backgroundColor: '#000000' }));
    expect(divider.props.style).toContainEqual(expect.objectContaining(customStyle)); // Verifique se o estilo personalizado foi aplicado
  });

  describe('Funcoes', () => {

    describe('AdicionaDiaDataAtual', () => {
      it('deve adicionar 5 dias à data atual', () => {
        const resultado = AdicionaDiaDataAtual(5);
        const hoje = new Date();
        hoje.setDate(hoje.getDate() + 5);

        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0'); // Janeiro é 0!
        const ano = hoje.getFullYear();
        const dataEsperada = `${dia}/${mes}/${ano}`;

        expect(resultado).toBe(dataEsperada);
      });

      it('deve adicionar 0 dias à data atual', () => {
        const resultado = AdicionaDiaDataAtual(0);
        const hoje = new Date();

        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0'); // Janeiro é 0!
        const ano = hoje.getFullYear();
        const dataEsperada = `${dia}/${mes}/${ano}`;

        expect(resultado).toBe(dataEsperada);
      });

      it('deve adicionar dias negativos à data atual', () => {
        const resultado = AdicionaDiaDataAtual(-5);
        const hoje = new Date();
        hoje.setDate(hoje.getDate() - 5);

        const dia = String(hoje.getDate()).padStart(2, '0');
        const mes = String(hoje.getMonth() + 1).padStart(2, '0'); // Janeiro é 0!
        const ano = hoje.getFullYear();
        const dataEsperada = `${dia}/${mes}/${ano}`;

        expect(resultado).toBe(dataEsperada);
      });
    });

    describe('padTo2Digits', () => {
      test('deve adicionar zero à esquerda para números menores que 10', () => {
        expect(padTo2Digits(5)).toBe('05');
      });

      test('não deve adicionar zero à esquerda para números maiores ou iguais a 10', () => {
        expect(padTo2Digits(10)).toBe('10');
        expect(padTo2Digits(25)).toBe('25');
      });

      test('deve retornar "00" para 0', () => {
        expect(padTo2Digits(0)).toBe('00');
      });
    });

    // Especificação do teste
    describe('DataCompleta', () => {
      test('deve formatar a data corretamente', () => {

        // Cria uma nova data para o teste
        const date = new Date(2025, 1, 7, 14, 25, 0); // 7 de Fevereiro de 2025, 14:25:00

        // Chama a função com a data de teste
        const formattedDate = DataCompleta(date);

        // Compara o resultado com o formato esperado
        expect(formattedDate).toBe('07/02/2025 14:25:00');
      });
    });

    describe('Data function', () => {
      it('deverá formatar a data corretamente', () => {
        const date = new Date(2023, 0, 1); // 1 de Janeiro de 2023
        const formattedDate = Data(date);
        expect(formattedDate).toBe('01/01/2023');
      });

      it('deve lidar com dias e meses de um dígito', () => {
        const date = new Date(2023, 5, 9); // 9 de Junho de 2023
        const formattedDate = Data(date);
        expect(formattedDate).toBe('09/06/2023');
      });

      it('deve lidar com dias e meses de dois dígitos', () => {
        const date = new Date(2023, 10, 20); // 20 de Novembro de 2023
        const formattedDate = Data(date);
        expect(formattedDate).toBe('20/11/2023');
      });
    });

    describe('Hora', () => {
      it('devera retornar a hora no formato HH:MM:SS', () => {
        const date = new Date('2025-02-07 12:34:56');
        const result = Hora(date);
        expect(result).toBe('12:34:56');
      });

      it('deve preencher horas, minutos e segundos de um dígito com um zero à esquerda', () => {
        const date = new Date('2025-02-07 03:04:05');
        const result = Hora(date);
        expect(result).toBe('03:04:05');
      });
    });

    describe('DataTimeStamp', () => {

      it('converte timestamp para uma data em string', () => {
        // Exemplo de timestamp para 5 de março de 2023
        const timestamp = 1678012800;  // 05/03/2023 em segundos desde 01/01/1970
        const result = DataTimeStamp(timestamp);
        expect(result).toBe('05/03/2023');
      });
    });

    describe('HoraTimeStamp', () => {
      test('deverá retornar a hora correta, mostrando os 04 segundos', () => {
        const timestamp = 946695604; // timestamp que representa 2000-01-01 01:00:04 UTC
        const show_second = true;
        const result = HoraTimeStamp(timestamp, show_second);
        expect(result).toBe('03:00:04'); // dependendo do seu fuso horário
      });

      test('deverá retornar a hora correta sem mostrar os segundos', () => {
        const timestamp = 946695604; // timestamp que representa 2000-01-01 01:00:04 UTC
        const show_second = false;
        const result = HoraTimeStamp(timestamp, show_second);
        expect(result).toBe('03:00'); // dependendo do seu fuso horário
      });
    });

    describe('MascaraIP', () => {
      it('deve remover caracteres não numéricos', () => {
        const input = 'abc123def456ghi789jkl012';
        const output = MascaraIP(input);
        expect(output).toBe('***************');
      });

      it('deve adicionar pontos nos lugares corretos', () => {
        const input = '123456789012';
        const output = MascaraIP(input);
        expect(output).toBe('***************');
      });

      it('deve limitar o comprimento a 15 caracteres', () => {
        const input = '12345678901234567890';
        const output = MascaraIP(input);
        expect(output).toBe('***************');
      });

      it('deve verificar se cada segmento é menor ou igual a 255', () => {
        const input = '123456789255300400500';
        const output = MascaraIP(input);
        expect(output).toBe('***************');
      });

      it('deve lidar com entradas curtas', () => {
        const input = '123';
        const output = MascaraIP(input);
        expect(output).toBe('123');
      });

      it('deve lidar com entradas vazias', () => {
        const input = '';
        const output = MascaraIP(input);
        expect(output).toBe('');
      });
    });

    describe('FormataTimer', () => {
      // Teste para verificar se a função formata corretamente para 00:00:00
      it('deve formatar 0 segundos como 00:00:00', () => {
        expect(FormataTimer(0)).toBe('00:00:00');
      });

      // Teste para verificar a formatação de 1 segundo
      it('deve formatar 1 segundo como 00:00:01', () => {
        expect(FormataTimer(1)).toBe('00:00:01');
      });

      // Teste para verificar a formatação de 1 minuto (60 segundos)
      it('deve formatar 60 segundos como 00:01:00', () => {
        expect(FormataTimer(60)).toBe('00:01:00');
      });

      // Teste para verificar a formatação de 1 hora (3600 segundos)
      it('deve formatar 3600 segundos como 01:00:00', () => {
        expect(FormataTimer(3600)).toBe('01:00:00');
      });

      // Teste para verificar a formatação de 1 hora, 1 minuto e 1 segundo (3661 segundos)
      it('deve formatar 3661 segundos como 01:01:01', () => {
        expect(FormataTimer(3661)).toBe('01:01:01');
      });

      // Teste para verificar a formatação de mais de 1 hora e alguns minutos e segundos
      it('deve formatar 3723 segundos como 01:02:03', () => {
        expect(FormataTimer(3723)).toBe('01:02:03');
      });
    });

    describe('isNumber', () => {
      it('deve retornar true para uma string numérica', () => {
        expect(isNumber('12345')).toBe(true);
      });

      it('deve retornar false para uma string não numérica', () => {
        expect(isNumber('abcde')).toBe(false);
      });

      it('deve retornar false para uma string mista', () => {
        expect(isNumber('123abc')).toBe(false);
      });

      it('deve retornar false para uma string vazia', () => {
        expect(isNumber('')).toBe(false);
      });
    });

    describe('isObject', () => {
      it('deverá retornar true se for object', () => {
        expect(isObject({})).toBe(true);
        expect(isObject([])).toBe(true);
        expect(isObject(new Date())).toBe(true);
      });

      it('deverá retornar false se não for object', () => {
        expect(isObject(42)).toBe(false);
        expect(isObject('string')).toBe(false);
        expect(isObject(null)).toBe(false);
        expect(isObject(undefined)).toBe(false);
        expect(isObject(true)).toBe(false);
        expect(isObject(false)).toBe(false);
      });
    });

    describe('getIndexParidade', () => {
      it('Deve retornar o id correto quando a paridades são encontradas', () => {
        expect(getIndexParidade('8N1')).toBe(0);
        expect(getIndexParidade('8E1')).toBe(2);
        expect(getIndexParidade('8O2')).toBe(5);
      });

      it('Deve retornar 0 quando a paridade não é encontrada', () => {
        expect(getIndexParidade('8N')).toBe(0);
      });
    });

    describe('getIndexBaudRate', () => {
      it('Deve retornar o id correto quando os baud rate são encontrados', () => {
        expect(getIndexBaudRate(600)).toBe(1);
        expect(getIndexBaudRate(9600)).toBe(5);
        expect(getIndexBaudRate(19200)).toBe(7);
      });

      it('Deve retornar 5 quando a descricao não é encontrada', () => {
        expect(getIndexBaudRate(100)).toBe(5);
      });
    });


    describe('getIndexTempoEnvio', () => {
      it('Deve retornar o id correto quando os tempos de envio são encontrados', () => {
        expect(getIndexTempoEnvio(60)).toBe(0);
        expect(getIndexTempoEnvio(300)).toBe(1);
        expect(getIndexTempoEnvio(900)).toBe(2);
        expect(getIndexTempoEnvio(3600)).toBe(3);
      });

      it('Deve retornar 2 quando a descricao não é encontrada', () => {
        expect(getIndexTempoEnvio(100)).toBe(2);
      });
    });

    describe('ValidarIP - Funcoes', () => {

      it('deve retornar true para IP válido', () => {
        const ip = '*************';
        expect(ValidarIP(ip)).toBe(true);
      });

      it('deve retornar false para IP inválido', () => {
        const ip = '500.168.000.100';
        expect(ValidarIP(ip)).toBe(false);
      });

      it('deve retornar false para IP não completo ', () => {
        const ip = '500.168.000';
        expect(ValidarIP(ip)).toBe(false);
      });
    });

    describe('getPercentualSinal', () => {
      test('deve retornar 0 se o sinal for menor ou igual ao minSinal', () => {
        expect(getPercentualSinal(5, 10, 20)).toBe(0);
      });

      test('deve retornar 100 se o sinal for maior ou igual ao maxSinal', () => {
        expect(getPercentualSinal(25, 10, 20)).toBe(100);
      });

      test('deve calcular o percentual corretamente para um sinal intermediário', () => {
        expect(getPercentualSinal(15, 10, 20)).toBe(50);
        expect(getPercentualSinal(12.5, 10, 20)).toBe(25);
        expect(getPercentualSinal(17.5, 10, 20)).toBe(75);
      });

      test('deve garantir que o valor esteja entre 0% e 100%', () => {
        expect(getPercentualSinal(5, 10, 20)).toBe(0);
        expect(getPercentualSinal(25, 10, 20)).toBe(100);
        expect(getPercentualSinal(15, 10, 20)).toBeGreaterThanOrEqual(0);
        expect(getPercentualSinal(15, 10, 20)).toBeLessThanOrEqual(100);
      });
    });

    describe('formatJson', () => {
      test('deve retornar se contem o topico se for uma string', () => {
        expect(formatJson("{\"usr\":\"App\",\"variable\":\"dbgack\"}", "action/10061C1D4998")).toContain("action/10061C1D4998");
      });

      test('deve retornar se contem o topico se for um objeto', () => {
        expect(formatJson(JSON.parse("{\"usr\":\"App\",\"variable\":\"dbgack\"}"), "action/10061C1D4998")).toContain("action/10061C1D4998");
      });
      it('deve retornar uma string vazia quando nenhum parâmetro é passado', () => {
        const resultado = formatJson('', '');

        expect(resultado).toBe(''); // Verifica se retorna vazio
      });
    });
  });
});
