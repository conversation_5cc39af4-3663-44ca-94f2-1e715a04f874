import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import Divider from './Divider';
import ProgressSteps from './ProgressSteps'; // Ajuste o caminho conforme necessário

describe('ProgressSteps Component', () => {

    it('deve renderizar corretamente quando todos os passos são visíveis', () => {
        const { getByText } = render(
            <ProgressSteps
                showStep1={true}
                showStep2={true}
                showStep3={true}
                showStep4={true}
                showStep5={true}
                showStep6={true}
                showStep7={true}
            />
        );

        // Verificar se todos os textos dos passos são renderizados
        expect(getByText('Passo 1')).toBeTruthy();
        expect(getByText('Passo 2')).toBeTruthy();
        expect(getByText('Passo 3')).toBeTruthy();
        expect(getByText('Passo 4')).toBeTruthy();
        expect(getByText('Passo 5')).toBeTruthy();
        expect(getByText('Passo 6')).toBeTruthy();
        expect(getByText('Passo 7')).toBeTruthy();
    });

    it('não deve exibir o passo 1 se showStep1 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep1={false} />
        );

        // Passo 1 não deve ser renderizado
        expect(queryByText('Passo 1')).toBeNull();
    });

    it('não deve exibir o passo 2 se showStep2 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep2={false} />
        );

        // Passo 2 não deve ser renderizado
        expect(queryByText('Passo 2')).toBeNull();
    });

    it('não deve exibir o passo 3 se showStep3 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep3={false} />
        );

        // Passo 3 não deve ser renderizado
        expect(queryByText('Passo 3')).toBeNull();
    });

    it('não deve exibir o passo 4 se showStep4 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep4={false} />
        );

        // Passo 4 não deve ser renderizado
        expect(queryByText('Passo 4')).toBeNull();
    });

    it('não deve exibir o passo 5 se showStep5 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep5={false} />
        );

        // Passo 5 não deve ser renderizado
        expect(queryByText('Passo 5')).toBeNull();
    });

    it('não deve exibir o passo 6 se showStep5 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep6={false} />
        );

        // Passo 6 não deve ser renderizado
        expect(queryByText('Passo 6')).toBeNull();
    });

    it('não deve exibir o passo 7 se showStep5 for false', () => {
        const { queryByText } = render(
            <ProgressSteps showStep7={false} />
        );

        // Passo 7 não deve ser renderizado
        expect(queryByText('Passo 7')).toBeNull();
    });

    it('deve chamar onPressStep1 quando o botão do passo 1 for pressionado', () => {
        const mockOnPressStep1 = jest.fn();

        const { getByText } = render(
            <ProgressSteps
                onPressStep1={mockOnPressStep1}
                step1={false} // Vamos assumir que o passo 1 ainda não foi completado
            />
        );

        const stepButton1 = getByText('Passo 1');

        // Simular um toque no botão
        fireEvent.press(stepButton1);

        // Verificar se a função foi chamada
        expect(mockOnPressStep1).toHaveBeenCalledTimes(1);
    });

    it('deve exibir o botão Editar do passo 2', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep2={true} step1={true} step2={true} />
        );
        const iconEditar = getByTestId('icon-editar-2');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o botão Editar do passo 3', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep3={true} step2={true} step3={true} />
        );
        const iconEditar = getByTestId('icon-editar-3');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o botão Editar do passo 4', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep4={true} step3={true} step4={true} />
        );
        const iconEditar = getByTestId('icon-editar-4');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o botão Editar do passo 5', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep5={true} step4={true} step5={true} />
        );
        const iconEditar = getByTestId('icon-editar-5');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o botão Editar do passo 6', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep6={true} step5={true} step6={true} />
        );
        const iconEditar = getByTestId('icon-editar-6');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o botão Editar do passo 7', () => {
        const { getByTestId } = render(
            <ProgressSteps showStep7={true} step6={true} step7={true} />
        );
        const iconEditar = getByTestId('icon-editar-7');
        expect(iconEditar).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 1 quando step1 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step1={true} />
        );

        const stepOn = getByTestId('step-on-1');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 2 quando step2 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step2={true} />
        );

        const stepOn = getByTestId('step-on-2');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 3 quando step3 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step3={true} />
        );

        const stepOn = getByTestId('step-on-3');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 4 quando step4 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step4={true} />
        );

        const stepOn = getByTestId('step-on-4');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 5 quando step5 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step5={true} />
        );

        const stepOn = getByTestId('step-on-5');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 6 quando step6 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step6={true} />
        );

        const stepOn = getByTestId('step-on-6');
        expect(stepOn).toBeTruthy();
    });

    it('deve exibir o StepOn para o passo 7 quando step7 for true', () => {
        const { getByTestId } = render(
            <ProgressSteps step7={true} />
        );

        const stepOn = getByTestId('step-on-7');
        expect(stepOn).toBeTruthy();
    });

    it('deve renderizar o Divider corretamente usando a propriedade default', () => {
        const { getByTestId } = render(<Divider />);
        const divider = getByTestId('view-divider');
        expect(divider).toBeTruthy();
        expect(divider.props.style.length).toBeGreaterThan(0); // Verifique se o estilo está aplicado
    });

    it('deve renderizar o Divider corretamente usando uma propriedade customizada', () => {
        const customStyle = { margin: 10 };
        const { getByTestId } = render(
            <Divider width={2} orientation="vertical" color="#000000" dividerStyle={customStyle} />
        );
        const divider = getByTestId('view-divider');
        expect(divider).toBeTruthy();
        expect(divider.props.style).toContainEqual(expect.objectContaining({ width: 2 }));
        expect(divider.props.style).toContainEqual(expect.objectContaining({ backgroundColor: '#000000' }));
        expect(divider.props.style).toContainEqual(expect.objectContaining(customStyle)); // Verifique se o estilo personalizado foi aplicado
    });
});
