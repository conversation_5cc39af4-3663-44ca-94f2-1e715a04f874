import React from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';
import IconCPU from '../assets/svg/icon_cpu.svg';

interface StatusFirmwareProps {
    value?: string;
    firmwareColor?: ColorValue;
    iconFirmwareColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    title?: string;
    loading?: boolean;
}

const StatusFirmware: React.FC<StatusFirmwareProps> = ({
    value = 'Versão',
    firmwareColor = '#F5F5F5',
    iconFirmwareColor = '#C0002B',
    height = 100,
    width = 150,
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    title = 'Firmware',
    loading = false,
}) => {

    return (

        <View style={{
            height: 104,
            width: width,
            paddingHorizontal: 20,
            paddingVertical: 24,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        }} testID='status-gsm-container'>

            <View style={{ height: 56, width: '100%', justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                {
                    (loading)
                        ?
                        <View style={{ height: 48, width: 48, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} testID='activity-indicator' />
                        </View>
                        :
                        <View style={{ height: 48, width: 48, borderRadius: 9999, backgroundColor: firmwareColor, justifyContent: 'center', alignItems: 'center' }}>
                            <IconCPU color={"#737373"} width={20} height={20} testID="icon-signal" />
                        </View>
                }

                <View >
                    <Text style={styles.textoTitle}>
                        {`${title}`}
                    </Text>
                    <Text style={styles.textoValue}>
                        {`${value}`}
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default StatusFirmware;

const styles = StyleSheet.create({

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
});
