import React from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';
import IconVBattery from '../assets/svg/icon_battery-charging.svg';

interface StatusVBatteryProps {
    value?: number;
    vBatteryColor?: ColorValue;
    iconVBatteryColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    title?: string;
    loading?: boolean;
}

const StatusVBattery: React.FC<StatusVBatteryProps> = ({
    value = 0,
    vBatteryColor = '#F5F5F5',
    iconVBatteryColor = '#C0002B',
    height = 100,
    width = 150,
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    title = 'Tensão',
    loading = false,
}) => {

    return (

        <View style={{
            height: 104,
            width: width,
            paddingHorizontal: 20,
            paddingVertical: 24,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        }}>

            <View style={{ height: 56, width: '100%', justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                {
                    (loading)
                        ?
                        <View style={{ height: 48, width: 48, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} testID="activity-indicator" />
                        </View>
                        :
                        <View style={{ height: 48, width: 48, borderRadius: 9999, backgroundColor: vBatteryColor, justifyContent: 'center', alignItems: 'center' }}>
                            <IconVBattery color={"#737373"} width={20} height={20} />
                        </View>
                }

                <View >
                    <Text style={styles.textoTitle}>
                        {title}
                    </Text>
                    <Text style={styles.textoValue}>
                        {`${value} v`}
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default StatusVBattery;

const styles = StyleSheet.create({
    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
});
