import { COGNITO_CALLBACK_URL_LOGOUT_PROD, COGNITO_CLIENT_ID_PROD, COGNITO_LOGIN_BASE_PROD_URL } from '@env';
import { NavigationContainer } from '@react-navigation/native';
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { URL_SIGNOUT } from '../services/Cognito/constants';
import { WebViewLogout } from './WebViewLogout';

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: jest.fn(),
  }),
}));

jest.mock('@react-native-clipboard/clipboard', () => 'Clipboard');
jest.mock('@shopify/react-native-skia', () => 'Canvas');
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => 'LeafletView');
jest.mock('sp-react-native-mqtt', () => 'MQTT');
jest.mock('react-native-qrcode-scanner', () => 'QRCodeScanner');
jest.mock('expo-location', () => 'LocationObject');
jest.mock('rn-slide-button', () => 'SlideButton');
jest.mock('@react-navigation/drawer', () => 'createDrawerNavigator');
jest.mock('@react-native-community/netinfo', () => 'NetInfo');
jest.mock('react-native-view-shot', () => 'ViewShot');
jest.mock('react-native-share', () => 'Share');
jest.mock('react-native-webview', () => 'WebView');
jest.mock('./Loading', () => 'Loading');
jest.mock('../modal/messagebox/Erro', () => 'MessageBoxErro');

jest.mock('../storages', () => ({
  KEY: { cognitoToken: 'cognitoToken' },
  setStorageString: jest.fn(),
}));

describe('WebViewLogout', () => {
  it('renderiza corretamente', async () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <WebViewLogout url={`${COGNITO_LOGIN_BASE_PROD_URL}${URL_SIGNOUT}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=${COGNITO_CALLBACK_URL_LOGOUT_PROD}`} />
      </NavigationContainer>
    );
    await waitFor(() => {
      expect(getByTestId('webview')).toBeVisible();
    });
  });

  it('trata logout corretamente enquanto pagina carregando', async () => {

    const uri = `${COGNITO_LOGIN_BASE_PROD_URL}${URL_SIGNOUT}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=${COGNITO_CALLBACK_URL_LOGOUT_PROD}`;

    const { getByTestId } = render(
      <NavigationContainer>
        <WebViewLogout url={uri} />
      </NavigationContainer>
    );

    fireEvent(getByTestId('webview'), 'onNavigationStateChange', { loading: true, url: uri });

    await waitFor(() => {
      expect(getByTestId('webview')).toBeVisible();
    });
  });

  it('trata logout corretamente quando pagina carregada', async () => {

    const uri = `${COGNITO_LOGIN_BASE_PROD_URL}${URL_SIGNOUT}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=${COGNITO_CALLBACK_URL_LOGOUT_PROD}`;

    const { getByTestId } = render(
      <NavigationContainer>
        <WebViewLogout url={uri} />
      </NavigationContainer>
    );

    fireEvent(getByTestId('webview'), 'onNavigationStateChange', { loading: false, url: uri });
  });

  it('trata logout corretamente quando pagina carregada mas url de retorno não válida', async () => {

    const uri = `${COGNITO_LOGIN_BASE_PROD_URL}?client_id=${COGNITO_CLIENT_ID_PROD}&redirect_uri=https://example.com`;

    const { getByTestId } = render(
      <NavigationContainer>
        <WebViewLogout url={uri} />
      </NavigationContainer>
    );

    fireEvent(getByTestId('webview'), 'onNavigationStateChange', { loading: false, url: uri });
  });
});
