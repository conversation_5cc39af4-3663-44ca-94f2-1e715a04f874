import React from 'react';
import { ActivityIndicator, ColorValue, Modal, StyleSheet, Text, View } from 'react-native';

interface LoadingProps {
    animating?: boolean;
    size?: 'large' | 'small';
    color?: ColorValue;
    text?: string;
}

const Loading: React.FC<LoadingProps> = ({
    animating = false,
    size = 'large',
    color = '#45D42E',
    text = '',
}) => {

    return (

        <Modal
            visible={animating}
            statusBarTranslucent={true}
            transparent={true}
            animationType="fade"
            testID="modal"
        >
            <View style={styles.container}>
                <ActivityIndicator size={size} color={color} animating={animating} style={{ transform: [{ scaleX: 2 }, { scaleY: 2 }] }} testID="activity-indicator" />
                <Text style={styles.texto}>{text}</Text>
            </View>

        </Modal>
    )
}

export default Loading;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0,0,0,0.7)",
        gap: 20
    },
    texto: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 16,
        textAlign: 'center',
        fontWeight: "400",
        lineHeight: 24,
        color: "#FFFFFF",
    },
    subtexto: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 28,
        textAlign: 'center',
        fontWeight: "400",
        lineHeight: 24,
        color: "#FFFFFF",
    },
});
