import { LeafletView } from '@charlespalmerbf/react-native-leaflet-js';
import React from 'react';
import { StyleSheet, View } from 'react-native';

interface MapaProps {
    latitude?: number;
    longitude?: number;
    zoom?: number;
}

const Mapa: React.FC<MapaProps> = ({
    latitude = -23.5931101,
    longitude = -46.6828087,
    zoom = 20
}) => {

    return (
        <View style={styles.container} testID='leaflet-view'>
            <LeafletView
                mapMarkers={[
                    {
                        position: {
                            lat: latitude,
                            lng: longitude,
                        },
                        icon: '📍',
                        size: [32, 32],
                        id: '1',
                    },
                ]}
                mapCenterPosition={{
                    lat: latitude,
                    lng: longitude,
                }}
                zoom={zoom}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
});

export default Mapa;
