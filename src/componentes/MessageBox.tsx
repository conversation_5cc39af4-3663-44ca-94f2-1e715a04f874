import React from 'react';

import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import IconAlertCircle from '../assets/svg/icon_alert-circle.svg';
import IconCheckCircle from '../assets/svg/icon_check-circle.svg';
import IconX from '../assets/svg/icon_x.svg';

interface MessageBoxProps {
    title?: string;
    description?: string;
    textButton?: string;
    type?: 'sucess' | 'error' | 'warning';
    onClose?: () => void;
}

const MessageBox: React.FC<MessageBoxProps> = ({
    title = "Message Box",
    description = "Descrição do message box",
    textButton = "OK",
    type = 'warning',
    onClose,
}) => {
    return (
        <View style={styles.overlay}>
            <View style={styles.containerCard}>
                <View style={styles.containerHeader}>
                    <View style={styles.containerIcons}>
                        <View style={styles.containerIconInfo}>
                            {
                                (type === 'sucess') &&
                                    <IconCheckCircle color={'#079455'} width={24} height={24} />
                            }
                            {
                                (type === 'error') &&
                                    <IconAlertCircle color={'#C0002B'} width={24} height={24} />
                            }
                            {
                                (type === 'warning') &&
                                    <IconAlertCircle color={'#D49600'} width={24} height={24} />
                            }                                                        
                        </View>
                        <TouchableOpacity style={styles.botaoFechar} onPress={onClose} testID="close-button">
                            <IconX color={'#A3A3A3'} width={20} height={20} />
                        </TouchableOpacity>                        
                    </View>
                    <View style={styles.containerInfo}>
                        <Text style={styles.textoInfoTitulo}>{title}</Text>
                        <Text style={styles.textoInfoDescricao}>{description}</Text>
                    </View>
                </View>
                <View style={styles.containerFooter}>
                    <TouchableOpacity style={styles.botaoFooterOK} onPress={onClose}>
                        <Text style={styles.textoBotaoOK}>{textButton}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    )
};

export default MessageBox;

const styles = StyleSheet.create({
    overlay:{
        position:'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9999,
    },

    containerCard: {
        width: 343,
        height: 292,
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
    },

    containerHeader: {
        flex: 1,
        gap: 12,        
    },

    containerFooter: {
        height: 80,
        justifyContent: 'center',
        alignItems: 'center',
    },

    containerIcons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',        
    },

    containerInfo: {
        alignItems: 'center',
    },

    containerIconInfo: {
        width: 48,
        height: 48,
        borderRadius: 9999,
        backgroundColor: '#FFE6AA',
        alignItems: 'center',
        justifyContent: 'center',
    },

    textoInfoTitulo: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: '600',
        fontSize: 18,
        lineHeight: 28,
        color: '#141414',
        marginBottom: 8,
        textAlign: 'center',
    },

    textoInfoDescricao: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: '400',
        fontSize: 16,
        lineHeight: 20,
        color: '#525252',
        textAlign: 'center',
    },

    textoBotaoOK: {
        fontFamily: 'Exo2_600SemiBold',
        fontWeight: '600',
        fontSize: 16,
        color: '#FFFFFF',
        textAlign: 'center',        
    },

    botaoFechar: {
        width: 44,
        height: 44,
        alignItems: 'center',
        justifyContent: 'center',
    },

    botaoFooterOK: {
        width: 311,
        height: 44,
        borderRadius: 8,
        backgroundColor: '#45D42E',
        justifyContent: 'center',
        alignItems: 'center',
    }
})