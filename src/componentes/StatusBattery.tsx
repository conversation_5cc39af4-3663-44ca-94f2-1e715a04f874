import React from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';
import IconBatteryEmpty from '../assets/svg/icon_battery-empty.svg';
import IconBatteryFull from '../assets/svg/icon_battery-full.svg';
import IconBatteryLow from '../assets/svg/icon_battery-low.svg';
import IconBatteryMid from '../assets/svg/icon_battery-mid.svg';

interface StatusBatteryProps {
    value?: number;
    batteryLowColor?: ColorValue;
    batteryMidColor?: ColorValue;
    batteryFullColor?: ColorValue;
    iconBatteryLowColor?: ColorValue;
    iconBatteryMidColor?: ColorValue;
    iconBatteryFullColor?: ColorValue;
    disabledColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    title?: string;
    loading?: boolean;
}

const StatusBattery: React.FC<StatusBatteryProps> = ({
    value = 0,
    batteryLowColor = '#FF0000',
    batteryMidColor = '#FFFF00',
    batteryFullColor = '#00FF00',
    iconBatteryLowColor = '#C0002B',
    iconBatteryMidColor = '#D49600',
    iconBatteryFullColor = '#38B026',
    disabledColor = '#F5F5F5',
    height = 100,
    width = 150,
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    title = 'Bateria',
    loading = false,
}) => {

    const getBatteryColor = () => {
        if (value <= 0) return disabledColor;
        if (value <= 15) return batteryLowColor;
        if (value <= 70) return batteryMidColor;
        return batteryFullColor;
    };

    const batteryColor = getBatteryColor();

    return (

        <View style={{
            height: 104,
            width: width,
            paddingHorizontal: 20,
            paddingVertical: 24,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        }}>

            <View style={{ height: 56, width: '100%', justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                {
                    (loading)
                        ?
                        <View style={{ height: 48, width: 48, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} testID='loading-indicator' />
                        </View>
                        :
                        <View style={{ height: 48, width: 48, borderRadius: 9999, backgroundColor: batteryColor, justifyContent: 'center', alignItems: 'center' }} testID='battery-view'>
                            {
                                // nivel da bateria
                                (value <= 0) ? <IconBatteryEmpty color={"#737373"} width={20} height={20} />
                                    :
                                    (() => {
                                        if (value <= 15) {
                                            return <IconBatteryLow color={iconBatteryLowColor} width={20} height={20} />;
                                        }
                                        if (value <= 70) {
                                            return <IconBatteryMid color={iconBatteryMidColor} width={20} height={20} />;
                                        }
                                        return <IconBatteryFull color={iconBatteryFullColor} width={20} height={20} />;
                                    })()
                            }
                        </View>
                }

                <View >
                    <Text style={styles.textoTitle}>
                        {title}
                    </Text>
                    <Text style={styles.textoValue}>
                        {`${value} %`}
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default StatusBattery;

const styles = StyleSheet.create({

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
});
