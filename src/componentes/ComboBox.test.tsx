import { act, fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import ComboBox, { ComboBoxProps } from './ComboBox';

describe('ComboBox Component', () => {

  const mockOnValueChange = jest.fn();
  const mockProps: ComboBoxProps = {
    onValueChange: mockOnValueChange,
    disable: false,
    value: -1,
    data: [{ id: 1, descricao: 'Item 1' }, { id: 2, descricao: 'Item 2' }],
    height: '100%',
    width: '100%',
    backgroundColor: 'transparent',
    focusColor: 'black',
    borderColor: '#A3A3A3',
    borderRadius: 1,
    borderWidth: 1,
    textoPlaceHolder: 'Selecione um item',
    textoCor: 'black',
    fontSize: 24,
    fontSizePlaceHolder: 18,
  };

  test('renderiza corretamente se não existirem itens', () => {
    expect(render(<ComboBox data={[]} value={null} />)).toBeNull
  });

  test('renderiza corretamente se value diferente de null', () => {
    expect(render(<ComboBox {...mockProps} value={1} />)).toHaveBeenCalled
  });

  test('renderiza corretamente se textoLabel está vazio', () => {
    const { getByText } = render(<ComboBox textoLabel="" />);
    expect(getByText('')).toBeTruthy();
  });

  test('renderiza corretamente se textoLabel está preenchido e desabilita', () => {
    const { getByText } = render(<ComboBox {...mockProps} textoLabel="texto" focus={true} />);
    expect(getByText('texto')).toBeTruthy();
  });

  test('renderiza corretamente se textoLabel está preenchido e habilitado', () => {
    const { getByText } = render(<ComboBox {...mockProps} textoLabel="texto" focus={true} disable={true} />);
    expect(getByText('texto')).toBeTruthy();
  });

  test('executa onValueChange quando um item é selecionado', () => {
    const { getByTestId } = render(
      <ComboBox {...mockProps} />
    );

    fireEvent.press(getByTestId('dropdown-item'));
    expect(mockProps.onValueChange).toHaveBeenCalledWith(-1);
  });

  test('trata o onFocus e onBlur corretamente', () => {
    const { queryByText, getByTestId } = render(<ComboBox {...mockProps} />);
    const dropdown = getByTestId('dropdown-item');

    act(() => { fireEvent(dropdown, 'focus') });
    expect(queryByText('Selecione um item')).toBeNull();

    act(() => { fireEvent(dropdown, 'blur') });
    expect(queryByText('Selecione um item')).toBeTruthy();
  });

  test('trata o onChange corretamente', () => {
    const { getByTestId } = render(<ComboBox {...mockProps} />);
    const dropdown = getByTestId('dropdown-item');
    act(() => {
      fireEvent(dropdown, 'change', { nativeEvent: { value: { id: 2, descricao: 'Item 2' } } })
    });
  });
});
