import React from 'react';
import { ActivityIndicator, ColorValue, Modal, StyleSheet, Text, View } from 'react-native';
import ProgressBar from './ProgressBar';

interface LoadingProgressBarProps {
    height?: any;
    animating?: boolean;
    size?: 'large' | 'small';
    color?: ColorValue;
    text?: string;
    showProgressBar1?: boolean;
    currentProgress1?: number;
    maxProgress1?: number;
    textProgress1?: string;
    showProgressBar2?: boolean;
    currentProgress2?: number;
    maxProgress2?: number;
    textProgress2?: string;
    showProgressBar3?: boolean;
    currentProgress3?: number;
    maxProgress3?: number;
    textProgress3?: string;
    showProgressBar4?: boolean;
    currentProgress4?: number;
    maxProgress4?: number;
    textProgress4?: string;
}

const LoadingProgressBar: React.FC<LoadingProgressBarProps> = ({
    height = '100%',
    animating = false,
    size = 'large',
    color = '#45D42E',
    text = '',
    showProgressBar1 = false,
    currentProgress1 = 0,
    maxProgress1 = 100,
    textProgress1 = 'Loading...',
    showProgressBar2 = false,
    currentProgress2 = 0,
    maxProgress2 = 100,
    textProgress2 = 'Loading...',
    showProgressBar3 = false,
    currentProgress3 = 0,
    maxProgress3 = 100,
    textProgress3 = 'Loading...',
    showProgressBar4 = false,
    currentProgress4 = 0,
    maxProgress4 = 100,
    textProgress4 = 'Loading...',
}) => {

    return (

        <Modal

            visible={animating}
            statusBarTranslucent={true}
            transparent={true}
            animationType="fade"
            testID="modal"
        >
            <View style={{ ...styles.container }}>
                <ActivityIndicator size={size} color={color} animating={animating} style={{ transform: [{ scaleX: 2 }, { scaleY: 2 }] }} testID="activity-indicator" />
                <Text style={styles.texto}>{text}</Text>

                <View style={{ width: '100%', gap: 30, justifyContent: 'center', alignItems: 'center', marginTop: 20 }}>

                    {
                        (showProgressBar1) &&
                        <View style={{ height: 40, width: '80%', alignItems: 'flex-start', justifyContent: 'center' }}>
                            <Text style={styles.textoProgress}>{textProgress1}</Text>
                            <ProgressBar progress={currentProgress1} maxProgress={maxProgress1} />
                        </View>
                    }

                    {
                        (showProgressBar2) &&
                        <View style={{ height: 40, width: '80%', alignItems: 'flex-start', justifyContent: 'center' }}>
                            <Text style={styles.textoProgress}>{textProgress2}</Text>
                            <ProgressBar progress={currentProgress2} maxProgress={maxProgress2} />
                        </View>
                    }

                    {
                        (showProgressBar3) &&
                        <View style={{ height: 40, width: '80%', alignItems: 'flex-start', justifyContent: 'center' }}>
                            <Text style={styles.textoProgress}>{textProgress3}</Text>
                            <ProgressBar progress={currentProgress3} maxProgress={maxProgress3} />
                        </View>
                    }

                    {
                        (showProgressBar4) &&
                        <View style={{ height: 40, width: '80%', alignItems: 'flex-start', justifyContent: 'center' }}>
                            <Text style={styles.textoProgress}>{textProgress4}</Text>
                            <ProgressBar progress={currentProgress4} maxProgress={maxProgress4} />
                        </View>
                    }

                </View>

            </View>

        </Modal>
    )
}

export default LoadingProgressBar;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "rgba(0,0,0,0.7)",
        gap: 20
    },
    texto: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 16,
        textAlign: 'center',
        fontWeight: "400",
        lineHeight: 24,
        color: "#FFFFFF",
    },
    textoProgress: {
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 14,
        textAlign: 'left',
        fontWeight: "400",
        lineHeight: 22,
        color: "#FFFFFF",
    },
    subtexto: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 28,
        textAlign: 'center',
        fontWeight: "400",
        lineHeight: 24,
        color: "#FFFFFF",
    },
});
