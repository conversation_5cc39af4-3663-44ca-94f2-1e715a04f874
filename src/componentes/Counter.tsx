import React from 'react';
import { ColorValue, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconMinus from '../assets/svg/icon_minus.svg';
import IconPlus from '../assets/svg/icon_plus.svg';

interface CounterProps {
    height?: any;
    width?: any;
    heightIcon?: number;
    widthIcon?: number;
    value?: number;
    texto?: string;
    maximo?: number;
    minimo?: number;
    enabledColor?: ColorValue;
    disabledColor?: ColorValue;
    onValueChange?: (value: number) => void;
}

const Counter: React.FC<CounterProps> = ({
    height = '100%',
    width = '100%',
    heightIcon = 20,
    widthIcon = 20,
    value = 0,
    texto = 'Item',
    maximo = 99,
    minimo = 0,
    enabledColor = '#D6D6D6',
    disabledColor = '#E5E5E5',
    onValueChange,
}) => {

    // incrementa contador
    const handleChangePlus = () => {

        if (value < maximo)
            onValueChange?.(value + 1);
    };

    // decrementa contador
    const handleChangeMinus = () => {

        if (value > minimo)
            onValueChange?.(value - 1);
    };

    const borderHeightIcon = heightIcon + (heightIcon * 0.6);
    const borderWidthIcon = widthIcon + (widthIcon * 0.6);

    return (

        <View style={{ height: height, width: width, flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>

            <Text style={{ ...styles.texto, width: '10%', color: (value) ? enabledColor : disabledColor }}> {value} </Text>

            <Text style={{ ...styles.texto, width: '60%', color: (value) ? enabledColor : disabledColor }}> {texto} </Text>

            <View style={{ width: '30%', flexDirection: 'row', justifyContent: 'flex-end', gap: 10 }}>
                <TouchableOpacity style={{
                    height: borderHeightIcon, width: borderWidthIcon,
                    justifyContent: 'center', alignItems: 'center',
                    borderWidth: 1, borderRadius: 8, borderColor: (value) ? enabledColor : disabledColor,
                    backgroundColor: (value) ? '#F5F5F5' : '#FFFFFF'
                }}
                    onPress={() => handleChangeMinus()}
                    testID='button-minus'>
                    <IconMinus height={heightIcon} width={widthIcon} color={'#737373'} />
                </TouchableOpacity>

                <TouchableOpacity style={{
                    height: borderHeightIcon, width: borderWidthIcon,
                    justifyContent: 'center', alignItems: 'center',
                    borderWidth: 1, borderRadius: 8, borderColor: enabledColor,
                    backgroundColor: '#F5F5F5'
                }}
                    onPress={() => handleChangePlus()}
                    testID='button-plus'>
                    <IconPlus height={heightIcon} width={widthIcon} color={'#737373'} />
                </TouchableOpacity>
            </View>


        </View>

    )
}

export default Counter;

const styles = StyleSheet.create({
    texto: {
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 20,
    },
})
