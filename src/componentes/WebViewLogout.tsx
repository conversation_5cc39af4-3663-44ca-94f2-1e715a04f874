import { useNavigation } from "@react-navigation/native";
import { useState } from "react";
import { View } from "react-native";
import WebView from "react-native-webview";
import React, { StackTypes } from "../routes";
import Loading from "./Loading";

// storage
import { KEY, setStorageString } from '../storages';

type WebViewLogoutProps = {
    url: string;
};

export function WebViewLogout({ url }: Readonly<WebViewLogoutProps>) {

    // navegação entre telas
    const navigation = useNavigation<StackTypes>();

    const [showWebView, setShowWebView] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(true);

    function handlelogout(event: any) {

        // se pagina ainda não carregada
        if (event.loading)
            return;

        // se existe url
        if (event.url.includes('/logout')) {

            // não deve mostrar webview
            setShowWebView(false);

            // finaliza loading
            setLoading(false);

            // limpa o token
            setStorageString(KEY.cognitoToken, '');
            navigation.navigate('Splash');

            return false;
        }
        
        return true;
    }

    return (

        <>

            <Loading animating={loading} text={''} />

            <View style={{ flex: 1 }}>
                {
                    showWebView
                        ? (
                            <WebView source={{ uri: url }}
                                originWhitelist={['*']}
                                onNavigationStateChange={handlelogout}
                                testID='webview'
                            />
                        )
                        : (
                            <View style={{ flex: 1, backgroundColor: 'white' }} />
                        )
                }

            </View>

        </>

    );
};
