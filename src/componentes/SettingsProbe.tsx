import React from 'react';
import { ActivityIndicator, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones svg
import IconCODI from '../assets/svg/icon_codi.svg';
import IconION from '../assets/svg/icon_ion.svg';
import IconModbus from '../assets/svg/icon_modbus.svg';
import IconModem from '../assets/svg/icon_modem.svg';
import IconPulso from '../assets/svg/icon_pulse.svg';
import IconRede from '../assets/svg/icon_rede.svg';
import IconSystem from '../assets/svg/icon_system.svg';
import IconOTA from '../assets/svg/icon_update_cpu.svg';

interface SettingsProbeProps {
    conectado?: boolean;
    sistemaConfigurado?: boolean
    redeConfigurado?: boolean
    modbusConfigurado?: boolean
    codiConfigurado?: boolean
    ionConfigurado?: boolean
    pulsoConfigurado?: boolean
    otaConfigurado?: boolean
    khompConfigurado?: boolean
    height?: any;
    width?: any;
    gap?: number;
    onPressSistema?: (value: any) => void;
    onPressRede?: (value: any) => void;
    onPressModbus?: (value: any) => void;
    onPressCODI?: (value: any) => void;
    onPressION?: (value: any) => void;
    onPressOTA?: (value: any) => void;
    onPressPulso?: (value: any) => void;
    onPressKhomp?: (value: any) => void;
}

const SettingsProbe: React.FC<SettingsProbeProps> = ({
    conectado = false,
    sistemaConfigurado = false,
    redeConfigurado = false,
    modbusConfigurado = false,
    codiConfigurado = false,
    ionConfigurado = false,
    pulsoConfigurado = false,
    otaConfigurado = false,
    khompConfigurado = false,
    height = '100%',
    width = '100%',
    gap = 5,
    onPressSistema,
    onPressRede,
    onPressModbus,
    onPressCODI,
    onPressION,
    onPressOTA,
    onPressPulso,
    onPressKhomp,
}) => {

    const iconSize = 25;
    const ColorIdle = '#E5E5E5'
    const ColorON = '#38B026';
    const ColorOFF = '#C0002B';

    // tamanho maximo para a area que vai comportar os botões dos módulos
    // botões = 7 | height = 60 | padding = 20
    const sizeMax = (7 * 60) + 20;

    // se tamanho da area for menor que tamanho padrão dos 7 botões recalcula tamanho dos botões
    const sizeButton = (height < sizeMax) ? (height - 20) / 7 : 60;

    // seta a cor de fundo do botão conforme status
    function getBackgroundColor(mqtt_conectado: boolean, status_modulo: boolean) {
        if (!mqtt_conectado) {
            return ColorIdle;
        }
        return status_modulo ? ColorON : ColorOFF;
    }

    return (

        <View style={{ height: height, width: width, justifyContent: 'center', paddingVertical: 10, paddingHorizontal: 65 }}>

            {
                (khompConfigurado)
                    ?
                    <View style={{ height: '100%', flexDirection: 'column', justifyContent: 'flex-start' }}>
                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(true, khompConfigurado) }}
                                onPress={onPressKhomp}
                                testID='button-khomp'>
                                <IconModem height={iconSize} width={iconSize} color={'#FFFFFF'} />
                            </TouchableOpacity>
                            <TouchableOpacity onPress={onPressKhomp} testID='text-khomp'>
                                <Text style={styles.textoBotaoConfiguracao}>Equipamento Khomp </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                    :
                    <View style={{ height: '100%', flexDirection: 'column', justifyContent: 'space-between' }}>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, sistemaConfigurado) }}
                                onPress={(conectado) ? onPressSistema : () => { }}
                                testID='button-sistema'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-sistema' />
                                        : <IconSystem height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressSistema : () => { }} testID='text-sistema'>
                                <Text style={styles.textoBotaoConfiguracao}> Sistema </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, redeConfigurado) }}
                                onPress={(conectado) ? onPressRede : () => { }}
                                testID='button-rede'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-rede' />
                                        : <IconRede height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressRede : () => { }} testID='text-rede'>
                                <Text style={styles.textoBotaoConfiguracao}> Configuração de Rede </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, modbusConfigurado) }}
                                onPress={(conectado) ? onPressModbus : () => { }}
                                testID='button-modbus'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-modbus' />
                                        : <IconModbus height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressModbus : () => { }} testID='text-modbus'>
                                <Text style={styles.textoBotaoConfiguracao}> Configuração Modbus </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, codiConfigurado) }}
                                onPress={(conectado) ? onPressCODI : () => { }}
                                testID='button-codi'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-codi' />
                                        : <IconCODI height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressCODI : () => { }} testID='text-codi'>
                                <Text style={styles.textoBotaoConfiguracao}> Configuração CODI </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, ionConfigurado) }}
                                onPress={(conectado) ? onPressION : () => { }}
                                testID='button-ion'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-ion' />
                                        : <IconION height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressION : () => { }} testID='text-ion'>
                                <Text style={styles.textoBotaoConfiguracao}> Configuração ION </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, pulsoConfigurado) }}
                                onPress={(conectado) ? onPressPulso : () => { }}
                                testID='button-pulso'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-pulso' />
                                        : <IconPulso height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressPulso : () => { }} testID='text-pulso'>
                                <Text style={styles.textoBotaoConfiguracao}> Configuração de Pulso </Text>
                            </TouchableOpacity>
                        </View>

                        <View style={{ ...styles.containerBotaoMenu, height: sizeButton }}>
                            <TouchableOpacity style={{ ...styles.botaoMenu, height: sizeButton - 10, width: sizeButton - 10, backgroundColor: getBackgroundColor(conectado, otaConfigurado) }}
                                onPress={(conectado) ? onPressOTA : () => { }}
                                testID='button-ota'>
                                {
                                    (!conectado)
                                        ? <ActivityIndicator size={'small'} color={'#FFFFFF'} animating={true} style={{ transform: [{ scaleX: 1 }, { scaleY: 1 }] }} testID='activity-indicator-ota' />
                                        : <IconOTA height={iconSize} width={iconSize} color={'#FFFFFF'} />
                                }
                            </TouchableOpacity>
                            <TouchableOpacity onPress={(conectado) ? onPressOTA : () => { }} testID='text-ota'>
                                <Text style={styles.textoBotaoConfiguracao}> Atualização OTA </Text>
                            </TouchableOpacity>
                        </View>

                    </View>
            }




        </View>
    )
}

export default SettingsProbe;

const styles = StyleSheet.create({

    containerBotaoMenu: {
        width: '100%',
        justifyContent: 'flex-start',
        alignItems: 'center',
        gap: 20,
        flexDirection: 'row',
    },

    botaoMenu: {
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 10,
        borderColor: '#E5E5E5',
        backgroundColor: '#E5E5E5'
    },

    textoBotaoConfiguracao: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 18,
        color: '#737373',
    },
})
