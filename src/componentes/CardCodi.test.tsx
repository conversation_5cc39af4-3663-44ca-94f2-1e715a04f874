import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import CardCodi from './CardCodi';

describe('CardCodi Component', () => {
  it('deve renderizar o componente corretamente quando existir codi', () => {
    const { getByText } = render(<CardCodi codi='codi' />);
    const codiText = getByText('codi');
    expect(codiText).toBeTruthy();
  });

  it('deve usar o valor padrão para codi quando não fornecido', () => {
    // Renderiza o componente sem passar a prop codi
    const { getByTestId } = render(<CardCodi />);

    // Verifica se o componente é renderizado corretamente usando o valor padrão para codi
    const component = getByTestId('view-principal');
    expect(component).toBeTruthy
  });

  it('não deve renderizar os botões se codi estiver vazio', () => {
    const { queryByTestId } = render(<CardCodi codi="" />);
    const deleteButton = queryByTestId('delete-button');
    const editButton = queryByTestId('edit-button');
    expect(deleteButton).toBeNull();
    expect(editButton).toBeNull();
  });

  it('deve chamar onPressDelCodi ao clicar no botão de deletar', () => {
    const mockOnPressDelCodi = jest.fn();
    const { getByTestId } = render(
      <CardCodi codi='codi' onPressDelCodi={mockOnPressDelCodi} />
    );
    const deleteButton = getByTestId('delete-button');
    fireEvent.press(deleteButton);
    expect(mockOnPressDelCodi).toHaveBeenCalled();
  });

  it('deve chamar onPressEditCodi ao clicar no botão de editar', () => {
    const mockOnPressEditCodi = jest.fn();
    const { getByTestId } = render(
      <CardCodi codi='codi' onPressEditCodi={mockOnPressEditCodi} />
    );
    const editButton = getByTestId('edit-button');
    fireEvent.press(editButton);
    expect(mockOnPressEditCodi).toHaveBeenCalled();
  });
});