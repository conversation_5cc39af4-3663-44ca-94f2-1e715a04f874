import { render } from '@testing-library/react-native';
import React from 'react';
import Mapa from './Mapa';

// Mock do LeafletView
jest.mock('@charlespalmerbf/react-native-leaflet-js', () => {
  return {
    LeafletView: () => null,  // Ou 'LeafletView' se preferir apenas renderizar o nome como string
  };
});

describe('Mapa Component', () => {
  it('deve renderizar corretamente com os valores padrão', () => {
    const { getByTestId } = render(<Mapa />);

    const leafletView = getByTestId('leaflet-view');

    expect(leafletView).toBeTruthy();
  });
});
