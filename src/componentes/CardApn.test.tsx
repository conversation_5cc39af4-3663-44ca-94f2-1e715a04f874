import React from 'react';
import { render, screen } from "@testing-library/react-native";
import CardApn from "./CardApn";

describe('CardApn Component', () => {

    it('deve renderizar o componente corretamente quando não existi apn configurada', () => {
        const {} = render(<CardApn/>);        
        expect(screen).toBeTruthy();
    });

    it('deve renderizar o componente corretamente quando existir apn configurada', () => {
        const { getByText } = render(<CardApn apn='apn.teste.com.br' user='teste' password='teste'/>);
        const apnText = getByText('apn.teste.com.br');
        expect(apnText).toBeTruthy();
    });

});