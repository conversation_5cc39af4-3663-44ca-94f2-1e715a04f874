import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import CardIon from './CardIon';


describe('CardIon Component', () => {
    it('deve renderizar o componente corretamente quando existir ion', () => {
        const { getByText } = render(<CardIon ion='ion' />);
        const ionText = getByText('ion');
        expect(ionText).toBeTruthy();
    });

    it('deve usar o valor padrão para ion quando não fornecido', () => {
        // Renderiza o componente sem passar a prop ion
        const { getByTestId } = render(<CardIon />);

        // Verifica se o componente é renderizado corretamente usando o valor padrão para ion
        const component = getByTestId('view-principal');
        expect(component).toBeTruthy
    });


    it('não deve renderizar os botões se ion estiver vazio', () => {
        const { queryByTestId } = render(<CardIon ion="" />);
        const deleteButton = queryByTestId('delete-button');
        const editButton = queryByTestId('edit-button');
        expect(deleteButton).toBeNull();
        expect(editButton).toBeNull();
    });

    it('deve chamar onPressDelIon ao clicar no botão de deletar', () => {
        const mockOnPressDelIon = jest.fn();
        const { getByTestId } = render(
            <CardIon ion='ion' onPressDelIon={mockOnPressDelIon} />
        );
        const deleteButton = getByTestId('delete-button');
        fireEvent.press(deleteButton);
        expect(mockOnPressDelIon).toHaveBeenCalled();
    });

    it('deve chamar onPressEditIon ao clicar no botão de editar', () => {
        const mockOnPressEditIon = jest.fn();
        const { getByTestId } = render(
            <CardIon ion='ion' onPressEditIon={mockOnPressEditIon} />
        );
        const editButton = getByTestId('edit-button');
        fireEvent.press(editButton);
        expect(mockOnPressEditIon).toHaveBeenCalled();
    });
});
