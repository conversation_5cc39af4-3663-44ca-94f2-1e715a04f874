import React from 'react';
import { ActivityIndicator, ColorValue, StyleSheet, Text, View } from 'react-native';
import IconWiFi from '../assets/svg/icon_wifi.svg';

interface StatusWiFiProps {
    value?: number;
    signalLowColor?: ColorValue;
    signalMediumColor?: ColorValue;
    signalHighColor?: ColorValue;
    iconSignalLowColor?: ColorValue;
    iconSignalMediumColor?: ColorValue;
    iconSignalHighColor?: ColorValue;
    disabledColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    title?: string;
    loading?: boolean;
}

const StatusWiFi: React.FC<StatusWiFiProps> = ({
    value = 0,
    signalLowColor = '#FF0000',
    signalMediumColor = '#FFFF00',
    signalHighColor = '#00FF00',
    iconSignalLowColor = '#C0002B',
    iconSignalMediumColor = '#D49600',
    iconSignalHighColor = '#38B026',
    disabledColor = '#D6D6D6',
    height = 100,
    width = 150,
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    title = 'WiFi',
    loading = false,
}) => {

    const signalColor = (value <= -80) ? signalLowColor : (value > -80 && value <= -70) ? signalMediumColor : signalHighColor;
    const iconColor = (value <= -80) ? iconSignalLowColor : (value > -80 && value <= -70) ? iconSignalMediumColor : iconSignalHighColor;

    return (

        <View style={{
            height: 104,
            width: width,
            paddingHorizontal: 20,
            paddingVertical: 24,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'center',
            alignItems: 'center',
        }}>

            <View style={{ height: 56, width: '100%', justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                {
                    (loading)
                        ?
                        <View style={{ height: 48, width: 48, justifyContent: 'center', alignItems: 'center' }}>
                            <ActivityIndicator size={'large'} color={'#45D42E'} animating={true} testID="activity-indicator" />
                        </View>
                        :
                        <View style={{ height: 48, width: 48, borderRadius: 9999, backgroundColor: signalColor, justifyContent: 'center', alignItems: 'center' }}>
                            <IconWiFi color={iconColor} width={20} height={20} />
                        </View>
                }

                <View >
                    <Text style={styles.textoTitle}>
                        {title}
                    </Text>
                    <Text style={styles.textoValue}>
                        {`${value} db`}
                    </Text>
                </View>

            </View>

        </View>
    )
}

export default StatusWiFi;

const styles = StyleSheet.create({
    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
});
