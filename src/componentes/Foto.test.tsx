import { act, fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import Foto, { FotoProps } from './Foto';

// Mockando o módulo `react-native-image-picker`
jest.mock('react-native-image-picker', () => ({
  launchImageLibrary: jest.fn(),
  launchCamera: jest.fn(),
}));

describe('Foto Component', () => {

  const image = 'https://www.comerc.com.br/hs-fs/hubfs/raw_assets/public/Comerc%20Website%202024/css/images/comerc_logo_rodape.png?width=245&height=74&name=comerc_logo_rodape.png';

  const mockProps: FotoProps = {
    imagem: '',
    texto_imagem: 'Imagem de teste',
    height: 100,
    width: 100,
    heightIcon: 30,
    widthIcon: 30,
    texto: '1024x768',
    subTexto: 'resolução mínima',
    onValueChange: jest.fn(),
    onValueText: jest.fn(),
    onPressImage: jest.fn(),
  };

  it('deve renderizar corretamente com os valores default', () => {
    const { getByTestId } = render(<Foto />);
    expect(getByTestId('texto-imagem').props.children).toBe(mockProps.texto);
    expect(getByTestId('sub-texto-imagem').props.children).toBe(mockProps.subTexto);
  });

  it('deve tratar a imagem deletada', async () => {
    const { getByTestId } = render(<Foto {...mockProps} imagem={image} />);

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-erase'));
      });

      expect(mockProps.onValueChange).toHaveBeenCalledWith('');
    });
  });

  it('deve tratar a escolha da imagem na galeria', async () => {
    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: false,
      assets: [{ uri: image }],
    };

    (launchImageLibrary as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-gallery'));
      });

      expect(mockProps.onValueChange).toHaveBeenCalledWith(image);
    });
  });

  it('deve tratar a escolha de uma imagem indefinida na galeria', async () => {
    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: false,
      assets: [{ uri: undefined }],
    };

    (launchImageLibrary as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-gallery'));
      });

      expect(mockProps.onValueChange).toHaveBeenCalledWith(image);
    });
  });

  it('deve tratar a captura da imagem pela camera', async () => {
    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: false,
      assets: [{ uri: image }],
    };

    (launchCamera as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-camera'));
      });

      expect(mockProps.onValueChange).toHaveBeenCalledWith(image);
    });
  });

  it('deve tratar a captura de uma imagem indefinida pela camera', async () => {
    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: false,
      assets: [{ uri: undefined }],
    };

    (launchCamera as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-camera'));
      });

      expect(mockProps.onValueChange).toHaveBeenCalledWith(image);
    });
  });

  it('deve tratar o cancelamento da foto', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: true,
      assets: [],
    };

    (launchCamera as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-camera'));
      });

      expect(consoleSpy).toHaveBeenCalledWith('Usuário cancelou a foto');
    });

    consoleSpy.mockRestore();
  });

  it('deve tratar o cancelamento da imagem na galeria', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    const { getByTestId } = render(<Foto {...mockProps} />);

    const mockResponse = {
      didCancel: true,
      assets: [],
    };

    (launchImageLibrary as jest.Mock).mockImplementation((options, callback) => {
      callback(mockResponse);
    });

    await waitFor(async () => {
      await act(async () => {
        fireEvent.press(getByTestId('button-gallery'));
      });

      expect(consoleSpy).toHaveBeenCalledWith('Usuário cancelou a escolha da imagem');
    });

    consoleSpy.mockRestore();
  });
});
