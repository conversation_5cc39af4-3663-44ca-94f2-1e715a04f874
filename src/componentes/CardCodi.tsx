import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones vetoriais
import IconEdit from '../assets/svg/icon_edit.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

interface CardCodiProps {
    codi?: string;
    height?: any;
    width?: any;
    onPressDelCodi?: (value: any) => void;
    onPressEditCodi?: (value: any) => void;
    onCodiCurrent?: (value: number) => void;
}

const CardCodi: React.FC<CardCodiProps> = ({
    codi = '',
    height = 60,
    width = '100%',
    onPressDelCodi,
    onPressEditCodi,
    onCodiCurrent,

}) => {

    return (

        <View style={{ ...styles.container, width: width }} testID="view-principal">

            {
                // se codi existe
                (codi.length > 0)
                    ?
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 5 }}>

                        <View style={styles.containerCodi}>
                            <Text style={styles.textoCodi}>{codi}</Text>
                        </View>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#C0002B' }}
                            onPress={onPressDelCodi}
                            testID="delete-button">
                            <IconTrash color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                        <TouchableOpacity style={{ height: 60, width: 60, borderRadius: 10, justifyContent: 'center', alignItems: 'center', backgroundColor: '#737373' }}
                            onPress={onPressEditCodi}
                            testID="edit-button">
                            <IconEdit color={"#FFFFFF"} width={25} height={25} />
                        </TouchableOpacity>
                    </View>
                    : null
            }


        </View>
    )
}

export default CardCodi;

const styles = StyleSheet.create({

    container: {
        flex: 1
    },

    containerCodi: {
        height: 60,
        width: '60%',
        flexDirection: 'row',
        paddingHorizontal: 20,
        borderRadius: 10,
        justifyContent: 'flex-start',
        alignItems: 'center',
        backgroundColor: '#2E8C1F',
        gap: 10
    },

    textoCodi: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,
        color: '#FFFFFF',
    },

});