import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { MascaraIP } from '../funcoes';
import TextEntry from './TextEntry';

// Mockando a função MascaraIP corretamente 
jest.mock('../funcoes', () => ({
    MascaraIP: jest.fn(),
}));

describe('TextEntry Component', () => {
    it('deve renderizar o componente corretamente', () => {

        const { getByPlaceholderText } = render(
            <TextEntry placeHolder="Digite aqui" />
        );
        const textInput = getByPlaceholderText('Digite aqui');
        expect(textInput).toBeTruthy();
    });

    it('deve chamar onValueChange ao alterar o texto', () => {
        const mockOnValueChange = jest.fn();
        const { getByPlaceholderText } = render(
            <TextEntry placeHolder="Digite aqui" onValueChange={mockOnValueChange} />
        );

        const textInput = getByPlaceholderText('Digite aqui');
        fireEvent.changeText(textInput, 'novo texto');
        expect(mockOnValueChange).toHaveBeenCalledWith('novo texto');
    });

    it('deve renderizar a label quando textoLabel está presente e value não é vazio', () => {
        const { getByText } = render(
            <TextEntry textoLabel="Etiqueta" value="texto" />
        );
        const label = getByText('Etiqueta');
        expect(label).toBeTruthy();
    });

    it('não deve renderizar a label quando textoLabel é vazio e value tem valor', () => {
        const { queryByText } = render(
            <TextEntry textoLabel="" value="texto" />
        );
        const label = queryByText('texto');
        expect(label).toBeNull();
    });

    it('não deve renderizar quando value está vazio', () => {
        const { queryByText } = render(
            <TextEntry textoLabel="Etiqueta" value="" />
        );
        const label = queryByText('Etiqueta');
        expect(label).toBeNull();
    });

    it('deve respeitar a prop editable', () => {
        const { getByPlaceholderText } = render(
            <TextEntry placeHolder="Digite aqui" editable={false} />
        );
        const textInput = getByPlaceholderText('Digite aqui');
        expect(textInput.props.editable).toBe(false);
    });

    it('deve formatar o texto como IP quando type é "ip"', () => {
        // Mockando a implementação da função MascaraIP 
        (MascaraIP as jest.Mock).mockImplementation((texto: string) => `formatted-${texto}`);
        const mockOnValueChange = jest.fn(); const {
            getByPlaceholderText } = render(
                <TextEntry placeHolder="Digite aqui" value={'192168000001'} onValueChange={mockOnValueChange} type="ip" />
            );
        const textInput = getByPlaceholderText('Digite aqui');
        fireEvent.changeText(textInput, '192168000001');
        expect(MascaraIP).toHaveBeenCalledWith('192168000001');
    });
});
