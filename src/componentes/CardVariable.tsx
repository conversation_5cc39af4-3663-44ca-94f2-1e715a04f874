import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import IconDown from '../assets/svg/icon_chevron-down.svg';
import IconRight from '../assets/svg/icon_chevron-right.svg';
import { DescricaoFormato } from '../constantes';
import { VariablesSettings } from '../data';
import CheckBox from './CheckBox';
import Divider from './Divider';

export interface CardVariableProps {
    data?: VariablesSettings[],
    descricao?: string;
    variavel?: string;
    ultimoEnvio?: boolean;
    mediaEnvio?: boolean;
    funcao?: number;
    endereco?: number;
    fator?: number;
    formato?: number;
    height?: any;
    width?: any;
    onUpdateData?: (value: VariablesSettings[]) => void;
}

const CardVariable: React.FC<CardVariableProps> = ({
    data = [],
    descricao = 'Descrição',
    variavel = 'variável',
    ultimoEnvio = true,
    mediaEnvio = false,
    funcao = 0,
    endereco = 0,
    fator = 0,
    formato = 0,
    height = 100,
    width = '100%',
    onUpdateData,
}) => {

    const [show, setShow] = useState<boolean>(false);
    const [ultimo, setUltimo] = useState<boolean>(ultimoEnvio);
    const [media, setMedia] = useState<boolean>(mediaEnvio);
    const [variaveis, setVariaveis] = useState<VariablesSettings[]>(data);

    const UpdateVariable = (variables: VariablesSettings[], indice: number, ultimo: boolean, media: boolean) => {

        // atualiza variavel selecionada
        const variableAtualizada = {
            ...variables[indice],
            valorUltimaVariable: ultimo,
            valorMediaVariable: media,
        }

        // atualiza variaveis
        variables.splice(indice, 1, variableAtualizada);

        return variables;
    }

    const handleChangeEnvio = () => {

        const indice = variaveis.findIndex(slave => slave.nomeVariable === variavel);
        setVariaveis(UpdateVariable(variaveis, indice, ultimo, media));

        if (onUpdateData) {
            onUpdateData(variaveis);
        }
    };

    const handleChangeMedia = () => {

        const indice = variaveis.findIndex(slave => slave.nomeVariable === variavel);
        setVariaveis(UpdateVariable(variaveis, indice, ultimo, media));

        if (onUpdateData) {
            onUpdateData(variaveis);
        }
    };

    useEffect(() => {
        handleChangeEnvio();
    }, [ultimo]);

    useEffect(() => {
        handleChangeMedia();
    }, [media]);

    return (

        <View style={styles.container}>

            <View style={{ height: (show) ? 160 : height, width: '100%', padding: 10, borderWidth: 1, borderRadius: 8, borderColor: '#D6D6D6', gap: 11 }}>

                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                    <View style={{ flexDirection: 'column' }}>
                        <Text style={styles.textoNome} testID='text-descricao'>{(descricao !== '') ? descricao : variavel}</Text>
                        <Text style={{ ...styles.textoNomeVariavel, fontSize: 14, color: '#A3A3A3' }} testID='text-variavel'>{variavel}</Text>
                    </View>
                    <TouchableOpacity onPress={() => setShow(!show)} testID='show-toggle-button'>
                        {
                            (show)
                                ?
                                <IconDown color={"#737373"} width={30} height={30} />
                                :
                                <IconRight color={"#737373"} width={30} height={30} />
                        }
                    </TouchableOpacity>
                </View>

                <View style={{ flexDirection: 'row', gap: 20 }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                        <CheckBox height={25} width={25} value={ultimo} onValueChange={setUltimo} testID='ultimo-checkbox' />
                        <Text style={styles.textoNomeVariavel}>Envia o último</Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}  >
                        <CheckBox height={25} width={25} value={media} onValueChange={setMedia} testID='media-checkbox' />
                        <Text style={styles.textoNomeVariavel}>Envia a média</Text>
                    </View>
                </View>

                {
                    (show) &&
                    <Divider testID='view-divider' />
                }

                {
                    (show) &&
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', gap: 10 }}>
                        <View style={{ height: 45 }}>
                            <Text style={styles.textoInfoTitulo}>Função</Text>
                            <Text style={styles.textoInfo}>{funcao}</Text>
                        </View>
                        <View style={{ height: 45 }}>
                            <Text style={styles.textoInfoTitulo}>Endereço</Text>
                            <Text style={styles.textoInfo}>{endereco}</Text>
                        </View>
                        <View style={{ height: 45 }}>
                            <Text style={styles.textoInfoTitulo}>Fator</Text>
                            <Text style={styles.textoInfo}>{fator}</Text>
                        </View>
                        <View style={{ height: 45 }}>
                            <Text style={styles.textoInfoTitulo}>Formato</Text>
                            <Text style={styles.textoInfo}>{DescricaoFormato[formato as keyof typeof DescricaoFormato]}</Text>
                        </View>
                    </View>
                }

            </View>

        </View>
    )
}

export default CardVariable;

const styles = StyleSheet.create({

    container: {
        flex: 1,
        paddingHorizontal: 0,
    },

    textoNome: {
        fontFamily: 'Exo2_700Bold',
        fontSize: 16,
    },

    textoNomeVariavel: {
        fontFamily: 'Exo2_500Medium',
        fontSize: 16,
    },

    textoInfoTitulo: {
        fontFamily: 'Exo2_700Bold',
        fontWeight: '600',
        fontSize: 16,
    },

    textoInfo: {
        marginStart: 15,
        fontFamily: 'SourceSans3_500Medium',
        fontWeight: '600',
        fontSize: 16,
    }
});
