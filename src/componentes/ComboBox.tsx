import React, { useEffect, useState } from 'react';
import { ColorValue, StyleSheet, Text, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

export interface ComboBoxProps {
  onValueChange?: (value: number) => void;
  disable?: boolean,
  value?: any;
  data?: any;
  height?: any,
  width?: any,
  backgroundColor?: ColorValue;
  focusColor?: ColorValue;
  borderColor?: ColorValue;
  borderRadius?: number;
  borderWidth?: number;
  textoPlaceHolder?: string;
  textoLabel?: string;
  textoCor?: ColorValue;
  fontSize?: number;
  fontSizePlaceHolder?: number;
  focus?: boolean;
  testID?: string;
}

const ComboBox: React.FC<ComboBoxProps> = ({
  onValueChange,
  disable = false,
  value = -1,
  data = [{ id: 1, descricao: 'Item 1' }],
  height = '100%',
  width = '100%',
  backgroundColor = 'transparent',
  focusColor = 'black',
  borderColor = 'black',
  borderRadius = 1,
  borderWidth = 1,
  textoPlaceHolder = "",
  textoLabel = "",
  textoCor = 'black',
  fontSize = 24,
  fontSizePlaceHolder = 18,
  focus = false,
  testID = 'dropdown-item'
}) => {

  const [id, setID] = useState<any>(value);
  const [isFocus, setIsFocus] = useState(focus);

  const renderLabel = () => {

    // se total de dados contidos for menor que o selecionado não apresenta label
    if (data.length < (id + 1))
      return null;

    // se id valido ou esta com focu apresenta label
    if (((id >= 0) && (id != null)) || isFocus) {

      return (
        (textoLabel) ? <Text style={[{
          ...styles.label,
          backgroundColor: backgroundColor
        },
        isFocus && { color: focusColor }]}> {textoLabel}
        </Text>
          : null
      );
    }

    return null;
  };

  // trata a mudança do item selecionado
  const handleChangeItem = (item: number) => {
    setID(item);
    onValueChange?.(item);
  };

  useEffect(() => {
    handleChangeItem(value);
  }, [value]);

  return (
    <View style={{ backgroundColor: backgroundColor, width: width }}>
      {renderLabel()}
      <Dropdown
        style={[{
          ...styles.dropdown,
          borderRadius: borderRadius,
          borderWidth: borderWidth,
          borderColor: (disable) ? '#A3A3A3' : 'black',
          height: height,
          width: '100%'
        },
        isFocus && { borderColor: focusColor }]
        }
        disable={disable}
        placeholderStyle={{ ...styles.placeholderStyle, fontSize: fontSizePlaceHolder }}
        selectedTextStyle={{ ...styles.selectedTextStyle, color: textoCor, fontSize: fontSize }}
        iconStyle={styles.iconStyle}
        data={data}
        maxHeight={300}
        labelField="descricao"
        valueField="id"
        placeholder={!isFocus ? textoPlaceHolder : undefined}
        value={id}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={item => {
          handleChangeItem(item.id);
          setIsFocus(false);
        }}
        testID={testID}
      />
    </View>
  );
};

export default ComboBox;

const styles = StyleSheet.create({
  dropdown: {
    paddingHorizontal: 8,
  },

  icon: {
    marginRight: 5,
  },

  label: {
    color: '#737373',
    fontFamily: 'Exo2_600SemiBold',
    position: 'absolute',
    left: 22,
    top: -10,
    zIndex: 999,
    paddingHorizontal: 8,
    fontSize: 14,
  },

  placeholderStyle: {
    fontFamily: 'SourceSans3_400Regular',
    fontSize: 18,
    color: '#737373',
    textAlign: 'center'
  },

  selectedTextStyle: {
    fontFamily: 'SourceSans3_600SemiBold',
    fontSize: 24,
    textAlign: 'center'
  },

  iconStyle: {
    width: 30,
    height: 30,
  },
});
