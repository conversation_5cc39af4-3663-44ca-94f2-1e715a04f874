import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import MessageBox from './MessageBox';


describe('MessageBox Component', () => {
  test('renderiza corretamente com as propriedades padrão', () => {
    const { getByText } = render(<MessageBox />);
    
    expect(getByText('Message Box')).toBeTruthy();
    expect(getByText('Descrição do message box')).toBeTruthy();
    expect(getByText('OK')).toBeTruthy();
  });

  test('rederiza corretamente com as propriedades customizadas', () => {
    const { getByText } = render(
      <MessageBox title="Sucesso" description="Mensagem de sucesso" textButton="Confirmar" type='sucess'/>
    );

    expect(getByText('Sucesso')).toBeTruthy();
    expect(getByText('Mensagem de sucesso')).toBeTruthy();
    expect(getByText('Confirmar')).toBeTruthy();
  });

  test('fecha o modal quando o botão fechar é pressionado', () => {
    const mockOnClose = jest.fn();
    const { getByTestId } = render(<MessageBox onClose={mockOnClose} type='error'/>);

    fireEvent.press(getByTestId('close-button'));
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  test('fecha o modal quando o botão fechar principal é pressionado', () => {
    const mockOnClose = jest.fn();
    const { getByText } = render(<MessageBox onClose={mockOnClose} />);

    fireEvent.press(getByText('OK'));
    
    expect(mockOnClose).toHaveBeenCalled();
  });
});