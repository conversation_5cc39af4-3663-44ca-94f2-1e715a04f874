import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import CardPulso from './CardPulso';

describe('CardPulso Component', () => {
  it('deve renderizar o componente corretamente quando existir contador de pulso', () => {
    const { getByText } = render(<CardPulso pulso='pulso' />);
    const pulsoText = getByText('pulso');
    expect(pulsoText).toBeTruthy();
  });

  it('deve usar o valor padrão para o contador de pulso quando não fornecido', () => {
    // Renderiza o componente sem passar a prop pulso
    const { getByTestId } = render(<CardPulso />);

    // Verifica se o componente é renderizado corretamente usando o valor padrão para o contador de pulso
    const component = getByTestId('view-principal');
    expect(component).toBeTruthy
  });

  it('não deve renderizar os botões se contador de pulso estiver vazio', () => {
    const { queryByTestId } = render(<CardPulso pulso="" />);
    const deleteButton = queryByTestId('delete-button');
    const editButton = queryByTestId('edit-button');
    expect(deleteButton).toBeNull();
    expect(editButton).toBeNull();
  });

  it('deve chamar onPressDelPulso ao clicar no botão de deletar', () => {
    const mockOnPressDelPulso = jest.fn();
    const { getByTestId } = render(
      <CardPulso pulso='pulso' onPressDelPulso={mockOnPressDelPulso} />
    );
    const deleteButton = getByTestId('delete-button');
    fireEvent.press(deleteButton);
    expect(mockOnPressDelPulso).toHaveBeenCalled();
  });

  it('deve chamar onPressEditPulso ao clicar no botão de editar', () => {
    const mockOnPressEditPulso = jest.fn();
    const { getByTestId } = render(
      <CardPulso pulso='pulso' onPressEditPulso={mockOnPressEditPulso} />
    );
    const editButton = getByTestId('edit-button');
    fireEvent.press(editButton);
    expect(mockOnPressEditPulso).toHaveBeenCalled();
  });
});
