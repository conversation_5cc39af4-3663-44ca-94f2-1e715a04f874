import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import Filter from './Filter';

describe('Filter Component', () => {
    it('deve renderizar corretamento com as propriedades default', () => {
        const { getByPlaceholderText } = render(<Filter />);
        expect(getByPlaceholderText('')).toBeTruthy();
    });

    it('deve renderizar corretamento quando existe um value passado pelo usuário', () => {
        const { getByPlaceholderText } = render(<Filter value={'teste'} />);
        expect(getByPlaceholderText('')).toBeTruthy();
    });

    it('deve chamar o onValueChange quando o text input muda', () => {
        const onValueChangeMock = jest.fn();
        const { getByPlaceholderText } = render(
            <Filter placeHolder="Search" onValueChange={onValueChangeMock} />
        );
        const input = getByPlaceholderText('Search');

        fireEvent.changeText(input, 'test');
        expect(onValueChangeMock).toHaveBeenCalledWith('test');
    });

    it('deve renderizar corretamente os icones', () => {
        const { getByTestId, rerender } = render(<Filter icon="pesquisa" />);
        expect(getByTestId('icon-pesquisa')).toBeTruthy();

        rerender(<Filter icon="filtro" />);
        expect(getByTestId('icon-filtro')).toBeTruthy();
    });
});