import { render } from '@testing-library/react-native';
import React from 'react';
import StatusVBattery from './StatusVBattery';

describe('StatusVBattery Component', () => {

  it('renderiza corretamento com as propriedades default', () => {
    const { getByText } = render(<StatusVBattery />);

    expect(getByText('Tensão')).toBeTruthy();
    expect(getByText('0 v')).toBeTruthy();
  });

  it('renderiza o estado do loading corretamente', () => {
    const { getByTestId } = render(<StatusVBattery loading={true} />);

    expect(getByTestId('activity-indicator')).toBeTruthy();
  });

  it('renderiza corretamene com as propriedades customizadas', () => {
    const { getByText } = render(<StatusVBattery value={5} title="Voltagem" />);

    expect(getByText('Voltagem')).toBeTruthy();
    expect(getByText('5 v')).toBeTruthy();
  });
});
