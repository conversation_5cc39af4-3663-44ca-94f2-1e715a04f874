import React from 'react';
import { View } from 'react-native';

interface DividerProps {
  width?: any;
  orientation?: 'horizontal' | 'vertical';
  color?: string;
  dividerStyle?: any;
  testID?: string;
}

const Divider: React.FC<DividerProps> = ({
  width = 1,
  orientation = 'horizontal',
  color = '#DFE4EA',
  dividerStyle,
  testID = 'view-divider',
}) => {
  const dividerStyles = [
    { width: orientation === 'horizontal' ? '100%' : width },
    { height: orientation === 'vertical' ? '100%' : width },
    { backgroundColor: color },
    dividerStyle,
  ];

  return <View style={dividerStyles} testID={testID} />;
};

export default Divider;
