import { render } from '@testing-library/react-native';
import React from 'react';
import Loading from "./Loading";

describe('Loading Component', () => {

    it('não deve renderizar o modal quando animating não é passado', () => {
        const { queryByTestId } = render(
            <Loading />
        );

        const modal = queryByTestId('modal');
        expect(modal).toBeNull();
    });

    it('deve renderizar o modal quando animating é true', () => {
        const { getByTestId } = render(
            <Loading animating={true} text="Carregando..." />
        );
        const modal = getByTestId('modal');
        expect(modal.props.visible).toBe(true);
    });

    it('não deve renderizar o modal quando animating é false', () => {
        const { queryByTestId } = render(
            <Loading animating={false} text="Carregando..." />
        );

        const modal = queryByTestId('modal');
        expect(modal).toBeNull();
    });

    it('deve renderizar o texto corretamente', () => {
        const { getByText } = render(
            <Loading animating={true} text="Carregando..." />
        );
        const text = getByText('Carregando...');
        expect(text).toBeTruthy();
    });

    it('deve aplicar o tamanho e a cor corretamente ao ActivityIndicator', () => {
        const { getByTestId } = render(
            <Loading animating={true} size="large" color="#FF0000" />
        );

        const activityIndicator = getByTestId('activity-indicator');
        expect(activityIndicator.props.size).toBe('large');
        expect(activityIndicator.props.color).toBe('#FF0000');
    });
});
