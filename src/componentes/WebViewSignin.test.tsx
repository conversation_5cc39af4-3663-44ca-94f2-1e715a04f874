
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react-native';
import axios, { AxiosError } from 'axios';
import React from 'react';
import * as storage from '../storages'; // mock do getStorageJson
import { WebViewSignin } from './WebViewSignin';

// Mock de dependências externas
jest.mock('@react-navigation/native', () => ({
    useNavigation: jest.fn(() => ({
        dispatch: jest.fn(),
    })),
    CommonActions: {
        navigate: jest.fn()
    }

}));

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

jest.mock('react-native-webview', () => {
    const { View } = require('react-native');
    return (props: any) => <View testID="webview-mock" {...props} />;
});

//jest.mock('react-native-webview', () => 'WebViewNavigation');
//jest.mock('../modal/messagebox/Erro', () => 'MessageBoxErro');
jest.mock('./Loading', () => 'Loading');
//jest.mock('../pages/InternetOff', () => 'InternetOff');
jest.mock('../pages/Logout', () => jest.fn());

jest.mock('../storages', () => ({
    getStorageString: jest.fn(() => Promise.resolve(null)),
    setStorageString: jest.fn(),
    setStorageJson: jest.fn(),
    KEY: {
        cognitoToken: 'cognitoToken',
        cognitoUser: 'cognitoUser',
        cognitoEmail: 'cognitoEmail',
        cognitoResponse: 'cognitoResponse'
    }
}));

jest.mock('@react-native-async-storage/async-storage', () => ({
    setItem: jest.fn(),
    getItem: jest.fn(),
    clear: jest.fn(),
}));

describe('WebViewSignin Component', () => {
    it('renderiza corretamente quando internet com falha', () => {
        const {unmount} = render(<WebViewSignin url="https://example.com" hasInternet={false} />);
        expect(screen).toBeTruthy();

        unmount();
    });  

    it('renderiza corretamente quando internet OK', () => {

        const {unmount} = render(<WebViewSignin url="https://example.com" hasInternet={true} />);
        expect(screen).toBeTruthy();

        unmount();
    });

    it('executa com um token específico e status 200', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('mocked_token');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);

        const { unmount } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);                

        unmount();
    });

    it('executa com um token específico e status error 400', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('mocked_token');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 400
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);

        const { unmount } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);
                

        unmount();
    });    

    it('executa com um token específico e status 200, mas grupo não é zordon', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('mocked_token');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["powerview"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);

        const { unmount } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);                

        unmount();
    });

    it('executa com um token específico, mas gerou exceção', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('mocked_token');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },
                status: 400               
            },
            
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);

        const { unmount } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);
          
        unmount();
    });

    it('executa com um token específico, mas gerou exceção o retornar o token', async () => {

        jest.spyOn(storage, 'getStorageString').mockRejectedValue(Error('token invalido'));  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },
                status: 400               
            },            
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);

        const { unmount } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);                

        unmount();
    });

    it('executa com um token específico e status 200, mas token não existe', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse);        
        
        
        await waitFor(() => {
           const { unmount } =  render(<WebViewSignin url="https://example.com" hasInternet={true} />);   
           unmount();         
        });        
    });    

    it('chama handleAuthorizationCodeExchange ao navegar para URL com code e loading true, porque esta carregando a pagina', async () => {

       jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse); 

        const {unmount, getByTestId } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);

        jest.useFakeTimers();


        const webview = await waitFor(() => getByTestId('webview-signin'));        

        // simula navegação com code
        fireEvent(webview, 'onNavigationStateChange', {
            loading: true,
            url: 'https://example.com/callback/cognito?code=123456'
        });
        
        unmount();
    });

    it('chama handleAuthorizationCodeExchange ao navegar para URL com code e loading false, carregou pagina mas code não possui valor', async () => {

       jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse); 

        const {unmount, getByTestId, getByText} = render( <WebViewSignin url="https://example.com" hasInternet={true} />);

        jest.useFakeTimers();

        const webview = await waitFor(() => getByTestId('webview-signin'));        

        // simula navegação com code
        fireEvent(webview, 'onNavigationStateChange', {
            loading: false,
            url: 'https://example.com/callback/cognito?code='
        });    

        act( () => {         
            fireEvent.press(getByText('OK'));
        });

        unmount();
    });    

    it('chama handleAuthorizationCodeExchange ao navegar para URL com code e loading false, carregou pagina errada', async () => {

       jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse); 

        const {unmount, getByTestId } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);

        jest.useFakeTimers();


        const webview = await waitFor(() => getByTestId('webview-signin'));        

        // simula navegação com code
        fireEvent(webview, 'onNavigationStateChange', {
            loading: false,
            url: 'https://example.com/callback/login?code=123456'
        });
            
        unmount();
    });    

    it('chama handleAuthorizationCodeExchange ao navegar para URL com code e loading false, porque ja carregou a pagina', async () => {

       jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse); 

        const {unmount, getByTestId } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);

        jest.useFakeTimers();


        const webview = await waitFor(() => getByTestId('webview-signin'));        

        // simula navegação com code
        fireEvent(webview, 'onNavigationStateChange', {
            loading: false,
            url: 'https://example.com/callback/cognito?code=123456'
        });

        unmount();
        
    });  

    it('chama handleAuthorizationCodeExchange ao navegar para URL com code e loading false, carrega a pagina e trata a autenticação', async () => {

        jest.spyOn(storage, 'getStorageString').mockResolvedValue('');  

        const mockResponse = {
            data: {
                data: { name: 'Usuário Teste', email: '<EMAIL>', enabled: true, groups: ["zordon"] },               
            },
            status: 200
        };          

        (axios.get as jest.Mock).mockResolvedValue(mockResponse); 

        mockedAxios.post.mockResolvedValueOnce({
            data: {
                access_token: 'TOKEN_FAKE'
            }
        });

        const {unmount, getByTestId } = render(<WebViewSignin url="https://example.com" hasInternet={true} />);

        jest.useFakeTimers();

        const webview = await waitFor(() => getByTestId('webview-signin'));        
        
        await act(async () => {
            webview.props.onNavigationStateChange({
                loading: false,
                url: 'https://example.com/callback/cognito?code=abc123'
            });
        });        

        expect(mockedAxios.post).toHaveBeenCalled();

        unmount();

    });
    
    it('entra no catch e executa o bloco axios.isAxiosError', async () => {
        
        // Mock do token vazio
        jest.spyOn(storage, 'getStorageString').mockResolvedValue('');

        // Cria um erro do tipo AxiosError
        const error = new AxiosError('Erro simulado do axios');

        // Mocka axios.post para lançar esse erro
        (axios.post as jest.Mock).mockRejectedValueOnce(error);

        // Mocka axios.isAxiosError para retornar true
        jest.spyOn(axios, 'isAxiosError').mockImplementation((err) => err instanceof AxiosError);

        const { getByTestId } = render(
            <WebViewSignin url="https://example.com" hasInternet={true} />
        );

        const webview = await waitFor(() => getByTestId('webview-signin'));

        await act(async () => {
            webview.props.onNavigationStateChange({
                loading: false,
                url: 'https://example.com/callback/cognito?code=abc123',
            });
        });

        expect(axios.post).toHaveBeenCalled();
        expect(axios.isAxiosError).toHaveBeenCalled(); // Confirma que a função foi usada
});

  
});
