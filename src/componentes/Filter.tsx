import React from 'react';
import { ColorValue, StyleSheet, TextInput, View } from 'react-native';
import IconFilter from '../assets/svg/icon_filter-funnel-01.svg';
import IconPesquisa from '../assets/svg/icon_search.svg';

interface FilterProps {
    value?: string;
    height?: any;
    width?: any;
    backgroundColor?: ColorValue;
    placeHolder?: string;
    fontSizePlaceHolder?: number;
    fontSize?: number;
    textoLabel?: string;
    maxLength?: number;
    icon?: 'pesquisa' | 'filtro';
    onValueChange?: (value: string) => void;
}

const Filter: React.FC<FilterProps> = ({
    value = '',
    height = 20,
    width = 20,
    backgroundColor = 'transparent',
    placeHolder = '',
    fontSizePlaceHolder = 20,
    fontSize = 22,
    textoLabel = '',
    maxLength = 15,
    icon = 'pesquisa',
    onValueChange,
}) => {

    const handleChange = (value: string) => {
        onValueChange?.(value);
    };

    return (

        <View style={{ ...styles.container, backgroundColor: backgroundColor }}>

            {
                // tipo de icone
                (icon === 'filtro')
                    ?
                    <IconFilter height={'20'} width={'20'} color={'#525252'} testID='icon-filtro' />
                    :
                    <IconPesquisa height={'20'} width={'20'} color={'#525252'} testID='icon-pesquisa' />
            }

            <TextInput
                style={{ ...styles.texto, fontSize: (value) ? fontSize : fontSizePlaceHolder }}
                placeholder={placeHolder}
                keyboardType='default'
                value={value}
                maxLength={maxLength}
                onChangeText={(texto) => handleChange(texto)} />
        </View>)
}

export default Filter;

const styles = StyleSheet.create({

    container: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: 30,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#D6D6D6',
    },

    label: {
        color: '#737373',
        fontFamily: 'Exo2_600SemiBold',
        position: 'absolute',
        left: 22,
        top: -10,
        zIndex: 999,
        paddingHorizontal: 8,
        fontSize: 14,
    },

    texto: {
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 22,
        textAlign: 'left',
        fontWeight: '500',
        color: '#737373',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        width: '100%',
        height: 60,
    },
});
