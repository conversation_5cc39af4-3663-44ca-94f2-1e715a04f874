import React, { useRef, useState } from 'react';
import { Image, Pressable, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

// icones svg
import IconCamera from '../assets/svg/icon_camera-01.svg';
import IconEdit from '../assets/svg/icon_edit.svg';
import IconGaleria from '../assets/svg/icon_image-03.svg';
import IconTrash from '../assets/svg/icon_trash-01.svg';

// componente de uso da camera e da galeria de fotos
import { CameraOptions, ImageLibraryOptions, launchCamera, launchImageLibrary } from 'react-native-image-picker';
import RBSheet from 'react-native-raw-bottom-sheet';

export interface FotoProps {
    imagem?: string;
    texto_imagem?: string;
    height?: number;
    width?: number;
    heightIcon?: number;
    widthIcon?: number;
    texto?: string;
    subTexto?: string;
    onValueChange?: (value: string) => void;
    onValueText?: (value: string) => void;
    onPressImage?: (value: any) => void;
}

const Foto: React.FC<FotoProps> = ({
    imagem = '',
    texto_imagem = '',
    height = 100,
    width = 100,
    heightIcon = 30,
    widthIcon = 30,
    texto = '1024x768',
    subTexto = 'resolução mínima',
    onValueChange,
    onValueText,
    onPressImage,
}) => {

    const handleChange = (imagem: string) => {
        onValueChange?.(imagem);
    };

    const handleText = (texto_imagem: string) => {
        onValueText?.(texto_imagem);
    };

    const [imageUri, setImageUri] = useState<string | null>(imagem);
    const [textImage, setTextImage] = useState<string>(texto_imagem);

    // referencia para o bottom sheet
    const refRBSheet = useRef(null);

    // trata a solicitação de apagar a imagem
    const handleImageErase = () => {
        setImageUri('');
        handleChange('');
    };

    // trata a solicitação de acesso a galeria de imagems
    const handleImageGallery = () => {

        const options: ImageLibraryOptions = {
            mediaType: 'photo',
            includeBase64: false,
        }

        launchImageLibrary(options, (response) => {
            if (response.didCancel) {
                console.log('Usuário cancelou a escolha da imagem');
            } else {
                setImageUri(response.assets?.[0]?.uri ?? null);
                handleChange(response.assets?.[0]?.uri ?? '');
            }
        });
    };

    // trata a solicitação de acesso a camera do celular
    const handleImageCamera = async () => {

        const options: CameraOptions = {
            mediaType: 'photo',
            saveToPhotos: true,
            cameraType: 'back',
            quality: 1,
        }

        launchCamera(options, (response) => {
            if (response.didCancel) {
                console.log('Usuário cancelou a foto');
            } else {
                setImageUri(response.assets?.[0]?.uri ?? null);
                handleChange(response.assets?.[0]?.uri ?? '');
            }
        });
    };

    return (

        <View style={{ ...styles.container, height: height, width: (width + widthIcon + 10) }}>

            <Pressable style={{ ...styles.containerImage, height: height, width: width }} onPress={onPressImage} testID='button-view'>

                {
                    imageUri ?
                        <Image source={{ uri: imageUri }} resizeMode='contain' style={{ height: height - 5, width: width - 10 }} />
                        :
                        <View style={styles.containerTexto}>
                            <Text style={styles.texto} testID='texto-imagem'>
                                {texto}
                            </Text>
                            <Text style={styles.subTexto} testID='sub-texto-imagem'>
                                {subTexto}
                            </Text>
                        </View>
                }

            </Pressable>

            {
                imageUri
                    ?
                    <View style={{ ...styles.containerIcons, height: height, width: width }}>
                        <TouchableOpacity style={{ width: widthIcon }}
                            onPress={() => (refRBSheet.current as any).open()} testID='button-edit'>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: widthIcon,
                                height: heightIcon,
                                borderWidth: 1,
                                borderRadius: 99,
                                backgroundColor: '#CA8504',
                                borderColor: '#CA8504'
                            }}>
                                <IconEdit color={"#FFFFFF"} width={widthIcon * 0.55} height={heightIcon * 0.55} />
                            </View>
                        </TouchableOpacity>
                        <TouchableOpacity style={{ width: widthIcon }} onPress={() => handleImageErase()} testID='button-erase'>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: widthIcon,
                                height: heightIcon,
                                borderWidth: 1,
                                borderRadius: 99,
                                backgroundColor: '#C0002B',
                                borderColor: '#C0002B'
                            }}>
                                <IconTrash color={"#FFFFFF"} width={widthIcon * 0.55} height={heightIcon * 0.55} />
                            </View>
                        </TouchableOpacity>
                    </View>
                    :
                    <View style={{ ...styles.containerIcons, height: height, width: width }}>

                        <TouchableOpacity style={{ width: widthIcon }} onPress={() => handleImageCamera()} testID='button-camera'>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: widthIcon,
                                height: heightIcon,
                                borderWidth: 1,
                                borderRadius: 99,
                                shadowColor: '#171717',
                                shadowOffset: { width: -2, height: 4 },
                                shadowOpacity: 0.2,
                                shadowRadius: 3,
                                backgroundColor: '#088AB2',
                                borderColor: '#088AB2'
                            }}>
                                <IconCamera color={"#FFFFFF"} width={widthIcon * 0.6} height={heightIcon * 0.6} />
                            </View>
                        </TouchableOpacity>

                        <TouchableOpacity style={{ width: widthIcon }} onPress={() => handleImageGallery()} testID='button-gallery'>
                            <View style={{
                                justifyContent: 'center',
                                alignItems: 'center',
                                width: widthIcon,
                                height: heightIcon,
                                borderWidth: 1,
                                borderRadius: 99,
                                backgroundColor: '#E04F16',
                                borderColor: '#E04F16'
                            }}>
                                <IconGaleria color={"#FFFFFF"} width={widthIcon * 0.55} height={heightIcon * 0.55} />
                            </View>
                        </TouchableOpacity>
                    </View>

            }

            <RBSheet ref={refRBSheet} height={300}>

                <View style={{ flex: 1 }}>

                    <View style={{ justifyContent: 'space-between', padding: 20 }}>

                        <View style={{ height: 200, gap: 15 }}>
                            <Text style={{ ...styles.texto, fontSize: 20, color: '#A3A3A3' }}>Descrição da imagem</Text>
                            <TextInput style={{ height: 120, borderWidth: 1, borderColor: '#D6D6D6', borderRadius: 5, padding: 10, fontFamily: 'SourceSans3_400Regular', fontSize: 16 }}
                                placeholder='Max. 100 caracteres'
                                placeholderTextColor={'#D6D6D6'}
                                multiline
                                textAlignVertical='top'
                                value={textImage}
                                onChangeText={(texto) => setTextImage(texto)}
                                testID='descricao-imagem'
                            >
                            </TextInput>
                        </View>

                        <TouchableOpacity style={styles.buttonSalvar} onPress={() => { handleText(textImage); (refRBSheet.current as any).close(); }} testID='button-save'>
                            <Text style={{ ...styles.texto, color: '#FFFFFF' }}>SALVAR</Text>
                        </TouchableOpacity>

                    </View>

                </View>

            </RBSheet>

        </View>
    )
}

export default Foto;

const styles = StyleSheet.create({

    container: {
        alignItems: 'center',
        flexDirection: 'column',
        gap: 30,
    },

    containerImage: {
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 3,
        borderRadius: 10,
        borderColor: '#A3A3A3',
        backgroundColor: '#F7F7F7'
    },

    containerTexto: {
        height: '50%',
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
    },

    containerIcons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 30,
        gap: 15,
    },

    texto: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 30,
        color: '#D6D6D6',
    },

    subTexto: {
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 18,
        color: '#D6D6D6',
    },

    buttonSalvar: {
        height: 60,
        backgroundColor: '#2E8C1F',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 8
    },
});
