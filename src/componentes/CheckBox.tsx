import React from 'react';
import { ColorValue, Pressable } from 'react-native';
import IconCheck from '../assets/svg/icon_check.svg';

interface CheckBoxProps {
    value?: boolean;
    enabledColor?: ColorValue;
    disabledColor?: ColorValue;
    height?: number;
    width?: number;
    testID?: string;  // Adicionando testID como uma propriedade opcional
    onValueChange?: (value: boolean) => void;
}

const CheckBox: React.FC<CheckBoxProps> = ({
    value = false,
    enabledColor = "#2E8C1F",
    disabledColor = 'transparent',
    height = 20,
    width = 20,
    testID = 'pressable-button',
    onValueChange,
}) => {

    const handleChange = () => {
        onValueChange?.(!value);
    };

    return (

        <Pressable style={{
            height: height,
            width: width,
            borderWidth: 2,
            borderRadius: 5,
            borderColor: (value) ? enabledColor : "#D6D6D6",
            backgroundColor: (value) ? enabledColor : disabledColor,
            justifyContent: 'center',
            alignItems: 'center'
        }}
            onPress={handleChange}
            testID={testID}>

            {value && (<IconCheck height={height - 10} width={width - 10} color={'#FFFFFF'} />)}

        </Pressable>
    )
}

export default CheckBox;
