import '@testing-library/jest-native/extend-expect';
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import SettingsProbe from './SettingsProbe';

describe('SettingsProbe Component', () => {
    const mockProps = {
        conectado: false,
        sistemaConfigurado: false,
        redeConfigurado: false,
        modbusConfigurado: false,
        codiConfigurado: false,
        ionConfigurado: false,
        pulsoConfigurado: false,
        otaConfigurado: false,
        onPressSistema: jest.fn(),
        onPressRede: jest.fn(),
        onPressModbus: jest.fn(),
        onPressCODI: jest.fn(),
        onPressION: jest.fn(),
        onPressOTA: jest.fn(),
        onPressPulso: jest.fn(),
    };

    it('deverá renderizar todos os botões e mostrar todos os activity indicators quando não conectados', () => {
        const { getByTestId } = render(<SettingsProbe {...mockProps} />);

        expect(getByTestId('button-sistema')).toBeTruthy();
        expect(getByTestId('activity-indicator-sistema')).toBeTruthy();

        expect(getByTestId('button-rede')).toBeTruthy();
        expect(getByTestId('activity-indicator-rede')).toBeTruthy();

        expect(getByTestId('button-modbus')).toBeTruthy();
        expect(getByTestId('activity-indicator-modbus')).toBeTruthy();

        expect(getByTestId('button-codi')).toBeTruthy();
        expect(getByTestId('activity-indicator-codi')).toBeTruthy();

        expect(getByTestId('button-ion')).toBeTruthy();
        expect(getByTestId('activity-indicator-ion')).toBeTruthy();

        expect(getByTestId('button-pulso')).toBeTruthy();
        expect(getByTestId('activity-indicator-pulso')).toBeTruthy();

        expect(getByTestId('button-ota')).toBeTruthy();
        expect(getByTestId('activity-indicator-ota')).toBeTruthy();
    });

    it('deverá renderizar todos os botões e mostrar todos os activity indicators quando não conectados e redimensiona botões', () => {
        const { getByTestId } = render(<SettingsProbe {...mockProps} height={400} />);

        expect(getByTestId('button-sistema')).toBeTruthy();
        expect(getByTestId('activity-indicator-sistema')).toBeTruthy();

        expect(getByTestId('button-rede')).toBeTruthy();
        expect(getByTestId('activity-indicator-rede')).toBeTruthy();

        expect(getByTestId('button-modbus')).toBeTruthy();
        expect(getByTestId('activity-indicator-modbus')).toBeTruthy();

        expect(getByTestId('button-codi')).toBeTruthy();
        expect(getByTestId('activity-indicator-codi')).toBeTruthy();

        expect(getByTestId('button-ion')).toBeTruthy();
        expect(getByTestId('activity-indicator-ion')).toBeTruthy();

        expect(getByTestId('button-pulso')).toBeTruthy();
        expect(getByTestId('activity-indicator-pulso')).toBeTruthy();

        expect(getByTestId('button-ota')).toBeTruthy();
        expect(getByTestId('activity-indicator-ota')).toBeTruthy();
    });

    it('deverá renderizar todos os botões com os valores padrão', () => {
        const { getByTestId } = render(<SettingsProbe />);

        expect(getByTestId('button-sistema').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-rede').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-modbus').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-codi').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-ion').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-pulso').props.style.backgroundColor).toBe('#E5E5E5');
        expect(getByTestId('button-ota').props.style.backgroundColor).toBe('#E5E5E5');

    });

    it('deverá renderizar todos os botões e mostrar icones dos botoes quando mqtt conectado e verificação dos módulos como ativos', () => {
        const { getByTestId } = render(<SettingsProbe {...mockProps}
            conectado={true}
            sistemaConfigurado={true}
            redeConfigurado={true}
            modbusConfigurado={true}
            codiConfigurado={true}
            ionConfigurado={true}
            pulsoConfigurado={true}
            otaConfigurado={true} />);

        expect(getByTestId('button-sistema').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-rede').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-modbus').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-codi').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-ion').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-pulso').props.style.backgroundColor).toBe('#38B026');
        expect(getByTestId('button-ota').props.style.backgroundColor).toBe('#38B026');

    });

    it('deverá renderizar o botão khomp e mostrar icones do botão quando mqtt conectado', () => {
        const { getByTestId } = render(<SettingsProbe {...mockProps}
            conectado={true}
            khompConfigurado={true} />);

        expect(getByTestId('button-khomp').props.style.backgroundColor).toBe('#38B026');

    });

    it('deverá renderizar todos os botões e mostrar icones dos botoes quando mqtt conectado e verificação dos módulos como desativados', () => {
        const { getByTestId } = render(<SettingsProbe conectado={true} />);

        expect(getByTestId('button-sistema').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-rede').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-modbus').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-codi').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-ion').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-pulso').props.style.backgroundColor).toBe('#C0002B');
        expect(getByTestId('button-ota').props.style.backgroundColor).toBe('#C0002B');

    });

    it('deverá chamar as funções onPress quando conectados e botões forem pressionados', () => {
        const connectedProps = { ...mockProps, conectado: true };
        const { getByTestId } = render(<SettingsProbe {...connectedProps} />);

        fireEvent.press(getByTestId('button-sistema'));
        fireEvent.press(getByTestId('button-rede'));
        fireEvent.press(getByTestId('button-modbus'));
        fireEvent.press(getByTestId('button-codi'));
        fireEvent.press(getByTestId('button-ion'));
        fireEvent.press(getByTestId('button-pulso'));
        fireEvent.press(getByTestId('button-ota'));

        expect(mockProps.onPressSistema).toHaveBeenCalled();
        expect(mockProps.onPressRede).toHaveBeenCalled();
        expect(mockProps.onPressModbus).toHaveBeenCalled();
        expect(mockProps.onPressCODI).toHaveBeenCalled();
        expect(mockProps.onPressION).toHaveBeenCalled();
        expect(mockProps.onPressPulso).toHaveBeenCalled();
        expect(mockProps.onPressOTA).toHaveBeenCalled();
    });

    it('não deve chamar a função de callback quando não está conectado', () => {
        const onPressSistema = jest.fn();
        const onPressRede = jest.fn();
        const onPressModbus = jest.fn();
        const onPressCODI = jest.fn();
        const onPressION = jest.fn();
        const onPressPulso = jest.fn();
        const onPressOTA = jest.fn();

        const { getByTestId } = render(
            <SettingsProbe
                conectado={false}
                onPressSistema={onPressSistema}
                onPressRede={onPressRede}
                onPressModbus={onPressModbus}
                onPressCODI={onPressCODI}
                onPressION={onPressION}
                onPressPulso={onPressPulso}
                onPressOTA={onPressOTA}
            />
        );

        fireEvent.press(getByTestId('button-sistema'));
        fireEvent.press(getByTestId('text-sistema'));
        fireEvent.press(getByTestId('button-rede'));
        fireEvent.press(getByTestId('text-rede'));
        fireEvent.press(getByTestId('button-modbus'));
        fireEvent.press(getByTestId('text-modbus'));
        fireEvent.press(getByTestId('button-codi'));
        fireEvent.press(getByTestId('text-codi'));
        fireEvent.press(getByTestId('button-ion'));
        fireEvent.press(getByTestId('text-ion'));
        fireEvent.press(getByTestId('button-pulso'));
        fireEvent.press(getByTestId('text-pulso'));
        fireEvent.press(getByTestId('button-ota'));
        fireEvent.press(getByTestId('text-ota'));

        expect(onPressSistema).not.toHaveBeenCalled();
        expect(onPressRede).not.toHaveBeenCalled();
        expect(onPressModbus).not.toHaveBeenCalled();
        expect(onPressCODI).not.toHaveBeenCalled();
        expect(onPressION).not.toHaveBeenCalled();
        expect(onPressPulso).not.toHaveBeenCalled();
        expect(onPressOTA).not.toHaveBeenCalled();
    });
});
