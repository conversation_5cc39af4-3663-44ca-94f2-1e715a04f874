import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';

// icones vetoriais
import IconTrash from '../assets/svg/icon_trash-01.svg'

interface CardApnProps {
    apn?: string;
    user?: string;
    password?: string;
    height?: any;
    width?: any; 
    onPressDelApn?: (value: any) => void;    
    onApnCurrent?: (value: number) => void;
}

const CardApn: React.FC<CardApnProps> = ({
        apn = '',
        user = '',
        password = '',
        height = 90,
        width = '100%',
        onPressDelApn,
        onApnCurrent,
    
   }) => {    
    
    return(

        <View style={{...styles.container, width:width}} testID="view-principal">

            {
                (apn.length > 0) && 
                    <View style={{flexDirection:'row', justifyContent:'space-between', alignItems:'center', gap:5}}>
                
                        <View style={styles.containerApn}>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.titleApn}>{`Apn:  `}</Text>
                                <Text style={styles.textoApn}>{apn}</Text>
                            </View>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.titleApn}>{`Usuário:  `}</Text>
                                <Text style={styles.textoApn}>{user}</Text>
                            </View>
                            <View style={{flexDirection:'row'}}>
                                <Text style={styles.titleApn}>{`Senha:  `}</Text>
                                <Text style={styles.textoApn}>{password}</Text>
                            </View>
                            
                        </View>
                        <TouchableOpacity style={{height:60, width:60, borderRadius:10, justifyContent:'center', alignItems:'center', backgroundColor:'#C0002B'}}
                                          onPress={onPressDelApn}
                                          testID="delete-button">
                            <IconTrash color={"#FFFFFF"} width={25} height={25}/>
                        </TouchableOpacity>
                    </View>                
                }

            
        </View>
    )
}

export default CardApn;

const styles = StyleSheet.create({

    container: {
        flex:1
    },

    containerApn: {
        height:100, 
        width:'80%', 
        flexDirection:'column', 
        padding: 10,
        borderRadius: 10,  
        justifyContent:'flex-start', 
        alignItems:'flex-start', 
        backgroundColor:'#2E8C1F',         
        gap:5
    },    

    titleApn: {      
        fontFamily: 'SourceSans3_400Regular',
        fontSize: 15,        
        color: '#FFFFFF',
    },

    textoApn: {      
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 16,        
        color: '#FFFFFF',
    },

});