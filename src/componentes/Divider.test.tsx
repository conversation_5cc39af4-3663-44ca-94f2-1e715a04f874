import { render } from '@testing-library/react-native';
import React from 'react';
import Divider from './Divider';

describe('Divider Component', () => {
  it('deve renderizar corretamente usando a propriedade default', () => {
    const { getByTestId } = render(<Divider />);
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style.length).toBeGreaterThan(0); // Verifique se o estilo está aplicado
  });

  it('deve renderizar corretamente usando uma propriedade customizada', () => {
    const customStyle = { margin: 10 };
    const { getByTestId } = render(
      <Divider width={2} orientation="vertical" color="#000000" dividerStyle={customStyle} />
    );
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style).toContainEqual(expect.objectContaining({ width: 2 }));
    expect(divider.props.style).toContainEqual(expect.objectContaining({ backgroundColor: '#000000' }));
    expect(divider.props.style).toContainEqual(expect.objectContaining(customStyle)); // Verifique se o estilo personalizado foi aplicado
  });
});