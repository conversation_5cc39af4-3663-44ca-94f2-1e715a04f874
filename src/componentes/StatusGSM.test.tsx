import { render, screen } from '@testing-library/react-native';
import React from 'react';
import { ColorValue } from 'react-native';
import StatusGSM from './StatusGSM';

describe('StatusGSM Component', () => {

  it('deve renderizar com as propriedades padrão', () => {
    render(<StatusGSM />);

    // Verificar se o título padrão é exibido
    expect(screen.getByText('WiFi')).toBeTruthy();

    // Verificar se o valor do sinal padrão (0 db) é exibido
    expect(screen.getByText('0 db')).toBeTruthy();

    // Verificar se o ícone de sinal é renderizado corretamente
    const iconSignal = screen.getByTestId('icon-signal');
    expect(iconSignal).toBeTruthy();
  });

  it('deve renderizar corretamente a cor do sinal no valor baixo', () => {

    render(<StatusGSM value={5} />);

    // Se o valor for <= 10, o ícone de sinal deve ter a cor 'iconSignalLowColor'
    const iconSignal = screen.getByTestId('icon-signal');
    expect(iconSignal.props.color).toBe('#C0002B');  // Cor do sinal fraco

    // Se o valor for > 15, o ícone de sinal deve ter a cor 'iconSignalHighColor'
    //expect(iconSignal.props.color).toBe('#38B026');  // Cor do sinal forte
  });

  it('deve renderizar corretamente a cor do sinal no valor médio', () => {

    render(<StatusGSM value={12} />);

    // Se o valor for <= 10, o ícone de sinal deve ter a cor 'iconSignalLowColor'
    const iconSignal = screen.getByTestId('icon-signal');
    expect(iconSignal.props.color).toBe('#D49600');  // Cor do sinal fraco
  });

  it('deve renderizar corretamente a cor do sinal no valor alto', () => {

    render(<StatusGSM value={20} />);

    // Se o valor for <= 10, o ícone de sinal deve ter a cor 'iconSignalLowColor'
    const iconSignal = screen.getByTestId('icon-signal');
    expect(iconSignal.props.color).toBe('#38B026');  // Cor do sinal fraco
  });

  it('deve renderizar corretamente e mostrar o loading quando true', () => {
    render(<StatusGSM loading={true} />);

    // Verificar se o ActivityIndicator é renderizado
    expect(screen.queryByTestId('activity-indicator')).toBeTruthy();

    // Verificar se o ícone de sinal não é renderizado
    expect(screen.queryByTestId('icon-signal')).toBeNull();
  });

  it('deve mostrar o valor do sinal corretamente', () => {
    render(<StatusGSM value={10} />);

    // Verificar se o valor do sinal (10 db) está sendo exibido corretamente
    expect(screen.getByText('10 db')).toBeTruthy();
  });

  it('deve aplicar a cor de fundo custumizada', () => {
    const customBackGroundColor: ColorValue = '#F0F0F0';
    render(<StatusGSM backGroundColor={customBackGroundColor} />);

    // Verificar se o componente aplica a cor de fundo personalizada
    const container = screen.getByTestId('status-gsm-container');
    expect(container.props.style.backgroundColor).toBe(customBackGroundColor);
  });

  it('deve aplicar o titulo customizado', () => {
    const customTitle = 'GSM Signal';
    render(<StatusGSM title={customTitle} />);

    // Verificar se o título personalizado é exibido
    expect(screen.getByText(customTitle)).toBeTruthy();
  });

  it('deve aplicar a cor da borda customizada', () => {
    const customBorderColor: ColorValue = '#0000FF';
    render(<StatusGSM borderColor={customBorderColor} />);

    // Verificar se a cor da borda personalizada é aplicada
    const container = screen.getByTestId('status-gsm-container');
    expect(container.props.style.borderColor).toBe(customBorderColor);
  });
});
