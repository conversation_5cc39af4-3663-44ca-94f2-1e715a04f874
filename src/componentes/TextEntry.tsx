import React from 'react';
import { ColorValue, StyleSheet, Text, TextInput, View } from 'react-native';
import { MascaraIP } from '../funcoes';

interface TextEntryProps {
    value?: string;
    editable?: boolean;
    height?: any;
    width?: any;
    backgroundColor?: ColorValue;
    placeHolder?: string;
    fontSizePlaceHolder?: number;
    fontSize?: number;
    textoLabel?: string;
    maxLength?: number;
    type?: 'texto' | 'ip' | 'telefone';
    onValueChange?: (value: string) => void;
    testID?: string
}

const TextEntry: React.FC<TextEntryProps> = ({
    value = '',
    editable = true,
    height = 20,
    width = '100%',
    backgroundColor = 'transparent',
    placeHolder = '',
    fontSizePlaceHolder = 16,
    fontSize = 22,
    textoLabel = '',
    maxLength = 15,
    type = 'texto',
    onValueChange,
    testID = 'input-entry'
}) => {

    const handleChange = (value: string) => {
        onValueChange?.(value);
    };

    const renderLabel = () => {

        // se total de dados contidos for menor que o selecionado não apresenta label
        if (!value)
            return null;

        return (

            // value existe e textoLabel existe deve renderizar o textoLabel caso contrário retorna null
            (textoLabel)
                ?
                <Text style={{ ...styles.label, backgroundColor: backgroundColor }}>
                    {textoLabel}
                </Text>
                :
                null
        )

    };

    return (

        <View style={{ height: height, width: width, backgroundColor: backgroundColor }}>

            {renderLabel()}

            <TextInput
                style={{ ...styles.texto, height: height, fontSize: (value) ? fontSize : fontSizePlaceHolder }}
                placeholder={placeHolder}
                keyboardType='default'
                value={value}
                editable={editable}
                maxLength={maxLength}
                onChangeText={(texto) => handleChange((type === 'ip') ? MascaraIP(texto) : texto)}
                testID={testID} />
        </View>)
}

export default TextEntry;

const styles = StyleSheet.create({
    label: {
        color: '#737373',
        fontFamily: 'Exo2_600SemiBold',
        position: 'absolute',
        left: 22,
        top: -10,
        zIndex: 999,
        paddingHorizontal: 8,
        fontSize: 14,
    },
    texto: {
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 22,
        textAlign: 'center',
        fontWeight: '500',
        color: '#737373',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        width: '100%',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#525252',
    },
});
