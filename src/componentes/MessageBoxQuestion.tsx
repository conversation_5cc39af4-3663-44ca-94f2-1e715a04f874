import React from 'react';
import { Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

// icones
import IconAlertCircle from '../assets/svg/icon_info-circle.svg';
import IconX from '../assets/svg/icon_x.svg';

interface MessageBoxQuestionProps {
    title?: string;
    description?: string;
    textYes?: string;
    textNo?: string;
    onConfirm: () => void;
    onCancel: () => void;
}

const MessageBoxQuestion: React.FC<MessageBoxQuestionProps> = ({
    title = 'Titulo',
    description = 'Descrição da pergunta',
    textYes = 'Sim',
    textNo = 'Não',
    onConfirm,
    onCancel,
}) => {
    return (
        <Modal 
            visible = {true}
            transparent = {true}
            statusBarTranslucent
            animationType = 'fade'
        >
            <View style={styles.overlay}>
                <View style={styles.containerCard}>
                    <View style={styles.containerHeader}>
                        <View style={styles.containerIcons}>
                            <View style={styles.containerIconInfo}>
                                <IconAlertCircle color={'#D49600'} width={24} height={24} />
                            </View>
                            <TouchableOpacity style={styles.botaoFechar} onPress={onCancel}>
                                <IconX color={'#A3A3A3'} width={20} height={20} />
                            </TouchableOpacity>                        
                        </View>
                        <View style={styles.containerInfo}>
                            <Text style={styles.textoInfoTitulo}>{title}</Text>
                            <Text style={styles.textoInfoDescricao}>{description}</Text>
                        </View>
                    </View>
                    <View style={styles.containerFooter}>
                        <TouchableOpacity style={styles.botaoFooterCancelar} onPress={onCancel}>
                            <Text style={styles.textoBotaoCancelar}>{textNo}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.botaoFooterConfirm} onPress={onConfirm}>
                            <Text style={styles.textoBotaoConfirm}>{textYes}</Text>
                        </TouchableOpacity>                        
                    </View>
                </View>
            </View>

        </Modal>
    )
}

export default MessageBoxQuestion;

const styles = StyleSheet.create ({
    overlay: {
        flex: 1,
        justifyContent: 'center',        
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },

    containerCard: {
        width: 343,
        height: 322,
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        shadowColor: '#101828',
        shadowOpacity: 8,
        justifyContent: 'space-between'
    },    

    containerHeader: {
        width: 343,
        height: 162, 
        paddingTop: 20,
        paddingRight: 16,
        paddingBottom: 0,
        paddingLeft: 16,
        gap: 8,
    },

    containerFooter: {
        width: 343,
        height: 140, 
        paddingTop: 24,
        paddingRight: 16,
        paddingBottom: 16,
        paddingLeft: 16,        
        justifyContent: 'center',
        alignItems: 'center',
        gap: 10,
    },

    containerIcons: {
        width: 311,
        height: 72, 
        paddingLeft: 0,
        flexDirection: 'row',
        gap: 220,
    },
    
    containerInfo: {
        width: 311,
        height: 72, 
        paddingLeft: 5,
        gap: 4,
        alignItems: 'center',
        justifyContent: 'flex-start',
    },

    containerIconInfo: {
        width: 48,
        height: 48,
        borderRadius: 9999,
        backgroundColor: '#F5F5F5',
        alignItems: 'center',
        justifyContent: 'center',
    },

    containerIconFechar: {
        width: 44,
        height: 44,
        borderRadius: 9999,
        alignItems: 'center',
        justifyContent: 'center',
    },

    textoInfoTitulo: {
        width: 311,
        height: 28,
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: '600',        
        fontSize: 18,
        marginBottom: 12,
        lineHeight: 28,
        color: '#424242',
    },
    
    textoInfoDescricao: {
        width: 311,
        height: 40,
        fontFamily: 'SourceSans3_600SemiBold',
        fontWeight: '400',        
        fontSize: 16,
        lineHeight: 20,
        color: '#525252',
    },    

    textoBotaoConfirm: {
        fontFamily: 'Exo2_600SemiBold',
        fontWeight: '400',        
        fontSize: 16,
        color: '#FFFFFF',
        textAlignVertical: 'center',
        textAlign: 'center',        
    },

    textoBotaoCancelar: {
        fontFamily: 'Exo2_600SemiBold',
        fontWeight: '400',        
        fontSize: 16,
        color: '#424242',
        textAlignVertical: 'center',
        textAlign: 'center',        
    },
    
    botaoFechar: {
        width: 44,
        height: 44,
        borderRadius: 8,
        alignItems: 'flex-end',
    },

    botaoFooterConfirm: {
        width: 311,
        height: 44,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#45D42E',
        paddingTop: 10,
        paddingRight: 16,
        paddingBottom: 10,
        paddingLeft: 16,
        gap: 6,
        backgroundColor: '#45D42E',
        shadowColor: '#101828',
        shadowOpacity: 5,
    },

    botaoFooterCancelar: {
        width: 311,
        height: 44,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#D6D6D6',
        paddingTop: 10,
        paddingRight: 16,
        paddingBottom: 10,
        paddingLeft: 16,
        gap: 6,
        backgroundColor: '#45D42E',
        shadowColor: '#101828',
        shadowOpacity: 5,
    }    

})