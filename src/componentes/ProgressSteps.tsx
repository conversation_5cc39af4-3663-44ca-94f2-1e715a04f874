import React from 'react';
import {View, Dimensions, StyleSheet, Text, TouchableOpacity} from 'react-native';

// componente
import Divider from './Divider'

import StepOff from '../assets/svg/step-off.svg'
import StepOn from '../assets/svg/step-on.svg'
import StepConector from '../assets/svg/step-conector.svg'
import IconProximo from '../assets/svg/icon_chevron-right.svg'
import IconEditar from '../assets/svg/icon_pencil-01.svg'

const screenHeight = Dimensions.get('window').height;
const screenWidth = Dimensions.get('window').width;

// 20% da tela
const width20 = screenWidth * 0.2;

// 80% da tela
const width80 = screenWidth * 0.8;

interface ProgressStepsProps {
    showStep1?: boolean;
    textStep1?: string;
    step1?: boolean;
    onPressStep1?: (value: any) => void;

    showStep2?: boolean;
    textStep2?: string;
    step2?: boolean;
    onPressStep2?: (value: any) => void;

    showStep3?: boolean;
    step3?: boolean;
    textStep3?: string;
    onPressStep3?: (value: any) => void;

    showStep4?: boolean;
    step4?: boolean;
    textStep4?: string;
    onPressStep4?: (value: any) => void;

    showStep5?: boolean;
    step5?: boolean;
    textStep5?: string;
    onPressStep5?: (value: any) => void;

    showStep6?: boolean;
    step6?: boolean;
    textStep6?: string;
    onPressStep6?: (value: any) => void;

    showStep7?: boolean;
    step7?: boolean;
    textStep7?: string;
    onPressStep7?: (value: any) => void;

   }

const ProgressSteps: React.FC<ProgressStepsProps> = ({
    showStep1 = true,
    textStep1 = 'Passo 1',
    step1 = false,
    onPressStep1,

    showStep2 = true,
    textStep2 = 'Passo 2',
    step2 = false,
    onPressStep2,

    showStep3 = true,
    textStep3 = 'Passo 3',
    step3 = false,
    onPressStep3,

    showStep4 = true,
    textStep4 = 'Passo 4',
    step4 = false,
    onPressStep4,

    showStep5 = true,
    textStep5 = 'Passo 5',
    step5 = false,
    onPressStep5,

    showStep6 = true,
    textStep6 = 'Passo 6',
    step6 = false,
    onPressStep6,

    showStep7 = true,
    textStep7 = 'Passo 7',
    step7 = false,
    onPressStep7,

   }) => {

    return(

        <View style={{flex:1, paddingVertical:20, flexDirection:'row'}}>

            <View style={{width:width20, flexDirection:'column', alignItems:'center'}}>

                {
                    // passo 1
                    (showStep1) 
                    ?
                        (step1) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:5, gap: 3, alignItems:'center'}}>
                                <StepOn testID='step-on-1'/>
                            </View>
                        : 
                            <View style={{flexDirection:'column', paddingTop:5, gap: 3, alignItems:'center'}}>
                                <StepOff testID='step-off-1'/>
                            </View>
                    : null
                }                
                

                {
                    // passo 2
                    (showStep2) 
                    ?
                        (step2) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-2'/>
                            </View>
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOff testID='step-off-2'/>
                            </View>
                    : null
                }

                {
                    // passo 3
                    (showStep3) 
                    ?                    
                        (step3) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-3'/>
                            </View>
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector testID='step-off-3'/>
                                <StepOff/>
                            </View>
                    : null
                }
                

                {
                    // passo 4
                    (showStep4) 
                    ?
                        (step4) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-4'/>
                            </View>
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOff testID='step-off-4'/>
                            </View>
                    : null
                }

                {
                    // passo 5
                    (showStep5) 
                    ?
                        (step5) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-5'/>
                            </View>
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, gap: 3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOff testID='step-off-5'/>
                            </View>
                    : null
                }


                {
                    // passo 6
                    (showStep6) ?
                        (step6) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-6'/>
                            </View> 
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOff testID='step-off-6'/>
                            </View>
                    : null
                }

                {
                    // passo 7
                    (showStep7) ?
                        (step7) 
                        ? 
                            <View style={{flexDirection:'column', paddingTop:3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOn testID='step-on-7'/>
                            </View> 
                        : 
                            <View style={{flexDirection:'column', paddingTop:3, alignItems:'center'}}>
                                <StepConector/>
                                <StepOff testID='step-off-7'/>
                            </View>
                    : null
                }

            </View>

            <View style={{width:width80, gap: 30}}>
                                
                {
                    /* etapa 1 */
                    (showStep1) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step1) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={onPressStep1} >
                            <Text style={styles.textoStepDescricao}>{textStep1}</Text>                
                            <View style={styles.containerProximo}>
                                <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                {
                                    (step1) 
                                    ? <IconEditar color={'#2E8C1F'} width={20} height={20}/> 
                                    : <IconProximo color={'#2E8C1F'} width={20} height={20}/>
                                }                
                            </View>
                        </TouchableOpacity>
                }


                {
                    /* etapa 2 */
                    (showStep2) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step2) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={ (step1) ? onPressStep2 : undefined}>                    
                            <Text style={styles.textoStepDescricao}>{textStep2}</Text>
                            {
                                (step1)
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step2) 
                                            ? <IconEditar color={'#2E8C1F'} width={20} height={20} testID={'icon-editar-2'}/> 
                                            : <IconProximo color={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-2'}/>
                                        }                                
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>                
                }

                
                {
                    /* etapa 3 */
                    (showStep3) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step3) ? '#2E8C1F': '#E5E5E5'}}
                                          onPress={(step2) ? onPressStep3 : undefined}>
                            <Text style={styles.textoStepDescricao}>{textStep3}</Text>
                            {
                                (step2)
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step3) 
                                            ? <IconEditar color={'#2E8C1F'} width={20} height={20} testID={'icon-editar-3'}/> 
                                            : <IconProximo color={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-3'}/>
                                        }
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>
                }

                {
                    /* etapa 4 */
                    (showStep4) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step4) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={(step3) ? onPressStep4 : undefined}>
                            <Text style={styles.textoStepDescricao}>{textStep4}</Text>
                            {
                                (step3)
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step4) 
                                            ? <IconEditar color={'#2E8C1F'} width={20} height={20} testID={'icon-editar-4'}/> 
                                            : <IconProximo color={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-4'}/>
                                        }
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>
                }

                
                {                    
                    /* etapa 5 */
                    (showStep5) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step5) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={(step4) ? onPressStep5 : undefined}>
                            <Text style={styles.textoStepDescricao}>{textStep5}</Text>
                            {
                                (step4) 
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step5) 
                                            ? <IconEditar color={'#2E8C1F'} width={20} height={20} testID={'icon-editar-5'}/> 
                                            : <IconProximo color={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-5'}/>
                                        }
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>                    
                }

                
                {
                    /* etapa 6 */
                    (showStep6) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step6) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={(step5) ? onPressStep6 : undefined}>                    
                            <Text style={styles.textoStepDescricao}>{textStep6}</Text>
                            {
                                (step5) 
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step6) 
                                            ? <IconEditar color={'#2E8C1F'} width={20} height={20} testID={'icon-editar-6'}/> 
                                            : <IconProximo color={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-6'}/>
                                        }
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>
                }

                {
                    /* etapa 7 */
                    (showStep7) &&
                        <TouchableOpacity style={{...styles.containerStepDescricao, borderColor: (step7) ? '#2E8C1F': '#E5E5E5'}} 
                                          onPress={ (step6) ? onPressStep7 : undefined}>
                            <Text style={styles.textoStepDescricao}>{textStep7}</Text>
                            {
                                (step6) 
                                ? 
                                    <View style={styles.containerProximo}>
                                        <Divider orientation='vertical' color='#E5E5E5' width={2}/>
                                        {
                                            (step7) 
                                            ? <IconEditar stroke={'#2E8C1F'} width={20} height={20} testID={'icon-editar-7'}/> 
                                            : <IconProximo stroke={'#2E8C1F'} width={20} height={20} testID={'icon-proximo-7'}/>
                                        }
                                    </View>
                                : null    
                            }
                        </TouchableOpacity>                    
                }

            </View>

        </View>
    )
}

export default ProgressSteps;

const styles = StyleSheet.create({

    containerStepDescricao: {
        width:'92%', 
        height: 55, 
        backgroundColor:'#FAFAFA', 
        borderRadius:5, 
        borderWidth:1,                 
        alignItems:'center',
        paddingHorizontal:20,
        flexDirection:'row',
        justifyContent:'space-between'
    },

    containerProximo:{
        flexDirection:'row', 
        alignItems:'center', 
        gap: 20, 
        height:'60%'
    },

    textoStepDescricao:{
        fontFamily: 'SourceSans3_600SemiBold',
        fontSize: 14,        
        color:'#2E8C1F', 
    },    

    linhaVertical:{
        height: 1,
        backgroundColor: 'red',
        marginVertical: 10,                
    },
})