import { render } from '@testing-library/react-native';
import React from 'react';
import StatusBattery from './StatusBattery';

describe('StatusBattery Component', () => {

  it('renderiza corretamente com a propriedades default', () => {
    const { getByText } = render(<StatusBattery />);
    expect(getByText('Bateria')).toBeTruthy();
    expect(getByText('0 %')).toBeTruthy();
  });

  it('mostra corretamente o valor da bateria e titulo', () => {
    const { getByText } = render(<StatusBattery value={50} title="Battery Status" />);
    expect(getByText('Battery Status')).toBeTruthy();
    expect(getByText('50 %')).toBeTruthy();
  });

  it('mostra corretament a cor da bateria baseada no valor low', () => {
    const { getByTestId } = render(<StatusBattery value={10} />);
    const batteryView = getByTestId('battery-view');
    expect(batteryView.props.style.backgroundColor).toBe('#FF0000'); // batteryLowColor
  });

  it('mostra corretament a cor da bateria baseada no valor mid', () => {
    const { getByTestId } = render(<StatusBattery value={50} />);
    const batteryView = getByTestId('battery-view');
    expect(batteryView.props.style.backgroundColor).toBe('#FFFF00'); // batteryMidColor
  });

  it('mostra corretament a cor da bateria baseada no valor full', () => {
    const { getByTestId } = render(<StatusBattery value={80} />);
    const batteryView = getByTestId('battery-view');
    expect(batteryView.props.style.backgroundColor).toBe('#00FF00'); // batteryFullColor
  });

  it('mostra o loading quando for true', () => {
    const { getByTestId } = render(<StatusBattery loading={true} />);
    expect(getByTestId('loading-indicator')).toBeTruthy();
  });

});
