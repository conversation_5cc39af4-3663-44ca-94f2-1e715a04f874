import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import Counter from './Counter';

describe('Counter Component', () => {

  test('renderiza corretamente sem o valor inicial', () => {
    const { getByText } = render(<Counter texto="Item de Teste" />);
    expect(getByText(/Item de Teste/)).toBeTruthy();
  });

  test('renderiza corretamente com o valor inicial', () => {
    const { getByText } = render(<Counter value={0} texto="Item de Teste" />);
    expect(getByText(/0/)).toBeTruthy();
    expect(getByText(/Item de Teste/)).toBeTruthy();
  });

  test('incrementa valor ao pressionar botão de mais', () => {
    const onValueChangeMock = jest.fn();
    const { getByTestId } = render(<Counter value={0} onValueChange={onValueChangeMock} />);

    fireEvent.press(getByTestId('button-plus'));
    expect(onValueChangeMock).toHaveBeenCalledWith(1);
  });

  test('decrementa valor ao pressionar botão de menos', () => {
    const onValueChangeMock = jest.fn();
    const { getByTestId } = render(<Counter value={1} onValueChange={onValueChangeMock} />);

    fireEvent.press(getByTestId('button-minus'));
    expect(onValueChangeMock).toHaveBeenCalledWith(0);
  });

  test('não incrementa acima do valor máximo', () => {
    const onValueChangeMock = jest.fn();
    const { getByTestId } = render(<Counter value={99} maximo={99} onValueChange={onValueChangeMock} />);

    fireEvent.press(getByTestId('button-plus'));
    expect(onValueChangeMock).not.toHaveBeenCalled();
  });

  test('não decrementa abaixo do valor mínimo', () => {
    const onValueChangeMock = jest.fn();
    const { getByTestId } = render(<Counter value={0} minimo={0} onValueChange={onValueChangeMock} />);

    fireEvent.press(getByTestId('button-minus'));
    expect(onValueChangeMock).not.toHaveBeenCalled();
  });
});