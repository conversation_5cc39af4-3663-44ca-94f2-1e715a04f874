import React from 'react';
import { ColorValue, StyleSheet, Text, View } from 'react-native';

// imagem svg
import IconMedidor from '../assets/svg/icon_server.svg';

interface CardMedidorProps {
    value?: string;
    valueKE?: string;
    valueID?: string;
    firmwareColor?: ColorValue;
    iconFirmwareColor?: ColorValue;
    height?: any;
    width?: any;
    backGroundColor?: ColorValue;
    borderColor?: ColorValue;
    title?: string;
    titleKE?: string;
}

const CardMedidor: React.FC<CardMedidorProps> = ({
    value = 'Medidor',
    valueKE = 'Ke',
    valueID = '000',
    firmwareColor = '#F5F5F5',
    iconFirmwareColor = '#C0002B',
    height = 120,
    width = '100%',
    backGroundColor = 'transparent',
    borderColor = '#E5E5E5',
    title = 'Descrição',
    titleKE = 'Constante',
}) => {

    return (

        <View style={{
            height: height,
            width: width,

            borderRadius: 12,
            borderWidth: 1,
            borderColor: borderColor,
            backgroundColor: backGroundColor,
            justifyContent: 'space-between',
            alignItems: 'center',
        }}>

            <View style={{ height: 80, width: '100%', paddingHorizontal: 20, justifyContent: 'flex-start', alignItems: 'center', flexDirection: 'row', gap: 16 }}>

                <View style={{ height: 48, width: 48, borderRadius: 10, backgroundColor: firmwareColor, justifyContent: 'center', alignItems: 'center' }}>
                    <IconMedidor color={"#737373"} width={20} height={20} />
                </View>

                <View style={{ width: '80%' }}>
                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text style={styles.textoTitle}>
                            {`${title}`}
                        </Text>
                        <Text style={styles.textoValueID}>
                            {`${valueID}`}
                        </Text>
                    </View>
                    <Text style={styles.textoValue}>
                        {`${value}`}
                    </Text>
                </View>

            </View>

            {/* divisor */}
            <View style={{
                height: 40,
                width: '100%',
                paddingHorizontal: 20,
                alignItems: 'center',
                justifyContent: 'space-between',
                borderColor: '#E5E5E5',
                borderTopWidth: 1,
                flexDirection: 'row'
            }} >

                <Text style={styles.textoTitleKE}>
                    {`${titleKE}`}
                </Text>
                <Text style={styles.textoValueKE}>
                    {`${valueKE}`}
                </Text>
            </View>


        </View>
    )
}

export default CardMedidor;

const styles = StyleSheet.create({

    textoTitle: {
        fontFamily: 'SourceSans3_600SemiBold',
        color: '#525252',
        fontSize: 14,
        height: 24,
    },
    textoValue: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 20,
        height: 32,
    },
    textoTitleKE: {
        fontFamily: 'SourceSans3_600SemiBold',
        color: '#2E8C1F',
        fontSize: 11,
    },
    textoValueKE: {
        fontFamily: 'Exo2_600SemiBold',
        color: '#2E8C1F',
        fontSize: 14,
    },

    textoValueID: {
        fontFamily: 'Exo2_600SemiBold',
        fontSize: 11,
    },

    linhaHorizontal: {
        borderBottomColor: '#D6D6D6',
        borderBottomWidth: StyleSheet.hairlineWidth,
    },
});
