import { render } from '@testing-library/react-native';
import React from 'react';
import StatusFirmware from './StatusFirmware';

describe('StatusFirmware Component', () => {
    it('deve renderizar corretamente com as propriedades padrão', () => {
        const { getByTestId, getByText } = render(<StatusFirmware />);

        // Verificar o container
        expect(getByTestId('status-gsm-container')).toBeTruthy();

        // Verificar o título e o valor padrão
        expect(getByText('Firmware')).toBeTruthy();
        expect(getByText('Versão')).toBeTruthy();

        // Verificar se o ícone é exibido
        expect(getByTestId('icon-signal')).toBeTruthy();
    });

    it('deve exibir o ActivityIndicator quando loading for true', () => {
        const { getByTestId } = render(<StatusFirmware loading={true} />);

        // Verificar se o ActivityIndicator é exibido
        expect(getByTestId('activity-indicator')).toBeTruthy();
    });

    it('deve aplicar as cores fornecidas via propriedades', () => {
        const { getByTestId } = render(
            <StatusFirmware
                firmwareColor="#F5F5F5"
                iconFirmwareColor="#654321"
                backGroundColor="transparent"
                borderColor="#fedcba"
            />
        );

        // Verificar se as cores foram aplicadas corretamente
        const container = getByTestId('status-gsm-container');
        expect(container.props.style.backgroundColor).toBe('transparent');
        expect(container.props.style.borderColor).toBe('#fedcba');
    });
});
