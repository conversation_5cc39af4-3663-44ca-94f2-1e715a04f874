import { render } from '@testing-library/react-native';
import React from 'react';
import StatusWiFi from './StatusWiFi';

describe('StatusWiFi Component', () => {
  test('renderiza corretamente com as propriedades default', () => {
    const { getByText } = render(<StatusWiFi />);

    expect(getByText('WiFi')).toBeTruthy();
    expect(getByText('0 db')).toBeTruthy();
  });

  test('renderiza corretamente com o valor alto', () => {
    const { getByText } = render(<StatusWiFi value={-60} />);

    expect(getByText('-60 db')).toBeTruthy();
  });

  test('renderiza corretamente com o valor médio', () => {
    const { getByText } = render(<StatusWiFi value={-75} />);

    expect(getByText('-75 db')).toBeTruthy();
  });

  test('renderiza corretamente com o valor baixo', () => {
    const { getByText } = render(<StatusWiFi value={-85} />);

    expect(getByText('-85 db')).toBeTruthy();
  });

  test('renderiza o estado do loading corretamente', () => {
    const { getByTestId } = render(<StatusWiFi loading={true} />);

    expect(getByTestId('activity-indicator')).toBeTruthy();
  });
});
