import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import ListFiles from './ListFiles';

describe('ListFiles Component', () => {
  it('deverá renderizar corretamente com as propriedades default', () => {
    const { getByText } = render(<ListFiles />);

    // Verifica se os textos padrão são renderizados
    expect(getByText('01/01/2000 00:00')).toBeTruthy();
    expect(getByText('0 bytes bytes')).toBeTruthy();
    expect(getByText('file')).toBeTruthy();
  });

  it('deverá chamar onDelete quando botão delete for pressionado', () => {
    const onDeleteMock = jest.fn();
    const { getByTestId } = render(<ListFiles onDelete={onDeleteMock} />);

    // Simula o clique no botão de deletar
    fireEvent.press(getByTestId('delete-button'));

    // Verifica se a função onDelete foi chamada
    expect(onDeleteMock).toHaveBeenCalled();
  });
});
