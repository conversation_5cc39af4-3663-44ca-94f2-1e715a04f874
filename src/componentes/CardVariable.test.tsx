
import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import { mockVariableSettings } from '../../__mocks__/VariablesSettingsMock';
import CardVariable, { CardVariableProps } from './CardVariable';
import CheckBox from './CheckBox';
import Divider from './Divider';

describe('CardVariable Component', () => {
  const mockHandlers = {
    onUpdateData: jest.fn(),
  };

  const mockOnUpdateData = jest.fn();
  const mockProps: CardVariableProps = {
    data: mockVariableSettings,
    descricao: 'Descrição do Card',
    variavel: 'Variável Exemplo',
    ultimoEnvio: true,
    mediaEnvio: false,
    funcao: 1,
    endereco: 123,
    fator: 2.5,
    formato: 1,
    height: 200,
    width: 300,
    onUpdateData: mockOnUpdateData,
  };

  it('deve alternar o estado', () => {
    const { getByTestId } = render(
      <CardVariable {...mockHandlers} />
    );
    const toggleButton = getByTestId('show-toggle-button');
    fireEvent.press(toggleButton);
    expect(toggleButton).toBeTruthy();
  });

  it('deve renderizar corretamente com as propriedades default', () => {
    const { getByTestId } = render(
      <CardVariable {...mockHandlers} />
    );

    const descricaoText = getByTestId('text-descricao');
    expect(descricaoText).toBeTruthy();

    const variavelText = getByTestId('text-variavel');
    expect(variavelText).toBeTruthy();
  });

  it('deve renderizar corretamente com as propriedades descricao = vazio', () => {
    const { getByTestId } = render(
      <CardVariable {...mockHandlers} descricao='' />
    );

    const descricaoText = getByTestId('text-descricao');
    expect(descricaoText).toBeTruthy();
  });

  it('deve chamar o onUpdateData quando o checkbox é alternado', () => {
    const { getByTestId } = render(
      <CardVariable {...mockProps} />
    );

    const ultimoCheckBoxParent = getByTestId('ultimo-checkbox');
    const mediaCheckBoxParent = getByTestId('media-checkbox');

    fireEvent.press(ultimoCheckBoxParent);
    expect(mockHandlers.onUpdateData).toHaveBeenCalled();

    fireEvent.press(mediaCheckBoxParent);
    expect(mockHandlers.onUpdateData).toHaveBeenCalled();
  });

  it('deve renderizar corretamente quandoo onUpdateData indefinido', () => {
    const { getByTestId } = render(
      <CardVariable />
    );

    const ultimoCheckBoxParent = getByTestId('ultimo-checkbox');
    const mediaCheckBoxParent = getByTestId('media-checkbox');

    fireEvent.press(ultimoCheckBoxParent);
    expect(mockHandlers.onUpdateData).toHaveBeenCalled();

    fireEvent.press(mediaCheckBoxParent);
    expect(mockHandlers.onUpdateData).toHaveBeenCalled();
  });




  it('deve atualizar o estado quando o checkbox ultimo é alternado', () => {

    const { getByTestId } = render(<CardVariable {...mockProps} />);
    const ultimoCheckbox = getByTestId('ultimo-checkbox');
    fireEvent.press(ultimoCheckbox);
    expect(mockVariableSettings[0].valorUltimaVariable).toBe(false);

    fireEvent.press(ultimoCheckbox);
    expect(mockVariableSettings[1].valorUltimaVariable).toBe(true);
  });

  it('deve atualizar o estado quando o checkbox media é alternado', () => {
    const { getByTestId } = render(<CardVariable {...mockProps} />);
    const mediaCheckBox = getByTestId('media-checkbox');
    fireEvent.press(mediaCheckBox);
    expect(mockVariableSettings[0].valorMediaVariable).toBe(true);

    fireEvent.press(mediaCheckBox);
    expect(mockVariableSettings[1].valorMediaVariable).toBe(false);
  });

  it('deve renderizar o Divider corretamente usando a propriedade default', () => {
    const { getByTestId } = render(<Divider />);
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style.length).toBeGreaterThan(0); // Verifique se o estilo está aplicado
  });

  it('deve renderizar o Divider corretamente usando uma propriedade customizada', () => {
    const customStyle = { margin: 10 };
    const { getByTestId } = render(
      <Divider width={2} orientation="vertical" color="#000000" dividerStyle={customStyle} />
    );
    const divider = getByTestId('view-divider');
    expect(divider).toBeTruthy();
    expect(divider.props.style).toContainEqual(expect.objectContaining({ width: 2 }));
    expect(divider.props.style).toContainEqual(expect.objectContaining({ backgroundColor: '#000000' }));
    expect(divider.props.style).toContainEqual(expect.objectContaining(customStyle)); // Verifique se o estilo personalizado foi aplicado
  });

  it('deve renderizar o componente CheckBox corretamente com valor padrão', () => {
    const { getByTestId } = render(<CheckBox />);
    const checkbox = getByTestId('pressable-button');
    expect(checkbox).toBeTruthy();
    expect(checkbox.props.style.backgroundColor).toBe('transparent');
  });

  it('deve renderizar o componente CheckBox com valor verdadeiro', () => {
    const { getByTestId } = render(<CheckBox value={true} />);
    const checkbox = getByTestId('pressable-button');
    expect(checkbox.props.style.backgroundColor).toBe('#2E8C1F');
  });

  it('deve chamar onValueChange ao pressionar o  CheckBox', () => {
    const mockOnValueChange = jest.fn();
    const { getByTestId } = render(<CheckBox onValueChange={mockOnValueChange} />);
    const checkbox = getByTestId('pressable-button');
    fireEvent.press(checkbox);
    expect(mockOnValueChange).toHaveBeenCalledWith(true);
  });
});
