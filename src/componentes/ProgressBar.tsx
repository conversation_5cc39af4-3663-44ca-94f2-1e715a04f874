import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

const ProgressBar = ({ progress, maxProgress }) => {
  const progressPercentage = (progress / maxProgress) * 100;

  return (
    <View style={styles.container}>
      <View style={styles.progressBar} testID="progress-bar">
        <View
          style={{
            ...styles.progress,
            width: `${progressPercentage}%`,
          }}
        />
      </View>
      <Text style={styles.label}>{`${progress}/${maxProgress}`}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginTop: 5,
    justifyContent: 'space-between',
    alignContent: 'center',
    flexDirection: 'row',
    gap: 5
  },
  label: {
    textAlign: 'center',
    fontFamily: 'Exo2_400Regular',
    marginBottom: 10,
    fontSize: 16,
    color: '#E0E0DF'
  },
  progressBar: {
    height: 20,
    width: '80%',
    backgroundColor: '#E0E0DF',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progress: {
    height: '100%',
    backgroundColor: '#45D42E',
    borderRadius: 5,
  },
});

export default ProgressBar;
