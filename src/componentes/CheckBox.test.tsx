import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';
import CheckBox from './CheckBox';

// Mock para os ícones SVG
jest.mock('../assets/svg/icon_check.svg', () => 'IconCheck');

describe('CheckBox Component', () => {
  it('deve renderizar o componente corretamente com valor padrão', () => {
    const { getByTestId } = render(<CheckBox />);
    const checkbox = getByTestId('pressable-button');
    expect(checkbox).toBeTruthy();
    expect(checkbox.props.style.backgroundColor).toBe('transparent');
  });

  it('deve renderizar o componente com valor verdadeiro', () => {
    const { getByTestId } = render(<CheckBox value={true} />);
    const checkbox = getByTestId('pressable-button');
    expect(checkbox.props.style.backgroundColor).toBe('#2E8C1F');
  });

  it('deve chamar onValueChange ao pressionar', () => {
    const mockOnValueChange = jest.fn();
    const { getByTestId } = render(<CheckBox onValueChange={mockOnValueChange} />);
    const checkbox = getByTestId('pressable-button');
    fireEvent.press(checkbox);
    expect(mockOnValueChange).toHaveBeenCalledWith(true);
  });

  it('deve mudar a cor do ícone quando valor é verdadeiro', () => {
    const { getByTestId } = render(<CheckBox value={true} />);
    const checkbox = getByTestId('pressable-button');
    const icon = checkbox.children.find(child => child.type === 'IconCheck');
    expect(icon.props.color).toBe('#FFFFFF');
  });
});
