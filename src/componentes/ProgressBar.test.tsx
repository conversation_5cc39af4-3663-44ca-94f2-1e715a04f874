import { render } from '@testing-library/react-native';
import React from 'react';
import ProgressBar from './ProgressBar';

describe('ProgressBar', () => {
  it('renders correctly with given progress and maxProgress', () => {
    const { getByText } = render(<ProgressBar progress={50} maxProgress={100} />);

    // Verifique se o texto de progresso está correto
    expect(getByText('50/100')).toBeTruthy();
  });
});
