import { ListaBaudRateProbe, ListaParidadeProbe } from "./constantes";

/**
 * função que adiciona dias a data atual
 * @param dias 
 * @returns 
 */
export function AdicionaDiaDataAtual(dias: number)
{
    /* pega a data de hoje */
    let hoje = new Date();

    /* adiciona os dias */
    hoje.setDate(hoje.getDate() + dias);

    /* formata a data para DD/MM/YYYY */
    let dia = String(hoje.getDate()).padStart(2, '0');
    let mes = String(hoje.getMonth() + 1).padStart(2, '0'); // Janeiro é 0!
    let ano = hoje.getFullYear();

    return `${dia}/${mes}/${ano}`;
}

/**
 * retorna o numero com 2 digitos
 * @param num 
 * @returns 
 */
export function padTo2Digits(num: number): string {
    return num.toString().padStart(2, '0');
}

/**
 * retorna a data e hora no formato: 01/01/2000 00:00:00
 * @param date 
 * @returns 
 */
export function DataCompleta(date: Date): string {
    return (
      [
        padTo2Digits(date.getDate()),
        padTo2Digits(date.getMonth() + 1),
        date.getFullYear(),        
      ].join('/') +
      ' ' +
      [
        padTo2Digits(date.getHours()),
        padTo2Digits(date.getMinutes()),
        padTo2Digits(date.getSeconds()),
      ].join(':')
    );
}

/**
 * retorna a data no formato: 01/01/2000
 * @param date 
 * @param separator 
 * @returns 
 */
export function Data(date: Date, separator: string = '/'): string {
    return (
      [
        padTo2Digits(date.getDate()),
        padTo2Digits(date.getMonth() + 1),
        date.getFullYear(),        
      ].join(separator)
    );
}

/**
 * retorna a hora no formato: 00:00:00
 * @param date 
 * @returns 
 */
export function Hora(date: Date): string {
    return (
      [
        padTo2Digits(date.getHours()),
        padTo2Digits(date.getMinutes()),
        padTo2Digits(date.getSeconds()),
      ].join(':')
    );
}

/**
 * retorna a data com parametro timestamp no formato 01/01/2000
 * @param timestamp 
 * @returns 
 */
export function DataTimeStamp(timestamp: number){
  
  var data = new Date(timestamp * 1000);  

  let ano = data.getFullYear();
  let mes = (data.getMonth() + 1 ).toString().padStart(2, '0');  
  let dia = data.getDate().toString().padStart(2, '0');
  
  return `${dia}/${mes}/${ano}`;
}

/**
 * retorna a hora parametro timestamp no formato: 00:00:00
 * @param timestamp 
 * @param show_second 
 * @returns 
 */
export function HoraTimeStamp(timestamp: number, show_second : boolean){
  
  var data = new Date(timestamp * 1000);   

  var hora = data.getHours().toString().padStart(2, '0');
  var min = data.getMinutes().toString().padStart(2, '0');
  var seg = data.getSeconds().toString().padStart(2, '0');

  if(show_second)
    return `${hora}:${min}:${seg}`;
  else
    return `${hora}:${min}`;
}

/**
 * formata o texto inserido para o ***************
 * @param input 
 * @returns 
 */
export function MascaraIP(input:string) {
  
  /* remove todos os caracteres que não são números */
  input = input.replace(/[^\d]/g, '');

  /* Adiciona os pontos nos lugares corretos */
  if (input.length > 3) {
      input = input.substring(0, 3) + '.' + input.substring(3);
  }
  if (input.length > 7) {
      input = input.substring(0, 7) + '.' + input.substring(7);
  }
  if (input.length > 11) {
      input = input.substring(0, 11) + '.' + input.substring(11);
  }

  /* Limita o comprimento a 15 caracteres (máximo para um endereço IP) */
  input = input.substring(0, 15);

  /* verifica se cada segmento é menor ou igual a 255 */
  let segments = input.split('.');
  for (let i = 0; i < segments.length; i++) {
      if (parseInt(segments[i]) > 255) {
          segments[i] = '255';
      }
  }

  return segments.join('.');
}

/**
 * itera por cada parte para verificar se é um número válido entre 0 e 255
 * @param ip 
 * @returns 
 */
export function  ValidarIP(ip: string): boolean {

  /* divide o IP em partes usando o ponto como separador */
  const partes = ip.split(".");

  /* verifica se o IP possui exatamente 4 partes */
  if (partes.length !== 4) {
    return false;
  }

  /* tera por cada parte para verificar se é um número válido entre 0 e 255 */
  for (const parte of partes) {
    const numero = Number(parte);

    // Verifica se a parte é um número e está no intervalo permitido
    if (isNaN(numero) || numero < 0 || numero > 255) {
      return false;
    }
  }

  return true;
}

/**
 * formata o tempo de numero para 00:00:00
 * @param timer 
 * @returns 
 */
export function FormataTimer (timer: number) {
  
  const getSeconds = `0${(timer % 60)}`.slice(-2);
  const minutes = Math.floor(timer / 60);
  const getMinutes = `0${minutes % 60}`.slice(-2);
  const getHours = `0${Math.floor(timer / 3600)}`.slice(-2);

  return `${getHours}:${getMinutes}:${getSeconds}`;
};

/**
 * verifica se string contem somente numeros 
 * @param str 
 * @returns 
 */
export function isNumber(str: string) {
  var auxiliar = /^[0-9]+$/;
  return (auxiliar.test(str));
}

// verifica se a variavel é do tipo object
export function isObject(obj: any) {
  return obj === Object(obj);
}

/**
 * retorna o indice da paridade
 * @param descricao 
 * @returns 
 */
export function getIndexParidade(descricao: string) {  

  const item = ListaParidadeProbe.find(item => item.descricao === descricao);

  /* retorna o indice de 8N2 se a descrição não for encontrada */
  return item ? item.id : 0;
}

/**
 * retorna o indice do baud reate
 * @param valor 
 * @returns 
 */
export function getIndexBaudRate(valor: number) {  

  const item = ListaBaudRateProbe.find(item => item.descricao === valor.toString());

  /* retorna o indice de 9600 se o valor não for encontrado */
  return item ? item.id : 5;
}

/**
 * retorna o indice do tempo de envio
 * @param valor 
 * @returns 
 */
export function getIndexTempoEnvio(valor: number) {  

  switch(valor) {
    case 60:
      return 0;
    case 300:
      return 1;
    case 900:
      return 2;
    case 3600:
      return 3;
  }

  /* retorna o indice de 15 minutos se não for encontrado */
  return 2;
}

/**
 * retorna o percentual do sinal do wifi ou gsm
 * @param sinal 
 * @param minSinal 
 * @param maxSinal 
 * @returns 
 */
export function getPercentualSinal(sinal: number, minSinal: number, maxSinal: number) {

  /* se se sinal fraco */
  if(sinal <= minSinal) {
    return 0;
  }    
  /* se sinal muito forte */
  else if(sinal >= maxSinal){
    return 100;
  }
  else {

    /* Calculando o percentual */
    const percentual = ((sinal - minSinal) / (maxSinal - minSinal)) * 100;

    /* Garantindo que o valor esteja entre 0% e 100% */
    return Math.min(100, Math.max(0, percentual));    
  }
}

/**
 * converte mensagem json para texto legivel
 */
export function formatJson(jsonInput: string, topico: string) : string {
  try {

    // Primeiro, verifica se é uma string e, caso seja, a converte para um objeto
    const jsonObject = typeof jsonInput === "string" ? JSON.parse(jsonInput) : jsonInput;
    const formattedJson = JSON.stringify(jsonObject, null, 2); // null mantém as chaves originais e 2 é o nível de indentação.
    return DataCompleta(new Date()) + '\n' + topico + '\n' + formattedJson;
  } catch (error) {    
    return '';
  }
}

/**
 *  separa os elementos do payload para configuração da apn
 * @param payload 
 * @returns 
 */
export function parseStringApn(payload: string) {
  const results = [];
  let start = 0;
  
  /* enquanto existir caracteres */
  while (start < payload.length) {
      const commaIndex = payload.indexOf(',', start);
      const closeParenthesisIndex = payload.indexOf(')', start);
            
      if (commaIndex !== -1 && closeParenthesisIndex !== -1 && commaIndex < closeParenthesisIndex) {
          const end = closeParenthesisIndex + 1;
          results.push(payload.slice(start, end));

          /* ir para o próximo elemento após a virgula */
          start = end + 2; 
          
      } else {
          results.push(payload.slice(start));
          break;
      }
  }
  
  return results;
}

/**
 * converte uma data e hora em utc
 * @param date 
 * @param time 
 * @returns 
 */
export function getDataUTC(date: string, time: string = '00:00') : string {

  /* dividir a string para obter dia, mês e ano */
  let [dia, mes, ano] = date.split('-').map(Number);

  /* dividir a string para obter hora e minuto */
  let [hora, minuto] = time.split(':').map(Number);

  /* criar um objeto Date ajustando para UTC */
  let date_utc = new Date(Date.UTC(ano, mes - 1, dia, hora, minuto, 0));

  /* Converter para string ISO */
  return (date_utc.toISOString());
}

/**
 * 
 * @param dataStr 
 * @returns 
 */
export function converterData(dataStr: string) {    
    let partes;

    if (dataStr.includes("-")) {
        partes = dataStr.split("-");
        if (partes[0].length === 4) {
            // Formato YYYY-MM-DD
            return `${partes[2]}-${partes[1]}-${partes[0]}`;
        } else {
            // Formato DD-MM-YYYY (já está correto)
            return dataStr;
        }
    }

    return "01-01-2000";
}
