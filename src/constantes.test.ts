import 'react-native';
import { BaudRateProbe, CoresStatusOS, 
         CoresStatusPreOS, 
         DescricaoConexao, 
         DescricaoFormato, 
         DescricaoOperadora, 
         DescricaoOS, 
         DescricaoStatuProbe, 
         DescricaoStatusOS, 
         DescricaoStatusPreOS, 
         EndianessProbe, 
         ListaBaudRateProbe, 
         ListaComunicacaoSerial, 
         ListaContactPulso, 
         ListaContatoPulso, 
         ListaEndianessProbe, 
         ListaFirmwareModo, 
         ListaFlowPulso, 
         ListaMedicaoPulso, 
         ListaModoBuscaION, 
         ListaParidadeProbe, 
         ListaProtocoloCodi, 
         ListaProtocoloCodi232, 
         ListaProtocoloCodi485, 
         ListaProtocoloProbe, 
         ListaTempoEnvioProbe, 
         ListaTipoConexaoProbe, 
         ListaTiposConexao, 
         ListaTiposPulso, 
         OperadorasCelular, 
         ParidadeProbe, 
         ProtocoloProbe, 
         ScreenResumoTipoOS, 
         ScreenTipoOS, 
         StatusAtualProbe, 
         StatusInstalador, 
         SubDescricaoOS, 
         TempoEnvioProbe, 
         TempoSegundosEnvioProbe, 
         TimeoutTipoConexao, 
         TipoConexao, 
         TipoConexaoMQTT, 
         TipoFirmwareModo, 
         TipoFormato, 
         TipoMedicaoPulso, 
         TipoOperadora, 
         TipoOS, 
         TiposCODI, 
         TiposComunicacaoION, 
         TiposPulso, 
         TipoStatusOS, 
         TipoStatusPreOS} from './constantes';

describe('constantes', () => {
    it('deve conter as telas de ordens de serviço corretas - ScreenTipoOS', () => {
        expect(ScreenTipoOS[0]).toBe('OrdemServicoInstalacaoFro1');
        expect(ScreenTipoOS[1]).toBe('OrdemServicoInstalacaoSet1');
        expect(ScreenTipoOS[2]).toBe('OrdemServicoInstalacaoSen1');
        expect(ScreenTipoOS[3]).toBe('OrdemServicoTrocaFro2');
        expect(ScreenTipoOS[4]).toBe('OrdemServicoManutencao');
        expect(ScreenTipoOS[5]).toBe('OrdemServicoRetiradaFro3');
        expect(ScreenTipoOS[6]).toBe('OrdemServicoVisita');
    });

    it('deve ter os valores corretos - TipoOS', () => {
        expect(TipoOS.INSTALACAO_FRO1).toBe(0);
        expect(TipoOS.INSTALACAO_SET1).toBe(1);
        expect(TipoOS.INSTALACAO_SEN1).toBe(2);
        expect(TipoOS.TROCA_FRO2).toBe(3);
        expect(TipoOS.MANUTENCAO).toBe(4);
        expect(TipoOS.RETIRADA_FRO3).toBe(5);
        expect(TipoOS.VISITA).toBe(6);
    });
    
    it('deve conter as telas de resumo do tipo de OS corretas - ScreenResumoTipoOS', () => {
        expect(ScreenResumoTipoOS[0]).toBe('ResumoOSInstalacaoFro1');
        expect(ScreenResumoTipoOS[1]).toBe('ResumoOSInstalacaoSet1');
        expect(ScreenResumoTipoOS[2]).toBe('ResumoOSInstalacaoSen1');
        expect(ScreenResumoTipoOS[3]).toBe('ResumoOSTrocaFro2');
        expect(ScreenResumoTipoOS[4]).toBe('ResumoOSManutencao');
        expect(ScreenResumoTipoOS[5]).toBe('ResumoOSRetiradaFro3');
        expect(ScreenResumoTipoOS[6]).toBe('ResumoOSVisita');
    });

      it('deve conter as descrições das ordens de serviço corretas - DescricaoOS', () => {
        expect(DescricaoOS[0]).toBe('Instalação - FRO1.1');
        expect(DescricaoOS[1]).toBe('Instalação - SET1.1');
        expect(DescricaoOS[2]).toBe('Instalação - SEN1.1');
        expect(DescricaoOS[3]).toBe('Troca - FRO2.1');
        expect(DescricaoOS[4]).toBe('Manutenção');
        expect(DescricaoOS[5]).toBe('Retirada - FRO3.1');
        expect(DescricaoOS[6]).toBe('Visita');
    });

    it('deve conter as sub descrições de ordens de serviço corretas - SubDescricaoOS', () => {
        expect(SubDescricaoOS[0]).toBe('Instalação de Fronteira');
        expect(SubDescricaoOS[1]).toBe('Instalação de telemetria setorizada');
        expect(SubDescricaoOS[2]).toBe('Instalação de medidor setorizado');
        expect(SubDescricaoOS[3]).toBe('Substituição de telemetria de fronteira de terceiros');
        expect(SubDescricaoOS[4]).toBe('Manutenção com troca de telemetria');
        expect(SubDescricaoOS[5]).toBe('Desinstalação de telemetria de fronteira');
        expect(SubDescricaoOS[6]).toBe('Manutenção sem troca de telemetria');
    });

    it('deve ter os valores corretos - TipoStatusOS', () => {
        expect(TipoStatusOS.CONCLUIDO).toBe(0);
        expect(TipoStatusOS.PENDENTE).toBe(1);
        expect(TipoStatusOS.NAO_INICIADO).toBe(2);
        expect(TipoStatusOS.TODOS).toBe(3);
    });

    it('deve conter as descrições de status da OS corretas - DescricaoStatusOS', () => {
        expect(DescricaoStatusOS[0]).toBe('Concluído');
        expect(DescricaoStatusOS[1]).toBe('Pendente');
        expect(DescricaoStatusOS[2]).toBe('Não Iniciado');
        expect(DescricaoStatusOS[3]).toBe('Todos');
    });

    it('deve conter as cores do status de OS corretas - CoresStatusOS', () => {
        expect(CoresStatusOS[0]).toBe('#2E8C1F');
        expect(CoresStatusOS[1]).toBe('#EF6820');
        expect(CoresStatusOS[2]).toBe('#EAAA08');    
    });

    it('deve ter os valores corretos - TipoStatusPreOS', () => {
        expect(TipoStatusPreOS.AGUARDANDO).toBe(0);
        expect(TipoStatusPreOS.ABERTA).toBe(1);
        expect(TipoStatusPreOS.AGENDADA).toBe(2);
        expect(TipoStatusPreOS.ENCERRADA).toBe(3);
        expect(TipoStatusPreOS.CANCELADA).toBe(4);
        expect(TipoStatusPreOS.ATENDIDA).toBe(5);
        expect(TipoStatusPreOS.TODOS).toBe(6);
    });  

    it('deve conter as descrições do status de OS corretas - DescricaoStatusPreOS', () => {
        expect(DescricaoStatusPreOS[0]).toBe('Aguardando');
        expect(DescricaoStatusPreOS[1]).toBe('Aberta');
        expect(DescricaoStatusPreOS[2]).toBe('Agendada');    
        expect(DescricaoStatusPreOS[3]).toBe('Encerrada');    
        expect(DescricaoStatusPreOS[4]).toBe('Cancelada');    
        expect(DescricaoStatusPreOS[5]).toBe('Atendida');    
        expect(DescricaoStatusPreOS[6]).toBe('Todos');    
    });

    it('deve conter as cores do status de OS corretas - CoresStatusPreOS', () => {
        expect(CoresStatusPreOS[0]).toBe('#737373');
        expect(CoresStatusPreOS[1]).toBe('#EF6820');
        expect(CoresStatusPreOS[2]).toBe('#2970FF');    
        expect(CoresStatusPreOS[3]).toBe('#7A5AF8');    
        expect(CoresStatusPreOS[4]).toBe('#E60034');    
        expect(CoresStatusPreOS[5]).toBe('#2E8C1F');    
        expect(CoresStatusPreOS[6]).toBe('#EAAA08');    
    });

    it('deve ter os valores corretos - TipoConexao', () => {
        expect(TipoConexao.WIFI).toBe(0);
        expect(TipoConexao.GSM).toBe(1);
        expect(TipoConexao.ETHERNET).toBe(2);
        expect(TipoConexao.NARROW_BAND).toBe(3);
    });

    it('deve conter os valores de timeout para o tipo de conexão corretas - TimeoutTipoConexao', () => {
        expect(TimeoutTipoConexao[0]).toBe(120000);
        expect(TimeoutTipoConexao[1]).toBe(300000);
        expect(TimeoutTipoConexao[2]).toBe(60000);    
        expect(TimeoutTipoConexao[3]).toBe(300000);    
    });

    it('deve conter as descrições do tipo de conexão corretas - DescricaoConexao', () => {
        expect(DescricaoConexao[0]).toBe('Wi-Fi');
        expect(DescricaoConexao[1]).toBe('GSM');
        expect(DescricaoConexao[2]).toBe('Ethernet');    
        expect(DescricaoConexao[3]).toBe('Narrow Band');    
    });

    it('deve conter 4 tipos de conexão', () => {
        expect(ListaTiposConexao).toHaveLength(4);
    });

    it('deve conter tipos de conexão esperados', () => {
        expect(ListaTiposConexao).toEqual(
            expect.arrayContaining([
                { id: 0, descricao: 'Wi-Fi' },
                { id: 1, descricao: 'GSM' },
                { id: 2, descricao: 'Ethernet' },
                { id: 3, descricao: 'Narrow Band' },
            ])
        );
    });

    it('deve conter o tipo de conexão Wi-Fi', () => {
        expect(ListaTiposConexao).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ descricao: 'Wi-Fi' })
            ])
        );
    });

    it('deve ter os valores corretos - TipoOperadora', () => {
        expect(TipoOperadora.DESCONHECIDO).toBe(0);
        expect(TipoOperadora.VIVO).toBe(1);
        expect(TipoOperadora.TIM).toBe(2);
        expect(TipoOperadora.CLARO).toBe(3);
        expect(TipoOperadora.OI).toBe(4);
        expect(TipoOperadora.NEXTEL).toBe(5);
        expect(TipoOperadora.ALGAR).toBe(6);
        expect(TipoOperadora.SERCOMTEL).toBe(7);
        expect(TipoOperadora.MVNOs).toBe(8);
    });

    it('deve conter as descrições do tipo de operadora corretas - DescricaoOperadora', () => {
        expect(DescricaoOperadora[0]).toBe('Desconhecido');
        expect(DescricaoOperadora[1]).toBe('Vivo');
        expect(DescricaoOperadora[2]).toBe('Tim');    
        expect(DescricaoOperadora[3]).toBe('Claro');
        expect(DescricaoOperadora[4]).toBe('Oi');
        expect(DescricaoOperadora[5]).toBe('Nextel');
        expect(DescricaoOperadora[6]).toBe('Algar');    
        expect(DescricaoOperadora[7]).toBe('SerComTel');    
        expect(DescricaoOperadora[8]).toBe('MVNOs');    
    });

    it('deve conter 8 operadoras', () => {
        expect(OperadorasCelular.length).toBe(8);
    });

    it('deve descrever corretamene as operadoras', () => {
        const descriptions = OperadorasCelular.map(op => op.descricao);
        expect(descriptions).toEqual([
            'VIVO',
            'TIM',
            'CLARO',
            'OI',
            'NEXTEL',
            'ALGAR',
            'SERCOMTEL',
            'MVNOs'
        ]);
    });
  
    it('deve conter uma especifica operadora', () => {
        const operadora = OperadorasCelular.find(op => op.descricao === 'VIVO');
        expect(operadora).toBeDefined();
        if(operadora)
            expect(operadora.id).toBe(1);
    });

    it('deve conter os status de instalador corretos - StatusInstalador', () => {
        expect(StatusInstalador.BLOQUEADO).toBe(1);
        expect(StatusInstalador.ATIVO).toBe(2);
    });

    it('deve conter os status atual da Probe corretos - StatusAtualProbe', () => {
        expect(StatusAtualProbe.DESCONHECIDO).toBe(0);
        expect(StatusAtualProbe.DISPONIVEL).toBe(1);
        expect(StatusAtualProbe.EM_USO).toBe(2);
        expect(StatusAtualProbe.EM_REPARO).toBe(3);
        expect(StatusAtualProbe.SUCATA).toBe(4);
    });

    it('deve conter as descrições do status da Probe corretas - DescricaoStatuProbe', () => {
        expect(DescricaoStatuProbe[0]).toBe('Desconhecido');
        expect(DescricaoStatuProbe[1]).toBe('Disponível');
        expect(DescricaoStatuProbe[2]).toBe('Em uso');    
        expect(DescricaoStatuProbe[3]).toBe('Em reparo');
        expect(DescricaoStatuProbe[4]).toBe('Sucata');
    });

    it('deve conter os tipos de conexão MQTT corretos - TipoConexaoMQTT', () => {
        expect(TipoConexaoMQTT.TESTE).toBe(0);
        expect(TipoConexaoMQTT.CONFIGURACAO).toBe(1);
    });

    it('deve conter os tipos de endianess corretos - EndianessProbe', () => {
        expect(EndianessProbe.BACD).toBe(0);
        expect(EndianessProbe.CDAB).toBe(1);
        expect(EndianessProbe.DCBA).toBe(2);
        expect(EndianessProbe.ABCD).toBe(3);
    });

    it('deve conter 4 tipos de endianess', () => {
        expect(ListaEndianessProbe.length).toBe(4);
    });

    it('deve descrever corretamene os tipos de endianess', () => {
        const descriptions = ListaEndianessProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
            'BACD',
            'CDAB',
            'DCBA',
            'ABCD',
        ]);
    });
  
    it('deve conter um especifico endianess', () => {
        const endianess = ListaEndianessProbe.find(op => op.descricao === 'CDAB');
        expect(endianess).toBeDefined();
        if(endianess)
            expect(endianess.id).toBe(1);
    });

    it('deve conter os tipos de tempo de envio corretos - TempoEnvioProbe', () => {
        expect(TempoEnvioProbe.T_01).toBe(0);
        expect(TempoEnvioProbe.T_05).toBe(1);
        expect(TempoEnvioProbe.T_15).toBe(2);
        expect(TempoEnvioProbe.T_60).toBe(3);
    });

    it('deve conter 4 tipos de tempo de envio', () => {
        expect(ListaTempoEnvioProbe.length).toBe(4);
    });

    it('deve descrever corretamente os tipos de envio', () => {
        const descriptions = ListaTempoEnvioProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
            '01 minuto',
            '05 minutos',
            '15 minutos',
            '60 minutos',
        ]);
    });
  
    it('deve conter um especifico tipo de tempo de envio', () => {
        const tempo = ListaTempoEnvioProbe.find(op => op.descricao === '05 minutos');
        expect(tempo).toBeDefined();
        if(tempo)
            expect(tempo.id).toBe(1);
    });

    it('deve conter os tipos de tempo de envio em segundos corretos - TempoSegundosEnvioProbe', () => {
        expect(TempoSegundosEnvioProbe[0]).toBe(60);
        expect(TempoSegundosEnvioProbe[1]).toBe(300);
        expect(TempoSegundosEnvioProbe[2]).toBe(900);
        expect(TempoSegundosEnvioProbe[3]).toBe(3600);
    });


    it('deve conter os tipos de protocolo da probe corretos - ProtocoloProbe', () => {
        expect(ProtocoloProbe.RTU).toBe(0);
        expect(ProtocoloProbe.TCP).toBe(1);
    });

    it('deve conter os 2 tipos de protocolos', () => {
        expect(ListaProtocoloProbe.length).toBe(2);
    });
    
    it('deve descrever corretamente a descrição das protocolos', () => {
        const descriptions = ListaProtocoloProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
          'RTU',
          'TCP',
        ]);
    });
      
    it('deve conter um protocolo especifico', () => {
        const protocolo = ListaProtocoloProbe.find(op => op.descricao === 'TCP');
        expect(protocolo).toBeDefined();
        if(protocolo)
            expect(protocolo.id).toBe(1);
    });

    it('deve conter os 2 tipos de conexão da probe', () => {
        expect(ListaTipoConexaoProbe.length).toBe(2);
    });
    
    it('deve descrever corretamente a descrição dos tipos de conexão', () => {
        const descriptions = ListaTipoConexaoProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
          'Wi-Fi',
          'Ethernet',
        ]);
    });
      
    it('deve conter um tipo de conexão especifico', () => {
        const conexao = ListaTipoConexaoProbe.find(op => op.descricao === 'Ethernet');
        expect(conexao).toBeDefined();
        if(conexao)
            expect(conexao.id).toBe(1);
    });

    it('deve conter os tipos de paridade corretos - ParidadeProbe', () => {
        expect(ParidadeProbe._8N1).toBe(0);
        expect(ParidadeProbe._8N2).toBe(1);
        expect(ParidadeProbe._8E1).toBe(2);
        expect(ParidadeProbe._8E2).toBe(3);
        expect(ParidadeProbe._8O1).toBe(4);
        expect(ParidadeProbe._8O2).toBe(5);
    });   

    it('deve ter 6 tipos de paridade', () => {
        expect(ListaParidadeProbe.length).toBe(6);
    });
    
    it('deve ter a descrição correta dos tipos de paridade', () => {
        const descriptions = ListaParidadeProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
            '8N1',
            '8N2',
            '8E1',
            '8E2',
            '8O1',
            '8O2',
        ]);
    });
      
    it('deve conter um especifico tipo de paridade', () => {
        const paridade = ListaParidadeProbe.find(op => op.descricao === '8N2');
        expect(paridade).toBeDefined();
        if(paridade)
            expect(paridade.id).toBe(1);
    });

    it('deve conter os tipos de baud rate corretos - BaudRateProbe', () => {
        expect(BaudRateProbe._110).toBe(0);
        expect(BaudRateProbe._600).toBe(1);
        expect(BaudRateProbe._1200).toBe(2);
        expect(BaudRateProbe._2400).toBe(3);
        expect(BaudRateProbe._4800).toBe(4);
        expect(BaudRateProbe._9600).toBe(5);
        expect(BaudRateProbe._14400).toBe(6);
        expect(BaudRateProbe._19200).toBe(7);
        expect(BaudRateProbe._28800).toBe(8);
        expect(BaudRateProbe._38400).toBe(9);
        expect(BaudRateProbe._56000).toBe(10);
        expect(BaudRateProbe._115200).toBe(11);
    });

    it('deve ter 12 tipos de baud rate', () => {
        expect(ListaBaudRateProbe.length).toBe(12);
    });
    
    it('deve ter a descrição correta dos tipos de paridade', () => {
        const descriptions = ListaBaudRateProbe.map(op => op.descricao);
        expect(descriptions).toEqual([
            '110',
            '600',
            '1200',
            '2400',
            '4800',
            '9600',
            '14400',
            '19200',
            '28800',
            '38400',
            '56000',
            '115200',
        ]);
    });
      
    it('deve conter um especifico tipo de paridade', () => {
        const baud = ListaBaudRateProbe.find(op => op.descricao === '600');
        expect(baud).toBeDefined();
        if(baud)
            expect(baud.id).toBe(1);
    });

    it('deve conter os tipos de formato corretos - TipoFormato', () => {
        expect(TipoFormato._INT16).toBe(0);
        expect(TipoFormato._UINT16).toBe(1);
        expect(TipoFormato._INT32).toBe(2);
        expect(TipoFormato._UINT32).toBe(3);
        expect(TipoFormato._INT64).toBe(4);
        expect(TipoFormato._UINT64).toBe(5);
        expect(TipoFormato._FLOAT32).toBe(6);
        expect(TipoFormato._STRING).toBe(7);
        expect(TipoFormato._RAW).toBe(8);
        expect(TipoFormato._DOUBLE64).toBe(9);
        expect(TipoFormato._UINT32_T5).toBe(10);
        expect(TipoFormato._UINT32_T6).toBe(11);
        expect(TipoFormato._UINT32_T7).toBe(12);
        expect(TipoFormato._UINT32_TFLOAT).toBe(13);
        expect(TipoFormato._BCD).toBe(14);
    });

    it('deve conter as descroções dos tipos de formato corretos - DescricaoFormato', () => {
        expect(DescricaoFormato[0]).toBe('INT16');
        expect(DescricaoFormato[1]).toBe('UINT16');
        expect(DescricaoFormato[2]).toBe('INT32');
        expect(DescricaoFormato[3]).toBe('UINT32');
        expect(DescricaoFormato[4]).toBe('INT64');
        expect(DescricaoFormato[5]).toBe('UINT64');
        expect(DescricaoFormato[6]).toBe('FLOAT32');
        expect(DescricaoFormato[7]).toBe('STRING');
        expect(DescricaoFormato[8]).toBe('RAW');
        expect(DescricaoFormato[9]).toBe('DOUBLE64');
        expect(DescricaoFormato[10]).toBe('UINT32_T5');
        expect(DescricaoFormato[11]).toBe('UINT32_T6');
        expect(DescricaoFormato[12]).toBe('UINT32_T7');
        expect(DescricaoFormato[13]).toBe('UINT32_TFLOAT');
        expect(DescricaoFormato[14]).toBe('BCD');
    });

    it('deve ser um array congelado - TiposCODI', () => {
        expect(Object.isFrozen(TiposCODI)).toBe(true);
    });

    it('deve conter os tipos de CODI corretos - TiposCODI', () => {
        const tiposEsperados = [
            'CODI 1',
            'CODI 2',
            'CODI RS232',
            'CODI RS485',
        ];
        expect(TiposCODI).toEqual(tiposEsperados);
    });

    it('deve ter 3 tipos de protocolo Codi - ListaProtocoloCodi', () => {
        expect(ListaProtocoloCodi.length).toBe(3);
    });
    
    it('deve ter a descrição correta dos tipos de protocolo codi - ListaProtocoloCodi', () => {
        const descriptions = ListaProtocoloCodi.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Normal',
            'Misto',
            'Estendido',
        ]);
    });
      
    it('deve conter um especifico tipo de protocolo codi - ListaProtocoloCodi', () => {
        const protocolo = ListaProtocoloCodi.find(op => op.descricao === 'Misto');
        expect(protocolo).toBeDefined();
        if(protocolo)
            expect(protocolo.id).toBe(1);
    });

    it('deve ter 4 tipos de protocolo Codi 232 - ListaProtocoloCodi232', () => {
        expect(ListaProtocoloCodi232.length).toBe(4);
    });
    
    it('deve ter a descrição correta dos tipos de protocolo codi 232 - ListaProtocoloCodi232', () => {
        const descriptions = ListaProtocoloCodi232.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Normal',
            'Misto',
            'Estendido',
            'Bidirecional',
        ]);
    });
      
    it('deve conter um especifico tipo de protocolo codi 232 - ListaProtocoloCodi232', () => {
        const protocolo = ListaProtocoloCodi232.find(op => op.descricao === 'Misto');
        expect(protocolo).toBeDefined();
        if(protocolo)
            expect(protocolo.id).toBe(1);
    });

    it('deve ter 5 tipos de protocolo Codi 485 - ListaProtocoloCodi485', () => {
        expect(ListaProtocoloCodi485.length).toBe(5);
    });
    
    it('deve ter a descrição correta dos tipos de protocolo codi 485 - ListaProtocoloCodi485', () => {
        const descriptions = ListaProtocoloCodi485.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Normal',
            'Misto',
            'Estendido',
            'Bidirecional',
            'Multiponto'
        ]);
    });
      
    it('deve conter um especifico tipo de protocolo codi 485 - ListaProtocoloCodi485', () => {
        const protocolo = ListaProtocoloCodi485.find(op => op.descricao === 'Misto');
        expect(protocolo).toBeDefined();
        if(protocolo)
            expect(protocolo.id).toBe(1);
    });

    it('deve ter 3 tipos de atualização de firmware - ListaFirmwareModo', () => {
        expect(ListaFirmwareModo.length).toBe(3);
    });
    
    it('deve ter a descrição correta dos tipos de atualização de firmware - ListaFirmwareModo', () => {
        const descriptions = ListaFirmwareModo.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Normal',
            'Estável',
            'Ultra estável',
        ]);
    });
      
    it('deve conter um especifico tipo de atualização de firmware - ListaFirmwareModo', () => {
        const protocolo = ListaFirmwareModo.find(op => op.descricao === 'Estável');
        expect(protocolo).toBeDefined();
        if(protocolo)
            expect(protocolo.id).toBe(1);
    });

    it('deve conter as descroções dos tipos de atualização de firmware - TipoFirmwareModo', () => {
        expect(TipoFirmwareModo.NORMAL).toBe(0);
        expect(TipoFirmwareModo.ESTAVEL).toBe(1);
        expect(TipoFirmwareModo.ULTRA_ESTAVEL).toBe(2);
    });

    it('deve ser um array congelado - TiposComunicacaoION', () => {
        expect(Object.isFrozen(TiposComunicacaoION)).toBe(true);
    });

    it('deve conter os tipos de comunicação ion corretos - TiposComunicacaoION', () => {
        const tiposEsperados = [
            'RS485',
            'RS232',
        ];
        expect(TiposComunicacaoION).toEqual(tiposEsperados);
    });

    it('deve ter 2 tipos de comunicação ion - TiposComunicacaoION', () => {
        expect(TiposComunicacaoION.length).toBe(2);
    });

    it('deve ter 2 tipos de comunicação serial - ListaComunicacaoSerial', () => {
        expect(ListaComunicacaoSerial.length).toBe(2);
    });
    
    it('deve ter a descrição correta dos tipos de comunicação serial - ListaComunicacaoSerial', () => {
        const descriptions = ListaComunicacaoSerial.map(op => op.descricao);
        expect(descriptions).toEqual([
            'RS485',
            'RS232',
        ]);
    });
      
    it('deve conter um especifico tipo de comunicação serial - ListaComunicacaoSerial', () => {
        const comunicacao = ListaComunicacaoSerial.find(op => op.descricao === 'RS232');
        expect(comunicacao).toBeDefined();
        if(comunicacao)
            expect(comunicacao.id).toBe(1);
    });

    it('deve ter 2 tipos de busca de ion na rede - ListaModoBuscaION', () => {
        expect(ListaModoBuscaION.length).toBe(2);
    });
    
    it('deve ter a descrição correta dos tipos de busca de ion na rede - ListaModoBuscaION', () => {
        const descriptions = ListaModoBuscaION.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Básico',
            'Completo',
        ]);
    });
      
    it('deve conter um especifico tipo de busca de ion na rede - ListaModoBuscaION', () => {
        const modo = ListaModoBuscaION.find(op => op.descricao === 'Completo');
        expect(modo).toBeDefined();
        if(modo)
            expect(modo.id).toBe(1);
    });

    it('deve ter 2 tipos de contador de pulso - TiposPulso', () => {
        expect(TiposPulso.length).toBe(2);
    });

    it('deve ser um array congelado - TiposPulso', () => {
        expect(Object.isFrozen(TiposPulso)).toBe(true);
    });

    it('deve conter os tipos de contador de pulso corretos - TiposPulso', () => {
        const tiposEsperados = [
            'PULSO 1',
            'PULSO 2',
        ];
        expect(TiposPulso).toEqual(tiposEsperados);
    });

    it('deve ter a descrição correta dos tipos de contador de pulso - ListaTiposPulso', () => {
        const descriptions = ListaTiposPulso.map(op => op.descricao);
        expect(descriptions).toEqual([
            'PULSO 1',
            'PULSO 2',
        ]);
    });
      
    it('deve conter um especifico tipo de busca de contador de pulso - ListaTiposPulso', () => {
        const tipos = ListaTiposPulso.find(op => op.descricao === 'PULSO 2');
        expect(tipos).toBeDefined();
        if(tipos)
        expect(tipos.id).toBe(1);
    });


    it('deve ter 6 tipos de medição de pulso - ListaMedicaoPulso', () => {
        expect(ListaMedicaoPulso.length).toBe(6);
    });
    
    it('deve ter a descrição correta dos tipos de medição de pulso - ListaMedicaoPulso', () => {
        const descriptions = ListaMedicaoPulso.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Energia',
            'Gás',
            'Água',
            'Comgás',
            'Contato',
            'Genérico',            
        ]);
    });
      
    it('deve conter um especifico tipo medição de pulso - ListaMedicaoPulso', () => {
        const medicao = ListaMedicaoPulso.find(op => op.descricao === 'Gás');
        expect(medicao).toBeDefined();
        if(medicao)
        expect(medicao.id).toBe(1);
    });

    it('deve ter 6 tipos de medição de pulso - ListaFlowPulso', () => {
        expect(ListaFlowPulso.length).toBe(6);
    });
    
    it('deve ter a descrição correta dos tipos de medição de pulso - ListaFlowPulso', () => {
        const descriptions = ListaFlowPulso.map(op => op.descricao);
        expect(descriptions).toEqual([
            'eflow',
            'gflow',
            'wflow',
            'comgas',
            'contact',
            'pflow',            
        ]);
    });
      
    it('deve conter um especifico tipo medição de pulso - ListaFlowPulso', () => {
        const medicao = ListaFlowPulso.find(op => op.descricao === 'gflow');
        expect(medicao).toBeDefined();
        if(medicao)
            expect(medicao.id).toBe(1);
    });    

    it('deve conter as tipos de medição do contador de pulso corretos - TipoMedicaoPulso', () => {
        expect(TipoMedicaoPulso.ENERGIA).toBe(0);
        expect(TipoMedicaoPulso.GAS).toBe(1);
        expect(TipoMedicaoPulso.AGUA).toBe(2);
        expect(TipoMedicaoPulso.COMGAS).toBe(3);
        expect(TipoMedicaoPulso.CONTATO).toBe(4);
        expect(TipoMedicaoPulso.GENERICO).toBe(5);
    });

    it('deve ter 2 tipos borda do contador de pulso - ListaContatoPulso', () => {
        expect(ListaContatoPulso.length).toBe(2);
    });
    
    it('deve ter a descrição correta dos tipos borda do contador de pulso - ListaContatoPulso', () => {
        const descriptions = ListaContatoPulso.map(op => op.descricao);
        expect(descriptions).toEqual([
            'Fechado',
            'Aberto',
        ]);
    });
      
    it('deve conter um especifico tipo borda do contador de pulso - ListaContatoPulso', () => {
        const contato = ListaContatoPulso.find(op => op.descricao === 'Aberto');
        expect(contato).toBeDefined();
        if(contato)
            expect(contato.id).toBe(1);
    }); 

    it('deve ter 2 tipos contato para o contador de pulso - ListaContactPulso', () => {
        expect(ListaContactPulso.length).toBe(2);
    });
    
    it('deve ter a descrição correta dos tipos contato para o contador de pulso - ListaContactPulso', () => {
        const descriptions = ListaContactPulso.map(op => op.descricao);
        expect(descriptions).toEqual([
            'nc',
            'no',
        ]);
    });
      
    it('deve conter um especifico tipo contato para o contador de pulso - ListaContactPulso', () => {
        const contact = ListaContactPulso.find(op => op.descricao === 'no');
        expect(contact).toBeDefined();
        if(contact)
            expect(contact.id).toBe(1);
    }); 


});
