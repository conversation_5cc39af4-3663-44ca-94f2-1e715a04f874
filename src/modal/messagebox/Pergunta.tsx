import React from "react";
import {StyleSheet, View, Modal, Text, TouchableOpacity} from 'react-native';

import IconAlert from '../../assets/svg/icon_alert-circle.svg'
import IconX from '../../assets/svg/icon_x.svg'

interface MessageBoxPerguntaProps {
    visivel?: boolean;
    titulo?: string;
    descricao?: string;
    textoBotaoOK?:string;
    textoBotaoCancelar?:string;
    children?:any;
    onOK?: (value: any) => void;
    onCancel?: (value: any) => void;
 }

const MessageBoxPergunta: React.FC<MessageBoxPerguntaProps> = ({    
    visivel = false,
    titulo = "Pergunta",
    descricao = "",
    textoBotaoOK = "OK",
    textoBotaoCancelar = "Cancelar",
    children,
    onOK,
    onCancel,
   }) => {

  return(

    <Modal
      visible={visivel}
      statusBarTranslucent={true}
      transparent={true}
      animationType="fade"
      testID="modal"
    >
        <View style={styles.containerContent}>
          
          <View style={styles.containerCard}>

            <View style={styles.containerHeader}>

              <View style = {styles.containerIcons}>
                <View style={styles.containerIconInfo}>
                  <IconAlert color={"#737373"} width={24} height={24}/>
                </View>
                <TouchableOpacity style = {styles.botaoFechar} onPress={onCancel}>
                  <IconX color={"#A3A3A3"} width={20} height={20}/>
                </TouchableOpacity>
              </View>

              <View style={styles.containerInfo}>
                <Text style={styles.textoInfoTitulo}>{titulo}</Text>
                <Text style={styles.textoInfoDescricao}>{descricao}</Text>
              </View>

            </View> 

            <View style={styles.containerFooter}>
              <TouchableOpacity style={styles.botaoFooterCancelar} onPress={onCancel}>
                <Text style = {styles.textoBotaoCancelar}>{textoBotaoCancelar}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.botaoFooterOK} onPress={onOK}>
                <Text style = {styles.textoBotaoOK}>{textoBotaoOK}</Text>
              </TouchableOpacity>
            </View>          
            
          </View>
          {children}
        </View>
        
      </Modal>    
  )
}

export default MessageBoxPergunta;

const styles = StyleSheet.create({
  containerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },

  containerCard: {
    width: 343,
    height: 322,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#101828",
    shadowOpacity: 8,
    justifyContent:'space-between'
  },

  containerHeader:{
    width: 343,
    height: 162,    
    paddingTop: 20,
    paddingRight: 16,
    paddingBottom: 0,
    paddingLeft: 16,    
    gap: 8,
  },

  containerFooter:{
    width: 343,
    height: 140,
    paddingTop: 24,
    paddingRight: 16,
    paddingBottom: 16,
    paddingLeft: 16,
    justifyContent: "center",
    alignItems:'center',
    gap: 10,    
  },

  containerIcons:{
    width: 311,
    height: 72,
    paddingLeft: 0,
    flexDirection: "row", 
    gap: 220,
  },

  containerInfo:{
    width: 311,
    height: 72,
    paddingLeft: 5,
    gap: 4,
    alignItems:"center",
    justifyContent: "flex-start",    
  },

  containerIconInfo:{
    width:48,
    height:48,
    borderRadius: 9999,
    backgroundColor: "#F5F5F5",
    alignItems: "center",
    justifyContent: "center"
  },

  containerIconFechar:{
    width:44,
    height:44,
    borderRadius: 9999,
    alignItems: "center",
    justifyContent: "center",
  },

  textoInfoTitulo: {
    width: 311,
    height: 28,
    fontFamily: "SourceSans3_600SemiBold",
    fontWeight: "600",
    fontSize: 18,
    marginBottom: 12,
    lineHeight: 28,
    color: "#424242"
  },

  textoInfoDescricao: {
    width: 311,
    height: 40,
    fontFamily: "SourceSans3_400Regular",
    fontWeight: "400",
    fontSize: 16,
    lineHeight: 20,
    color: "#525252"    
  },

  textoBotaoOK: {
    fontFamily: "Exo2_600SemiBold",
    fontWeight: "600",
    fontSize: 16,    
    color: "#FFFFFF",
    textAlignVertical: "center",
    textAlign:"center",
  },

  textoBotaoCancelar: {
    fontFamily: "Exo2_600SemiBold",
    fontWeight: "600",
    fontSize: 16,    
    color: "#424242",
    textAlignVertical: "center",
    textAlign:"center",
  },

  botaoFechar: {
    width: 44,
    height: 44,
    borderRadius: 8,    
    alignItems:"flex-end"
  },  

  botaoFooterOK: {
    width: 311,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#45D42E",
    paddingTop: 10,
    paddingRight: 16,
    paddingBottom: 10,
    paddingLeft: 16,
    gap: 6,
    backgroundColor: "#45D42E",
    shadowColor: "#101828",   
    shadowOpacity: 5,
  },

  botaoFooterCancelar: {
    width: 311,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#D6D6D6",
    paddingTop: 10,
    paddingRight: 16,
    paddingBottom: 10,
    paddingLeft: 16,
    gap: 6,
    backgroundColor: "#FFFFFF",
    shadowColor: "#101828",   
    shadowOpacity: 5,
  },  
})