import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxPergunta from './Pergunta';


describe('MessageBoxPergunta Modal', () => {
    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxPergunta  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });    

    it('should render correctly when visible', () => {
        const { getByText, queryByText } = render(
            <MessageBoxPergunta
                visivel={true}
                titulo="Pergunta de Teste"
                descricao="Descrição de Teste"
                textoBotaoOK="OK"
                textoBotaoCancelar="Cancelar"
                onOK={() => {}}
                onCancel={() => {}}
            />
        );

        expect(getByText('Pergunta de Teste')).toBeTruthy();
        expect(getByText('Descrição de Teste')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByText('Cancelar')).toBeTruthy();
        expect(queryByText('Não Visível')).toBeNull();
    });

    it('should call onOK when OK button is pressed', () => {
        const onOKMock = jest.fn();
        const { getByText } = render(
            <MessageBoxPergunta
                visivel={true}
                onOK={onOKMock}
            />
        );

        fireEvent.press(getByText('OK'));
        expect(onOKMock).toHaveBeenCalled();
    });

    it('should call onCancel when Cancel button is pressed', () => {
        const onCancelMock = jest.fn();
        const { getByText } = render(
        <MessageBoxPergunta
            visivel={true}
            onCancel={onCancelMock}
        />
        );

        fireEvent.press(getByText('Cancelar'));
        expect(onCancelMock).toHaveBeenCalled();
    });
});
