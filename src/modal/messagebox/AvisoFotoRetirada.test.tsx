import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxAvisoFotoRetirada from './AvisoFotoRetirada';


describe('MessageBoxAvisoFotoRetirada Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxAvisoFotoRetirada  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxAvisoFotoRetirada visivel={true} />);
    
        expect(getByText('AVISO IMPORTANTE')).toBeTruthy();
        expect(getByText('ENTENDI!')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxAvisoFotoRetirada visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('ENTENDI!'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxAvisoFotoRetirada 
            visivel={true}
            titulo="AVISO IMPORTANTE"
            descricao="Esta é uma mensagem de aviso"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('AVISO IMPORTANTE')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de aviso')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
