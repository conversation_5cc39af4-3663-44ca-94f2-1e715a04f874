import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxAvisoFotoLocal from './AvisoFotoLocal';

describe('MessageBoxAvisoFotoLocal Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxAvisoFotoLocal  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxAvisoFotoLocal visivel={true} />);
    
        expect(getByText('AVISO IMPORTANTE')).toBeTruthy();
        expect(getByText('ENTENDI!')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxAvisoFotoLocal visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('ENTENDI!'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
            <MessageBoxAvisoFotoLocal 
                visivel={true}
                titulo="AVISO IMPORTANTE"
                descricao="Esta é uma mensagem de aviso"
                textoBotao="Fechar"
            />
        );
    
        expect(getByText('AVISO IMPORTANTE')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de aviso')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
