import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxAtencao from './Atencao';


describe('MessageBoxAtencao Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxAtencao  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxAtencao visivel={true} />);
    
        expect(getByText('Erro')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxAtencao visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('OK'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxAtencao 
            visivel={true}
            titulo="Atenção"
            descricao="Esta é uma mensagem de atenção"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Atenção')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de atenção')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
