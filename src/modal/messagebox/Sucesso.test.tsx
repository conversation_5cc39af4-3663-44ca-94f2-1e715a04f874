import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxSucesso from './Sucesso';


describe('MessageBoxSucesso Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxSucesso  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxSucesso visivel={true} />);
    
        expect(getByText('Sucesso')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxSucesso visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('OK'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxSucesso 
            visivel={true}
            titulo="Sucesso"
            descricao="Esta é uma mensagem de sucesso"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Sucesso')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de sucesso')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
