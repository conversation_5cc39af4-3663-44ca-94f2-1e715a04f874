import React from "react";
import {StyleSheet, View, Modal, Text, TouchableOpacity, Image} from 'react-native';

interface MessageBoxAvisoSucessoProps {
  visivel?: boolean;
  titulo?: string;
  descricao?: string;
  textoBotao?:string;
  children?:any;
  onFechar?: (value: any) => void;
 }

const MessageBoxAvisoSucesso: React.FC<MessageBoxAvisoSucessoProps> = ({

    visivel = false,
    titulo = "AVISO IMPORTANTE",
    descricao = "Lorem Ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt.",
    textoBotao = "ENTENDI!",
    children,
    onFechar,
   }) => {

  return(

    <Modal
      visible={visivel}
      statusBarTranslucent={true}
      transparent={true}
      animationType="fade"
      testID="modal"
    >
        <View style={styles.containerContent}>
          
          <View style={styles.containerCard}>

            {/* header */}
            <View style={styles.containerHeader}>

                <Text style={styles.textoInfoTitulo}>{titulo}</Text>
                <Image 
                    style={styles.imageUserFoto}
                    source={require('../../assets/png/probe_sucesso.png')}
                />

            </View>   

            {/* conteudo */}
            <View style={styles.containerConteudo}>
                <Text style={styles.textoInfoDescricao}>{descricao}</Text>
            </View>

            {/* footer */}
            <View style={styles.containerFooter}>
              <TouchableOpacity style={styles.botaoFooterOK} onPress={onFechar}>
                <Text style = {styles.textoBotaoOK}>{textoBotao}</Text>
              </TouchableOpacity>
            </View>
            
          </View>

          {children}

        </View>
        
      </Modal>    
  )
}

export default MessageBoxAvisoSucesso;

const styles = StyleSheet.create({
  containerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },

  containerCard: {
    paddingTop: 15,
    width: 343,
    height: 360,    
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#101828",
    shadowOpacity: 8,
  },

  containerHeader:{
    width: 343,
    height: 160,    
    gap: 5,    
    justifyContent:'center',
    alignItems:'center',
  },

  containerConteudo:{
    width: 343,
    height: 100,     
    justifyContent:'center',
    alignItems:'center',
    gap:15,
  },

  containerFooter:{
    width: 343,
    height: 100,
    paddingBottom:20,
    justifyContent: "center",
    alignItems: 'center'
  },

  textoInfoTitulo: {
    width: 311,    
    fontFamily: "SourceSans3_600SemiBold",
    fontWeight: "800",
    fontSize: 20,
    marginBottom: 12,
    lineHeight: 28,
    color: "#2E8C1F",
    textAlign:'center'
  },

  textoInfoDescricao: {
    width: 311,
    height: 40,
    fontFamily: "SourceSans3_400Regular",
    fontWeight: "400",
    fontSize: 16,
    color: "#525252",
    textAlign:'center',
    justifyContent:'center',
    alignItems:'center',
  },

  textoBotaoOK: {
    fontFamily: "Exo2_600SemiBold",
    fontWeight: "600",
    fontSize: 16,    
    color: "#FFFFFF",
    textAlignVertical: "center",
    textAlign:"center",
  },

  textoSubConteudo: {
    width:90,
    fontFamily: "SourceSans3_600SemiBold",
    fontWeight: "600",
    fontSize: 16,    
    color: "#2E8C1F",
    textAlignVertical: "center",
    textAlign:'left',
  },  

  botaoFechar: {
    width: 44,
    height: 44,
    borderRadius: 8,    
    alignItems:"flex-end"
  },  

  botaoFooterOK: {
    width: 311,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#2E8C1F",
    paddingTop: 10,
    paddingRight: 16,
    paddingBottom: 10,
    paddingLeft: 16,
    gap: 6,
    backgroundColor: "#2E8C1F",
    shadowColor: "#101828",   
    shadowOpacity: 5,
  },

  imageUserFoto:{
    resizeMode: 'contain',
    height:100,
    width:100,
    alignSelf: 'center',    
  },  
})