import React from "react";
import {StyleSheet, View, Modal, Text, TouchableOpacity} from 'react-native';

import IconAlertCircle from '../../assets/svg/icon_alert-circle.svg'
import IconX from '../../assets/svg/icon_x.svg'

interface MessageBoxAtencaoProps {
  visivel?: boolean;
  titulo?: string;
  descricao?: string;
  textoBotao?:string;
  children?:any;
  onFechar?: (value: any) => void;
 }

 const MessageBoxAtencao: React.FC<MessageBoxAtencaoProps> = ({
    visivel = false,
    titulo = "Erro",
    descricao = "",
    textoBotao = "OK",
    children,
    onFechar,
  }) => {

  return(

    <Modal
      visible={visivel}
      statusBarTranslucent={true}
      transparent={true}
      animationType="fade"
      testID="modal"
    >
        <View style={styles.containerContent}>
          
          <View style={styles.containerCard}>

            <View style={styles.containerHeader}>

              <View style = {styles.containerIcons}>
                <View style={styles.containerIconInfo}>
                  <IconAlertCircle color={"#D49600"} width={24} height={24}/>
                </View>
                <TouchableOpacity style = {styles.botaoFechar} onPress={onFechar}>
                  <IconX color={"#A3A3A3"} width={20} height={20}/>
                </TouchableOpacity>
              </View>

              <View style={styles.containerInfo}>
                <Text style={styles.textoInfoTitulo}>{titulo}</Text>
                <Text style={styles.textoInfoDescricao}>{descricao}</Text>
              </View>

            </View>          
            <View style={styles.containerFooter}>
              <TouchableOpacity style={styles.botaoFooterOK} onPress={onFechar}>
                <Text style = {styles.textoBotaoOK}>{textoBotao}</Text>
              </TouchableOpacity>
            </View>          
            
          </View>
          {children}
        </View>
        
      </Modal>    
  )
}

export default MessageBoxAtencao;

const styles = StyleSheet.create({
  containerContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.5)",
  },

  containerCard: {
    width: 343,
    height: 292,
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    shadowColor: "#101828",
    shadowOpacity: 8,
  },

  containerHeader:{
    width: 343,
    height: 162,    
    paddingTop: 20,
    paddingRight: 16,
    paddingBottom: 0,
    paddingLeft: 16,    
    gap: 12,
  },

  containerFooter:{
    width: 343,
    height: 130,
    paddingTop: 40,
    paddingRight: 16,
    paddingBottom: 16,
    paddingLeft: 16,
    justifyContent: "center",    
  },

  containerIcons:{
    width: 311,
    height: 72,
    paddingLeft: 0,
    flexDirection: "row", 
    gap: 220,
  },

  containerInfo:{
    width: 311,
    height: 72,
    paddingLeft: 5,
    gap: 4,
    alignItems:"center",
    justifyContent: "flex-start",    
  },

  containerIconInfo:{
    width:48,
    height:48,
    borderRadius: 9999,
    backgroundColor: "#FFE6AA",
    alignItems: "center",
    justifyContent: "center"
  },

  containerIconFechar:{
    width:44,
    height:44,
    borderRadius: 9999,
    alignItems: "center",
    justifyContent: "center",
  },

  textoInfoTitulo: {
    width: 311,
    height: 28,
    fontFamily: "SourceSans3_600SemiBold",
    fontWeight: "600",
    fontSize: 18,
    marginBottom: 12,
    lineHeight: 28,
    color: "#141414"
  },

  textoInfoDescricao: {
    width: 311,
    height: 40,
    fontFamily: "SourceSans3_400Regular",
    fontWeight: "400",
    fontSize: 16,
    lineHeight: 20,
    color: "#525252"    
  },

  textoBotaoOK: {
    fontFamily: "Exo2_600SemiBold",
    fontWeight: "600",
    fontSize: 16,    
    color: "#FFFFFF",
    textAlignVertical: "center",
    textAlign:"center",
  },    

  botaoFechar: {
    width: 44,
    height: 44,
    borderRadius: 8,    
    alignItems:"flex-end"
  },  

  botaoFooterOK: {
    width: 311,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#45D42E",
    paddingTop: 10,
    paddingRight: 16,
    paddingBottom: 10,
    paddingLeft: 16,
    gap: 6,
    backgroundColor: "#45D42E",
    shadowColor: "#101828",   
    shadowOpacity: 5,
  },  
})