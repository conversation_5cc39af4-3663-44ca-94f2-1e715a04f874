import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxErro from './Erro';


describe('MessageBoxErro Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxErro  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxErro visivel={true} />);
    
        expect(getByText('Erro')).toBeTruthy();
        expect(getByText('OK')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxErro visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('OK'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxErro 
            visivel={true}
            titulo="Atenção"
            descricao="Esta é uma mensagem de erro"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Atenção')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de erro')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
