import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageBoxAvisoSucesso from './AvisoSucesso';


describe('MessageBoxAvisoSucesso Modal', () => {

    it('deve renderizar corretamente com os props padrões', () => {
        const { queryByTestId } = render(<MessageBoxAvisoSucesso  />);
        
        expect(queryByTestId('modal')).toBeNull();
    });

    it('deve renderizar corretamente se visivel = true', () => {
        const { getByText, getByTestId } = render(<MessageBoxAvisoSucesso visivel={true} />);
    
        expect(getByText('AVISO IMPORTANTE')).toBeTruthy();
        expect(getByText('ENTENDI!')).toBeTruthy();
        expect(getByTestId('modal')).toBeTruthy();
    });  

    it('deve chamar onFechar quando o botão OK for clicado', () => {
        const onFecharMock = jest.fn();
        const { getByText } = render(<MessageBoxAvisoSucesso visivel={true} onFechar={onFecharMock} />);
    
        fireEvent.press(getByText('ENTENDI!'));
        expect(onFecharMock).toHaveBeenCalled();
    });

    it('deve renderizar corretamente com props personalizados', () => {
        const { getByText } = render(
          <MessageBoxAvisoSucesso 
            visivel={true}
            titulo="Sucesso"
            descricao="Esta é uma mensagem de sucesso"
            textoBotao="Fechar"
        />
        );
    
        expect(getByText('Sucesso')).toBeTruthy();
        expect(getByText('Esta é uma mensagem de sucesso')).toBeTruthy();
        expect(getByText('Fechar')).toBeTruthy();
    });
});
