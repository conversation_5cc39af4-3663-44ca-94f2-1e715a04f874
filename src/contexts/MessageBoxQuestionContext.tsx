import React, { ReactNode, createContext, useContext, useMemo, useState } from "react";
import MessageBoxQuestion from '../componentes/MessageBoxQuestion';

interface MessageBoxQuestionOptions {
    title?: string;
    description?: string;
    textYes?: string;
    textNo?: string;
}

interface MessageBoxQuestionProps {
    showMessageBoxQuestion: (options: MessageBoxQuestionOptions) => Promise<boolean>;
}

const MessageBoxQuestionContext = createContext<MessageBoxQuestionProps | undefined>(undefined);

export const MessageBoxQuestionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [visible, setVisible] = useState(false);
    const [options, setOptions] = useState<MessageBoxQuestionOptions>({});
    const [resolveCallback, setResolveCallback] = useState<(value: boolean) => void>();

    const showMessageBoxQuestion = (options: MessageBoxQuestionOptions): Promise<boolean> => {
        setOptions(options);
        setVisible(true);
        return new Promise<boolean>((resolve) => {
            setResolveCallback(() => resolve);
        });
    };

    const handleConfirm = () => {
        setVisible(false);
        resolveCallback?.(true);
    };

    const handleCancel = () => {
        setVisible(false);
        resolveCallback?.(false);
    };

    const contextValue = useMemo(() => ({
        showMessageBoxQuestion,        
    }), []);

    return (
        <MessageBoxQuestionContext.Provider value={ contextValue }>
            {children}
            {visible && (
                <MessageBoxQuestion
                    title={options.title}
                    description={options.description}
                    textYes={options.textYes}
                    textNo={options.textNo}
                    onConfirm={handleConfirm}
                    onCancel={handleCancel}
                />
            )}
        </MessageBoxQuestionContext.Provider>
    );
};

export const useMessageBoxQuestion = () => {
    const context = useContext(MessageBoxQuestionContext);
    
    if (!context) {
        throw new Error('useMessageBoxQuestion deve ser usado dentro de MessageBoxQuestionProvider');
        
    }
    
    return context;
};
