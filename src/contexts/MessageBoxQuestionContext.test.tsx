import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Button } from 'react-native';
import { MessageBoxQuestionProvider, useMessageBoxQuestion } from './MessageBoxQuestionContext';

jest.mock('../componentes/MessageBoxQuestion', () => {
  const React = require('react');
  const { Text, Button, View } = require('react-native');

  return (props: any) => {
    const { title, description, textYes, textNo, onConfirm, onCancel } = props;
    return (
      <View>
        <Text testID="question-title">{title}</Text>
        <Text testID="question-description">{description}</Text>
        <Button testID="yes-button" title={textYes || 'Sim'} onPress={onConfirm} />
        <Button testID="no-button" title={textNo || 'Não'} onPress={onCancel} />
      </View>
    );
  };
});

describe('MessageBoxQuestionContext', () => {
  it('lança erro se useMessageBoxQuestion for usado fora do provider', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    const BrokenComponent = () => {
      useMessageBoxQuestion();
      return null;
    };

    expect(() => render(<BrokenComponent />)).toThrow(
      'useMessageBoxQuestion deve ser usado dentro de MessageBoxQuestionProvider'
    );

    consoleErrorSpy.mockRestore();
  });

  it('mostra MessageBoxQuestion e resolve como true ao confirmar', async () => {
    const TestComponent = () => {
      const { showMessageBoxQuestion } = useMessageBoxQuestion();

      const handleClick = async () => {
        const result = await showMessageBoxQuestion({
          title: 'Confirmação',
          description: 'Tem certeza?',
          textYes: 'Sim',
          textNo: 'Não',
        });        
      };

      return <Button title="Mostrar" testID="trigger-button" onPress={handleClick} />;
    };

    const { getByTestId, findByTestId } = render(
      <MessageBoxQuestionProvider>
        <TestComponent />
      </MessageBoxQuestionProvider>
    );

    fireEvent.press(getByTestId('trigger-button'));

    const yesButton = await findByTestId('yes-button');
    fireEvent.press(yesButton);

    await waitFor(() => {
      // Garantia indireta de que a promessa foi resolvida e o modal fechado
      expect(() => getByTestId('question-title')).toThrow();
    });
  });

  it('resolve como false ao cancelar', async () => {
    let resultado: boolean | null = null;

    const TestComponent = () => {
      const { showMessageBoxQuestion } = useMessageBoxQuestion();

      const handleClick = async () => {
        resultado = await showMessageBoxQuestion({
          title: 'Confirmação',
          description: 'Deseja cancelar?',
        });
      };

      return <Button title="Mostrar" testID="trigger-button" onPress={handleClick} />;
    };

    const { getByTestId, findByTestId } = render(
      <MessageBoxQuestionProvider>
        <TestComponent />
      </MessageBoxQuestionProvider>
    );

    fireEvent.press(getByTestId('trigger-button'));

    const noButton = await findByTestId('no-button');
    fireEvent.press(noButton);

    await waitFor(() => {
      expect(resultado).toBe(false);
    });
  });
});
