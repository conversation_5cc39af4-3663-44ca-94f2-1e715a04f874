// __tests__/MessageBoxProvider.test.tsx
import { fireEvent, render, waitFor } from '@testing-library/react-native';
import React from 'react';
import { Button } from 'react-native';
import { MessageBoxProvider, useMessageBox } from './MessageBoxContext';

jest.mock('../componentes/MessageBox', () => {
    const React = require('react');
    const { Text, Button } = require('react-native');

    return (props: any) => {
        const { title, description, textButton, onClose } = props;
        return (
            <>
                <Text testID="message-title">{title}</Text>
                <Text testID="message-description">{description}</Text>
                <Button testID="message-button" title={textButton} onPress={onClose} />
            </>
        );
    };
});

// Componente de teste que usa o contexto
const TestComponent = () => {
    const { showMessageBox } = useMessageBox();

    const handlePress = () => {
        showMessageBox({
            title: '<PERSON><PERSON><PERSON><PERSON> de Teste',
            description: 'Descrição de Teste',
            textButton: 'Confirmar',
            onConfirm: jest.fn(),
        });
    };

    return <Button testID="trigger-button" title="Mostrar" onPress={handlePress} />;
};

describe('MessageBoxProvider', () => {
    it('não mostra MessageBox inicialmente', () => {
        const { queryByTestId } = render(
            <MessageBoxProvider>
                <TestComponent />
            </MessageBoxProvider>
        );

        expect(queryByTestId('message-title')).toBeNull();
    });

    it('mostra MessageBox quando showMessageBox é chamado', async () => {
        const { getByTestId, findByTestId } = render(
            <MessageBoxProvider>
                <TestComponent />
            </MessageBoxProvider>
        );

        fireEvent.press(getByTestId('trigger-button'));

        expect(await findByTestId('message-title')).toHaveTextContent('Título de Teste');
        expect(await findByTestId('message-description')).toHaveTextContent('Descrição de Teste');
    });

    it('chama onConfirm e fecha MessageBox ao pressionar botão', async () => {
        const mockConfirm = jest.fn();

        const CustomComponent = () => {
            const { showMessageBox } = useMessageBox();
            return (
                <Button
                    testID="trigger-button"
                    title="Mostrar"
                    onPress={() => showMessageBox({
                        title: 'Título',
                        description: 'Descrição',
                        textButton: '',
                        onConfirm: mockConfirm,
                    })}
                />
            );
        };

        const { getByTestId, queryByTestId } = render(
            <MessageBoxProvider>
                <CustomComponent />
            </MessageBoxProvider>
        );

        fireEvent.press(getByTestId('trigger-button'));

        const button = await waitFor(() => getByTestId('message-button'));
        fireEvent.press(button);

        expect(mockConfirm).toHaveBeenCalled();
        expect(queryByTestId('message-title')).toBeNull(); // MessageBox fechado
    });

    it('lança erro se useMessageBox for usado fora do Provider', () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {}); // evita log poluído

        const BrokenComponent = () => {
            useMessageBox(); // vai lançar o erro
            return null;
        };

        expect(() => render(<BrokenComponent />)).toThrow(
            'useMessageBox deve ser usado dentro de MessageBoxProvider'
        );

        consoleErrorSpy.mockRestore();
    });    
});
