import React, { createContext, useContext, useMemo, useState } from "react";

// componentes
import MessageBox from "../componentes/MessageBox";

type MessageBoxData = {
    title: string;
    description: string;
    textButton?: string;
    type?: 'sucess' | 'error' | 'warning';
    onConfirm?: () => void;
};

type ContextType = {
    showMessageBox: (data: MessageBoxData) => void;
    closeMessageBox: () => void;
}

const MessageBoxContext = createContext<ContextType | undefined>(undefined);

export const MessageBoxProvider = ({children}: {children: React.ReactNode}) => {
    const [visible, setVisible] = useState(false);
    const [data, setData] = useState<MessageBoxData | null>(null);

    const showMessageBox = (params: MessageBoxData) => {
        setData(params);
        setVisible(true);
    };

    const closeMessageBox = () => {
        setData(null);
        setVisible(false);
    };

    const onConfirm = () => {
        data?.onConfirm?.();
        closeMessageBox();
    };

    const contextValue = useMemo(() => ({
        showMessageBox,
        closeMessageBox
    }), []);

    return (
        <MessageBoxContext.Provider value={contextValue}>
            {children}
            {visible && data && (
                <MessageBox
                    title={data.title}
                    description={data.description}
                    textButton={data.textButton || 'OK'}
                    type={data.type}
                    onClose={onConfirm}
                />
            )}
        </MessageBoxContext.Provider>
    );
}

export  const useMessageBox = (): ContextType => {
    const context = useContext(MessageBoxContext);

    if(!context) {
        throw new Error ('useMessageBox deve ser usado dentro de MessageBoxProvider');
    }
    
    return context;
}