definitions:
  steps:
    - step: &test
        deployment: testing
        image:
          name: node:22.15.1-alpine
        services:
          - docker
        caches:
          - docker
          - node
        script:
          - npm install
          - npm run test:coverage
        artifacts:
          paths:
            - coverage/lcov.info
            - coverage/clover.xml

    - step: &snyk
        image:
          name: atlassian/pipelines-awscli:1.29.35
        services:
          - docker
        caches:
          - docker
        script:
          - apk update && apk upgrade
          - docker build -t ${BITBUCKET_COMMIT::7}:snyk .
          - apk add jq curl nodejs npm
          - npm install snyk snyk-to-html -g
          - snyk auth $SNYK_TOKEN_PLATFORM
          - snyk container test ${BITBUCKET_COMMIT::7}:snyk --file=./Dockerfile --json-file-output=resultado-snyk-container.json --severity-threshold=high || true
          - cat resultado-snyk-container.json | snyk-to-html > resultado-snyk-container.html
          - critical_vulnerabilities=$(jq '[.vulnerabilities[] | select(.severity == "high" or .severity == "critical")] | length' resultado-snyk-container.json)
          - echo "Número de vulnerabilidades críticas/altas:$critical_vulnerabilities"
          - if [ "$critical_vulnerabilities" -ge 1 ]; then exit 1; fi

    - step: &sonar
        image: sonarsource/sonar-scanner-cli
        script:
          - >-
            sonar-scanner
            -Dsonar.projectName="Telemetria Software App Suporte"
            -Dsonar.projectKey=app-zordon-suporte
            -Dsonar.host.url=${SONAR_HOST}
            -Dsonar.token=${SONAR_TOKEN}
            -Dsonar.sources=src/
            -Dsonar.tests=src/
            -Dsonar.test.inclusions=**/*.test.ts,**/*.test.tsx
            -Dsonar.language=TypeScript
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.javascript.coverage.reportPaths=coverage/clover.xml
            -Dsonar.exclusions=**/*.test.ts,**/*.test.tsx,**/__mock__/**
            -Dsonar.qualitygate.wait=true

  services:
    docker:
      memory: 2048

pipelines:
  pull-requests:
    '**':
      - parallel:
          fail-fast: true
          steps:
            - step:
                <<: *test
                name: Test on pull request
            - step:
                <<: *snyk
                name: Test snyk on pull request
      - step:
          <<: *sonar
          name: Sonar scan on pull request
