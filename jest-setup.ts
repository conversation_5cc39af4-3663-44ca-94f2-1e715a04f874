import '@testing-library/react-native/extend-expect';
import 'jest-canvas-mock';

jest.mock("@react-native-async-storage/async-storage", () =>
  require("@react-native-async-storage/async-storage/jest/async-storage-mock"),
)

jest.mock('react-native-permissions', () => require('./__mocks__/react-native-permissions'));
jest.mock('react-native-qrcode-scanner', () => require('./__mocks__/react-native-qrcode-scanner'));

import { NativeModules } from 'react-native';

NativeModules.RNCNetInfo = {
  getCurrentState: jest.fn(() => Promise.resolve()),
  addListener: jest.fn(),
  removeListeners: jest.fn()
};

process.env.LANG = 'pt_BR.UTF-8';
process.env.LC_ALL = 'pt_BR.UTF-8';
