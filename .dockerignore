# Node modules
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
build/
dist/
.expo/
.expo-shared/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Android
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/app/release/
*.keystore
!debug.keystore

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/Podfile.lock

# Expo
.expo/
.expo-shared/

# React Native
.react-native/

# Metro
.metro-health-check*

# Testing
/src/__tests__/
**/*.test.tsx
coverage/
.nyc_output/

# Temporary files
tmp/
temp/

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation
README.md
docs/

# Scripts
scripts/

# CI/CD
.github/
bitbucket-pipelines.yml
.gitlab-ci.yml

# Misc
*.tgz
*.tar.gz