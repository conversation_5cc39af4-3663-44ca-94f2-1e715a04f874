{"name": "zordonsuporte", "version": "1.0.1", "main": "index.js", "scripts": {"start": "expo start -c", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest", "test:coverage": "jest --bail --coverage --runInBand", "docker:build": "./scripts/docker-dev.sh build", "docker:start": "./scripts/docker-dev.sh start", "docker:stop": "./scripts/docker-dev.sh stop", "docker:restart": "./scripts/docker-dev.sh restart", "docker:logs": "./scripts/docker-dev.sh logs", "docker:shell": "./scripts/docker-dev.sh shell", "docker:android": "./scripts/docker-dev.sh android", "docker:ios": "./scripts/docker-dev.sh ios", "docker:web": "./scripts/docker-dev.sh web", "docker:clean": "./scripts/docker-dev.sh clean", "docker:install": "./scripts/docker-dev.sh install", "docker:test": "./scripts/docker-dev.sh test", "docker:emulator": "./scripts/docker-dev.sh emulator"}, "dependencies": {"@charlespalmerbf/react-native-leaflet-js": "^1.3.4", "@expo-google-fonts/exo-2": "^0.3.0", "@expo-google-fonts/source-sans-3": "^0.3.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.10.1", "@shopify/react-native-skia": "^1.3.13", "axios": "^1.9.0", "axios-mock-adapter": "^2.1.0", "buffer": "^6.0.3", "cross-spawn": "^7.0.6", "expo": "51.0.22", "expo-checkbox": "~3.0.0", "expo-font": "~12.0.9", "expo-location": "~17.0.1", "expo-status-bar": "^2.2.3", "image-size": "^1.2.1", "leaflet": "^1.9.4", "micromatch": "^4.0.8", "mock-async-storage": "^2.9.0", "react": "18.2.0", "react-native": "0.74.3", "react-native-app-intro-slider": "^4.0.4", "react-native-calendar-events": "^2.2.0", "react-native-camera": "^4.2.1", "react-native-element-dropdown": "^2.12.4", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "2.16.1", "react-native-image-picker": "^7.1.2", "react-native-maps": "^1.20.1", "react-native-modal": "^13.0.1", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.4.0", "react-native-permissions": "^4.1.5", "react-native-qrcode-scanner": "^1.5.5", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "3.10.1", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-share": "^12.0.9", "react-native-svg": "15.2.0", "react-native-tab-view": "^3.5.2", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.5", "rn-slide-button": "^1.0.3", "sp-react-native-mqtt": "^0.5.1", "victory-native": "^41.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "13.2.0", "@tsconfig/react-native": "^3.0.5", "@types/jest": "^29.5.14", "@types/react": "^18.3.18", "@types/react-navigation": "^3.0.8", "@types/react-test-renderer": "^19.0.0", "eslint-plugin-testing-library": "^7.1.1", "jest": "^29.7.0", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.2.0", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "private": true, "engines": {"node": "22.16"}}