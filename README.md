[![Coverage](https://sonar.doc88.com.br/api/project_badges/measure?project=app-zordon-suporte&metric=coverage&token=sqb_32313a73410e8aecda893543faf753f9e5ecb086)](https://sonar.doc88.com.br/dashboard?id=app-zordon-suporte)
[![Lines of Code](https://sonar.doc88.com.br/api/project_badges/measure?project=app-zordon-suporte&metric=ncloc&token=sqb_32313a73410e8aecda893543faf753f9e5ecb086)](https://sonar.doc88.com.br/dashboard?id=app-zordon-suporte)

# Telemetria Software App Suporte

Aplicativo de suporte as técnicos nas instalações de equipamentos de medição para telemetria.

# Instruções para rodar o projeto

## ▶️ 1. Suba o ambiente com Docker Compose

No terminal, execute:

> docker-compose up --build

Esse comando irá:

- Construir a imagem Docker com Node.js, Android SDK e o emulador
- Criar um AVD (Android Virtual Device)
- Iniciar o emulador em modo headless
- Iniciar o Metro bundler (npm start)

## 🧪 2. Verifique se o Metro bundler está rodando

Abra seu navegador e acesse:

> http://localhost:8081

## 📱 3. Inicie o aplicativo no emulador

Como o emulador está rodando em modo headless (sem interface gráfica), você pode:

- Usar adb para verificar se o dispositivo está ativo:

  > docker exec -it react_native_dev adb devices

- Instalar e iniciar o app manualmente (se já estiver compilado):

  ```bash
  docker exec -it react_native_dev bash
  # Dentro do contêiner:
  npx react-native run-android
  ```

## 🛠️ 4. Debug e logs

Você pode acompanhar os logs do contêiner com:

> docker logs -f react_native_dev
