// Only import react-native-gesture-handler on native platforms
import 'react-native-gesture-handler';

import React from 'react';
import { LogBox, StatusBar } from 'react-native';
import Orientation from 'react-native-orientation-locker';

import { Exo2_400Regular, Exo2_500Medium, Exo2_600SemiBold, Exo2_700Bold } from '@expo-google-fonts/exo-2';
import { SourceSans3_400Regular, SourceSans3_400Regular_Italic, SourceSans3_500Medium, SourceSans3_600SemiBold } from '@expo-google-fonts/source-sans-3';
import { useFonts } from 'expo-font';

import { NavigationContainer } from '@react-navigation/native';


// rotas
import { MessageBoxProvider } from './src/contexts/MessageBoxContext';
import { MessageBoxQuestionProvider } from './src/contexts/MessageBoxQuestionContext';
import Routes from './src/routes/index';

export default function App() {

  //LogBox.ignoreLogs(['Warning: The onTouch property is deprecated and will be removed in the next Skia release.']); // Ignore log notification by message
  //LogBox.ignoreLogs(['new NativeEventEmitter']); // Ignore log notification by message
  LogBox.ignoreAllLogs();//Ignore all log notifications

  // aplica imutabilidade para as fontes
  let [fontsLoaded] = useFonts({
    Exo2_400Regular, Exo2_500Medium, Exo2_600SemiBold, Exo2_700Bold,
    SourceSans3_400Regular, SourceSans3_500Medium, SourceSans3_600SemiBold, SourceSans3_400Regular_Italic
  });

  // se carregou as fontes
  if (!fontsLoaded) {
    return null;
  }

  // trava a rotação da tela para retrato
  Orientation.lockToPortrait();

  return (
    <NavigationContainer>
      <StatusBar barStyle="dark-content" translucent backgroundColor="transparent" />
          <MessageBoxProvider>
            <MessageBoxQuestionProvider>
              <Routes />
            </MessageBoxQuestionProvider>              
          </MessageBoxProvider>
    </NavigationContainer>
  );
}