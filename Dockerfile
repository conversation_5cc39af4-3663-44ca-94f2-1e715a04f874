# Use a imagem base do Ubuntu
FROM ubuntu:24.04

# Instale dependências básicas
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    openjdk-8-jdk \
    libgl1-mesa-dev \
    libqt5widgets5 \
    git \
    ca-certificates \
    build-essential

# Instale o Node.js 22.16.0 manualmente
RUN curl -fsSL https://nodejs.org/dist/v22.16.0/node-v22.16.0-linux-x64.tar.xz -o node.tar.xz \
    && mkdir -p /usr/local/lib/nodejs \
    && tar -xJf node.tar.xz -C /usr/local/lib/nodejs \
    && rm node.tar.xz

ENV NODEJS_HOME=/usr/local/lib/nodejs/node-v22.16.0-linux-x64
ENV PATH=$NODEJS_HOME/bin:$PATH

# Verifique a instalação do Node.js e npm
RUN node -v && npm -v

# Instale o Android SDK
RUN wget -q https://dl.google.com/android/repository/sdk-tools-linux-4333796.zip -O android-sdk.zip \
    && unzip android-sdk.zip -d /usr/local/android-sdk \
    && rm android-sdk.zip

ENV ANDROID_HOME=/usr/local/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/tools/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/emulator

# Aceite as licenças do Android SDK
RUN yes | sdkmanager --licenses

# Instale ferramentas do SDK e imagem do sistema
RUN sdkmanager \
    "platform-tools" \
    "platforms;android-29" \
    "build-tools;29.0.3" \
    "emulator" \
    "system-images;android-29;google_apis;x86_64"

# Crie diretório de trabalho
WORKDIR /app

# Copie os arquivos do projeto
COPY . .

# Copie o script de configuração do AVD
COPY setup_avd.sh /usr/local/bin/setup_avd.sh
RUN chmod +x /usr/local/bin/setup_avd.sh

# Instale as dependências do projeto
RUN npm install

# Comando padrão
CMD ["sh", "-c", "/usr/local/bin/setup_avd.sh && npm start"]
