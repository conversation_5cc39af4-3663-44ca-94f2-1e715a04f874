import type { Config } from 'jest';

const config: Config = {
    preset: 'react-native',
    setupFilesAfterEnv: [
        "@testing-library/jest-native/extend-expect"
    ],
    coverageReporters: ['text', 'lcov', 'clover', ['text', { skipFull: true }]],
    transformIgnorePatterns: [
        "node_modules/(?!react-native|@react-native|react-native-vector-icons|@react-navigation)"
    ],
    moduleNameMapper: {
        "\\.svg": "<rootDir>/__mocks__/svgMock.js",
        "^@env$": "<rootDir>/__mocks__/envMock.ts",
    }
};

export default config;