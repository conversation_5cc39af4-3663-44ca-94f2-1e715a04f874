import { modbusSettings } from "../src/data";
import { mockSlavesSettings, mockSlavesSettingsNoVariables, mockSlavesSettingsVariables } from "./SlavesSettingsMock";

// estrutura inicial de configuração dos equipamentos configurados na probe
export const MockModbusSettings = () => {

  var modbus = {} as modbusSettings;

  modbus = {
    id_device: -1,
    nameDevice: undefined,
    endianessDevice: -1,
    slaves: [],
  }

  return [modbus];    
}

// estrutura com slaves e variaveis configurados na probe
export const MockModbusSettingsSlaves = () => {

  var modbus = {} as modbusSettings;

  modbus = {
    id_device: -1,
    nameDevice: 'Device 1',
    endianessDevice: -1,
    slaves: mockSlavesSettings,
  }

  return [modbus];    
}

// estrutura com slaves e variaveis configurados na probe
export const MockModbusSettingsSlaves1 = () => {

  var modbus = {} as modbusSettings;

  modbus = {
    id_device: 1,
    nameDevice: 'Device 1',
    endianessDevice: 1,
    slaves: mockSlavesSettings,
  }

  return [modbus];    
}

// estrutura com slaves com variaveis
export const MockModbusSettingsVariables = () => {

  var modbus = {} as modbusSettings;

  modbus = {
    id_device: -1,
    nameDevice: '',
    endianessDevice: -1,
    slaves: mockSlavesSettingsVariables,
  }

  return [modbus];    
}

// estrutura com slaves e sem variaveis
export const MockModbusSettingsNoVariables = () => {

  var modbus = {} as modbusSettings;

  modbus = {
    id_device: -1,
    nameDevice: '',
    endianessDevice: -1,
    slaves: mockSlavesSettingsNoVariables,
  }

  return [modbus];    
}