import { pulsoSettings } from "../src/data";

// estrutura inicial de configuração dos equipamentos configurados na probe
export const MockPulsoSettings = () => {

  var pulso = {} as pulsoSettings;

  pulso = {
    pulso: 'PULSO 1',
    port:0,
    type: 0,
    contact: 0,
    reactive: false,
    repo: true,
    factor: 1.0,
    offset_time: 100,
    send_time: 900,
    send_time_repo: 90,
  }

  return [pulso];    
}