
import React, { forwardRef, useEffect } from 'react';
import { View, Text } from 'react-native';

interface QRCodeScannerProps {
  onRead?: (event: { data: string }) => void;
  bottomContent?: React.ReactNode;
  testID?: string;
}

// Usando forwardRef para permitir refs externas
const MockQRCodeScanner = forwardRef<View, QRCodeScannerProps>(({ onRead, bottomContent, testID }, ref) => {

  useEffect(() => {
    if (onRead) {
      onRead({ data: 'ABCDEFGHIJKL' }); // Simula a leitura do QR Code
    }
  }, [onRead]); // Dispara apenas quando `onRead` muda

  return (
    <View ref={ref} testID={testID || 'mock-qrcode-scanner'}>
      <Text>Mock QR Code Scanner</Text>
      {bottomContent}      
    </View>
  );
});

export default MockQRCodeScanner;


