// __mocks__/SlavesSettingsMock.js

export const mockSlavesSettings = [
    {
      id_slave: 1,
      statusSlave: true,
      nomeSlave: 'Slave 1',
      enderecoSlave: '1',
      tempoEnvioSlave: 60,
      protocoloSlave: 0,
      paridadeSlave: 0,
      baudeRateSlave: 9600,
      variables: [
        {
            nomeVariable: 'active',
            descricaoVariable: 'ativo',
            valorMediaVariable: false,
            valorUltimaVariable: true,
            funcaoVariable: 3,
            fatorVariable: 10,
            enderecoVariable: 99,
            formatoVariable: 199,
        },
      ],
    },
    {
      id_slave: 2,
      statusSlave: false,
      nomeSlave: 'Slave 2',
      enderecoSlave: '2',
      tempoEnvioSlave: 120,
      protocoloSlave: 1,
      paridadeSlave: 1,
      baudeRateSlave: 115200,
      ipSlave:'***********',
      portSlave: 1,
      conexaoSlave:0,
      variables: [
        {
            nomeVariable: 'reactive',
            descricaoVariable: 'reativo',
            valorMediaVariable: true,
            valorUltimaVariable: true,
            funcaoVariable: 3,
            fatorVariable: 10,
            enderecoVariable: 100,
            formatoVariable: 200,
        },
      ],
    },
    {
      id_slave: 3,
      statusSlave: false,
      nomeSlave: 'Slave 3',
      enderecoSlave:  undefined,
      tempoEnvioSlave: 120,
      protocoloSlave: 1,
      paridadeSlave: 1,
      baudeRateSlave: 115200,
      variables: [
        {
            nomeVariable: 'reactive',
            descricaoVariable: 'reativo',
            valorMediaVariable: true,
            valorUltimaVariable: true,
            funcaoVariable: 3,
            fatorVariable: 10,
            enderecoVariable: 100,
            formatoVariable: 200,
        },
      ],
    },    
  ];

  export const mockSlavesSettingsVariables = [
    {
      id_slave: 1,
      statusSlave: true,
      nomeSlave: 'Slave 1',
      enderecoSlave: '1',
      tempoEnvioSlave: 60,
      protocoloSlave: 1,
      paridadeSlave: 0,
      baudeRateSlave: 9600,
      variables: [
        {
            nomeVariable: 'active',
            descricaoVariable: 'ativo',
            valorMediaVariable: false,
            valorUltimaVariable: false,
            funcaoVariable: 3,
            fatorVariable: 10,
            enderecoVariable: 99,
            formatoVariable: 199,
        },
      ],
    },
    {
      id_slave: 2,
      statusSlave: false,
      nomeSlave: 'Slave 2',
      enderecoSlave: '2',
      tempoEnvioSlave: 120,
      protocoloSlave: 2,
      paridadeSlave: 1,
      baudeRateSlave: 115200,
      variables: [
        {
            nomeVariable: 'reactive',
            descricaoVariable: 'reativo',
            valorMediaVariable: true,
            valorUltimaVariable: false,
            funcaoVariable: 3,
            fatorVariable: 10,
            enderecoVariable: 100,
            formatoVariable: 200,
        },
      ],
    },
  ];

  export const mockSlavesSettingsNoVariables = [
    {
      id_slave: 1,
      statusSlave: true,
      nomeSlave: 'Slave 1',
      enderecoSlave: '1',
      tempoEnvioSlave: 60,
      protocoloSlave: 1,
      paridadeSlave: 0,
      baudeRateSlave: 9600,
      variables: [],
    },
    {
      id_slave: 2,
      statusSlave: false,
      nomeSlave: 'Slave 2',
      enderecoSlave: '2',
      tempoEnvioSlave: 120,
      protocoloSlave: 2,
      paridadeSlave: 1,
      baudeRateSlave: 115200,
      variables: [],
    },
  ];  




