import { ionSettings } from "../src/data";

// estrutura inicial de configuração dos equipamentos configurados na probe
export const MockIonSettings = () => {

  var ion = {} as ionSettings;

  ion = {
    id_ion: 0,
    ion: 'RS232',
    port:0,
    paridade: 0,
    baudRate: 5,
  }

  return [ion];    
}

export const MockIonSettingsBaudRateInvalido = () => {

  var ion = {} as ionSettings;

  ion = {
    id_ion: 0,
    ion: 'RS232',
    port:0,
    paridade: 0,
    baudRate: -1,
  }

  return [ion];    
}

export const MockIonSettingsParidadeInvalido = () => {

  var ion = {} as ionSettings;

  ion = {
    id_ion: 0,
    ion: 'RS232',
    port:0,
    paridade: -1,
    baudRate: 5,
  }

  return [ion];    
}