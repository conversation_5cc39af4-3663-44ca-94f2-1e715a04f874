export const Mo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = {
    modbus: {
      Agua: {
        Conaut: {
          "IFC 050": "AGU_CNT_IFC050"
        },
        Incontrol: {
          ITS2000: "AGU_INCTRL_ITS2000",
          "PRO 1000": "AGU_INCTRL_PRO1000"
        },
        Indflow: {
          "M-Blit-U": "AGU_INDFLOW_BLITU"
        },
        Pase: {
          PFM: "AGU_PASE_PFM"
        }
      },
      Ambiental: {
        Novus: {
          RHT: "AMB_NOVUS_RHT"
        }
      },
      Energia: {
        ABB: {
          NEXUS2: "EN_ABB_NEXUS2"
        },
        "<PERSON>": {
          EM100: "EN_CG_EM100",
          EM111: "EN_CG_EM100",
          EM112: "EN_CG_EM100",
          EM200: "EN_CG_EM200",
          EM210: "EN_CG_EM210",
          EM24: "EN_CG_EM24",
          EM330: "EN_CG_EM330",
          EM340: "EN_CG_EM330",
          ET112: "EN_CG_EM100",
          ET330: "EN_CG_EM330",
          ET340: "EN_CG_EM330",
          WM30: "EN_CG_WM30"
        },
        Circutor: {
          "CVM NRG96": "EN_CIRCUTOR_NRG96"
        },
        Frer: {
          Q96B4W: "EN_FR_Q96B4W"
        },
        GE: {
          "Multilin F650": "EN_GE_MLTLIN_F650"
        },
        "Gauge Tech": {
          "Shark 100": "EN_GTECH_SHARK100"
        },
        IMS: {
          "PowerNET M150": "EN_POLUS_PL-M2",
          "PowerNET M160": "EN_POLUS_PL-M2",
          "PowerNET M200": "EN_WEG_MMW02",
          "Smart Indicator": "EN_IMS_INDICATOR"
        },
        Kron: {
          "Ikron 01-D": "EN_KRON_IKRON_01_D",
          "Ikron 03": "EN_KRON_IKRON_03",
          Konect: "EN_KRON_KONECT",
          "M Box": "EN_KRON_M_BOX",
          "MKM 120": "EN_KRON_MKM120",
          "Multi K": "EN_KRON_MULT_K",
          "Multi K 05": "EN_KRON_MULTK5",
          "Multi K 120": "EN_KRON_MULTK120",
          "Multi K 30Wh": "EN_KRON_MULTK30WH",
          "Multi K Monofasico": "EN_KRON_MULTK_MONO",
          "TKE-01": "EN_KRON_TKE01"
        },
        "Landis+Gyr": {
          E650: "EN_LDSGYR_E650"
        },
        Mercato: {
          Omnirate: "EN_MRCT_OMNIRATE"
        },
        Mitsubishi: {
          ME96SS: "EN_MIT_ME96SS"
        },
        Nansen: {
          "SPECTRUM K": "EN_NANS_SPECTRUM_K"
        },
        Polus: {
          MT100: "EN_POLUS_MT100",
          "PL-M2": "EN_POLUS_PL-M2"
        },
        QDE: {
          "QDE-3S": "EN_QED_QDE-3S"
        },
        Schneider: {
          IEM3250: "EN_SCHDR_IEM3250",
          IEM3x00: "EN_SCHDR_IEM3x00",
          "ION 6800": "EN_SCHDR_ION6800",
          "ION 7650": "EN_SCHDR_ION7650",
          "ION 8600": "EN_SCHDR_ION8600",
          "ION CHESF": "EN_SCHDR_ION_CHESF",
          PM1200: "EN_SCHDR_PM1200",
          PM210: "EN_SCHDR_PM210",
          PM2100: "EN_SCHDR_PM2100",
          PM2200: "EN_SCHDR_PM2200",
          PM5340: "EN_SCHDR_PM5340",
          PM710: "EN_SCHDR_PM710"
        },
        Siemens: {
          "PAC 1020": "EN_SIEMENS_PAC1020",
          "PAC 3100": "EN_SIEMENS_PAC3100",
          "PAC 3200": "EN_SIEMENS_PAC3200",
          "SIPROTEC 5": "EN_SIEMENS_SIPROTEC5",
          "SMART 7KT": "EN_SIEMENS_SMART7KT"
        },
        Weg: {
          MMW01: "EN_WEG_MMW01",
          MMW02: "EN_WEG_MMW02",
          MMW03: "EN_WEG_MMW03",
          "MMW03 CH": "EN_WEG_MMW03CH"
        }
      },
      Especial: {
        Urbanity: {
          CLP: "ESP_URBANITY_CLP",
          QGBT_1QG1: "URBANITY_QGBT_1QG1",
          QGBT_1QG1_Total: "URBANITY_QGBT_1QG1_T",
          QGBT_2QG1: "URBANITY_QGBT_2QG1",
          QGBT_2QG1_Total: "URBANITY_QGBT_2QG1_T",
          QGBT_3QG1: "URBANITY_QGBT_3QG1",
          QGBT_3QG1_Total: "URBANITY_QGBT_3QG1_T",
          QGBT_4QG1: "URBANITY_QGBT_4QG1",
          QGBT_4QG1_Total: "URBANITY_QGBT_4QG1_T",
          QGBT_5QG1: "URBANITY_QGBT_5QG1",
          QGBT_5QG1_Total: "URBANITY_QGBT_5QG1_T",
          QGBT_6QG1: "URBANITY_QGBT_6QG1",
          QGBT_6QG1_Total: "URBANITY_QGBT_6QG1_T",
          QGBT_7QG1: "URBANITY_QGBT_7QG1",
          QGBT_8QG1: "URBANITY_QGBT_8QG1",
          QGBT_9QG1: "URBANITY_QGBT_9QG1",
          QGBT_F1: "URBANITY_QGBT_F1",
          QGBT_Q1: "URBANITY_QGBT_Q1",
          QGBT_Q1_Total: "URBANITY_QGBT_Q1_T",
          QGBT_Q8: "URBANITY_QGBT_Q8",
          QGBT_Q8_Total: "URBANITY_QGBT_Q8_T",
          QGBT_QR1: "URBANITY_QGBT_QR1",
          QGBT_QR2: "URBANITY_QGBT_QR2",
          QGBT_QR2_Total: "URBANITY_QGBT_QR2_T"
        }
      },
      Gas: {
        Contech: {
          ft2: "GS_CNT_FT2"
        }
      },
      Telecom: {
        Delta: {
          Orion: "TLC_DLT_ORION"
        }
      },
      Universal: {
        Novus: {
          "DigiRail Connect": "UNV_NVS_DIGIRAIL_CN",
          "DigiRail-2A": "UNV_NVS_DIGIRAIL_2A"
        }
      }
    }
  };
  