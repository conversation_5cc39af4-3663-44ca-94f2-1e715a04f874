export const MockVariable<PERSON>son = {
	"dev_name": "EN_KRON_MULTK120",
	"endianness": 2,
	"variables": {
		"Tensão A-N (V)":{
			"var_name": "voltan",
			"function": 4,
			"address": 16,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Tensão B-N (V)":{
			"var_name": "voltbn",
			"function": 4,
			"address": 18,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Tensão C-N (V)":{
			"var_name": "voltcn",
			"function": 4,
			"address": 20,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Tensão A-B (V)":{
			"var_name": "voltab",
			"function": 4,
			"address": 84,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Tensão B-C (V)":{
			"var_name": "voltbc",
			"function": 4,
			"address": 86,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Tensão C-A (V)":{
			"var_name": "voltca",
			"function": 4,
			"address": 88,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Corrente A (A)":{
			"var_name": "cura",
			"function": 4,
			"address": 22,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Corrente B (A)":{
			"var_name": "curb",
			"function": 4,
			"address": 24,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Corrente C (A)":{
			"var_name": "curc",
			"function": 4,
			"address": 26,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Fator de Potência Total":{
			"var_name": "powerf",
			"function": 4,
			"address": 6,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Frequência (Hz)":{
			"var_name": "freq",
			"function": 4,
			"address": 14,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Energia Ativa Total (1/4 Wh)":{
			"var_name": "active",
			"function": 4,
			"address": 12,
			"factor": 0.25,
			"send": false,
			"mean": true,
			"data_type": 6
		},
		"Energia Reativa (1/4 VArh)":{
			"var_name": "reactive",
			"function": 4,
			"address": 10,
			"factor": 0.25,
			"send": false,
			"mean": true,
			"data_type": 6
		},
		"Energia Ativa Total (Wh)":{
			"var_name": "active",
			"function": 4,
			"address": 12,
			"factor": 1,
			"send": true,
			"mean": true,
			"data_type": 6
		},
		"Energia Reativa (VArh)":{
			"var_name": "reactive",
			"function": 4,
			"address": 10,
			"factor": 1,
			"send": true,
			"mean": true,
			"data_type": 6
		},
		"Energia Ativa Acumulada (kWh)":{
			"var_name": "energy_act_forward",
			"function": 4,
			"address": 52,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Energia Reativa Acumulada (kVArh)":{
			"var_name": "energy_react_forward",
			"function": 4,
			"address": 54,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Energia Ativa Negativa (kWh)":{
			"var_name": "energy_act_reverse",
			"function": 4,
			"address": 56,
			"factor": 1,
			"send": false,
			"mean": false,
			"data_type": 6
		},
		"Potência Ativa A (W)": {
            "var_name": "actpowera",
            "function": 4,
            "address": 28,
            "data_type": 6,
            "send": true,
            "mean": false,
            "factor": 1
    	},
        "Potência Ativa B (W)": {
            "var_name": "actpowerb",
            "function": 4,
            "address": 30,
            "data_type": 6,
            "send": true,
            "mean": false,
            "factor": 1
        },
        "Potência Ativa C (W)": {
            "var_name": "actpowerc",
            "function": 4,
            "address": 32,
            "data_type": 6,
            "send": true,
            "mean": false,
            "factor": 1
        },
	    "Error Code": {
	    	"var_name": 0,
	        "function": 0,
	        "address": 0,
	        "data_type": 0,
	        "factor": 0,
	        "send": true,
	        "mean": false
	    }
	}
}

export const MockVariableJson1 = {
	"dev_name": "EN_KRON_MULTK120",
	"endianness": 2,
	"variables": {
		"Tensão A-N (V)":{
			"var_name": "voltan",
			"function": 4,
			"address": 16,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		},
		"Tensão B-N (V)":{
			"var_name": "voltbn",
			"function": 4,
			"address": 18,
			"factor": 1,
			"send": true,
			"mean": false,
			"data_type": 6
		}
	}
}
