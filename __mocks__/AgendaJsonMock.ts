
let data_hoje = new Date;
let data_amanha = new Date;
    data_amanha.setDate(data_hoje.getDate() + 1);
let data_futura = new Date;
    data_futura.setDate(data_amanha.getDate() + 1);

const getData = (data: Date): string => {
    
    return new Intl.DateTimeFormat('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' }).format(data);
}

export const MockAgendaInstaladorJson = {
    "NomeEmpr": "KPI",  // Empresa do instalador
    "Status": "1",	    // Status do instalador 	**Obs 2
    "Agendas": 			// Agendas do instalador
    [
        {
            "Emissao": '01-05-2025',                    					// Emissão da PréOS
            "Loja": "01",													// Loja do cliente (filial do cliente)
            "Fone2": "",													// Fone2 do cliente
            "Status": "2",                 									// Status da PréOS **Obs 1
            "Fone1": "9291122786",											// Fone1 do cliente
            "DescOcor": " Instalação de telemetria de fronteira",			// Descrição da ocorrência
            "HrFim": "12:00",												// Hora final da agenda
            "Bairro": "DISTRITO INDUSTRIAL",								// Bairro do cliente
            "DtInicio": getData(data_hoje),										        // Data de início da agenda
            "HrInicio": "09:00",											// Hora de início da agenda
            "ObsPreOS": "09/11/2020 - Procurar por Douglas - Renan",	    // Observações da PréOS
            "CodPro": "K2.1.3.FG",											// Código do produto
            "UF": "AM",													    // UF do cliente
            "DescPro": "PROBE MEDICAO DE GRANDEZAS ELETRICAS",				// Descrição do produto
            "Endereco": "R IPE, 194",										// Endereço do cliente
            "CEP": "69075100",												// CEP do cliente
            "CodOcor": "FRO1.1",											// Código da ocorrência
            "PreOs": "00000001",    										// Número da PréOS (PK)
            "CodCli": "060552",												// Código do Cliente
            "Contato": "DOUGLAS",											// Contato do cliente
            "Referencia": "",												// Ref. endereço do cliente	
            "ObsAgenda": "",												// Observações da agenda
            "NomeSC": "METALFINO",										    // Nome do cliente
            "Ramal": "",													// Ramal (ref fone1 cliente)
            "Email": "<EMAIL>",					// Email contato do cliente
            "DtFim":  getData(data_hoje),											        // Data final da agenda
            "Municipio": "MANAUS"											// Município do cliente
        },
        {
            "Emissao": "01-05-2025",
            "Loja": "02",
            "Fone2": "43984939453",
            "Status": "2",
            "Fone1": "4331745053",
            "DescOcor": "Desinstalação de telemetria de fronteira",
            "HrFim": "18:00",
            "Bairro": "JARDIM SANTO AMARO",
            "DtInicio":  getData(data_hoje),
            "HrInicio": "13:00",
            "ObsPreOS": "",
            "CodPro": "K2.1.3.FG",
            "UF": "PR",
            "DescPro": "PROBE MEDICAO DE GRANDEZAS ELETRICAS",
            "Endereco": "AV GABRIEL FRECEIRO DE MIRANDA, 21",
            "CEP": "86185010",
            "CodOcor": "FRO3.1",
            "PreOs": "00000002",
            "CodCli": "074898",
            "Contato": "APARECIDO SERAPIAO DOS SANTOS",
            "Referencia": "",
            "ObsAgenda": "",
            "NomeSC": "MGL",
            "Ramal": "",
            "Email": "<EMAIL>",
            "DtFim":  getData(data_hoje),
            "Municipio": "CAMBE"
        },
        {
            "Emissao": "01-05-2025",
            "Loja": "02",
            "Fone2": "43984939453",
            "Status": "2",
            "Fone1": "4331745053",
            "DescOcor": "Manutenção com troca de telemetria",
            "HrFim": "18:00",
            "Bairro": "Jardim Paraiso",
            "DtInicio":  getData(data_amanha),
            "HrInicio": "09:00",
            "ObsPreOS": "",
            "CodPro": "K2.1.3.FG",
            "UF": "PR",
            "DescPro": "PROBE MEDICAO DE GRANDEZAS ELETRICAS",
            "Endereco": "Rua Robson Crusoe, 21",
            "CEP": "86185010",
            "CodOcor": "MANUT",
            "PreOs": "00000003",
            "CodCli": "074898",
            "Contato": "John Doe",
            "Referencia": "",
            "ObsAgenda": "",
            "NomeSC": "MGL",
            "Ramal": "",
            "Email": "<EMAIL>",
            "DtFim": getData(data_amanha),
            "Municipio": "SP"
        },
        {
            "Emissao": "01-05-2025",
            "Loja": "02",
            "Fone2": "43984939453",
            "Status": "2",
            "Fone1": "4331745053",
            "DescOcor": "Substituição de telemetria de fronteira de terceiros",
            "HrFim": "18:00",
            "Bairro": "Jardim Paraiso",
            "DtInicio": getData(data_futura),
            "HrInicio": "09:00",
            "ObsPreOS": "",
            "CodPro": "K2.1.3.FG",
            "UF": "PR",
            "DescPro": "PROBE MEDICAO DE GRANDEZAS ELETRICAS",
            "Endereco": "Rua Robson Crusoe, 21",
            "CEP": "86185010",
            "CodOcor": "FRO2.1",
            "PreOs": "00000004",
            "CodCli": "074898",
            "Contato": "John Doe",
            "Referencia": "",
            "ObsAgenda": "",
            "NomeSC": "MGL",
            "Ramal": "",
            "Email": "<EMAIL>",
            "DtFim":  getData(data_futura),
            "Municipio": "SP"
        }                
    ]
}
