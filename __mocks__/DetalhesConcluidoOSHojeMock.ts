import { EtapasInstalacao } from "../src/data";

// etapas de instalação
export const MockDetalhesConcluidoOSHoje = () => {

  var detalhes_os = {} as EtapasInstalacao; 

  detalhes_os = {
    cliente: 'ABC Ltda',
    numero_os: '12839821701',
    tipo_os : 0,
    status_os: 0,
    latitude: -23.5930874,
    longitude: -46.6828586,
    horario_ini: '08:15:23',
    horario_fim: '10:07:00',
    descricao_problema: '',
    codigo_probe: '4C752584CC2C',
    tipo_conexao: 0,
    operadora: 1,
    ip:'',
    mascara:'',
    gateway:'',
    ssid:'',
    ssid_senha:'',
    medidor_fabricante: 1,
    medidor_modelo: 1,            
    medidor_ns: '57678/888',
    teste_conexao: true,    
    imagem_local1: '',
    imagem_texto_local1: '',
    imagem_local2: '',
    imagem_texto_local2: '',
    imagem_local3: '',
    imagem_texto_local3: '',
    imagem_local4: '',
    imagem_texto_local4: '',
    imagem_local5: '',
    imagem_texto_local5: '',
    imagem_local6: '',
    imagem_texto_local6: '',
    imagem_local7: '',
    imagem_texto_local7: '',
    imagem_local8: '',
    imagem_texto_local8: '',
    totalMaterial1: 2,
    textoMaterial1: 'Antena',
    totalMaterial2: 0,
    textoMaterial2: 'Caixa Externa',
    totalMaterial3: 1,
    textoMaterial3: 'Duplicador',
    totalMaterial4: 1,
    textoMaterial4: 'Conversor',
    totalMaterial5: 0,
    textoMaterial5: 'Cabo Externo',
    totalMaterial6: 2,
    textoMaterial6: 'Cabo Interno',
    materialExtra: true,
    textoMaterialExtra:'2 metros de fio 3mm',    
    imagem_instalacao1: '',
    imagem_texto_instalacao1: '',
    imagem_instalacao2: '',
    imagem_texto_instalacao2: '',
    imagem_instalacao3: '',
    imagem_texto_instalacao3: '',
    imagem_instalacao4: '',
    imagem_texto_instalacao4: '',
    imagem_instalacao5: '',
    imagem_texto_instalacao5: '',
    imagem_instalacao6: '',
    imagem_texto_instalacao6: '',    
    imagem_retirada1: '',
    imagem_texto_retirada1: '',
    imagem_retirada2: '',
    imagem_texto_retirada2: '',
    imagem_retirada3: '',
    imagem_texto_retirada3: '',
    imagem_retirada4: '',
    imagem_texto_retirada4: '',
    observacoes: 'Nada consta.',
    imagem_assinatura: '',
    cliente_nome: '',
    cliente_telefone: '',
    concorda: true,
    step1: false,
    step2: false,
    step3: false,
    step4: false,
    step5: false,
    step6: false,
    step7: false,
  };

  return detalhes_os;

};