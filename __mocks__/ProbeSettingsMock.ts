import { ProbeSettings } from "../src/data";

// estrutura inicial de configuração da probe
export const MockProbeSettings = () => {      
        
  var probeSettings = {} as ProbeSettings;

  probeSettings = {

    user: 'App',
    id : '',
    firmware : 'Firmware',
    datetime : 946692000, // 01/01/2000 00:00    
    sinal : 9999,
    bateria : 9999,
    vBateria: 9999,
    tipoConexao : 9999,
    tipoOperadora : 9999,
    v_probe : 9999,    
    firmwareBoot : 946692000, // 01/01/2000 00:00 
    excecoes : 0,
    resets: 0,
    heap : 0,
    totalMemory: 0, 
    usedMemory: 0,
    sistema : false,
    rede : false,
    modbus : false,
    codi : false,
    ion : false,
    pulso: false,
    ota : false,

    wifiNome: '',
    wifiSenha: '',
    wifiIP: '0.0.0.0',
    wifiGateway: '0.0.0.0',
    wifiMascara: '0.0.0.0',
    wifiDNS: '0.0.0.0',
    wifiSinal: 0,
    wifiMAC: '00:00:00:00:00:00',
  
    gsmRede: '---',
    gsmModem: '---',
    gsmIMEI: '---',
    gsmIMSI: '---',
    gsmICCID: '---',
    gsmSinal: 0,
    gsmOperadora: '---',
    gsmAPN: '---',
    gsmIP: '0.0.0.0',
    gsmGateway: '0.0.0.0',
    gsmMascara: '0.0.0.0',
    gsmLatitude: 0,
    gsmLongitude: 0,

    ethernetIP: '0.0.0.0',
    ethernetMascara: '0.0.0.0',
    ethernetGateway: '0.0.0.0',
    ethernetDNS: '0.0.0.0',
    ethernetMAC: '00:00:00:00:00:00',
    ethernetDHCP: 0,
    
    modbusDevices: [],

    codiDevices: [],

    ionDevices: [],

    pulsoDevices: [],
  }

  return probeSettings;
} 