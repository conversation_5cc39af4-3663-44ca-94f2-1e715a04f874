import { EtapasInstalacao } from "../src/data";
import { <PERSON><PERSON> } from "../src/funcoes";

// etapas da visita
export const Mo<PERSON>EtapasVisitaHoje = () => {
        
    var etapasVisita = {} as EtapasInstalacao; 
   
    etapasVisita = {
        cliente: '',
        numero_os: '',
        tipo_os : 0,
        status_os: 0,
        latitude: 0,
        longitude: 0,
        horario_ini: '00:00:00',
        horario_fim: '00:00:00',
        descricao_problema: 'Procurar encarregado do setor para maiores informações',
        codigo_probe: '4C752584CC2C',
        tipo_conexao: 0,
        operadora: 2,
        ip:'',
        mascara:'',
        gateway:'',
        ssid:'',
        ssid_senha:'',      
        medidor_fabricante: 0,
        medidor_modelo: 0,            
        medidor_ns: '1234888/999',
        teste_conexao: false,
        imagem_local1: '',
        imagem_texto_local1: '',
        imagem_local2: '',
        imagem_texto_local2: '',
        imagem_local3: '',
        imagem_texto_local3: '',
        imagem_local4: '',
        imagem_texto_local4: '',
        imagem_local5: '',
        imagem_texto_local5: '',
        imagem_local6: '',
        imagem_texto_local6: '',
        imagem_local7: '',
        imagem_texto_local7: '',
        imagem_local8: '',
        imagem_texto_local8: '',
        totalMaterial1: 0,
        textoMaterial1: 'Antena',
        totalMaterial2: 0,
        textoMaterial2: 'Caixa Externa',
        totalMaterial3: 0,
        textoMaterial3: 'Duplicador',
        totalMaterial4: 0,
        textoMaterial4: 'Conversor',
        totalMaterial5: 0,
        textoMaterial5: 'Cabo Externo',
        totalMaterial6: 0,
        textoMaterial6: 'Cabo Interno',
        materialExtra: false,
        textoMaterialExtra:'',
        imagem_instalacao1: '',
        imagem_texto_instalacao1: '',
        imagem_instalacao2: '',
        imagem_texto_instalacao2: '',
        imagem_instalacao3: '',
        imagem_texto_instalacao3: '',
        imagem_instalacao4: '',
        imagem_texto_instalacao4: '',
        imagem_instalacao5: '',
        imagem_texto_instalacao5: '',
        imagem_instalacao6: '',
        imagem_texto_instalacao6: '',      
        imagem_retirada1: '',
        imagem_texto_retirada1: '',
        imagem_retirada2: '',
        imagem_texto_retirada2: '',
        imagem_retirada3: '',
        imagem_texto_retirada3: '',
        imagem_retirada4: '',
        imagem_texto_retirada4: '',
        observacoes: '',
        imagem_assinatura: '', 
        cliente_nome: '',
        cliente_telefone: '',           
        concorda:true,
        step1: false,
        step2: false,
        step3: false,
        step4: false,
        step5: false,
        step6: false,
        step7: false,
    };
   
    return etapasVisita;
  }